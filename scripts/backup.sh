#!/bin/bash
# GlowRoya Backup Script - Updated for Remote VPS Database
# Automated backup script for PostgreSQL database and application files

set -e  # Exit on any error

# Configuration
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Database Configuration (Remote VPS)
DB_HOST="*************"
DB_USER="remote_admin"
DB_NAME="glowroya"
DB_PORT="5432"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

log "Starting GlowRoya backup process..."

# Database backup
log "Starting remote database backup..."
if command -v pg_dump >/dev/null 2>&1; then
    # Set PGPASSWORD environment variable for non-interactive backup
    export PGPASSWORD="Vahid6636!"
    
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" | gzip > "$BACKUP_DIR/db_backup_$DATE.sql.gz"; then
        log "✅ Database backup completed: db_backup_$DATE.sql.gz"
    else
        error "❌ Database backup failed"
        exit 1
    fi
    
    # Clear password from environment
    unset PGPASSWORD
else
    error "pg_dump not found. Please install PostgreSQL client tools."
    exit 1
fi

# File system backup (uploads)
log "Starting file system backup..."
if [ -d "./backend/uploads" ]; then
    if tar -czf "$BACKUP_DIR/uploads_backup_$DATE.tar.gz" ./backend/uploads/; then
        log "✅ Uploads backup completed: uploads_backup_$DATE.tar.gz"
    else
        error "❌ Uploads backup failed"
        exit 1
    fi
else
    warning "⚠️ Uploads directory not found, skipping uploads backup"
fi

# Configuration backup
log "Starting configuration backup..."
CONFIG_FILES=(
    "./docker-compose.yml"
    "./backend/.env"
    "./.env"
    "./nginx/"
    "./scripts/"
)

# Check which files exist and add them to backup
EXISTING_FILES=()
for file in "${CONFIG_FILES[@]}"; do
    if [ -e "$file" ]; then
        EXISTING_FILES+=("$file")
    fi
done

if [ ${#EXISTING_FILES[@]} -gt 0 ]; then
    if tar -czf "$BACKUP_DIR/config_backup_$DATE.tar.gz" "${EXISTING_FILES[@]}"; then
        log "✅ Configuration backup completed: config_backup_$DATE.tar.gz"
    else
        error "❌ Configuration backup failed"
        exit 1
    fi
else
    warning "⚠️ No configuration files found, skipping config backup"
fi

# Application logs backup (if exists)
if [ -d "./backend/logs" ]; then
    log "Starting logs backup..."
    if tar -czf "$BACKUP_DIR/logs_backup_$DATE.tar.gz" ./backend/logs/; then
        log "✅ Logs backup completed: logs_backup_$DATE.tar.gz"
    else
        warning "⚠️ Logs backup failed, continuing..."
    fi
fi

# Cleanup old backups
log "Cleaning up old backups (older than $RETENTION_DAYS days)..."
if find "$BACKUP_DIR" -name "*_backup_*.gz" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null; then
    log "✅ Old backups cleaned up"
else
    warning "⚠️ Could not clean up old backups"
fi

# Generate backup summary
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
BACKUP_COUNT=$(find "$BACKUP_DIR" -name "*_backup_$DATE.*" -type f | wc -l)

log "🎉 Backup process completed successfully!"
log "📊 Backup Summary:"
log "   - Date: $DATE"
log "   - Files created: $BACKUP_COUNT"
log "   - Total backup size: $BACKUP_SIZE"
log "   - Location: $BACKUP_DIR"

# Optional: Send notification (uncomment if needed)
# echo "GlowRoya backup completed successfully on $(date)" | mail -s "Backup Success" <EMAIL>

exit 0
