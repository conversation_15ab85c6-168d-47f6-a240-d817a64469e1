#!/bin/bash
# GlowRoya Setup Verification Script
# Comprehensive verification of all system components

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[✅]${NC} $1"; }
error() { echo -e "${RED}[❌]${NC} $1" >&2; }
warning() { echo -e "${YELLOW}[⚠️]${NC} $1"; }
info() { echo -e "${BLUE}[ℹ️]${NC} $1"; }

echo "🔍 GlowRoya System Verification"
echo "================================"

# Check Node.js and npm
info "Checking Node.js and npm..."
if command -v node >/dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    log "Node.js installed: $NODE_VERSION"
else
    error "Node.js not found"
    exit 1
fi

if command -v npm >/dev/null 2>&1; then
    NPM_VERSION=$(npm --version)
    log "npm installed: $NPM_VERSION"
else
    error "npm not found"
    exit 1
fi

# Check project structure
info "Checking project structure..."
REQUIRED_DIRS=("src" "backend" "backend/src" "backend/prisma" "scripts")
for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        log "Directory exists: $dir"
    else
        error "Missing directory: $dir"
        exit 1
    fi
done

REQUIRED_FILES=("package.json" "backend/package.json" "docker-compose.yml" "backend/prisma/schema.prisma")
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        log "File exists: $file"
    else
        error "Missing file: $file"
        exit 1
    fi
done

# Check environment files
info "Checking environment configuration..."
if [ -f "backend/.env" ]; then
    log "Backend environment file exists"
    
    # Check for required environment variables
    if grep -q "DATABASE_URL.**************" backend/.env; then
        log "Remote database URL configured"
    else
        warning "Remote database URL not found in .env"
    fi
    
    if grep -q "remote_admin" backend/.env; then
        log "Remote database user configured"
    else
        warning "Remote database user not configured"
    fi
else
    warning "Backend .env file not found"
fi

# Check database connection
info "Testing remote database connection..."
if command -v pg_isready >/dev/null 2>&1; then
    if pg_isready -h ************* -p 5432 -U remote_admin -d glowroya >/dev/null 2>&1; then
        log "Remote database connection successful"
    else
        error "Cannot connect to remote database"
    fi
else
    warning "pg_isready not found, skipping database connection test"
fi

# Check backend dependencies
info "Checking backend dependencies..."
cd backend
if [ -d "node_modules" ]; then
    log "Backend dependencies installed"
else
    warning "Backend dependencies not installed, installing now..."
    npm install
    log "Backend dependencies installed"
fi

# Check Prisma client
if [ -d "node_modules/.prisma" ]; then
    log "Prisma client generated"
else
    warning "Prisma client not generated, generating now..."
    npx prisma generate
    log "Prisma client generated"
fi

# Test backend build
info "Testing backend build..."
if npm run build >/dev/null 2>&1; then
    log "Backend builds successfully"
else
    error "Backend build failed"
    exit 1
fi

cd ..

# Check frontend dependencies
info "Checking frontend dependencies..."
if [ -d "node_modules" ]; then
    log "Frontend dependencies installed"
else
    warning "Frontend dependencies not installed, installing now..."
    npm install
    log "Frontend dependencies installed"
fi

# Test frontend build
info "Testing frontend build..."
if npm run build >/dev/null 2>&1; then
    log "Frontend builds successfully"
else
    error "Frontend build failed"
    exit 1
fi

# Check Docker
info "Checking Docker..."
if command -v docker >/dev/null 2>&1; then
    if docker info >/dev/null 2>&1; then
        log "Docker is running"
    else
        warning "Docker is installed but not running"
    fi
else
    warning "Docker not found"
fi

if command -v docker-compose >/dev/null 2>&1; then
    log "Docker Compose available"
else
    warning "Docker Compose not found"
fi

# Check if backend server can start
info "Testing backend server startup..."
cd backend
if timeout 10s node dist/server.js >/dev/null 2>&1 &
then
    SERVER_PID=$!
    sleep 5
    
    # Test health endpoint
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        log "Backend server starts and responds to health checks"
    else
        warning "Backend server starts but health check failed"
    fi
    
    # Kill the test server
    kill $SERVER_PID 2>/dev/null || true
else
    error "Backend server failed to start"
fi

cd ..

# Summary
echo ""
echo "🎉 Verification Summary"
echo "======================"
log "✅ Project structure verified"
log "✅ Dependencies installed"
log "✅ Remote database configured"
log "✅ Build processes working"
log "✅ Backend server functional"

echo ""
info "🚀 System is ready for development and deployment!"
info "📚 Next steps:"
echo "   1. Start backend: cd backend && npm run dev"
echo "   2. Start frontend: npm run dev"
echo "   3. Visit: http://localhost:5173"
echo "   4. API Health: http://localhost:3001/health"

exit 0
