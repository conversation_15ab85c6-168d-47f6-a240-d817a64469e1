<!DOCTYPE html>
<html>
<head>
  <title>PWA Icon Generator</title>
  <style>
    .icon {
      width: 144px;
      height: 144px;
      background: linear-gradient(135deg, #eb4d7c, #d93861);
      border-radius: 14.4px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      font-family: Arial, sans-serif;
      font-weight: bold;
    }
    .main-text {
      font-size: 21.599999999999998px;
      margin-bottom: 7.2px;
    }
    .sub-text {
      font-size: 14.4px;
    }
  </style>
</head>
<body>
  <div class="icon">
    <div class="main-text">آرامش</div>
    <div class="sub-text">پوست</div>
  </div>
  <script>
    // Instructions for manual PNG generation:
    // 1. Right-click on the icon
    // 2. "Inspect Element"
    // 3. Right-click on the div.icon element in DevTools
    // 4. "Capture node screenshot"
    // 5. Save as icon-144x144.png
    console.log('To generate PNG: Right-click icon > Inspect > Right-click div.icon > Capture node screenshot');
  </script>
</body>
</html>