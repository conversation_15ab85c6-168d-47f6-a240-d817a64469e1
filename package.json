{"name": "persian-skincare-ecommerce", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@sentry/react": "^9.26.0", "@sentry/tracing": "^7.120.3", "compression-webpack-plugin": "^11.1.0", "date-fns": "^4.1.0", "date-fns-jalali": "^4.1.0-0", "framer-motion": "^11.18.2", "jalaali-js": "^1.2.8", "lucide-react": "^0.344.0", "persian-tools": "^1.0.6", "react": "^18.3.1", "react-confetti": "^6.4.0", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-ga4": "^2.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-hotjar": "^6.3.1", "react-image-gallery": "^1.4.0", "react-intersection-observer": "^9.16.0", "react-rating-stars-component": "^2.2.0", "react-router-dom": "^6.22.1", "react-select": "^5.10.1", "react-slider": "^2.0.6", "react-virtualized": "^9.22.6", "swiper": "^11.2.8", "vite-plugin-pwa": "^1.0.0", "workbox-webpack-plugin": "^7.3.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-helmet-async": "^1.0.1", "@types/yup": "^0.29.14", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5"}}