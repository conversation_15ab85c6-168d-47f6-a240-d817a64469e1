# Comprehensive Self-Hosted E-commerce Platform Project Plan
## Persian Skincare E-commerce Platform - Complete Development Roadmap

### Executive Summary
This document provides a comprehensive project plan for developing a complete self-hosted e-commerce platform for Persian skincare products, building upon the existing foundation and targeting deployment on VPS infrastructure (*************) with PostgreSQL and Docker containerization.

### Current Status Assessment
**Existing Infrastructure:**
- ✅ VPS Server: ************* (operational)
- ✅ PostgreSQL Database: Configured (root/Vahid6636!)
- ✅ Docker Environment: Nginx + PostgreSQL containers
- ✅ Frontend Framework: React 18 + TypeScript + Vite
- ✅ UI Framework: Tailwind CSS + Headless UI
- ✅ PWA Support: Configured with service workers
- ✅ Persian/RTL Support: Comprehensive implementation

**Current Development Status:**
- ✅ Phase 1: Core Features (100% Complete)
  - Advanced mega menu navigation
  - Product detail pages with variants
  - Search with autocomplete
  - Comprehensive filtering system
  - SEO optimization
  - Trust signals and security badges
- ✅ Phase 2: Advanced Features (95% Complete)
  - User authentication system
  - Administrative backend (Products, Orders, Users, Reviews, Loyalty, Content, Analytics)
  - Admin layout and navigation
  - Customer reviews and rating system
  - Loyalty program with tiers and rewards
  - Newsletter integration
- ✅ Backend API: Fully implemented and running (Phase 1 Backend Complete)
  - Product Management API with CRUD operations
  - User Authentication API with JWT
  - Order Management API
  - Customer Management API
  - Review and Rating System API
  - Loyalty Program API
  - PostgreSQL database integration
- ❌ Payment Gateway: Not implemented (Phase 2 Backend)
- ❌ Production Deployment: Not configured (Phase 4)

---

## Technology Stack Recommendations

### Backend Architecture
**Primary Stack:**
- **API Framework:** Node.js + Express.js + TypeScript
- **Database:** PostgreSQL 15+ (existing)
- **ORM:** Prisma (type-safe, excellent TypeScript support)
- **Authentication:** JWT + bcrypt + refresh tokens
- **File Storage:** Local filesystem + nginx static serving
- **Caching:** Redis (for sessions, cart data, search cache)
- **Email Service:** Nodemailer + SMTP (self-hosted)

**Alternative Stack (if Node.js not preferred):**
- **API Framework:** Python + FastAPI + SQLAlchemy
- **Database:** PostgreSQL 15+ (existing)
- **Authentication:** JWT + passlib
- **File Storage:** Local filesystem + nginx static serving

### DevOps & Deployment
- **Containerization:** Docker + Docker Compose
- **Reverse Proxy:** Nginx (existing)
- **SSL/TLS:** Let's Encrypt + Certbot
- **Process Management:** PM2 (Node.js) or systemd
- **Monitoring:** Prometheus + Grafana (self-hosted)
- **Backup:** PostgreSQL automated backups + file system snapshots
- **CI/CD:** GitHub Actions + self-hosted runners

### Security Implementation
- **API Security:** Rate limiting, CORS, helmet.js
- **Database Security:** Connection pooling, prepared statements
- **File Upload Security:** Type validation, size limits, virus scanning
- **SSL/TLS:** A+ grade SSL configuration
- **Backup Encryption:** GPG encrypted backups

---

## Phase-Based Development Plan

### Phase 1: Backend Foundation (Weeks 1-2)
**Goal:** Establish robust backend API and database integration

#### Task 1.1: Backend API Setup ✅ COMPLETED
**Priority:** CRITICAL
**Estimated Time:** 16 hours
**Dependencies:** None
**Status:** COMPLETED - Backend API server successfully implemented and running
**Completion Date:** June 6, 2025

**Deliverables:**
- Express.js + TypeScript API server
- PostgreSQL connection with Prisma ORM
- Database schema design and migrations
- Basic CRUD endpoints for products, users, orders
- Authentication middleware with JWT
- File upload handling for product images
- API documentation with Swagger/OpenAPI

**Database Schema Design:**
```sql
-- Core tables needed:
- users (id, email, password_hash, profile_data, created_at)
- products (id, name, description, price, stock, images, seo_data)
- categories (id, name, slug, parent_id, description)
- orders (id, user_id, status, total, shipping_address, created_at)
- order_items (id, order_id, product_id, quantity, price)
- reviews (id, product_id, user_id, rating, comment, created_at)
- loyalty_points (id, user_id, points, transaction_type, created_at)
- admin_users (id, email, password_hash, role, permissions)
```

#### Task 1.2: Authentication & Authorization ✅ COMPLETED
**Priority:** CRITICAL
**Estimated Time:** 12 hours
**Dependencies:** Task 1.1 ✅
**Status:** COMPLETED - Full authentication system implemented and tested
**Completion Date:** June 6, 2025

**Deliverables:**
- JWT-based authentication system
- Role-based access control (Customer, Admin, Super Admin)
- Password reset functionality
- Session management with refresh tokens
- Admin authentication separation
- Persian error messages and validation

#### Task 1.3: Product Management API
**Priority:** HIGH
**Estimated Time:** 10 hours
**Dependencies:** Task 1.1, 1.2

**Deliverables:**
- Complete product CRUD operations
- Product variant management
- Category hierarchy management
- Image upload and processing
- SEO data management
- Inventory tracking
- Persian slug generation

### Phase 2: Core E-commerce Features (Weeks 3-4)
**Goal:** Implement essential e-commerce functionality

#### Task 2.1: Shopping Cart & Checkout API
**Priority:** HIGH
**Estimated Time:** 14 hours
**Dependencies:** Phase 1 complete

**Deliverables:**
- Cart management (add, update, remove items)
- Cart persistence (database + session)
- Checkout process API
- Order creation and management
- Inventory validation
- Price calculation with discounts
- Shipping cost calculation

#### Task 2.2: Payment Gateway Integration
**Priority:** HIGH
**Estimated Time:** 16 hours
**Dependencies:** Task 2.1

**Deliverables:**
- Iranian payment gateway integration (Zarinpal, Mellat, Parsian)
- Payment verification and callback handling
- Order status updates based on payment
- Refund processing capability
- Payment security implementation
- Transaction logging and audit trail

#### Task 2.3: Order Management System
**Priority:** HIGH
**Estimated Time:** 12 hours
**Dependencies:** Task 2.2

**Deliverables:**
- Order status workflow (pending → processing → shipped → delivered)
- Order tracking system
- Shipping integration (if applicable)
- Order notifications (email/SMS)
- Return and refund management
- Order analytics and reporting

### Phase 3: Advanced Features (Weeks 5-6)
**Goal:** Implement user engagement and business intelligence features

#### Task 3.1: Review & Rating System
**Priority:** MEDIUM
**Estimated Time:** 10 hours
**Dependencies:** Phase 2 complete

**Deliverables:**
- Product review submission and moderation
- Rating aggregation and display
- Review helpfulness voting
- Review analytics
- Spam detection and filtering
- Persian text processing for reviews

#### Task 3.2: Loyalty Program
**Priority:** MEDIUM
**Estimated Time:** 8 hours
**Dependencies:** Task 3.1

**Deliverables:**
- Points earning system (purchase, review, referral)
- Points redemption functionality
- Tier-based loyalty levels
- Loyalty analytics dashboard
- Persian loyalty messaging

#### Task 3.3: Newsletter & Marketing
**Priority:** MEDIUM
**Estimated Time:** 6 hours
**Dependencies:** Task 3.2

**Deliverables:**
- Newsletter subscription management
- Email template system
- Automated marketing campaigns
- Subscriber segmentation
- Analytics and tracking

### Phase 4: Production Deployment (Week 7)
**Goal:** Deploy to production environment with monitoring and security

#### Task 4.1: Docker Containerization
**Priority:** CRITICAL
**Estimated Time:** 8 hours
**Dependencies:** Phase 3 complete

**Deliverables:**
- Multi-stage Docker builds for frontend and backend
- Docker Compose configuration for production
- Environment variable management
- Health checks and restart policies
- Volume management for persistent data

#### Task 4.2: Production Deployment
**Priority:** CRITICAL
**Estimated Time:** 12 hours
**Dependencies:** Task 4.1

**Deliverables:**
- Nginx configuration for reverse proxy
- SSL certificate setup with Let's Encrypt
- Domain configuration and DNS setup
- Production environment variables
- Database migration and seeding
- Static file serving optimization

#### Task 4.3: Monitoring & Backup
**Priority:** HIGH
**Estimated Time:** 8 hours
**Dependencies:** Task 4.2

**Deliverables:**
- Application monitoring (uptime, performance)
- Error tracking and logging
- Automated database backups
- File system backup strategy
- Monitoring dashboard setup
- Alert system configuration

### Phase 5: Optimization & Security (Week 8)
**Goal:** Performance optimization and security hardening

#### Task 5.1: Performance Optimization
**Priority:** HIGH
**Estimated Time:** 10 hours
**Dependencies:** Phase 4 complete

**Deliverables:**
- Database query optimization
- API response caching
- Image optimization and CDN setup
- Frontend bundle optimization
- Database indexing strategy
- Performance monitoring setup

#### Task 5.2: Security Hardening
**Priority:** CRITICAL
**Estimated Time:** 8 hours
**Dependencies:** Task 5.1

**Deliverables:**
- Security audit and penetration testing
- Rate limiting implementation
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Security headers configuration

---

## Infrastructure Requirements

### Server Specifications (Current VPS)
- **CPU:** Minimum 2 cores (4 cores recommended)
- **RAM:** Minimum 4GB (8GB recommended)
- **Storage:** Minimum 50GB SSD (100GB recommended)
- **Bandwidth:** Unlimited or high limit

### Additional Services Needed
- **Redis:** For caching and session storage
- **Email SMTP:** For transactional emails
- **Backup Storage:** External backup solution
- **Monitoring:** Self-hosted monitoring stack

### Domain and SSL
- **Domain:** Configure DNS for your domain
- **SSL Certificate:** Let's Encrypt (free) or commercial
- **Subdomain Strategy:** 
  - `www.yourdomain.com` - Main site
  - `admin.yourdomain.com` - Admin panel
  - `api.yourdomain.com` - API endpoints

---

## Risk Assessment & Mitigation

### High-Risk Areas
1. **Payment Gateway Integration**
   - Risk: Complex integration with Iranian gateways
   - Mitigation: Start with one gateway, extensive testing

2. **Database Performance**
   - Risk: Poor performance with large product catalogs
   - Mitigation: Proper indexing, query optimization

3. **Security Vulnerabilities**
   - Risk: Data breaches, payment fraud
   - Mitigation: Security audit, regular updates

4. **Server Downtime**
   - Risk: Business interruption
   - Mitigation: Monitoring, backup strategies

### Medium-Risk Areas
1. **Third-party Dependencies**
   - Risk: Package vulnerabilities
   - Mitigation: Regular updates, security scanning

2. **Scalability Issues**
   - Risk: Performance degradation with growth
   - Mitigation: Horizontal scaling preparation

---

## Success Metrics & KPIs

### Technical Metrics
- **Uptime:** 99.9% availability
- **Performance:** Page load time < 3 seconds
- **Security:** Zero critical vulnerabilities
- **Backup:** 100% backup success rate

### Business Metrics
- **Conversion Rate:** Track checkout completion
- **User Engagement:** Session duration, page views
- **Revenue:** Monthly recurring revenue growth
- **Customer Satisfaction:** Review ratings, support tickets

---

## Next Steps

1. **Immediate Actions (Week 1):**
   - Set up development environment
   - Initialize backend API project
   - Design and implement database schema
   - Connect frontend to backend API

2. **Short-term Goals (Weeks 2-4):**
   - Complete core e-commerce functionality
   - Implement payment gateway
   - Set up basic deployment pipeline

3. **Long-term Goals (Weeks 5-8):**
   - Advanced features implementation
   - Production deployment
   - Performance optimization
   - Security hardening

This comprehensive plan provides a systematic approach to building a complete self-hosted e-commerce platform while leveraging your existing frontend foundation and VPS infrastructure.

---

## Detailed Implementation Specifications

### Backend API Architecture

#### Directory Structure
```
backend/
├── src/
│   ├── controllers/          # Route handlers
│   ├── middleware/           # Authentication, validation
│   ├── models/              # Database models (Prisma)
│   ├── routes/              # API route definitions
│   ├── services/            # Business logic
│   ├── utils/               # Helper functions
│   ├── config/              # Configuration files
│   └── types/               # TypeScript type definitions
├── prisma/
│   ├── schema.prisma        # Database schema
│   ├── migrations/          # Database migrations
│   └── seed.ts             # Database seeding
├── uploads/                 # File upload directory
├── logs/                    # Application logs
├── docker/                  # Docker configuration
├── tests/                   # Unit and integration tests
└── docs/                    # API documentation
```

#### Core API Endpoints
```typescript
// Authentication
POST   /api/auth/register
POST   /api/auth/login
POST   /api/auth/logout
POST   /api/auth/refresh
POST   /api/auth/forgot-password
POST   /api/auth/reset-password

// Products
GET    /api/products              # List products with filters
GET    /api/products/:id          # Get product details
POST   /api/products              # Create product (admin)
PUT    /api/products/:id          # Update product (admin)
DELETE /api/products/:id          # Delete product (admin)
POST   /api/products/:id/images   # Upload product images

// Categories
GET    /api/categories            # List categories
GET    /api/categories/:id        # Get category details
POST   /api/categories            # Create category (admin)
PUT    /api/categories/:id        # Update category (admin)

// Cart & Checkout
GET    /api/cart                  # Get user cart
POST   /api/cart/items            # Add item to cart
PUT    /api/cart/items/:id        # Update cart item
DELETE /api/cart/items/:id        # Remove cart item
POST   /api/checkout              # Process checkout

// Orders
GET    /api/orders                # List user orders
GET    /api/orders/:id            # Get order details
POST   /api/orders/:id/cancel     # Cancel order
GET    /api/admin/orders          # List all orders (admin)
PUT    /api/admin/orders/:id      # Update order status (admin)

// Reviews
GET    /api/products/:id/reviews  # Get product reviews
POST   /api/products/:id/reviews  # Create review
PUT    /api/reviews/:id           # Update review
DELETE /api/reviews/:id           # Delete review

// Loyalty
GET    /api/loyalty/points        # Get user points
POST   /api/loyalty/redeem        # Redeem points
GET    /api/loyalty/history       # Points history

// Admin
GET    /api/admin/dashboard       # Dashboard statistics
GET    /api/admin/users           # User management
GET    /api/admin/analytics       # Business analytics
```

### Database Schema Design

#### Core Tables
```sql
-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    birth_date DATE,
    gender VARCHAR(10),
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    loyalty_tier VARCHAR(20) DEFAULT 'bronze',
    total_spent DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description TEXT,
    sku VARCHAR(100) UNIQUE,
    barcode VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    compare_at_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    stock_quantity INTEGER DEFAULT 0,
    track_inventory BOOLEAN DEFAULT TRUE,
    allow_backorder BOOLEAN DEFAULT FALSE,
    low_stock_threshold INTEGER DEFAULT 5,
    weight DECIMAL(8,2),
    dimensions JSONB, -- {length, width, height, unit}
    status VARCHAR(20) DEFAULT 'active', -- active, draft, archived
    visibility VARCHAR(20) DEFAULT 'visible', -- visible, hidden
    featured BOOLEAN DEFAULT FALSE,
    brand VARCHAR(100),
    category_id INTEGER REFERENCES categories(id),
    images JSONB, -- Array of image URLs
    benefits TEXT[],
    ingredients TEXT[],
    how_to_use TEXT[],
    seo_title VARCHAR(255),
    seo_description TEXT,
    seo_keywords TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    parent_id INTEGER REFERENCES categories(id),
    image_url VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product variants table
CREATE TABLE product_variants (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL, -- e.g., "50ml", "Red"
    type VARCHAR(50) NOT NULL, -- e.g., "size", "color"
    value VARCHAR(255) NOT NULL, -- e.g., "50ml", "#FF0000"
    price_adjustment DECIMAL(10,2) DEFAULT 0,
    stock_quantity INTEGER DEFAULT 0,
    sku VARCHAR(100),
    image_url VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Orders table
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, processing, shipped, delivered, cancelled
    payment_status VARCHAR(20) DEFAULT 'pending', -- pending, paid, failed, refunded
    payment_method VARCHAR(50),
    payment_gateway VARCHAR(50),
    transaction_id VARCHAR(255),
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'IRR',
    shipping_address JSONB NOT NULL,
    billing_address JSONB,
    notes TEXT,
    tracking_number VARCHAR(100),
    shipped_at TIMESTAMP,
    delivered_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Order items table
CREATE TABLE order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id),
    variant_id INTEGER REFERENCES product_variants(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    product_snapshot JSONB, -- Store product details at time of order
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Shopping cart table
CREATE TABLE cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255), -- For guest users
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    variant_id INTEGER REFERENCES product_variants(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, product_id, variant_id),
    UNIQUE(session_id, product_id, variant_id)
);

-- Reviews table
CREATE TABLE reviews (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    order_id INTEGER REFERENCES orders(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    comment TEXT,
    pros TEXT[],
    cons TEXT[],
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT FALSE,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Loyalty points table
CREATE TABLE loyalty_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    order_id INTEGER REFERENCES orders(id),
    type VARCHAR(20) NOT NULL, -- earned, redeemed, expired, bonus
    points INTEGER NOT NULL,
    description TEXT,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admin users table
CREATE TABLE admin_users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'admin', -- super_admin, admin, moderator, viewer
    permissions JSONB, -- Specific permissions
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Newsletter subscriptions
CREATE TABLE newsletter_subscriptions (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    user_id INTEGER REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'active', -- active, unsubscribed
    source VARCHAR(50), -- popup, footer, checkout
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP
);
```

### Docker Configuration

#### docker-compose.yml
```yaml
version: '3.8'

services:
  # Frontend (React/Vite)
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    container_name: glowroya_frontend
    restart: unless-stopped
    volumes:
      - ./dist:/app/dist:ro
    networks:
      - glowroya_network

  # Backend API (Node.js/Express)
  backend:
    build:
      context: ./backend
      dockerfile: docker/backend.Dockerfile
    container_name: glowroya_backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************************************
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      - redis
      # postgres dependency removed as using remote VPS database
    networks:
      - glowroya_network

  # PostgreSQL Database (using remote VPS database)
  # Note: Using remote PostgreSQL on VPS *************
  # Local PostgreSQL container not needed as we use remote database
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: glowroya_postgres
  #   restart: unless-stopped
  #   environment:
  #     - POSTGRES_DB=glowroya
  #     - POSTGRES_USER=remote_admin
  #     - POSTGRES_PASSWORD=Vahid6636!
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./database/init:/docker-entrypoint-initdb.d
  #   networks:
  #     - glowroya_network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: glowroya_redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - glowroya_network

  # Nginx reverse proxy (existing)
  nginx:
    image: nginx:alpine
    container_name: glowroya_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./backend/uploads:/var/www/uploads:ro
    depends_on:
      - frontend
      - backend
    networks:
      - glowroya_network

volumes:
  postgres_data:
  redis_data:

networks:
  glowroya_network:
    driver: bridge
```

### Security Implementation

#### Authentication & Authorization
```typescript
// JWT token structure
interface JWTPayload {
  userId: number;
  email: string;
  role: 'customer' | 'admin';
  permissions?: string[];
  iat: number;
  exp: number;
}

// Password hashing
import bcrypt from 'bcrypt';
const saltRounds = 12;
const hashedPassword = await bcrypt.hash(password, saltRounds);

// Rate limiting
import rateLimit from 'express-rate-limit';
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many login attempts, please try again later'
});

// Input validation
import Joi from 'joi';
const productSchema = Joi.object({
  name: Joi.string().min(3).max(255).required(),
  price: Joi.number().positive().required(),
  description: Joi.string().max(5000),
  // ... other validations
});
```

#### File Upload Security
```typescript
import multer from 'multer';
import path from 'path';

const storage = multer.diskStorage({
  destination: './uploads/products',
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req: any, file: any, cb: any) => {
  const allowedTypes = /jpeg|jpg|png|webp/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});
```

---

## Payment Gateway Integration

### Iranian Payment Gateways

#### Zarinpal Integration
```typescript
// Zarinpal payment service
class ZarinpalService {
  private merchantId: string;
  private sandbox: boolean;

  constructor() {
    this.merchantId = process.env.ZARINPAL_MERCHANT_ID!;
    this.sandbox = process.env.NODE_ENV !== 'production';
  }

  async createPayment(amount: number, description: string, callbackUrl: string) {
    const url = this.sandbox
      ? 'https://sandbox.zarinpal.com/pg/rest/WebGate/PaymentRequest.json'
      : 'https://api.zarinpal.com/pg/rest/WebGate/PaymentRequest.json';

    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        MerchantID: this.merchantId,
        Amount: amount,
        Description: description,
        CallbackURL: callbackUrl
      })
    });

    const data = await response.json();

    if (data.Status === 100) {
      const paymentUrl = this.sandbox
        ? `https://sandbox.zarinpal.com/pg/StartPay/${data.Authority}`
        : `https://www.zarinpal.com/pg/StartPay/${data.Authority}`;

      return { success: true, authority: data.Authority, paymentUrl };
    }

    throw new Error(`Payment creation failed: ${data.Status}`);
  }

  async verifyPayment(authority: string, amount: number) {
    const url = this.sandbox
      ? 'https://sandbox.zarinpal.com/pg/rest/WebGate/PaymentVerification.json'
      : 'https://api.zarinpal.com/pg/rest/WebGate/PaymentVerification.json';

    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        MerchantID: this.merchantId,
        Amount: amount,
        Authority: authority
      })
    });

    const data = await response.json();

    return {
      success: data.Status === 100,
      refId: data.RefID,
      status: data.Status
    };
  }
}
```

#### Payment Flow Implementation
```typescript
// Payment controller
export class PaymentController {
  async initiatePayment(req: Request, res: Response) {
    try {
      const { orderId } = req.body;
      const order = await Order.findById(orderId);

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      const zarinpal = new ZarinpalService();
      const callbackUrl = `${process.env.FRONTEND_URL}/payment/callback`;

      const payment = await zarinpal.createPayment(
        order.total_amount,
        `پرداخت سفارش #${order.order_number}`,
        callbackUrl
      );

      // Store payment info
      await PaymentTransaction.create({
        order_id: orderId,
        gateway: 'zarinpal',
        authority: payment.authority,
        amount: order.total_amount,
        status: 'pending'
      });

      res.json({
        success: true,
        paymentUrl: payment.paymentUrl,
        authority: payment.authority
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleCallback(req: Request, res: Response) {
    try {
      const { Authority, Status } = req.query;

      const transaction = await PaymentTransaction.findOne({
        where: { authority: Authority }
      });

      if (!transaction) {
        return res.redirect(`${process.env.FRONTEND_URL}/payment/failed`);
      }

      if (Status === 'OK') {
        const zarinpal = new ZarinpalService();
        const verification = await zarinpal.verifyPayment(
          Authority as string,
          transaction.amount
        );

        if (verification.success) {
          // Update transaction and order
          await transaction.update({
            status: 'completed',
            ref_id: verification.refId,
            verified_at: new Date()
          });

          await Order.update(
            { payment_status: 'paid' },
            { where: { id: transaction.order_id } }
          );

          return res.redirect(`${process.env.FRONTEND_URL}/payment/success?ref=${verification.refId}`);
        }
      }

      await transaction.update({ status: 'failed' });
      res.redirect(`${process.env.FRONTEND_URL}/payment/failed`);
    } catch (error) {
      res.redirect(`${process.env.FRONTEND_URL}/payment/failed`);
    }
  }
}
```

### Email Service Implementation

#### Nodemailer Configuration
```typescript
import nodemailer from 'nodemailer';

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  async sendOrderConfirmation(order: Order, user: User) {
    const template = await this.loadTemplate('order-confirmation', {
      userName: user.first_name,
      orderNumber: order.order_number,
      orderTotal: order.total_amount.toLocaleString('fa-IR'),
      orderItems: order.items,
      trackingUrl: `${process.env.FRONTEND_URL}/orders/${order.id}/track`
    });

    await this.transporter.sendMail({
      from: process.env.FROM_EMAIL,
      to: user.email,
      subject: `تایید سفارش #${order.order_number}`,
      html: template
    });
  }

  async sendPasswordReset(user: User, resetToken: string) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

    const template = await this.loadTemplate('password-reset', {
      userName: user.first_name,
      resetUrl,
      expiryTime: '24 ساعت'
    });

    await this.transporter.sendMail({
      from: process.env.FROM_EMAIL,
      to: user.email,
      subject: 'بازیابی رمز عبور',
      html: template
    });
  }

  private async loadTemplate(templateName: string, variables: any): Promise<string> {
    // Load and compile email template with variables
    // Implementation depends on chosen template engine (Handlebars, EJS, etc.)
    return compiledTemplate;
  }
}
```

---

## Deployment Configuration

### Nginx Configuration
```nginx
# /etc/nginx/sites-available/glowroya.conf
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Frontend (React App)
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Handle client-side routing
        try_files $uri $uri/ /index.html;
    }

    # API Backend
    location /api/ {
        proxy_pass http://backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase timeout for file uploads
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }

    # Static file serving for uploads
    location /uploads/ {
        alias /var/www/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";

        # Security for uploaded files
        location ~* \.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }

    # Admin Panel (if separate subdomain)
    location /admin/ {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Rate limiting for API
    location /api/auth/ {
        limit_req zone=auth burst=5 nodelay;
        proxy_pass http://backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Rate limiting zones
http {
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
}
```

### Environment Variables
```bash
# Backend .env file
NODE_ENV=production
PORT=5000

# Database
DATABASE_URL=****************************************************************

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# Redis
REDIS_URL=redis://redis:6379

# Email
SMTP_HOST=your-smtp-host.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
FROM_EMAIL=<EMAIL>

# Payment Gateways
ZARINPAL_MERCHANT_ID=your-zarinpal-merchant-id
MELLAT_TERMINAL_ID=your-mellat-terminal-id
MELLAT_USERNAME=your-mellat-username
MELLAT_PASSWORD=your-mellat-password

# File Upload
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_PATH=/app/uploads

# Frontend URL
FRONTEND_URL=https://yourdomain.com

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000  # 15 minutes
RATE_LIMIT_MAX=100

# Monitoring
SENTRY_DSN=your-sentry-dsn-if-using
LOG_LEVEL=info
```

---

## Monitoring & Backup Strategy

### Application Monitoring
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: glowroya_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - glowroya_network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: glowroya_grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - glowroya_network

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: glowroya_node_exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - glowroya_network

volumes:
  prometheus_data:
  grafana_data:
```

### Backup Scripts
```bash
#!/bin/bash
# backup.sh - Automated backup script

# Configuration
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Database backup
echo "Starting database backup..."
# Remote database backup using pg_dump with remote connection
pg_dump -h ************* -U remote_admin -d glowroya | gzip > "$BACKUP_DIR/db_backup_$DATE.sql.gz"

# File system backup (uploads)
echo "Starting file system backup..."
tar -czf "$BACKUP_DIR/uploads_backup_$DATE.tar.gz" ./backend/uploads/

# Configuration backup
echo "Starting configuration backup..."
tar -czf "$BACKUP_DIR/config_backup_$DATE.tar.gz" \
  ./docker-compose.yml \
  ./nginx/ \
  ./.env \
  ./backend/.env

# Cleanup old backups
echo "Cleaning up old backups..."
find "$BACKUP_DIR" -name "*.gz" -mtime +$RETENTION_DAYS -delete

# Upload to remote storage (optional)
if [ "$REMOTE_BACKUP" = "true" ]; then
  echo "Uploading to remote storage..."
  # rsync or scp commands here
fi

echo "Backup completed successfully!"
```

### Health Check Implementation
```typescript
// health.controller.ts
export class HealthController {
  async checkHealth(req: Request, res: Response) {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      checks: {
        database: await this.checkDatabase(),
        redis: await this.checkRedis(),
        storage: await this.checkStorage(),
        memory: this.checkMemory()
      }
    };

    const isHealthy = Object.values(health.checks).every(check => check.status === 'ok');

    res.status(isHealthy ? 200 : 503).json(health);
  }

  private async checkDatabase() {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return { status: 'ok', responseTime: Date.now() };
    } catch (error) {
      return { status: 'error', error: error.message };
    }
  }

  private async checkRedis() {
    try {
      await redis.ping();
      return { status: 'ok' };
    } catch (error) {
      return { status: 'error', error: error.message };
    }
  }

  private async checkStorage() {
    try {
      const stats = await fs.promises.stat('./uploads');
      return {
        status: 'ok',
        freeSpace: await this.getFreeSpace()
      };
    } catch (error) {
      return { status: 'error', error: error.message };
    }
  }

  private checkMemory() {
    const usage = process.memoryUsage();
    const total = os.totalmem();
    const free = os.freemem();

    return {
      status: 'ok',
      usage: {
        rss: Math.round(usage.rss / 1024 / 1024),
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
        external: Math.round(usage.external / 1024 / 1024)
      },
      system: {
        total: Math.round(total / 1024 / 1024),
        free: Math.round(free / 1024 / 1024),
        used: Math.round((total - free) / 1024 / 1024)
      }
    };
  }
}
```

---

## Testing Strategy

### Backend Testing
```typescript
// tests/integration/auth.test.ts
import request from 'supertest';
import app from '../src/app';
import { prisma } from '../src/config/database';

describe('Authentication', () => {
  beforeEach(async () => {
    await prisma.user.deleteMany();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('token');
      expect(response.body.user.email).toBe(userData.email);
    });

    it('should not register user with existing email', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User'
      };

      await request(app).post('/api/auth/register').send(userData);

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Test',
          lastName: 'User'
        });
    });

    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200);

      expect(response.body).toHaveProperty('token');
    });

    it('should not login with invalid credentials', async () => {
      await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(401);
    });
  });
});
```

### Frontend Testing
```typescript
// src/components/__tests__/ProductCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ProductCard } from '../common/ProductCard';
import { CartProvider } from '../../context/CartContext';

const mockProduct = {
  id: 1,
  name: 'Test Product',
  price: 100000,
  imageSrc: '/test-image.jpg',
  rating: 4.5,
  reviewCount: 10,
  category: 'skincare',
  description: 'Test description',
  benefits: [],
  ingredients: [],
  howToUse: [],
  stock: 10
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <CartProvider>
      {component}
    </CartProvider>
  );
};

describe('ProductCard', () => {
  it('renders product information correctly', () => {
    renderWithProviders(<ProductCard product={mockProduct} />);

    expect(screen.getByText('Test Product')).toBeInTheDocument();
    expect(screen.getByText('100,000 تومان')).toBeInTheDocument();
    expect(screen.getByAltText('Test Product')).toBeInTheDocument();
  });

  it('adds product to cart when button is clicked', () => {
    renderWithProviders(<ProductCard product={mockProduct} />);

    const addToCartButton = screen.getByText('افزودن به سبد خرید');
    fireEvent.click(addToCartButton);

    // Verify cart state change
    expect(screen.getByText('اضافه شد')).toBeInTheDocument();
  });

  it('shows out of stock message when stock is 0', () => {
    const outOfStockProduct = { ...mockProduct, stock: 0 };
    renderWithProviders(<ProductCard product={outOfStockProduct} />);

    expect(screen.getByText('ناموجود')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

---

## Performance Optimization

### Database Optimization
```sql
-- Essential indexes for performance
CREATE INDEX idx_products_category_status ON products(category_id, status);
CREATE INDEX idx_products_featured_status ON products(featured, status);
CREATE INDEX idx_products_price_status ON products(price, status);
CREATE INDEX idx_products_created_at ON products(created_at DESC);
CREATE INDEX idx_products_search ON products USING gin(to_tsvector('english', name || ' ' || description));

CREATE INDEX idx_orders_user_status ON orders(user_id, status);
CREATE INDEX idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX idx_orders_status_created ON orders(status, created_at DESC);

CREATE INDEX idx_reviews_product_approved ON reviews(product_id, is_approved);
CREATE INDEX idx_reviews_user_created ON reviews(user_id, created_at DESC);

CREATE INDEX idx_cart_user_session ON cart_items(user_id, session_id);
CREATE INDEX idx_cart_product_variant ON cart_items(product_id, variant_id);

-- Full-text search optimization
CREATE INDEX idx_products_fulltext ON products USING gin(
  to_tsvector('simple',
    coalesce(name, '') || ' ' ||
    coalesce(description, '') || ' ' ||
    coalesce(brand, '')
  )
);
```

### Caching Strategy
```typescript
// Redis caching service
class CacheService {
  private redis: Redis;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
  }

  async get<T>(key: string): Promise<T | null> {
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }

  async del(key: string): Promise<void> {
    await this.redis.del(key);
  }

  async invalidatePattern(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }

  // Product caching
  async cacheProduct(product: Product): Promise<void> {
    await this.set(`product:${product.id}`, product, 3600); // 1 hour
  }

  async getCachedProduct(id: number): Promise<Product | null> {
    return this.get(`product:${id}`);
  }

  async invalidateProductCache(id: number): Promise<void> {
    await this.del(`product:${id}`);
    await this.invalidatePattern(`products:*`);
  }

  // Search result caching
  async cacheSearchResults(query: string, filters: any, results: any): Promise<void> {
    const key = `search:${this.generateSearchKey(query, filters)}`;
    await this.set(key, results, 1800); // 30 minutes
  }

  private generateSearchKey(query: string, filters: any): string {
    return Buffer.from(JSON.stringify({ query, filters })).toString('base64');
  }
}
```

This comprehensive project plan provides a complete roadmap for developing your self-hosted e-commerce platform. The plan builds upon your existing frontend foundation and provides detailed implementation specifications for the backend, database, deployment, and optimization strategies.

**Key Highlights:**
- ✅ Leverages your existing React/TypeScript frontend
- ✅ Provides complete backend API architecture
- ✅ Includes Iranian payment gateway integration
- ✅ Self-hosted deployment on your VPS infrastructure
- ✅ Comprehensive security and monitoring
- ✅ Persian language support throughout
- ✅ Production-ready Docker configuration

The plan follows your preferred systematic methodology and provides the foundation for building a complete, professional e-commerce platform.
