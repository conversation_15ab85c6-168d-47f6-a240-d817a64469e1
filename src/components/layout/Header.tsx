import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ShoppingBag, Search, Menu, X, ChevronDown, User, LogIn, UserPlus, Heart, Eye, ChevronLeft, ChevronRight, Shield } from 'lucide-react';
import { useCart } from '../../context/CartContext';
import { useWishlist } from '../../hooks/useWishlist';
import { useProductComparison } from '../../hooks/useProductComparison';
import { useAuth } from '../../context/AuthContext';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { useMobileDetection } from '../../hooks/useMobileDetection';
import AdvancedSearchBar from '../search/AdvancedSearchBar';
import MegaMenu from '../navigation/MegaMenu';
import AuthModal from '../auth/AuthModal';
import MobileNavigation from '../mobile/MobileNavigation';
import { navigationItems } from '../../data/categories';

const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [activeMegaMenu, setActiveMegaMenu] = useState<number | null>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<'login' | 'register'>('login');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const navScrollRef = useRef<HTMLDivElement>(null);
  const location = useLocation();
  const { totalItems, toggleCart } = useCart();
  const { totalItems: wishlistItems } = useWishlist();
  const { items: comparisonItems } = useProductComparison();
  const { user, isAuthenticated, logout } = useAuth();
  const { user: adminUser, isAuthenticated: isAdminAuthenticated, logout: adminLogout } = useAdminAuth();
  const { isMobile } = useMobileDetection();

  // Check if user is authenticated either as customer or admin
  const isUserAuthenticated = isAuthenticated || isAdminAuthenticated;
  const currentUser = user || (adminUser ? {
    id: adminUser.id,
    firstName: adminUser.firstName,
    lastName: adminUser.lastName,
    email: adminUser.email,
    role: 'admin' as const,
    isEmailVerified: true,
    isPhoneVerified: true,
    createdAt: adminUser.createdAt,
    updatedAt: adminUser.updatedAt,
    preferences: {
      language: 'fa' as const,
      newsletter: false,
      smsNotifications: true,
      emailNotifications: true,
      theme: 'light' as const
    },
    addresses: []
  } : null);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsMobileMenuOpen(false);
    setIsSearchOpen(false);
    setActiveMegaMenu(null);
    setIsAuthModalOpen(false);
    setShowUserMenu(false);
  }, [location]);

  const handleMegaMenuToggle = (itemId: number) => {
    setActiveMegaMenu(activeMegaMenu === itemId ? null : itemId);
  };

  const handleMegaMenuClose = () => {
    setActiveMegaMenu(null);
  };

  const handleAuthModal = (mode: 'login' | 'register') => {
    setAuthModalMode(mode);
    setIsAuthModalOpen(true);
  };

  const handleLogout = async () => {
    setShowUserMenu(false);
    if (isAuthenticated) {
      await logout();
    } else if (isAdminAuthenticated) {
      await adminLogout();
    }
  };

  const checkScrollButtons = () => {
    if (navScrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = navScrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scrollNavigation = (direction: 'left' | 'right') => {
    if (navScrollRef.current) {
      const scrollAmount = 200;
      const newScrollLeft = direction === 'left'
        ? navScrollRef.current.scrollLeft - scrollAmount
        : navScrollRef.current.scrollLeft + scrollAmount;

      navScrollRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      checkScrollButtons();
    }, 100);

    const handleResize = () => {
      setTimeout(checkScrollButtons, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    if (navScrollRef.current) {
      const navElement = navScrollRef.current;
      navElement.addEventListener('scroll', checkScrollButtons);
      return () => {
        navElement.removeEventListener('scroll', checkScrollButtons);
      };
    }
  }, []);

  return (
    <>
      <header
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled ? 'bg-white shadow-md py-3' : 'bg-transparent py-5'
        } ${isMobile ? 'pb-safe-bottom' : ''}`}
      >
      <div className="container-custom">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <img 
              src="/logo.png" 
              alt="GlowRoya" 
              className="h-12 w-auto"
            />
          </Link>

          {/* Desktop Navigation (1024px+) */}
          <nav className="hidden lg:flex items-center space-x-1 space-x-reverse">
            {navigationItems.map((item) => (
              <div key={item.id} className="relative">
                {item.type === 'link' ? (
                  <Link
                    to={item.path!}
                    className={`nav-link mx-3 ${
                      location.pathname === item.path ? 'active' : ''
                    }`}
                  >
                    {item.name}
                  </Link>
                ) : item.type === 'megamenu' ? (
                  <button
                    onClick={() => handleMegaMenuToggle(item.id)}
                    className={`nav-link mx-3 flex items-center ${
                      activeMegaMenu === item.id ? 'active' : ''
                    }`}
                  >
                    {item.name}
                    <ChevronDown
                      className={`w-4 h-4 mr-1 transition-transform duration-200 ${
                        activeMegaMenu === item.id ? 'rotate-180' : ''
                      }`}
                    />
                  </button>
                ) : null}
              </div>
            ))}
          </nav>

          {/* Tablet Navigation (768px-1024px) - Scrollable */}
          <div className="hidden md:flex lg:hidden items-center flex-1 mx-4 relative">
            {/* Left Scroll Button */}
            {canScrollLeft && (
              <button
                onClick={() => scrollNavigation('left')}
                className="absolute left-0 z-10 p-1 bg-white shadow-md rounded-full hover:bg-gray-50 transition-colors"
                aria-label="اسکرول به چپ"
              >
                <ChevronRight className="w-4 h-4 text-gray-600" />
              </button>
            )}

            {/* Scrollable Navigation */}
            <div
              ref={navScrollRef}
              className="flex items-center space-x-1 space-x-reverse overflow-x-auto scrollbar-hide scroll-smooth"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
              onScroll={checkScrollButtons}
            >
              {navigationItems.map((item) => (
                <div key={item.id} className="relative flex-shrink-0">
                  {item.type === 'link' ? (
                    <Link
                      to={item.path!}
                      className={`nav-link mx-2 whitespace-nowrap ${
                        location.pathname === item.path ? 'active' : ''
                      }`}
                    >
                      {item.name}
                    </Link>
                  ) : item.type === 'megamenu' ? (
                    <button
                      onClick={() => handleMegaMenuToggle(item.id)}
                      className={`nav-link mx-2 flex items-center whitespace-nowrap ${
                        activeMegaMenu === item.id ? 'active' : ''
                      }`}
                    >
                      {item.name}
                      <ChevronDown
                        className={`w-4 h-4 mr-1 transition-transform duration-200 ${
                          activeMegaMenu === item.id ? 'rotate-180' : ''
                        }`}
                      />
                    </button>
                  ) : null}
                </div>
              ))}
            </div>

            {/* Right Scroll Button */}
            {canScrollRight && (
              <button
                onClick={() => scrollNavigation('right')}
                className="absolute right-0 z-10 p-1 bg-white shadow-md rounded-full hover:bg-gray-50 transition-colors"
                aria-label="اسکرول به راست"
              >
                <ChevronLeft className="w-4 h-4 text-gray-600" />
              </button>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-1 md:space-x-2 space-x-reverse">
            {/* Search */}
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="p-1.5 md:p-2 rounded-full hover:bg-primary-50 transition-colors"
              aria-label="جستجو"
            >
              <Search className="h-5 w-5 text-text-secondary" />
            </button>

            {/* Wishlist - Hidden on tablet, shown on desktop */}
            <Link
              to="/wishlist"
              className="hidden lg:flex p-2 rounded-full hover:bg-primary-50 transition-colors relative"
              aria-label="علاقه‌مندی‌ها"
            >
              <Heart className="h-5 w-5 text-text-secondary" />
              {wishlistItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                  {wishlistItems}
                </span>
              )}
            </Link>

            {/* Comparison - Hidden on tablet, shown on desktop */}
            <Link
              to="/comparison"
              className="hidden lg:flex p-2 rounded-full hover:bg-primary-50 transition-colors relative"
              aria-label="مقایسه محصولات"
            >
              <Eye className="h-5 w-5 text-text-secondary" />
              {comparisonItems.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                  {comparisonItems.length}
                </span>
              )}
            </Link>

            {/* Authentication */}
            {isUserAuthenticated && currentUser ? (
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center gap-1 md:gap-2 p-1.5 md:p-2 rounded-full hover:bg-primary-50 transition-colors"
                  aria-label="منوی کاربری"
                >
                  <div className="bg-primary-100 rounded-full w-7 md:w-8 h-7 md:h-8 flex items-center justify-center">
                    <User className="h-3.5 md:h-4 w-3.5 md:w-4 text-primary-600" />
                  </div>
                  <span className="hidden lg:block text-sm text-text-primary">
                    {currentUser.firstName}
                  </span>
                  <ChevronDown className="h-3.5 md:h-4 w-3.5 md:w-4 text-text-secondary" />
                </button>

                {/* User Menu Dropdown */}
                {showUserMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                  >
                    {/* Show admin panel link for admin users */}
                    {isAdminAuthenticated && (
                      <Link
                        to="/admin"
                        className="flex items-center gap-2 px-4 py-2 text-sm text-purple-700 hover:bg-purple-50"
                      >
                        <Shield className="h-4 w-4" />
                        پنل مدیریت
                      </Link>
                    )}

                    {/* Show profile link only for regular users */}
                    {isAuthenticated && (
                      <Link
                        to="/profile"
                        className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <User className="h-4 w-4" />
                        پروفایل کاربری
                      </Link>
                    )}
                    <Link
                      to="/wishlist"
                      className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <Heart className="h-4 w-4" />
                      علاقه‌مندی‌ها
                      {wishlistItems > 0 && (
                        <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                          {wishlistItems}
                        </span>
                      )}
                    </Link>
                    <Link
                      to="/comparison"
                      className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <Eye className="h-4 w-4" />
                      مقایسه محصولات
                      {comparisonItems.length > 0 && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                          {comparisonItems.length}
                        </span>
                      )}
                    </Link>
                    <Link
                      to="/loyalty"
                      className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <ShoppingBag className="h-4 w-4" />
                      باشگاه مشتریان
                    </Link>
                    <hr className="my-2" />
                    <button
                      onClick={handleLogout}
                      className="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-right"
                    >
                      <LogIn className="h-4 w-4" />
                      خروج
                    </button>
                  </motion.div>
                )}
              </div>
            ) : (
              <div className="hidden md:flex items-center gap-1 lg:gap-2">
                <button
                  onClick={() => handleAuthModal('login')}
                  className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-1.5 lg:py-2 text-xs lg:text-sm text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                >
                  <LogIn className="h-3.5 lg:h-4 w-3.5 lg:w-4" />
                  <span className="hidden lg:inline">ورود</span>
                </button>
                <button
                  onClick={() => handleAuthModal('register')}
                  className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-1.5 lg:py-2 text-xs lg:text-sm bg-primary-600 text-white hover:bg-primary-700 rounded-lg transition-colors"
                >
                  <UserPlus className="h-3.5 lg:h-4 w-3.5 lg:w-4" />
                  <span className="hidden lg:inline">ثبت‌نام</span>
                </button>
              </div>
            )}

            {/* Cart */}
            <button
              onClick={toggleCart}
              className="p-1.5 md:p-2 rounded-full hover:bg-primary-50 transition-colors relative"
              aria-label="سبد خرید"
            >
              <ShoppingBag className="h-5 w-5 text-text-secondary" />
              {totalItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary-500 text-white text-xs w-4 md:w-5 h-4 md:h-5 flex items-center justify-center rounded-full">
                  {totalItems}
                </span>
              )}
            </button>

            {/* Mobile Menu Button - Only show on mobile */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-1.5 md:hidden rounded-full hover:bg-primary-50 transition-colors"
              aria-label={isMobileMenuOpen ? 'بستن منو' : 'باز کردن منو'}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5 text-text-secondary" />
              ) : (
                <Menu className="h-5 w-5 text-text-secondary" />
              )}
            </button>
          </div>
        </div>

        {/* Advanced Search Bar */}
        {isSearchOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute left-0 right-0 top-full mt-1 p-4"
          >
            <AdvancedSearchBar onClose={() => setIsSearchOpen(false)} />
          </motion.div>
        )}
      </div>

      {/* Mega Menu */}
      {navigationItems.find(item => item.id === activeMegaMenu && item.type === 'megamenu') && (
        <MegaMenu
          categories={navigationItems.find(item => item.id === activeMegaMenu)?.megaMenuCategories || []}
          isOpen={activeMegaMenu !== null}
          onClose={handleMegaMenuClose}
        />
      )}

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="md:hidden bg-white shadow-md"
        >
          <nav className="container-custom py-4 flex flex-col">
            {navigationItems.map((item) => (
              <div key={item.id}>
                {item.type === 'link' ? (
                  <Link
                    to={item.path!}
                    className={`py-3 px-4 border-b border-gray-100 ${
                      location.pathname === item.path
                        ? 'text-primary-500'
                        : 'text-text-primary'
                    }`}
                  >
                    {item.name}
                  </Link>
                ) : item.type === 'megamenu' ? (
                  <div className="py-3 px-4 border-b border-gray-100">
                    <div className="text-text-primary font-medium mb-2">{item.name}</div>
                    <div className="grid grid-cols-2 gap-2 pr-4">
                      {item.megaMenuCategories?.map((category) => (
                        <Link
                          key={category.id}
                          to={`/products?category=${category.slug}`}
                          className="text-sm text-text-secondary hover:text-primary-500 py-1"
                        >
                          {category.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : null}
              </div>
            ))}
          </nav>
        </motion.div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        defaultMode={authModalMode}
      />
    </header>

    {/* Mobile Navigation - Only show on mobile */}
    <MobileNavigation />
  </>
  );
};

export default Header;