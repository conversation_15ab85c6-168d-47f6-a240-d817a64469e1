import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Crown, 
  Gift, 
  History, 
  Star,
  Users,
  TrendingUp,
  Award,
  Sparkles
} from 'lucide-react';
import { LoyaltyMember } from '../../types/loyalty';
import LoyaltyCard from './LoyaltyCard';
import PointsHistory from './PointsHistory';
import RewardsShop from './RewardsShop';
import { useLoyalty } from '../../hooks/useLoyalty';
import toast from 'react-hot-toast';

interface LoyaltyProgramProps {
  className?: string;
}

const LoyaltyProgram: React.FC<LoyaltyProgramProps> = ({ className = '' }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'rewards'>('overview');
  
  const {
    member,
    pointHistory,
    availableRewards,
    isLoading,
    error,
    joinProgram,
    redeemReward,
    getPointHistory,
    getAvailableRewards
  } = useLoyalty();

  const handleJoinProgram = async () => {
    try {
      await joinProgram();
      toast.success('به باشگاه مشتریان گلورویا خوش آمدید! 🎉');
    } catch (error) {
      toast.error('خطا در عضویت در باشگاه مشتریان');
    }
  };

  const handleRedeemReward = async (rewardId: string) => {
    try {
      await redeemReward(rewardId);
      // Refresh data after redemption
      await getAvailableRewards();
    } catch (error) {
      throw error; // Let the RewardsShop component handle the error
    }
  };

  // If not a member, show join program
  if (!member) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-8 text-center ${className}`}>
        <div className="max-w-md mx-auto">
          <div className="w-20 h-20 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <Crown className="w-10 h-10 text-white" />
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            باشگاه مشتریان گلورویا
          </h2>
          
          <p className="text-gray-600 mb-6">
            با عضویت در باشگاه مشتریان، از هر خرید امتیاز کسب کنید و از تخفیف‌ها و مزایای ویژه بهره‌مند شوید.
          </p>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-green-50 rounded-lg p-4">
              <Star className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-medium text-green-800 mb-1">کسب امتیاز</h3>
              <p className="text-sm text-green-600">از هر خرید</p>
            </div>
            
            <div className="bg-blue-50 rounded-lg p-4">
              <Gift className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-medium text-blue-800 mb-1">جوایز ویژه</h3>
              <p className="text-sm text-blue-600">با امتیازات</p>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4">
              <TrendingUp className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-medium text-purple-800 mb-1">تخفیف‌های ویژه</h3>
              <p className="text-sm text-purple-600">برای اعضا</p>
            </div>
            
            <div className="bg-yellow-50 rounded-lg p-4">
              <Award className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
              <h3 className="font-medium text-yellow-800 mb-1">ارتقای سطح</h3>
              <p className="text-sm text-yellow-600">مزایای بیشتر</p>
            </div>
          </div>

          <button
            onClick={handleJoinProgram}
            disabled={isLoading}
            className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                در حال عضویت...
              </>
            ) : (
              <>
                <Users className="w-5 h-5" />
                عضویت در باشگاه مشتریان
              </>
            )}
          </button>

          <p className="text-xs text-gray-500 mt-4">
            عضویت رایگان است و ۱۰۰ امتیاز هدیه دریافت خواهید کرد
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200 p-1">
        <div className="flex">
          <button
            onClick={() => setActiveTab('overview')}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg font-medium transition-colors ${
              activeTab === 'overview'
                ? 'bg-primary-600 text-white'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <Crown className="w-4 h-4" />
            <span className="hidden sm:inline">کارت عضویت</span>
          </button>
          
          <button
            onClick={() => setActiveTab('history')}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg font-medium transition-colors ${
              activeTab === 'history'
                ? 'bg-primary-600 text-white'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <History className="w-4 h-4" />
            <span className="hidden sm:inline">تاریخچه امتیازات</span>
          </button>
          
          <button
            onClick={() => setActiveTab('rewards')}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg font-medium transition-colors ${
              activeTab === 'rewards'
                ? 'bg-primary-600 text-white'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <Gift className="w-4 h-4" />
            <span className="hidden sm:inline">فروشگاه جوایز</span>
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <AnimatePresence>
        {activeTab === 'overview' && (
          <motion.div
            key="overview"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <LoyaltyCard member={member} showDetails={true} />
          </motion.div>
        )}

        {activeTab === 'history' && (
          <motion.div
            key="history"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <PointsHistory
              transactions={pointHistory}
              isLoading={isLoading}
              onLoadMore={getPointHistory}
              hasMore={false}
            />
          </motion.div>
        )}

        {activeTab === 'rewards' && (
          <motion.div
            key="rewards"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <RewardsShop
              rewards={availableRewards}
              member={member}
              onRedeemReward={handleRedeemReward}
              isLoading={isLoading}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-700 mb-2">
            <Sparkles className="w-5 h-5" />
            <span className="font-medium">خطا در بارگذاری اطلاعات باشگاه مشتریان</span>
          </div>
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Quick Stats */}
      {member && activeTab === 'overview' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4"
        >
          <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-green-800">کل امتیازات کسب شده</h4>
                <p className="text-2xl font-bold text-green-600">
                  {member.totalEarned.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <Gift className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-blue-800">کل خریدها</h4>
                <p className="text-2xl font-bold text-blue-600">
                  {member.totalSpent.toLocaleString('fa-IR')} تومان
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                <Award className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-purple-800">سطح عضویت</h4>
                <p className="text-2xl font-bold text-purple-600">
                  {member.tier.persianName}
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default LoyaltyProgram;
