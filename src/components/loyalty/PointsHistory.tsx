import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  History, 
  Plus, 
  Minus, 
  Gift, 
  ShoppingBag,
  Calendar,
  Filter,
  TrendingUp,
  TrendingDown,
  Clock
} from 'lucide-react';
import { PointTransaction } from '../../types/loyalty';
import { formatDistanceToNow } from 'date-fns';
import { faIR } from 'date-fns/locale';

interface PointsHistoryProps {
  transactions: PointTransaction[];
  isLoading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

const PointsHistory: React.FC<PointsHistoryProps> = ({
  transactions,
  isLoading = false,
  onLoadMore,
  hasMore = false
}) => {
  const [filter, setFilter] = useState<'all' | 'earned' | 'redeemed'>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'highest'>('newest');

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'earned':
        return <Plus className="w-4 h-4 text-green-600" />;
      case 'redeemed':
        return <Minus className="w-4 h-4 text-red-600" />;
      case 'bonus':
        return <Gift className="w-4 h-4 text-purple-600" />;
      case 'expired':
        return <Clock className="w-4 h-4 text-gray-600" />;
      default:
        return <History className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'earned':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'redeemed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'bonus':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'expired':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTransactionTypeText = (type: string) => {
    switch (type) {
      case 'earned':
        return 'کسب امتیاز';
      case 'redeemed':
        return 'استفاده امتیاز';
      case 'bonus':
        return 'امتیاز جایزه';
      case 'expired':
        return 'انقضای امتیاز';
      default:
        return 'تراکنش';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { 
        addSuffix: true, 
        locale: faIR 
      });
    } catch {
      return 'تاریخ نامشخص';
    }
  };

  const filteredTransactions = transactions
    .filter(transaction => {
      if (filter === 'all') return true;
      return transaction.type === filter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'highest':
          return Math.abs(b.points) - Math.abs(a.points);
        default:
          return 0;
      }
    });

  const totalEarned = transactions
    .filter(t => t.type === 'earned' || t.type === 'bonus')
    .reduce((sum, t) => sum + t.points, 0);

  const totalRedeemed = transactions
    .filter(t => t.type === 'redeemed')
    .reduce((sum, t) => sum + Math.abs(t.points), 0);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <History className="w-5 h-5 text-primary-600" />
          تاریخچه امتیازات
        </h3>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h4 className="font-medium text-green-800">کل امتیازات کسب شده</h4>
              <p className="text-2xl font-bold text-green-600">
                {totalEarned.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-red-50 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
              <TrendingDown className="w-5 h-5 text-white" />
            </div>
            <div>
              <h4 className="font-medium text-red-800">کل امتیازات استفاده شده</h4>
              <p className="text-2xl font-bold text-red-600">
                {totalRedeemed.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <History className="w-5 h-5 text-white" />
            </div>
            <div>
              <h4 className="font-medium text-blue-800">تعداد تراکنش‌ها</h4>
              <p className="text-2xl font-bold text-blue-600">
                {transactions.length.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === 'all'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            همه
          </button>
          <button
            onClick={() => setFilter('earned')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === 'earned'
                ? 'bg-green-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            کسب شده
          </button>
          <button
            onClick={() => setFilter('redeemed')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === 'redeemed'
                ? 'bg-red-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            استفاده شده
          </button>
        </div>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="newest">جدیدترین</option>
          <option value="oldest">قدیمی‌ترین</option>
          <option value="highest">بیشترین امتیاز</option>
        </select>
      </div>

      {/* Transactions List */}
      <div className="space-y-3">
        {isLoading && filteredTransactions.length === 0 ? (
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredTransactions.length > 0 ? (
          <AnimatePresence mode="popLayout">
            {filteredTransactions.map((transaction, index) => (
              <motion.div
                key={transaction.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className={`
                  flex items-center gap-4 p-4 border rounded-lg transition-all duration-200 hover:shadow-md
                  ${getTransactionColor(transaction.type)}
                `}
              >
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 rounded-full border flex items-center justify-center">
                    {getTransactionIcon(transaction.type)}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900 truncate">
                      {transaction.description}
                    </h4>
                    <span className="text-xs px-2 py-0.5 rounded-full bg-white bg-opacity-50">
                      {getTransactionTypeText(transaction.type)}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {formatDate(transaction.createdAt)}
                    </span>
                    
                    {transaction.orderId && (
                      <span className="flex items-center gap-1">
                        <ShoppingBag className="w-3 h-3" />
                        سفارش #{transaction.orderId.slice(-6)}
                      </span>
                    )}
                    
                    {transaction.expiryDate && (
                      <span className="flex items-center gap-1 text-orange-600">
                        <Clock className="w-3 h-3" />
                        انقضا: {formatDate(transaction.expiryDate)}
                      </span>
                    )}
                  </div>
                </div>

                <div className="text-right">
                  <span className={`text-lg font-bold ${
                    transaction.type === 'earned' || transaction.type === 'bonus'
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {transaction.type === 'earned' || transaction.type === 'bonus' ? '+' : '-'}
                    {Math.abs(transaction.points).toLocaleString('fa-IR')}
                  </span>
                  <p className="text-xs text-gray-500">امتیاز</p>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        ) : (
          <div className="text-center py-8">
            <History className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              تراکنشی یافت نشد
            </h3>
            <p className="text-gray-600">
              {filter === 'all' 
                ? 'هنوز تراکنشی انجام نداده‌اید'
                : `تراکنش ${filter === 'earned' ? 'کسب شده' : 'استفاده شده'}ای یافت نشد`
              }
            </p>
          </div>
        )}
      </div>

      {/* Load More */}
      {hasMore && !isLoading && (
        <div className="mt-6 text-center">
          <button
            onClick={onLoadMore}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            نمایش بیشتر
          </button>
        </div>
      )}

      {/* Loading More */}
      {isLoading && filteredTransactions.length > 0 && (
        <div className="mt-6 text-center">
          <div className="inline-flex items-center gap-2 text-gray-600">
            <div className="w-4 h-4 border-2 border-gray-300 border-t-primary-600 rounded-full animate-spin"></div>
            در حال بارگذاری...
          </div>
        </div>
      )}
    </div>
  );
};

export default PointsHistory;
