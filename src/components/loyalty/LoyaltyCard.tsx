import React from 'react';
import { motion } from 'framer-motion';
import { 
  Star, 
  Gift, 
  TrendingUp, 
  Calendar,
  Crown,
  Sparkles
} from 'lucide-react';
import { LoyaltyMember, LoyaltyTier } from '../../types/loyalty';

interface LoyaltyCardProps {
  member: LoyaltyMember;
  showDetails?: boolean;
  compact?: boolean;
  className?: string;
}

const LoyaltyCard: React.FC<LoyaltyCardProps> = ({
  member,
  showDetails = true,
  compact = false,
  className = ''
}) => {
  const getTierGradient = (tier: LoyaltyTier) => {
    switch (tier.id) {
      case 'bronze':
        return 'from-amber-600 to-amber-800';
      case 'silver':
        return 'from-gray-400 to-gray-600';
      case 'gold':
        return 'from-yellow-400 to-yellow-600';
      case 'platinum':
        return 'from-purple-400 to-purple-600';
      default:
        return 'from-gray-400 to-gray-600';
    }
  };

  const getTierIcon = (tier: LoyaltyTier) => {
    switch (tier.id) {
      case 'bronze':
        return <Star className="w-6 h-6 text-amber-200" />;
      case 'silver':
        return <Gift className="w-6 h-6 text-gray-200" />;
      case 'gold':
        return <Crown className="w-6 h-6 text-yellow-200" />;
      case 'platinum':
        return <Sparkles className="w-6 h-6 text-purple-200" />;
      default:
        return <Star className="w-6 h-6 text-gray-200" />;
    }
  };

  const formatMembershipNumber = (number: string) => {
    return number.replace(/(\d{4})(?=\d)/g, '$1-');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fa-IR');
  };

  if (compact) {
    return (
      <div className={`bg-gradient-to-r ${getTierGradient(member.tier)} rounded-lg p-4 text-white ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getTierIcon(member.tier)}
            <div>
              <h3 className="font-semibold">{member.tier.persianName}</h3>
              <p className="text-sm opacity-90">{member.points.toLocaleString('fa-IR')} امتیاز</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-xs opacity-75">شماره عضویت</p>
            <p className="text-sm font-mono">{formatMembershipNumber(member.membershipNumber)}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`relative overflow-hidden rounded-xl ${className}`}
    >
      {/* Card Background */}
      <div className={`bg-gradient-to-br ${getTierGradient(member.tier)} p-6 text-white relative`}>
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-4 right-4 w-32 h-32 rounded-full border-2 border-white"></div>
          <div className="absolute bottom-4 left-4 w-24 h-24 rounded-full border border-white"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full border border-white"></div>
        </div>

        {/* Card Content */}
        <div className="relative z-10">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              {getTierIcon(member.tier)}
              <div>
                <h2 className="text-xl font-bold">باشگاه مشتریان گلورویا</h2>
                <p className="text-sm opacity-90">عضویت {member.tier.persianName}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-xs opacity-75">شماره عضویت</p>
              <p className="text-lg font-mono">{formatMembershipNumber(member.membershipNumber)}</p>
            </div>
          </div>

          {/* Points Display */}
          <div className="mb-6">
            <div className="flex items-baseline gap-2 mb-2">
              <span className="text-3xl font-bold">{member.points.toLocaleString('fa-IR')}</span>
              <span className="text-sm opacity-90">امتیاز فعال</span>
            </div>
            <div className="text-sm opacity-75">
              مجموع امتیازات کسب شده: {member.totalEarned.toLocaleString('fa-IR')}
            </div>
          </div>

          {/* Progress to Next Tier */}
          {member.nextTierProgress && member.nextTierProgress.requiredPoints > 0 && (
            <div className="mb-4">
              <div className="flex justify-between text-sm mb-2">
                <span>پیشرفت تا سطح بعدی</span>
                <span>{member.nextTierProgress.percentage}%</span>
              </div>
              <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                <motion.div
                  className="bg-white rounded-full h-2"
                  initial={{ width: 0 }}
                  animate={{ width: `${member.nextTierProgress.percentage}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
              <div className="text-xs opacity-75 mt-1">
                {(member.nextTierProgress.requiredPoints - member.nextTierProgress.currentPoints).toLocaleString('fa-IR')} امتیاز تا سطح بعدی
              </div>
            </div>
          )}

          {/* Member Since */}
          <div className="flex items-center gap-2 text-sm opacity-75">
            <Calendar className="w-4 h-4" />
            <span>عضو از {formatDate(member.joinDate)}</span>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      {showDetails && (
        <div className="bg-white border-x border-b border-gray-200 rounded-b-xl p-6">
          <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Gift className="w-5 h-5 text-primary-600" />
            مزایای عضویت {member.tier.persianName}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Discount */}
            {member.tier.discountPercentage > 0 && (
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">{member.tier.discountPercentage}%</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">تخفیف همیشگی</h4>
                  <p className="text-sm text-gray-600">در تمام خریدها</p>
                </div>
              </div>
            )}

            {/* Free Shipping */}
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">ارسال رایگان</h4>
                <p className="text-sm text-gray-600">
                  {member.tier.freeShippingThreshold === 0 
                    ? 'برای تمام خریدها'
                    : `خریدهای بالای ${member.tier.freeShippingThreshold.toLocaleString('fa-IR')} تومان`
                  }
                </p>
              </div>
            </div>

            {/* Birthday Bonus */}
            <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
              <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                <Gift className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">هدیه تولد</h4>
                <p className="text-sm text-gray-600">{member.tier.birthdayBonus} امتیاز</p>
              </div>
            </div>

            {/* Points Multiplier */}
            <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
              <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                <Star className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">ضریب امتیاز</h4>
                <p className="text-sm text-gray-600">
                  {member.tier.level === 1 ? '۱' : 
                   member.tier.level === 2 ? '۱.۵' :
                   member.tier.level === 3 ? '۲' : '۳'} برابر
                </p>
              </div>
            </div>
          </div>

          {/* Additional Benefits */}
          {member.tier.benefits.length > 4 && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">مزایای اضافی:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                {member.tier.benefits.slice(4).map((benefit, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-primary-500 mt-1">•</span>
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </motion.div>
  );
};

// Mini loyalty badge for headers/footers
interface LoyaltyBadgeProps {
  member: LoyaltyMember;
  onClick?: () => void;
}

export const LoyaltyBadge: React.FC<LoyaltyBadgeProps> = ({ member, onClick }) => {
  const getTierColor = (tierId: string) => {
    switch (tierId) {
      case 'bronze': return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'silver': return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'gold': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'platinum': return 'text-purple-600 bg-purple-50 border-purple-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <button
      onClick={onClick}
      className={`
        flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium transition-all duration-200 hover:scale-105
        ${getTierColor(member.tier.id)}
      `}
    >
      <span>{member.tier.icon}</span>
      <span>{member.points.toLocaleString('fa-IR')}</span>
    </button>
  );
};

export default LoyaltyCard;
