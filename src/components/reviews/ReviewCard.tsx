import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ThumbsUp, 
  ThumbsDown, 
  Shield, 
  Calendar, 
  User, 
  ChevronDown,
  ChevronUp,
  Flag,
  Share2,
  MoreHorizontal
} from 'lucide-react';
import { Review } from '../../types/review';
import RatingStars from './RatingStars';
import { formatDistanceToNow } from 'date-fns';
import { faIR } from 'date-fns/locale';

interface ReviewCardProps {
  review: Review;
  onVote?: (reviewId: string, isHelpful: boolean) => void;
  onReport?: (reviewId: string) => void;
  onShare?: (reviewId: string) => void;
  showActions?: boolean;
  compact?: boolean;
}

const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  onVote,
  onReport,
  onShare,
  showActions = true,
  compact = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showImages, setShowImages] = useState(false);
  const [userVote, setUserVote] = useState<boolean | null>(null);
  const [showMenu, setShowMenu] = useState(false);

  const handleVote = (isHelpful: boolean) => {
    if (userVote === isHelpful) {
      // Remove vote if clicking the same button
      setUserVote(null);
    } else {
      setUserVote(isHelpful);
      onVote?.(review.id, isHelpful);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { 
        addSuffix: true, 
        locale: faIR 
      });
    } catch {
      return 'تاریخ نامشخص';
    }
  };

  const getHelpfulPercentage = () => {
    if (review.totalVotes === 0) return 0;
    return Math.round((review.helpfulVotes / review.totalVotes) * 100);
  };

  const shouldTruncate = review.comment.length > 200 && !isExpanded;
  const displayComment = shouldTruncate 
    ? review.comment.substring(0, 200) + '...'
    : review.comment;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`
        bg-white rounded-lg border border-gray-200 
        ${compact ? 'p-4' : 'p-6'} 
        hover:shadow-md transition-shadow duration-200
      `}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start gap-3">
          {/* User Avatar */}
          <div className="w-10 h-10 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold">
            {review.userAvatar ? (
              <img 
                src={review.userAvatar} 
                alt={review.userName}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <User className="w-5 h-5" />
            )}
          </div>

          {/* User Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-gray-900">{review.userName}</h4>
              {review.isVerifiedPurchase && (
                <div className="flex items-center gap-1 bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs">
                  <Shield className="w-3 h-3" />
                  <span>خرید تأیید شده</span>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>{formatDate(review.createdAt)}</span>
              </div>
              
              {review.usageDuration && (
                <span>مدت استفاده: {review.usageDuration}</span>
              )}
            </div>
          </div>
        </div>

        {/* Actions Menu */}
        {showActions && (
          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
            >
              <MoreHorizontal className="w-4 h-4 text-gray-400" />
            </button>

            <AnimatePresence>
              {showMenu && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="absolute left-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-32"
                >
                  <button
                    onClick={() => {
                      onShare?.(review.id);
                      setShowMenu(false);
                    }}
                    className="w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                  >
                    <Share2 className="w-3 h-3" />
                    اشتراک‌گذاری
                  </button>
                  <button
                    onClick={() => {
                      onReport?.(review.id);
                      setShowMenu(false);
                    }}
                    className="w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                  >
                    <Flag className="w-3 h-3" />
                    گزارش
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Rating and Title */}
      <div className="mb-3">
        <div className="flex items-center gap-2 mb-2">
          <RatingStars rating={review.rating} size="small" />
          {review.isRecommended && (
            <span className="text-green-600 text-sm font-medium">پیشنهاد می‌کنم</span>
          )}
        </div>
        <h3 className="font-semibold text-gray-900 text-lg">{review.title}</h3>
      </div>

      {/* Review Content */}
      <div className="mb-4">
        <p className="text-gray-700 leading-relaxed mb-3">
          {displayComment}
        </p>

        {review.comment.length > 200 && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center gap-1"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="w-4 h-4" />
                کمتر نشان بده
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4" />
                بیشتر نشان بده
              </>
            )}
          </button>
        )}

        {/* Pros and Cons */}
        {(review.pros.length > 0 || review.cons.length > 0) && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            {review.pros.length > 0 && (
              <div>
                <h5 className="font-medium text-green-700 mb-2">نکات مثبت:</h5>
                <ul className="space-y-1">
                  {review.pros.map((pro, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                      <span className="text-green-500 mt-1">+</span>
                      <span>{pro}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {review.cons.length > 0 && (
              <div>
                <h5 className="font-medium text-red-700 mb-2">نکات منفی:</h5>
                <ul className="space-y-1">
                  {review.cons.map((con, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                      <span className="text-red-500 mt-1">-</span>
                      <span>{con}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Review Images */}
        {review.images && review.images.length > 0 && (
          <div className="mt-4">
            <button
              onClick={() => setShowImages(!showImages)}
              className="text-primary-600 hover:text-primary-700 text-sm font-medium mb-2"
            >
              {showImages ? 'مخفی کردن تصاویر' : `نمایش ${review.images.length} تصویر`}
            </button>

            <AnimatePresence>
              {showImages && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="grid grid-cols-2 md:grid-cols-4 gap-2"
                >
                  {review.images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`تصویر نظر ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg border border-gray-200"
                    />
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* User Details */}
      {(review.skinType || review.ageRange) && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex flex-wrap gap-4 text-sm text-gray-600">
            {review.skinType && (
              <span>نوع پوست: <strong>{review.skinType}</strong></span>
            )}
            {review.ageRange && (
              <span>سن: <strong>{review.ageRange}</strong></span>
            )}
          </div>
        </div>
      )}

      {/* Footer Actions */}
      {showActions && (
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center gap-4">
            {/* Helpful Votes */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => handleVote(true)}
                className={`
                  flex items-center gap-1 px-3 py-1 rounded-full text-sm transition-colors
                  ${userVote === true 
                    ? 'bg-green-100 text-green-700' 
                    : 'bg-gray-100 text-gray-600 hover:bg-green-50 hover:text-green-600'
                  }
                `}
              >
                <ThumbsUp className="w-3 h-3" />
                <span>مفید ({review.helpfulVotes.toLocaleString('fa-IR')})</span>
              </button>

              <button
                onClick={() => handleVote(false)}
                className={`
                  flex items-center gap-1 px-3 py-1 rounded-full text-sm transition-colors
                  ${userVote === false 
                    ? 'bg-red-100 text-red-700' 
                    : 'bg-gray-100 text-gray-600 hover:bg-red-50 hover:text-red-600'
                  }
                `}
              >
                <ThumbsDown className="w-3 h-3" />
                <span>غیرمفید</span>
              </button>
            </div>
          </div>

          {/* Helpful Percentage */}
          {review.totalVotes > 0 && (
            <div className="text-sm text-gray-500">
              {getHelpfulPercentage()}% مفید بوده
            </div>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default ReviewCard;
