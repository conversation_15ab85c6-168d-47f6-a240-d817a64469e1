import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Star } from 'lucide-react';

interface RatingStarsProps {
  rating: number;
  maxRating?: number;
  size?: 'small' | 'medium' | 'large';
  interactive?: boolean;
  showValue?: boolean;
  showCount?: boolean;
  reviewCount?: number;
  onChange?: (rating: number) => void;
  disabled?: boolean;
  className?: string;
}

const RatingStars: React.FC<RatingStarsProps> = ({
  rating,
  maxRating = 5,
  size = 'medium',
  interactive = false,
  showValue = false,
  showCount = false,
  reviewCount,
  onChange,
  disabled = false,
  className = ''
}) => {
  const [hoverRating, setHoverRating] = useState<number>(0);
  const [isHovering, setIsHovering] = useState(false);

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          star: 'w-3 h-3',
          text: 'text-xs',
          gap: 'gap-0.5'
        };
      case 'large':
        return {
          star: 'w-6 h-6',
          text: 'text-lg',
          gap: 'gap-1'
        };
      default:
        return {
          star: 'w-4 h-4',
          text: 'text-sm',
          gap: 'gap-1'
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const displayRating = isHovering && interactive ? hoverRating : rating;

  const handleStarClick = (starRating: number) => {
    if (!interactive || disabled) return;
    onChange?.(starRating);
  };

  const handleStarHover = (starRating: number) => {
    if (!interactive || disabled) return;
    setHoverRating(starRating);
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    if (!interactive || disabled) return;
    setIsHovering(false);
    setHoverRating(0);
  };

  const formatRating = (value: number) => {
    return value.toLocaleString('fa-IR', { 
      minimumFractionDigits: 1, 
      maximumFractionDigits: 1 
    });
  };

  const formatCount = (count: number) => {
    if (count >= 1000) {
      return (count / 1000).toLocaleString('fa-IR', { 
        minimumFractionDigits: 1, 
        maximumFractionDigits: 1 
      }) + 'هزار';
    }
    return count.toLocaleString('fa-IR');
  };

  return (
    <div className={`flex items-center ${sizeClasses.gap} ${className}`}>
      {/* Stars */}
      <div 
        className={`flex items-center ${sizeClasses.gap} ${
          interactive && !disabled ? 'cursor-pointer' : ''
        }`}
        onMouseLeave={handleMouseLeave}
      >
        {Array.from({ length: maxRating }, (_, index) => {
          const starValue = index + 1;
          const isFilled = starValue <= displayRating;
          const isPartiallyFilled = starValue === Math.ceil(displayRating) && displayRating % 1 !== 0;
          const fillPercentage = isPartiallyFilled ? (displayRating % 1) * 100 : 0;

          return (
            <motion.div
              key={index}
              className="relative"
              whileHover={interactive && !disabled ? { scale: 1.1 } : {}}
              whileTap={interactive && !disabled ? { scale: 0.95 } : {}}
              onClick={() => handleStarClick(starValue)}
              onMouseEnter={() => handleStarHover(starValue)}
            >
              {/* Background star */}
              <Star
                className={`
                  ${sizeClasses.star} 
                  ${interactive && !disabled ? 'transition-colors duration-150' : ''}
                  ${isFilled || isPartiallyFilled ? 'text-yellow-400' : 'text-gray-300'}
                  ${interactive && !disabled && isHovering && starValue <= hoverRating ? 'text-yellow-500' : ''}
                `}
                fill={isFilled ? 'currentColor' : 'none'}
                stroke="currentColor"
                strokeWidth={1.5}
              />

              {/* Partial fill for fractional ratings */}
              {isPartiallyFilled && (
                <div
                  className="absolute inset-0 overflow-hidden"
                  style={{ width: `${fillPercentage}%` }}
                >
                  <Star
                    className={`${sizeClasses.star} text-yellow-400`}
                    fill="currentColor"
                    stroke="currentColor"
                    strokeWidth={1.5}
                  />
                </div>
              )}

              {/* Hover effect overlay */}
              {interactive && !disabled && (
                <motion.div
                  className="absolute inset-0 rounded-full bg-yellow-400 opacity-0"
                  whileHover={{ opacity: 0.1 }}
                />
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Rating Value */}
      {showValue && (
        <span className={`${sizeClasses.text} font-medium text-gray-700 mr-1`}>
          {formatRating(displayRating)}
        </span>
      )}

      {/* Review Count */}
      {showCount && reviewCount !== undefined && (
        <span className={`${sizeClasses.text} text-gray-500`}>
          ({formatCount(reviewCount)} نظر)
        </span>
      )}

      {/* Interactive feedback */}
      {interactive && !disabled && isHovering && (
        <motion.span
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -10 }}
          className={`${sizeClasses.text} text-yellow-600 font-medium mr-2`}
        >
          {hoverRating} ستاره
        </motion.span>
      )}
    </div>
  );
};

// Rating distribution component for review stats
interface RatingDistributionProps {
  distribution: { [key: number]: number };
  totalReviews: number;
  onRatingFilter?: (rating: number) => void;
}

export const RatingDistribution: React.FC<RatingDistributionProps> = ({
  distribution,
  totalReviews,
  onRatingFilter
}) => {
  const ratings = [5, 4, 3, 2, 1];

  return (
    <div className="space-y-2">
      {ratings.map((rating) => {
        const count = distribution[rating] || 0;
        const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;

        return (
          <div
            key={rating}
            className={`flex items-center gap-2 ${
              onRatingFilter ? 'cursor-pointer hover:bg-gray-50 p-1 rounded' : ''
            }`}
            onClick={() => onRatingFilter?.(rating)}
          >
            <div className="flex items-center gap-1 w-16">
              <span className="text-sm text-gray-600">{rating}</span>
              <Star className="w-3 h-3 text-yellow-400 fill-current" />
            </div>
            
            <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-yellow-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${percentage}%` }}
                transition={{ duration: 0.5, delay: (5 - rating) * 0.1 }}
              />
            </div>
            
            <span className="text-xs text-gray-500 w-8 text-left">
              {count.toLocaleString('fa-IR')}
            </span>
          </div>
        );
      })}
    </div>
  );
};

// Quick rating selector for forms
interface QuickRatingProps {
  value: number;
  onChange: (rating: number) => void;
  labels?: string[];
  error?: string;
}

export const QuickRating: React.FC<QuickRatingProps> = ({
  value,
  onChange,
  labels = ['خیلی بد', 'بد', 'متوسط', 'خوب', 'عالی'],
  error
}) => {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-center gap-2">
        <RatingStars
          rating={value}
          interactive
          onChange={onChange}
          size="large"
        />
      </div>
      
      {value > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <span className="text-sm font-medium text-gray-700">
            {labels[value - 1]}
          </span>
        </motion.div>
      )}
      
      {error && (
        <p className="text-sm text-red-600 text-center">{error}</p>
      )}
    </div>
  );
};

export default RatingStars;
