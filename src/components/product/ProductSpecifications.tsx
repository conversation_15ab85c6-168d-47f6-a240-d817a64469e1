import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, CheckCircle, Info, Building2 } from 'lucide-react';
import { Product } from '../../types';
import { getBrandInfo } from '../../utils/brandUtils';

interface ProductSpecificationsProps {
  product: Product;
}

const ProductSpecifications: React.FC<ProductSpecificationsProps> = ({ product }) => {
  const [activeTab, setActiveTab] = useState<'benefits' | 'ingredients' | 'howToUse'>('benefits');

  const tabs = [
    { id: 'benefits', label: 'فواید و خواص', icon: CheckCircle },
    { id: 'ingredients', label: 'ترکیبات', icon: Info },
    { id: 'howToUse', label: 'نحوه استفاده', icon: Info }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'benefits':
        return (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-3"
          >
            {product.benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-start gap-3 p-3 bg-green-50 rounded-lg"
              >
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-text-primary">{benefit}</span>
              </motion.div>
            ))}
          </motion.div>
        );
      
      case 'ingredients':
        return (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-3"
          >
            {product.ingredients.map((ingredient, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg"
              >
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2.5 flex-shrink-0"></div>
                <span className="text-text-primary">{ingredient}</span>
              </motion.div>
            ))}
          </motion.div>
        );
      
      case 'howToUse':
        return (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-3"
          >
            {product.howToUse.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-start gap-3 p-3 bg-primary-50 rounded-lg"
              >
                <div className="w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                  {index + 1}
                </div>
                <span className="text-text-primary">{step}</span>
              </motion.div>
            ))}
          </motion.div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-soft overflow-hidden">
      {/* Tab Headers */}
      <div className="border-b border-gray-100">
        <div className="flex">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center gap-2 py-4 px-6 text-sm font-medium transition-colors relative ${
                  activeTab === tab.id
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-text-secondary hover:text-text-primary hover:bg-gray-50'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
                {activeTab === tab.id && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-600"
                    initial={false}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  />
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        <AnimatePresence>
          {renderTabContent()}
        </AnimatePresence>
      </div>

      {/* Additional Product Details */}
      {(product.brand || product.size || product.weight) && (
        <div className="border-t border-gray-100 p-6 bg-gray-50">
          <h4 className="font-semibold text-text-primary mb-4">مشخصات فنی</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {product.brand && (
              <div className="flex justify-between items-center py-2 border-b border-gray-200">
                <span className="text-text-secondary">برند:</span>
                <div className="flex items-center gap-2">
                  {(() => {
                    const brandInfo = getBrandInfo(product.brand);
                    return brandInfo.logo ? (
                      <img
                        src={brandInfo.logo}
                        alt={`لوگو ${brandInfo.name}`}
                        className="w-5 h-5 rounded object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : (
                      <Building2 className="w-4 h-4 text-gray-400" />
                    );
                  })()}
                  <span className="font-medium text-text-primary">{product.brand}</span>
                </div>
              </div>
            )}
            {product.size && (
              <div className="flex justify-between items-center py-2 border-b border-gray-200">
                <span className="text-text-secondary">حجم:</span>
                <span className="font-medium text-text-primary">{product.size}</span>
              </div>
            )}
            {product.weight && (
              <div className="flex justify-between items-center py-2 border-b border-gray-200">
                <span className="text-text-secondary">وزن:</span>
                <span className="font-medium text-text-primary">{product.weight}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductSpecifications;
