import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  X, 
  ShoppingCart, 
  Heart, 
  Share2, 
  Crown, 
  Check,
  Star,
  Eye,
  Filter
} from 'lucide-react';
import { useProductComparison } from '../../hooks/useProductComparison';
import { useWishlist } from '../../hooks/useWishlist';
import { useCart } from '../../context/CartContext';
import { 
  ComparisonCategory, 
  PERSIAN_COMPARISON_MESSAGES 
} from '../../types/comparison';
import { formatNumber } from '../../utils/persianUtils';
import Rating from '../common/Rating';

interface ComparisonTableProps {
  className?: string;
}

const ComparisonTable: React.FC<ComparisonTableProps> = ({ className = '' }) => {
  const {
    items,
    results,
    removeItem,
    clearComparison,
    shareComparison
  } = useProductComparison();

  const { addItem: addToWishlist, isInWishlist } = useWishlist();
  const { addItem: addToCart } = useCart();

  const [selectedCategories, setSelectedCategories] = useState<ComparisonCategory[]>([
    'basic', 'pricing', 'specifications', 'ratings'
  ]);
  const [showAllCriteria, setShowAllCriteria] = useState(false);

  if (items.length === 0) {
    return (
      <div className={`text-center py-16 ${className}`}>
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Eye className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-xl font-semibold text-text-primary mb-2">
          {PERSIAN_COMPARISON_MESSAGES.empty}
        </h3>
        <p className="text-text-secondary mb-6">
          برای شروع مقایسه، محصولات را از صفحه محصولات اضافه کنید
        </p>
        <button 
          onClick={() => window.location.href = '/products'}
          className="btn-primary"
        >
          مشاهده محصولات
        </button>
      </div>
    );
  }

  const filteredResults = results.filter(result => 
    selectedCategories.includes(result.criteria.category) &&
    (showAllCriteria || result.criteria.important)
  );

  const categories: ComparisonCategory[] = ['basic', 'pricing', 'specifications', 'ingredients', 'benefits', 'ratings'];

  const toggleCategory = (category: ComparisonCategory) => {
    setSelectedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const handleAddToCart = (productId: string) => {
    const item = items.find(i => i.product.id === productId);
    if (item) {
      addToCart(item.product, 1);
    }
  };

  const handleAddToWishlist = (productId: string) => {
    const item = items.find(i => i.product.id === productId);
    if (item && !isInWishlist(productId)) {
      addToWishlist(item.product);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">
            {PERSIAN_COMPARISON_MESSAGES.title}
          </h1>
          <p className="text-text-secondary">
            مقایسه {items.length} محصول
          </p>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={shareComparison}
            className="btn-secondary"
          >
            <Share2 className="w-4 h-4" />
            اشتراک
          </button>
          <button
            onClick={clearComparison}
            className="btn-secondary text-red-600 hover:bg-red-50"
          >
            <X className="w-4 h-4" />
            پاک کردن همه
          </button>
        </div>
      </div>

      {/* Category Filters */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex flex-wrap items-center gap-2 mb-4">
          <span className="text-sm font-medium text-text-primary">دسته‌بندی‌ها:</span>
          {categories.map(category => (
            <button
              key={category}
              onClick={() => toggleCategory(category)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                selectedCategories.includes(category)
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {PERSIAN_COMPARISON_MESSAGES.categories[category]}
            </button>
          ))}
        </div>

        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={showAllCriteria}
            onChange={(e) => setShowAllCriteria(e.target.checked)}
            className="rounded border-gray-300"
          />
          <span className="text-sm text-text-secondary">
            نمایش همه معیارها (شامل جزئیات بیشتر)
          </span>
        </label>
      </div>

      {/* Comparison Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            {/* Product Headers */}
            <thead>
              <tr className="border-b bg-gray-50">
                <th className="text-right p-4 font-medium text-text-primary min-w-[200px]">
                  محصولات
                </th>
                {items.map(item => (
                  <th key={item.id} className="p-4 min-w-[250px]">
                    <div className="space-y-3">
                      {/* Product Image */}
                      <div className="relative">
                        <img
                          src={item.product.images[0]}
                          alt={item.product.name}
                          className="w-20 h-20 object-cover rounded-lg mx-auto"
                        />
                        <button
                          onClick={() => removeItem(item.product.id)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>

                      {/* Product Info */}
                      <div className="text-center">
                        <h3 className="font-semibold text-text-primary text-sm leading-tight">
                          {item.product.name}
                        </h3>
                        <p className="text-xs text-text-secondary mt-1">
                          {item.product.brand}
                        </p>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center justify-center gap-2">
                        <button
                          onClick={() => handleAddToCart(item.product.id)}
                          disabled={item.product.stock === 0}
                          className="btn-primary btn-sm disabled:opacity-50"
                          title="افزودن به سبد خرید"
                        >
                          <ShoppingCart className="w-3 h-3" />
                        </button>
                        <button
                          onClick={() => handleAddToWishlist(item.product.id)}
                          disabled={isInWishlist(item.product.id)}
                          className={`btn-sm border transition-colors ${
                            isInWishlist(item.product.id)
                              ? 'bg-red-50 border-red-200 text-red-600'
                              : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                          }`}
                          title="افزودن به علاقه‌مندی‌ها"
                        >
                          <Heart className={`w-3 h-3 ${isInWishlist(item.product.id) ? 'fill-current' : ''}`} />
                        </button>
                      </div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>

            {/* Comparison Rows */}
            <tbody>
              {filteredResults.map((result, index) => (
                <motion.tr
                  key={result.criteria.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="border-b hover:bg-gray-50"
                >
                  <td className="p-4 font-medium text-text-primary bg-gray-50">
                    <div className="flex items-center gap-2">
                      {result.criteria.name}
                      {result.criteria.important && (
                        <Star className="w-3 h-3 text-yellow-500 fill-current" />
                      )}
                    </div>
                  </td>
                  {items.map(item => {
                    const value = result.values[item.product.id];
                    const isWinner = result.winner === item.product.id;
                    const formattedValue = result.criteria.format 
                      ? result.criteria.format(value)
                      : String(value);

                    return (
                      <td 
                        key={item.id} 
                        className={`p-4 text-center ${
                          isWinner ? 'bg-green-50 border-l-2 border-green-500' : ''
                        }`}
                      >
                        <div className="flex items-center justify-center gap-2">
                          {isWinner && (
                            <Crown className="w-4 h-4 text-green-600" />
                          )}
                          <span className={`${isWinner ? 'font-semibold text-green-800' : 'text-text-secondary'}`}>
                            {result.criteria.id === 'rating' ? (
                              <div className="flex items-center justify-center gap-1">
                                <Rating value={Number(value)} size="sm" />
                                <span className="text-xs">({value})</span>
                              </div>
                            ) : (
                              formattedValue
                            )}
                          </span>
                        </div>
                      </td>
                    );
                  })}
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Legend */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex flex-wrap items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Crown className="w-4 h-4 text-green-600" />
            <span className="text-text-secondary">بهترین مقدار</span>
          </div>
          <div className="flex items-center gap-2">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span className="text-text-secondary">معیار مهم</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-2 bg-green-100 border-l-2 border-green-500 rounded-sm"></div>
            <span className="text-text-secondary">برنده در این معیار</span>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">خلاصه مقایسه</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {items.map(item => {
            const winCount = results.filter(r => r.winner === item.product.id).length;
            const winPercentage = results.length > 0 ? Math.round((winCount / results.length) * 100) : 0;
            
            return (
              <div key={item.id} className="text-center p-4 bg-gray-50 rounded-lg">
                <img
                  src={item.product.images[0]}
                  alt={item.product.name}
                  className="w-12 h-12 object-cover rounded-lg mx-auto mb-2"
                />
                <h4 className="font-medium text-text-primary text-sm mb-1">
                  {item.product.name}
                </h4>
                <div className="text-xs text-text-secondary mb-2">
                  برنده در {winCount} معیار ({winPercentage}%)
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-500"
                    style={{ width: `${winPercentage}%` }}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ComparisonTable;
