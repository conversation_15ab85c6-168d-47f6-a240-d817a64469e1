import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Clock, Eye, X, RotateCcw, TrendingUp } from 'lucide-react';
import { Product } from '../../types';
import { 
  RecentlyViewedItem, 
  ViewSource, 
  PERSIAN_RECENTLY_VIEWED_MESSAGES 
} from '../../types/comparison';
import { formatNumber } from '../../utils/persianUtils';
import ProductCard from '../common/ProductCard';

interface RecentlyViewedProps {
  className?: string;
  maxItems?: number;
  showTitle?: boolean;
  compact?: boolean;
}

// Storage utilities
const RECENTLY_VIEWED_STORAGE_KEY = 'glowroya_recently_viewed';

const saveRecentlyViewedToStorage = (items: RecentlyViewedItem[]) => {
  localStorage.setItem(RECENTLY_VIEWED_STORAGE_KEY, JSON.stringify(items));
};

const loadRecentlyViewedFromStorage = (): RecentlyViewedItem[] => {
  try {
    const stored = localStorage.getItem(RECENTLY_VIEWED_STORAGE_KEY);
    if (stored) {
      const items = JSON.parse(stored);
      return items.map((item: any) => ({
        ...item,
        viewedAt: new Date(item.viewedAt)
      }));
    }
  } catch (error) {
    // console.error('Error loading recently viewed from storage:', error);
  }
  return [];
};

const RecentlyViewed: React.FC<RecentlyViewedProps> = ({
  className = '',
  maxItems = 8,
  showTitle = true,
  compact = false
}) => {
  const [items, setItems] = useState<RecentlyViewedItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load recently viewed items on mount
  useEffect(() => {
    const stored = loadRecentlyViewedFromStorage();
    setItems(stored.slice(0, maxItems));
    setIsLoading(false);
  }, [maxItems]);

  // Save to storage when items change
  useEffect(() => {
    if (!isLoading) {
      saveRecentlyViewedToStorage(items);
    }
  }, [items, isLoading]);

  const addItem = (product: Product, source?: ViewSource, timeSpent?: number) => {
    setItems(prev => {
      // Remove existing item if present
      const filtered = prev.filter(item => item.product.id !== product.id);
      
      // Find existing item to update view count
      const existingItem = prev.find(item => item.product.id === product.id);
      
      const newItem: RecentlyViewedItem = {
        id: `recently_viewed_${product.id}_${Date.now()}`,
        product,
        viewedAt: new Date(),
        viewCount: existingItem ? existingItem.viewCount + 1 : 1,
        timeSpent,
        source
      };

      // Add to beginning and limit to maxItems
      return [newItem, ...filtered].slice(0, maxItems);
    });
  };

  const removeItem = (productId: string) => {
    setItems(prev => prev.filter(item => item.product.id !== productId));
  };

  const clearHistory = () => {
    setItems([]);
  };

  const getViewCount = (productId: string): number => {
    const item = items.find(i => i.product.id === productId);
    return item?.viewCount || 0;
  };

  const getTotalTimeSpent = (productId: string): number => {
    const item = items.find(i => i.product.id === productId);
    return item?.timeSpent || 0;
  };

  const getMostViewedProducts = (limit: number = 5): RecentlyViewedItem[] => {
    return [...items]
      .sort((a, b) => b.viewCount - a.viewCount)
      .slice(0, limit);
  };

  const getSourceIcon = (source?: ViewSource) => {
    switch (source) {
      case 'search': return '🔍';
      case 'category': return '📂';
      case 'recommendation': return '💡';
      case 'direct_link': return '🔗';
      case 'related_products': return '🔄';
      default: return '👁️';
    }
  };

  const formatTimeSpent = (seconds?: number) => {
    if (!seconds) return '';
    if (seconds < 60) return `${seconds} ثانیه`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes} دقیقه`;
  };

  const formatViewedAt = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'همین الان';
    if (diffInHours < 24) return `${diffInHours} ساعت پیش`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} روز پیش`;
    return date.toLocaleDateString('fa-IR');
  };

  // Note: useImperativeHandle removed as it was incorrectly implemented
  // External access to functions can be achieved through custom hooks

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-6 bg-gray-200 rounded w-32 mb-4"></div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="bg-gray-200 rounded-lg h-48"></div>
          ))}
        </div>
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <Clock className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <p className="text-text-secondary">
          {PERSIAN_RECENTLY_VIEWED_MESSAGES.empty}
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      {showTitle && (
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-primary" />
            <h2 className="text-xl font-semibold text-text-primary">
              {PERSIAN_RECENTLY_VIEWED_MESSAGES.title}
            </h2>
            <span className="text-sm text-text-secondary">
              ({items.length} محصول)
            </span>
          </div>
          
          {items.length > 0 && (
            <button
              onClick={clearHistory}
              className="text-sm text-red-600 hover:text-red-800 flex items-center gap-1"
            >
              <RotateCcw className="w-4 h-4" />
              {PERSIAN_RECENTLY_VIEWED_MESSAGES.clearHistory}
            </button>
          )}
        </div>
      )}

      {compact ? (
        // Compact horizontal scroll view
        <div className="flex gap-4 overflow-x-auto pb-2 scrollbar-hide">
          {items.map(item => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex-shrink-0 w-48"
            >
              <div className="relative group">
                <ProductCard 
                  product={item.product}
                  compact={true}
                />
                
                {/* View info overlay */}
                <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                  {item.viewCount} بار • {formatViewedAt(item.viewedAt)}
                </div>

                {/* Remove button */}
                <button
                  onClick={() => removeItem(item.product.id)}
                  className="absolute top-2 right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        // Full grid view
        <div className="space-y-6">
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg border p-4 text-center">
              <div className="text-2xl font-bold text-primary">
                {items.length}
              </div>
              <div className="text-sm text-text-secondary">
                محصول مشاهده شده
              </div>
            </div>
            
            <div className="bg-white rounded-lg border p-4 text-center">
              <div className="text-2xl font-bold text-primary">
                {items.reduce((sum, item) => sum + item.viewCount, 0)}
              </div>
              <div className="text-sm text-text-secondary">
                کل بازدید
              </div>
            </div>
            
            <div className="bg-white rounded-lg border p-4 text-center">
              <div className="text-2xl font-bold text-primary">
                {Math.round(items.reduce((sum, item) => sum + (item.timeSpent || 0), 0) / 60)}
              </div>
              <div className="text-sm text-text-secondary">
                دقیقه زمان صرف شده
              </div>
            </div>
            
            <div className="bg-white rounded-lg border p-4 text-center">
              <div className="text-2xl font-bold text-primary">
                {getMostViewedProducts(1)[0]?.viewCount || 0}
              </div>
              <div className="text-sm text-text-secondary">
                بیشترین بازدید
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <AnimatePresence mode="popLayout">
              {items.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                  className="relative group"
                >
                  <ProductCard product={item.product} />
                  
                  {/* View info */}
                  <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded-full">
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {item.viewCount}
                    </div>
                  </div>

                  {/* Source badge */}
                  {item.source && (
                    <div className="absolute top-2 right-2 bg-primary text-white text-xs px-2 py-1 rounded-full">
                      {getSourceIcon(item.source)} {PERSIAN_RECENTLY_VIEWED_MESSAGES.source[item.source]}
                    </div>
                  )}

                  {/* Remove button */}
                  <button
                    onClick={() => removeItem(item.product.id)}
                    className="absolute top-10 right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                  >
                    <X className="w-3 h-3" />
                  </button>

                  {/* View details */}
                  <div className="mt-2 text-xs text-text-secondary space-y-1">
                    <div className="flex items-center justify-between">
                      <span>{formatViewedAt(item.viewedAt)}</span>
                      {item.timeSpent && (
                        <span>{formatTimeSpent(item.timeSpent)}</span>
                      )}
                    </div>
                    {item.viewCount > 1 && (
                      <div className="flex items-center gap-1 text-primary">
                        <TrendingUp className="w-3 h-3" />
                        {item.viewCount} بار مشاهده
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      )}
    </div>
  );
};

// Export the component and utility functions
export default RecentlyViewed;

// Hook for using recently viewed functionality
export const useRecentlyViewed = (maxItems: number = 20) => {
  const [items, setItems] = useState<RecentlyViewedItem[]>([]);

  useEffect(() => {
    const stored = loadRecentlyViewedFromStorage();
    setItems(stored.slice(0, maxItems));
  }, [maxItems]);

  const addItem = (product: Product, source?: ViewSource, timeSpent?: number) => {
    setItems(prev => {
      const filtered = prev.filter(item => item.product.id !== product.id);
      const existingItem = prev.find(item => item.product.id === product.id);
      
      const newItem: RecentlyViewedItem = {
        id: `recently_viewed_${product.id}_${Date.now()}`,
        product,
        viewedAt: new Date(),
        viewCount: existingItem ? existingItem.viewCount + 1 : 1,
        timeSpent,
        source
      };

      const newItems = [newItem, ...filtered].slice(0, maxItems);
      saveRecentlyViewedToStorage(newItems);
      return newItems;
    });
  };

  const removeItem = (productId: string) => {
    setItems(prev => {
      const newItems = prev.filter(item => item.product.id !== productId);
      saveRecentlyViewedToStorage(newItems);
      return newItems;
    });
  };

  const clearHistory = () => {
    setItems([]);
    saveRecentlyViewedToStorage([]);
  };

  return {
    items,
    addItem,
    removeItem,
    clearHistory
  };
};
