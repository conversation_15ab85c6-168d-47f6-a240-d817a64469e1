import React from 'react';
import { motion } from 'framer-motion';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import ProductCard from '../common/ProductCard';
import { Product } from '../../types';
import { products } from '../../data/products';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface RelatedProductsProps {
  currentProduct: Product;
}

const RelatedProducts: React.FC<RelatedProductsProps> = ({ currentProduct }) => {
  // Get related products from the same category, excluding current product
  const relatedProducts = products
    .filter(product => 
      product.category === currentProduct.category && 
      product.id !== currentProduct.id
    )
    .slice(0, 8); // Limit to 8 products

  // If no products in same category, get random products
  const finalRelatedProducts = relatedProducts.length > 0 
    ? relatedProducts 
    : products
        .filter(product => product.id !== currentProduct.id)
        .slice(0, 8);

  if (finalRelatedProducts.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-2xl shadow-soft overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-text-primary">
            محصولات مرتبط
          </h3>
          <div className="flex gap-2">
            <button className="related-products-prev w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors">
              <ChevronRight className="w-4 h-4 text-text-secondary" />
            </button>
            <button className="related-products-next w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors">
              <ChevronLeft className="w-4 h-4 text-text-secondary" />
            </button>
          </div>
        </div>

        <div className="relative">
          <Swiper
            modules={[Navigation, Pagination]}
            spaceBetween={20}
            slidesPerView={1}
            navigation={{
              nextEl: '.related-products-next',
              prevEl: '.related-products-prev',
            }}
            pagination={{ 
              clickable: true,
              el: '.related-products-pagination'
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
              },
              768: {
                slidesPerView: 2,
              },
              1024: {
                slidesPerView: 3,
              },
              1280: {
                slidesPerView: 4,
              },
            }}
            className="pb-12"
          >
            {finalRelatedProducts.map((product, index) => (
              <SwiperSlide key={product.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <ProductCard product={product} />
                </motion.div>
              </SwiperSlide>
            ))}
          </Swiper>
          
          {/* Custom pagination */}
          <div className="related-products-pagination flex justify-center mt-4"></div>
        </div>
      </div>

      {/* Category Link */}
      <div className="border-t border-gray-100 p-6 bg-gray-50">
        <div className="text-center">
          <p className="text-text-secondary mb-3">
            مشاهده همه محصولات دسته‌بندی {currentProduct.category}
          </p>
          <a
            href={`/products?category=${currentProduct.category}`}
            className="inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium transition-colors"
          >
            مشاهده همه
            <ChevronLeft className="w-4 h-4" />
          </a>
        </div>
      </div>
    </div>
  );
};

export default RelatedProducts;
