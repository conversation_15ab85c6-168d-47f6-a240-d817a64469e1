import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Heart, 
  ShoppingCart, 
  Trash2, 
  Filter, 
  Share2, 
  Download,
  Grid,
  List,
  Star,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { useWishlist } from '../../hooks/useWishlist';
import { WishlistPriority, PERSIAN_WISHLIST_MESSAGES } from '../../types/comparison';
import { formatNumber } from '../../utils/formatters';
import ProductCard from '../common/ProductCard';

interface WishlistProps {
  className?: string;
}

const Wishlist: React.FC<WishlistProps> = ({ className = '' }) => {
  const {
    filteredItems,
    totalItems,
    filters,
    setFilters,
    removeItem,
    moveToCart,
    clearWishlist,
    shareWishlist,
    exportWishlist,
    getWishlistStats,
    getFilterOptions,
    updatePriority,
    addNotes
  } = useWishlist();

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [editingNotes, setEditingNotes] = useState<string | null>(null);
  const [notesText, setNotesText] = useState('');

  const stats = getWishlistStats();
  const filterOptions = getFilterOptions();

  const getPriorityColor = (priority: WishlistPriority) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-blue-600 bg-blue-50';
      case 'low': return 'text-gray-600 bg-gray-50';
    }
  };

  const getPriorityIcon = (priority: WishlistPriority) => {
    switch (priority) {
      case 'urgent': return <AlertCircle className="w-4 h-4" />;
      case 'high': return <Star className="w-4 h-4" />;
      case 'medium': return <CheckCircle className="w-4 h-4" />;
      case 'low': return <Heart className="w-4 h-4" />;
    }
  };

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };

  const handleBulkMoveToCart = () => {
    selectedItems.forEach(itemId => moveToCart(itemId));
    setSelectedItems([]);
  };

  const handleBulkRemove = () => {
    selectedItems.forEach(itemId => removeItem(itemId));
    setSelectedItems([]);
  };

  const handleEditNotes = (itemId: string, currentNotes?: string) => {
    setEditingNotes(itemId);
    setNotesText(currentNotes || '');
  };

  const handleSaveNotes = (itemId: string) => {
    addNotes(itemId, notesText);
    setEditingNotes(null);
    setNotesText('');
  };

  if (totalItems === 0) {
    return (
      <div className={`text-center py-16 ${className}`}>
        <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-text-primary mb-2">
          {PERSIAN_WISHLIST_MESSAGES.empty}
        </h3>
        <p className="text-text-secondary mb-6">
          محصولات مورد علاقه خود را اینجا ذخیره کنید
        </p>
        <button 
          onClick={() => window.location.href = '/products'}
          className="btn-primary"
        >
          مشاهده محصولات
        </button>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">
            {PERSIAN_WISHLIST_MESSAGES.title}
          </h1>
          <p className="text-text-secondary">
            {totalItems} محصول • ارزش کل: {formatNumber(stats.totalValue)} تومان
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid' 
                  ? 'bg-white text-primary shadow-sm' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list' 
                  ? 'bg-white text-primary shadow-sm' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>

          {/* Action Buttons */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-secondary"
          >
            <Filter className="w-4 h-4" />
            فیلتر
          </button>

          <button
            onClick={shareWishlist}
            className="btn-secondary"
          >
            <Share2 className="w-4 h-4" />
            اشتراک
          </button>

          <button
            onClick={exportWishlist}
            className="btn-secondary"
          >
            <Download className="w-4 h-4" />
            دانلود
          </button>
        </div>
      </div>

      {/* Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white rounded-lg border p-4 space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  دسته‌بندی
                </label>
                <select
                  value={filters.category || ''}
                  onChange={(e) => setFilters({ category: e.target.value || undefined })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">همه دسته‌ها</option>
                  {filterOptions.categories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Priority Filter */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  اولویت
                </label>
                <select
                  value={filters.priority || ''}
                  onChange={(e) => setFilters({ priority: e.target.value as WishlistPriority || undefined })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">همه اولویت‌ها</option>
                  {filterOptions.priorities.map(priority => (
                    <option key={priority} value={priority}>
                      {PERSIAN_WISHLIST_MESSAGES.priority[priority]}
                    </option>
                  ))}
                </select>
              </div>

              {/* Availability Filter */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  موجودی
                </label>
                <select
                  value={filters.availability || 'all'}
                  onChange={(e) => setFilters({ availability: e.target.value as any })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="all">همه</option>
                  <option value="in_stock">موجود</option>
                  <option value="out_of_stock">ناموجود</option>
                </select>
              </div>

              {/* Sort Filter */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  مرتب‌سازی
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters({ sortBy: e.target.value as any })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  {Object.entries(PERSIAN_WISHLIST_MESSAGES.sort).map(([key, label]) => (
                    <option key={key} value={key}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex justify-between items-center pt-2 border-t">
              <button
                onClick={() => setFilters({})}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                پاک کردن فیلترها
              </button>
              <span className="text-sm text-text-secondary">
                {filteredItems.length} محصول یافت شد
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-primary-50 border border-primary-200 rounded-lg p-4"
        >
          <div className="flex items-center justify-between">
            <span className="text-primary-800 font-medium">
              {selectedItems.length} محصول انتخاب شده
            </span>
            <div className="flex items-center gap-3">
              <button
                onClick={handleBulkMoveToCart}
                className="btn-primary btn-sm"
              >
                <ShoppingCart className="w-4 h-4" />
                انتقال به سبد
              </button>
              <button
                onClick={handleBulkRemove}
                className="btn-secondary btn-sm text-red-600 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4" />
                حذف
              </button>
              <button
                onClick={() => setSelectedItems([])}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                لغو انتخاب
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Select All */}
      <div className="flex items-center justify-between">
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={selectedItems.length === filteredItems.length && filteredItems.length > 0}
            onChange={handleSelectAll}
            className="rounded border-gray-300"
          />
          <span className="text-sm text-text-secondary">
            انتخاب همه ({filteredItems.length} محصول)
          </span>
        </label>

        {totalItems > 0 && (
          <button
            onClick={clearWishlist}
            className="text-sm text-red-600 hover:text-red-800"
          >
            پاک کردن همه
          </button>
        )}
      </div>

      {/* Products Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredItems.map(item => (
            <div key={item.id} className="relative">
              <label className="absolute top-2 left-2 z-10 cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(item.id)}
                  onChange={() => handleSelectItem(item.id)}
                  className="rounded border-gray-300"
                />
              </label>
              <ProductCard 
                product={item.product}
                showWishlistButton={false}
              />
              {/* Priority Badge */}
              <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${getPriorityColor(item.priority)}`}>
                {getPriorityIcon(item.priority)}
                {PERSIAN_WISHLIST_MESSAGES.priority[item.priority]}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredItems.map(item => (
            <motion.div
              key={item.id}
              layout
              className="bg-white rounded-lg border p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center gap-4">
                <label className="cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item.id)}
                    onChange={() => handleSelectItem(item.id)}
                    className="rounded border-gray-300"
                  />
                </label>

                <img
                  src={item.product.images[0]}
                  alt={item.product.name}
                  className="w-16 h-16 object-cover rounded-lg"
                />

                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-text-primary truncate">
                    {item.product.name}
                  </h3>
                  <p className="text-sm text-text-secondary">
                    {item.product.brand} • {item.product.category}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${getPriorityColor(item.priority)}`}>
                      {getPriorityIcon(item.priority)}
                      {PERSIAN_WISHLIST_MESSAGES.priority[item.priority]}
                    </span>
                    {item.product.stock === 0 && (
                      <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                        ناموجود
                      </span>
                    )}
                  </div>
                </div>

                <div className="text-left">
                  <div className="font-bold text-text-primary">
                    {formatNumber(item.product.discountedPrice || item.product.price)} تومان
                  </div>
                  {item.product.discountedPrice && (
                    <div className="text-sm text-gray-500 line-through">
                      {formatNumber(item.product.price)} تومان
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => moveToCart(item.id)}
                    disabled={item.product.stock === 0}
                    className="btn-primary btn-sm disabled:opacity-50"
                  >
                    <ShoppingCart className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => removeItem(item.id)}
                    className="btn-secondary btn-sm text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Notes Section */}
              {item.notes && (
                <div className="mt-3 pt-3 border-t">
                  <p className="text-sm text-text-secondary">
                    <strong>یادداشت:</strong> {item.notes}
                  </p>
                </div>
              )}
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Wishlist;
