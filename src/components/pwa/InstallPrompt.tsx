import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, X, Smartphone, Monitor, Tablet } from 'lucide-react';
import { pwaManager, PWA_MESSAGES } from '../../utils/pwaUtils';

interface InstallPromptProps {
  className?: string;
  variant?: 'banner' | 'modal' | 'button';
  autoShow?: boolean;
  showDelay?: number;
  onInstall?: () => void;
  onDismiss?: () => void;
}

const InstallPrompt: React.FC<InstallPromptProps> = ({
  className = '',
  variant = 'banner',
  autoShow = true,
  showDelay = 3000,
  onInstall,
  onDismiss
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installState, setInstallState] = useState(pwaManager.getInstallState());

  useEffect(() => {
    // Update install state
    const updateInstallState = () => {
      setInstallState(pwaManager.getInstallState());
    };

    // Listen for PWA events
    pwaManager.on('installable', updateInstallState);
    pwaManager.on('installed', updateInstallState);

    // Auto show logic
    if (autoShow && installState.canInstall) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, showDelay);

      return () => clearTimeout(timer);
    }

    return () => {
      pwaManager.off('installable', updateInstallState);
      pwaManager.off('installed', updateInstallState);
    };
  }, [autoShow, showDelay, installState.canInstall]);

  const handleInstall = async () => {
    setIsInstalling(true);
    
    try {
      const success = await pwaManager.promptInstall();
      
      if (success) {
        setIsVisible(false);
        onInstall?.();
      }
    } catch (error) {
      // console.error('Install failed:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  const getPlatformIcon = () => {
    switch (installState.platform) {
      case 'android':
        return <Smartphone className="w-5 h-5" />;
      case 'ios':
        return <Smartphone className="w-5 h-5" />;
      case 'windows':
      case 'macos':
      case 'linux':
        return <Monitor className="w-5 h-5" />;
      default:
        return <Tablet className="w-5 h-5" />;
    }
  };

  const getPlatformInstructions = () => {
    switch (installState.platform) {
      case 'ios':
        return 'برای نصب، روی دکمه اشتراک‌گذاری کلیک کرده و "Add to Home Screen" را انتخاب کنید';
      case 'android':
        return 'برای نصب، روی دکمه زیر کلیک کنید';
      default:
        return 'برای نصب اپلیکیشن روی دستگاه خود، روی دکمه زیر کلیک کنید';
    }
  };

  // Don't render if not installable or already installed
  if (!installState.canInstall || installState.isInstalled) {
    return null;
  }

  // Button variant
  if (variant === 'button') {
    return (
      <button
        onClick={handleInstall}
        disabled={isInstalling}
        className={`
          inline-flex items-center gap-2 px-4 py-2 bg-primary-500 text-white rounded-lg
          hover:bg-primary-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed
          ${className}
        `}
      >
        {isInstalling ? (
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
        ) : (
          <Download className="w-4 h-4" />
        )}
        {isInstalling ? 'در حال نصب...' : 'نصب اپلیکیشن'}
      </button>
    );
  }

  // Banner variant
  if (variant === 'banner') {
    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -100, opacity: 0 }}
            className={`
              fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-primary-500 to-primary-600
              text-white shadow-lg ${className}
            `}
          >
            <div className="container mx-auto px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getPlatformIcon()}
                  <div>
                    <h3 className="font-semibold text-sm">
                      {PWA_MESSAGES.install.title}
                    </h3>
                    <p className="text-xs opacity-90">
                      {PWA_MESSAGES.install.description}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleInstall}
                    disabled={isInstalling}
                    className="
                      px-4 py-2 bg-white text-primary-600 rounded-lg text-sm font-medium
                      hover:bg-gray-100 transition-colors disabled:opacity-50
                    "
                  >
                    {isInstalling ? 'در حال نصب...' : PWA_MESSAGES.install.button}
                  </button>
                  
                  <button
                    onClick={handleDismiss}
                    className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  // Modal variant
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
          onClick={handleDismiss}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
            className={`
              bg-white rounded-xl shadow-xl max-w-md w-full p-6 text-center
              ${className}
            `}
          >
            <div className="mb-4">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {getPlatformIcon()}
              </div>
              
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                {PWA_MESSAGES.install.title}
              </h2>
              
              <p className="text-gray-600 text-sm mb-4">
                {getPlatformInstructions()}
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleInstall}
                disabled={isInstalling}
                className="
                  w-full px-4 py-3 bg-primary-500 text-white rounded-lg font-medium
                  hover:bg-primary-600 transition-colors disabled:opacity-50
                  disabled:cursor-not-allowed
                "
              >
                {isInstalling ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    در حال نصب...
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <Download className="w-4 h-4" />
                    {PWA_MESSAGES.install.button}
                  </div>
                )}
              </button>
              
              <button
                onClick={handleDismiss}
                className="
                  w-full px-4 py-3 bg-gray-100 text-gray-700 rounded-lg font-medium
                  hover:bg-gray-200 transition-colors
                "
              >
                {PWA_MESSAGES.install.cancel}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default InstallPrompt;
