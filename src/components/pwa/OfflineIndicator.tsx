import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { WifiOff, Wifi, RefreshCw, Signal } from 'lucide-react';
import { pwaManager, PWA_MESSAGES } from '../../utils/pwaUtils';

interface OfflineIndicatorProps {
  className?: string;
  position?: 'top' | 'bottom';
  showConnectionInfo?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  className = '',
  position = 'bottom',
  showConnectionInfo = true,
  autoHide = true,
  autoHideDelay = 5000
}) => {
  const [networkState, setNetworkState] = useState(pwaManager.getNetworkState());
  const [isVisible, setIsVisible] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    const updateNetworkState = () => {
      const newState = pwaManager.getNetworkState();
      setNetworkState(newState);
      
      // Show indicator when going offline
      if (!newState.isOnline) {
        setIsVisible(true);
      }
      
      // Auto hide when coming back online
      if (newState.isOnline && autoHide) {
        const timer = setTimeout(() => {
          setIsVisible(false);
        }, autoHideDelay);
        
        return () => clearTimeout(timer);
      }
    };

    // Listen for network events
    pwaManager.on('online', updateNetworkState);
    pwaManager.on('offline', updateNetworkState);

    // Initial state check
    updateNetworkState();

    return () => {
      pwaManager.off('online', updateNetworkState);
      pwaManager.off('offline', updateNetworkState);
    };
  }, [autoHide, autoHideDelay]);

  const handleRetry = async () => {
    setIsRetrying(true);
    
    // Simulate retry delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check connection again
    const newState = pwaManager.getNetworkState();
    setNetworkState(newState);
    
    if (newState.isOnline) {
      setIsVisible(false);
    }
    
    setIsRetrying(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  const getConnectionIcon = () => {
    if (isRetrying) {
      return <RefreshCw className="w-5 h-5 animate-spin" />;
    }
    
    if (networkState.isOnline) {
      return <Wifi className="w-5 h-5" />;
    }
    
    return <WifiOff className="w-5 h-5" />;
  };

  const getConnectionText = () => {
    if (isRetrying) {
      return 'در حال بررسی اتصال...';
    }
    
    if (networkState.isOnline) {
      return 'اتصال برقرار شد';
    }
    
    return PWA_MESSAGES.offline.description;
  };

  const getConnectionDetails = () => {
    if (!showConnectionInfo || !networkState.isOnline) {
      return null;
    }

    const { effectiveType, downlink } = networkState;
    
    return (
      <div className="flex items-center gap-2 text-xs opacity-75">
        <Signal className="w-3 h-3" />
        <span>
          {effectiveType === '4g' ? '4G' : 
           effectiveType === '3g' ? '3G' : 
           effectiveType === '2g' ? '2G' : 'نامشخص'}
        </span>
        {downlink > 0 && (
          <span>• {downlink.toFixed(1)} Mbps</span>
        )}
      </div>
    );
  };

  const positionClasses = position === 'top' 
    ? 'top-0 left-0 right-0' 
    : 'bottom-0 left-0 right-0';

  const animationProps = position === 'top'
    ? {
        initial: { y: -100, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        exit: { y: -100, opacity: 0 }
      }
    : {
        initial: { y: 100, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        exit: { y: 100, opacity: 0 }
      };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          {...animationProps}
          className={`
            fixed ${positionClasses} z-40 
            ${networkState.isOnline 
              ? 'bg-green-500' 
              : 'bg-red-500'
            } 
            text-white shadow-lg ${className}
          `}
        >
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getConnectionIcon()}
                <div>
                  <div className="font-medium text-sm">
                    {getConnectionText()}
                  </div>
                  {getConnectionDetails()}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {!networkState.isOnline && (
                  <button
                    onClick={handleRetry}
                    disabled={isRetrying}
                    className="
                      px-3 py-1 bg-white/20 rounded text-sm font-medium
                      hover:bg-white/30 transition-colors disabled:opacity-50
                    "
                  >
                    {PWA_MESSAGES.offline.retry}
                  </button>
                )}
                
                <button
                  onClick={handleDismiss}
                  className="p-1 hover:bg-white/20 rounded transition-colors"
                >
                  ×
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Hook for using offline state
export const useOfflineState = () => {
  const [networkState, setNetworkState] = useState(pwaManager.getNetworkState());

  useEffect(() => {
    const updateNetworkState = () => {
      setNetworkState(pwaManager.getNetworkState());
    };

    pwaManager.on('online', updateNetworkState);
    pwaManager.on('offline', updateNetworkState);

    return () => {
      pwaManager.off('online', updateNetworkState);
      pwaManager.off('offline', updateNetworkState);
    };
  }, []);

  return networkState;
};

// Hook for PWA install state
export const usePWAInstall = () => {
  const [installState, setInstallState] = useState(pwaManager.getInstallState());

  useEffect(() => {
    const updateInstallState = () => {
      setInstallState(pwaManager.getInstallState());
    };

    pwaManager.on('installable', updateInstallState);
    pwaManager.on('installed', updateInstallState);

    return () => {
      pwaManager.off('installable', updateInstallState);
      pwaManager.off('installed', updateInstallState);
    };
  }, []);

  const promptInstall = async () => {
    return await pwaManager.promptInstall();
  };

  return {
    ...installState,
    promptInstall
  };
};

export default OfflineIndicator;
