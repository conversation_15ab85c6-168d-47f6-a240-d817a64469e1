import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Package, FileText, Zap, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import { formatBytes } from '../../utils/performanceUtils';
import { useAdminAuth } from '../../hooks/useAdminAuth';

interface BundleInfo {
  name: string;
  size: number;
  gzipSize: number;
  modules: string[];
  loadTime: number;
  isLazy: boolean;
  isPreloaded: boolean;
}

interface BundleAnalyzerProps {
  showInProduction?: boolean;
  autoAnalyze?: boolean;
  onAnalysisComplete?: (analysis: BundleAnalysis) => void;
}

interface BundleAnalysis {
  totalSize: number;
  totalGzipSize: number;
  chunkCount: number;
  lazyChunks: number;
  recommendations: string[];
  performance: 'excellent' | 'good' | 'fair' | 'poor';
}

/**
 * Bundle Analyzer Component
 *
 * Provides detailed analysis of JavaScript bundle sizes and performance.
 * SECURITY: Only visible to authenticated admin users.
 */
const BundleAnalyzer: React.FC<BundleAnalyzerProps> = ({
  showInProduction = false,
  autoAnalyze = true,
  onAnalysisComplete
}) => {
  const [bundles, setBundles] = useState<BundleInfo[]>([]);
  const [analysis, setAnalysis] = useState<BundleAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Get admin authentication status
  const { isAuthenticated: isAdminAuthenticated, user: adminUser } = useAdminAuth();

  // Check if should show - only for authenticated admins
  useEffect(() => {
    const shouldShow = isAdminAuthenticated &&
                      adminUser &&
                      (process.env.NODE_ENV === 'development' || showInProduction);
    setIsVisible(shouldShow);

    if (shouldShow && autoAnalyze) {
      analyzeBundles();
    }
  }, [showInProduction, autoAnalyze, isAdminAuthenticated, adminUser]);

  // Analyze bundle information
  const analyzeBundles = async () => {
    setIsAnalyzing(true);
    
    try {
      // Get performance navigation timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      // Get resource timing for scripts
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const scripts = resources.filter(resource => 
        resource.name.includes('.js') && 
        (resource.name.includes('/assets/') || resource.name.includes('chunk'))
      );

      // Mock bundle analysis (in real app, this would come from build tools)
      const mockBundles: BundleInfo[] = [
        {
          name: 'main',
          size: 245 * 1024, // 245KB
          gzipSize: 85 * 1024, // 85KB
          modules: ['App', 'Router', 'main'],
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          isLazy: false,
          isPreloaded: true
        },
        {
          name: 'vendor',
          size: 512 * 1024, // 512KB
          gzipSize: 165 * 1024, // 165KB
          modules: ['react', 'react-dom', 'framer-motion'],
          loadTime: 0,
          isLazy: false,
          isPreloaded: true
        },
        {
          name: 'ui',
          size: 128 * 1024, // 128KB
          gzipSize: 45 * 1024, // 45KB
          modules: ['lucide-react', 'components'],
          loadTime: 0,
          isLazy: false,
          isPreloaded: false
        },
        {
          name: 'admin',
          size: 89 * 1024, // 89KB
          gzipSize: 32 * 1024, // 32KB
          modules: ['AdminDashboard', 'AdminComponents'],
          loadTime: 0,
          isLazy: true,
          isPreloaded: false
        }
      ];

      // Add actual script sizes if available
      scripts.forEach((script, index) => {
        if (mockBundles[index]) {
          mockBundles[index].loadTime = script.duration;
        }
      });

      setBundles(mockBundles);

      // Generate analysis
      const totalSize = mockBundles.reduce((sum, bundle) => sum + bundle.size, 0);
      const totalGzipSize = mockBundles.reduce((sum, bundle) => sum + bundle.gzipSize, 0);
      const lazyChunks = mockBundles.filter(bundle => bundle.isLazy).length;
      
      const recommendations: string[] = [];
      let performance: BundleAnalysis['performance'] = 'excellent';

      // Performance analysis
      if (totalGzipSize > 500 * 1024) { // > 500KB
        performance = 'poor';
        recommendations.push('Bundle size is too large. Consider code splitting.');
      } else if (totalGzipSize > 300 * 1024) { // > 300KB
        performance = 'fair';
        recommendations.push('Bundle size could be optimized.');
      } else if (totalGzipSize > 200 * 1024) { // > 200KB
        performance = 'good';
      }

      // Lazy loading recommendations
      if (lazyChunks === 0) {
        recommendations.push('Consider implementing lazy loading for non-critical routes.');
      }

      // Large chunk recommendations
      mockBundles.forEach(bundle => {
        if (bundle.gzipSize > 100 * 1024 && !bundle.isLazy) {
          recommendations.push(`${bundle.name} chunk is large. Consider splitting further.`);
        }
      });

      const bundleAnalysis: BundleAnalysis = {
        totalSize,
        totalGzipSize,
        chunkCount: mockBundles.length,
        lazyChunks,
        recommendations,
        performance
      };

      setAnalysis(bundleAnalysis);
      onAnalysisComplete?.(bundleAnalysis);

    } catch (error) {
      // console.error('Bundle analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Get performance color
  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'text-green-600 bg-green-50';
      case 'good': return 'text-blue-600 bg-blue-50';
      case 'fair': return 'text-yellow-600 bg-yellow-50';
      case 'poor': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // Get performance icon
  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return <CheckCircle className="w-4 h-4" />;
      case 'good': return <CheckCircle className="w-4 h-4" />;
      case 'fair': return <AlertTriangle className="w-4 h-4" />;
      case 'poor': return <AlertTriangle className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  // Don't render if not visible or not admin authenticated
  if (!isVisible || !isAdminAuthenticated || !adminUser) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Package className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Bundle Analysis</h2>
        </div>
        
        <button
          onClick={analyzeBundles}
          disabled={isAnalyzing}
          className="
            px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 
            transition-colors disabled:opacity-50 disabled:cursor-not-allowed
            flex items-center gap-2
          "
        >
          {isAnalyzing ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <Zap className="w-4 h-4" />
              Analyze
            </>
          )}
        </button>
      </div>

      {/* Analysis Results */}
      {analysis && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Performance Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-sm text-gray-600 mb-1">Total Size</div>
              <div className="text-lg font-semibold">{formatBytes(analysis.totalSize)}</div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-sm text-gray-600 mb-1">Gzipped</div>
              <div className="text-lg font-semibold">{formatBytes(analysis.totalGzipSize)}</div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-sm text-gray-600 mb-1">Chunks</div>
              <div className="text-lg font-semibold">{analysis.chunkCount}</div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-sm text-gray-600 mb-1">Lazy Chunks</div>
              <div className="text-lg font-semibold">{analysis.lazyChunks}</div>
            </div>
          </div>

          {/* Performance Score */}
          <div className={`rounded-lg p-4 ${getPerformanceColor(analysis.performance)}`}>
            <div className="flex items-center gap-2 mb-2">
              {getPerformanceIcon(analysis.performance)}
              <span className="font-semibold capitalize">
                Performance: {analysis.performance}
              </span>
            </div>
            <div className="text-sm opacity-80">
              Bundle optimization score based on size and loading strategy
            </div>
          </div>

          {/* Bundle Details */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Bundle Details</h3>
            <div className="space-y-3">
              {bundles.map((bundle, index) => (
                <motion.div
                  key={bundle.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4 text-gray-500" />
                      <span className="font-medium">{bundle.name}</span>
                      {bundle.isLazy && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                          Lazy
                        </span>
                      )}
                      {bundle.isPreloaded && (
                        <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                          Preloaded
                        </span>
                      )}
                    </div>
                    
                    <div className="text-sm text-gray-600">
                      {formatBytes(bundle.gzipSize)} gzipped
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Size: </span>
                      <span className="font-mono">{formatBytes(bundle.size)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Load Time: </span>
                      <span className="font-mono">{bundle.loadTime.toFixed(2)}ms</span>
                    </div>
                  </div>
                  
                  <div className="mt-2">
                    <span className="text-gray-600 text-sm">Modules: </span>
                    <span className="text-sm">{bundle.modules.join(', ')}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Recommendations */}
          {analysis.recommendations.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Recommendations</h3>
              <div className="space-y-2">
                {analysis.recommendations.map((recommendation, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                  >
                    <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-yellow-800">{recommendation}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      )}

      {/* No Analysis State */}
      {!analysis && !isAnalyzing && (
        <div className="text-center py-8 text-gray-500">
          <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>Click "Analyze" to examine your bundle performance</p>
        </div>
      )}
    </div>
  );
};

export default BundleAnalyzer;
