import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Activity,
  Cpu,
  HardDrive,
  Wifi,
  Clock,
  Zap,
  Eye,
  EyeOff,
  BarChart3,
  X
} from 'lucide-react';
import { usePerformanceMonitor, useApiPerformanceMonitor } from '../../hooks/usePerformanceMonitor';
import { formatBytes, formatDuration } from '../../utils/performanceUtils';
import { useAdminAuth } from '../../hooks/useAdminAuth';

interface PerformanceMonitorProps {
  enabled?: boolean; // Will only show if admin is authenticated
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showInProduction?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

/**
 * Performance Monitor Component
 *
 * This component provides real-time performance monitoring including:
 * - Frame rate (FPS)
 * - Memory usage
 * - Render times
 * - API response times
 *
 * SECURITY: Only visible to authenticated admin users for security and UX reasons.
 * Regular users should not see performance debugging information.
 */
const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = true, // Default to true, but will be controlled by admin auth
  position = 'bottom-right',
  showInProduction = false,
  autoHide = true,
  autoHideDelay = 5000
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [autoHideTimer, setAutoHideTimer] = useState<NodeJS.Timeout | null>(null);

  // Get admin authentication status
  const { isAuthenticated: isAdminAuthenticated, user: adminUser } = useAdminAuth();

  const {
    metrics,
    frameRate,
    memoryUsage,
    renderTime,
    isMonitoring,
    startMonitoring,
    stopMonitoring
  } = usePerformanceMonitor({
    componentName: 'PerformanceMonitor',
    enableFrameRateMonitoring: true,
    enableMemoryMonitoring: true,
    enableRenderTimeMonitoring: true,
    monitoringInterval: 1000
  });

  const { apiMetrics } = useApiPerformanceMonitor();

  // Show/hide logic - only show for authenticated admins
  useEffect(() => {
    // Check if should be enabled based on environment and admin authentication
    const shouldShow = enabled &&
                      isAdminAuthenticated &&
                      adminUser &&
                      (process.env.NODE_ENV === 'development' || showInProduction);

    if (!shouldShow) {
      setIsVisible(false);
      stopMonitoring();
      return;
    }

    setIsVisible(true);
    startMonitoring();

    return () => {
      stopMonitoring();
    };
  }, [enabled, showInProduction, isAdminAuthenticated, adminUser, startMonitoring, stopMonitoring]);

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide && isExpanded) {
      if (autoHideTimer) {
        clearTimeout(autoHideTimer);
      }

      const timer = setTimeout(() => {
        setIsExpanded(false);
      }, autoHideDelay);

      setAutoHideTimer(timer);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [autoHide, isExpanded, autoHideDelay]); // Removed autoHideTimer to avoid infinite re-renders

  // Position classes
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  // Performance status indicators
  const getPerformanceStatus = () => {
    const fps = frameRate || 0;
    const memory = memoryUsage?.used || 0;
    const render = renderTime || 0;

    if (fps < 30 || memory > 100 * 1024 * 1024 || render > 100) {
      return { status: 'poor', color: 'bg-red-500', textColor: 'text-red-600' };
    }

    if (fps < 50 || memory > 50 * 1024 * 1024 || render > 50) {
      return { status: 'fair', color: 'bg-yellow-500', textColor: 'text-yellow-600' };
    }

    return { status: 'good', color: 'bg-green-500', textColor: 'text-green-600' };
  };

  const performanceStatus = getPerformanceStatus();

  // Don't render anything if not visible or not admin authenticated
  if (!isVisible || !isAdminAuthenticated || !adminUser) {
    return null;
  }

  return (
    <div className={`fixed ${positionClasses[position]} z-50`}>
      <AnimatePresence>
        {!isExpanded ? (
          // Collapsed view - floating button
          <motion.button
            key="collapsed"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            onClick={() => setIsExpanded(true)}
            className={`
              w-12 h-12 rounded-full ${performanceStatus.color} text-white
              shadow-lg hover:shadow-xl transition-shadow duration-200
              flex items-center justify-center
            `}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <Activity className="w-5 h-5" />
          </motion.button>
        ) : (
          // Expanded view - detailed panel
          <motion.div
            key="expanded"
            initial={{ scale: 0.8, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 20 }}
            className="
              bg-white rounded-lg shadow-xl border border-gray-200 p-4 min-w-[300px]
              backdrop-blur-sm bg-white/95
            "
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${performanceStatus.color}`} />
                <h3 className="font-semibold text-gray-900">Performance Monitor</h3>
              </div>
              
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setIsExpanded(false)}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  <EyeOff className="w-4 h-4 text-gray-500" />
                </button>
                <button
                  onClick={() => setIsVisible(false)}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  <X className="w-4 h-4 text-gray-500" />
                </button>
              </div>
            </div>

            {/* Metrics Grid */}
            <div className="space-y-3">
              {/* Frame Rate */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-500" />
                  <span className="text-sm font-medium">FPS</span>
                </div>
                <span className={`text-sm font-mono ${performanceStatus.textColor}`}>
                  {frameRate ? Math.round(frameRate) : '--'}
                </span>
              </div>

              {/* Memory Usage */}
              {memoryUsage && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Cpu className="w-4 h-4 text-purple-500" />
                    <span className="text-sm font-medium">Memory</span>
                  </div>
                  <span className="text-sm font-mono text-gray-600">
                    {formatBytes(memoryUsage.used)} / {formatBytes(memoryUsage.total)}
                  </span>
                </div>
              )}

              {/* Render Time */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-orange-500" />
                  <span className="text-sm font-medium">Render</span>
                </div>
                <span className="text-sm font-mono text-gray-600">
                  {formatDuration(renderTime)}
                </span>
              </div>

              {/* Load Time */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <HardDrive className="w-4 h-4 text-green-500" />
                  <span className="text-sm font-medium">Load</span>
                </div>
                <span className="text-sm font-mono text-gray-600">
                  {formatDuration(metrics.loadTime)}
                </span>
              </div>

              {/* Network Status */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Wifi className="w-4 h-4 text-cyan-500" />
                  <span className="text-sm font-medium">Network</span>
                </div>
                <span className="text-sm font-mono text-gray-600">
                  {navigator.onLine ? 'Online' : 'Offline'}
                </span>
              </div>

              {/* API Metrics */}
              {Object.keys(apiMetrics).length > 0 && (
                <div className="border-t pt-3 mt-3">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart3 className="w-4 h-4 text-indigo-500" />
                    <span className="text-sm font-medium">API Calls</span>
                  </div>
                  
                  <div className="space-y-1 max-h-20 overflow-y-auto">
                    {Object.entries(apiMetrics).map(([api, duration]) => (
                      <div key={api} className="flex items-center justify-between text-xs">
                        <span className="text-gray-600 truncate">{api}</span>
                        <span className="font-mono text-gray-500 ml-2">
                          {formatDuration(duration)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Performance Tips */}
              {performanceStatus.status !== 'good' && (
                <div className="border-t pt-3 mt-3">
                  <div className="text-xs text-gray-600">
                    <div className="font-medium mb-1">Performance Tips:</div>
                    <ul className="space-y-1">
                      {frameRate && frameRate < 30 && (
                        <li>• Reduce animations or effects</li>
                      )}
                      {memoryUsage && memoryUsage.used > 100 * 1024 * 1024 && (
                        <li>• Clear unused data/cache</li>
                      )}
                      {renderTime > 100 && (
                        <li>• Optimize component rendering</li>
                      )}
                    </ul>
                  </div>
                </div>
              )}
            </div>

            {/* Controls */}
            <div className="flex items-center justify-between mt-4 pt-3 border-t">
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${isMonitoring ? 'bg-green-500' : 'bg-gray-400'}`} />
                <span className="text-xs text-gray-600">
                  {isMonitoring ? 'Monitoring' : 'Stopped'}
                </span>
              </div>
              
              <button
                onClick={() => setIsExpanded(false)}
                className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
              >
                Minimize
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PerformanceMonitor;
