import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Loader2 } from 'lucide-react';
import { useAdvancedSearch } from '../../hooks/useAdvancedSearch';
import SearchSuggestions from './SearchSuggestions';
import SearchResults from './SearchResults';

interface AdvancedSearchBarProps {
  onClose: () => void;
  placeholder?: string;
  autoFocus?: boolean;
  showHistory?: boolean;
}

const AdvancedSearchBar: React.FC<AdvancedSearchBarProps> = ({
  onClose,
  placeholder = "جستجوی محصولات...",
  autoFocus = true,
  showHistory = true
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const {
    query,
    results,
    suggestions,
    popularTerms,
    searchHistory,
    isLoading,
    hasResults,
    hasSuggestions,
    showNoResults,
    showSuggestions,
    activeIndex,
    setQuery,
    selectSuggestion,
    selectProduct,
    clearSearch,
    navigateToResults,
    handleKeyDown
  } = useAdvancedSearch({
    enableHistory: showHistory,
    enableAnalytics: true
  });

  // Auto focus input
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDownEvent = (event: KeyboardEvent) => {
      handleKeyDown(event);
    };

    document.addEventListener('keydown', handleKeyDownEvent);
    return () => document.removeEventListener('keydown', handleKeyDownEvent);
  }, [handleKeyDown]);

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleClear = () => {
    clearSearch();
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      navigateToResults();
      onClose();
    }
  };

  const handleSelectSuggestion = (suggestion: string) => {
    selectSuggestion(suggestion);
  };

  const handleSelectProduct = (product: any) => {
    selectProduct(product);
    onClose();
  };

  const handleViewAllResults = () => {
    navigateToResults();
    onClose();
  };

  const showDropdown = showSuggestions || hasResults || showNoResults;

  return (
    <div ref={containerRef} className="relative w-full max-w-2xl mx-auto">
      {/* Search Input */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex items-center bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="flex items-center px-4">
            <Search className="w-5 h-5 text-text-muted" />
          </div>
          
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            placeholder={placeholder}
            className="flex-1 py-4 px-2 bg-transparent focus:outline-none text-text-primary placeholder-text-muted"
            dir="rtl"
          />
          
          <div className="flex items-center px-2">
            {isLoading && (
              <Loader2 className="w-4 h-4 text-primary-500 animate-spin mr-2" />
            )}
            
            {query && (
              <button
                type="button"
                onClick={handleClear}
                className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="پاک کردن جستجو"
              >
                <X className="w-4 h-4 text-text-muted" />
              </button>
            )}
            
            <button
              type="button"
              onClick={onClose}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors mr-1"
              aria-label="بستن جستجو"
            >
              <X className="w-5 h-5 text-text-secondary" />
            </button>
          </div>
        </div>
      </form>

      {/* Search Dropdown */}
      <AnimatePresence>
        {showDropdown && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-xl border border-gray-200 overflow-hidden z-50 max-h-96 overflow-y-auto custom-scroll"
          >
            {/* Suggestions */}
            {(showSuggestions || (!query && (searchHistory.length > 0 || popularTerms.length > 0))) && (
              <SearchSuggestions
                suggestions={suggestions}
                popularTerms={popularTerms}
                searchHistory={searchHistory}
                query={query}
                activeIndex={activeIndex}
                onSelectSuggestion={handleSelectSuggestion}
                showHistory={showHistory}
              />
            )}

            {/* Results */}
            {hasResults && (
              <SearchResults
                results={results}
                query={query}
                activeIndex={activeIndex}
                suggestionsCount={suggestions.length}
                onSelectProduct={handleSelectProduct}
                onViewAllResults={handleViewAllResults}
                maxResults={5}
              />
            )}

            {/* No Results */}
            {showNoResults && (
              <SearchResults
                results={[]}
                query={query}
                activeIndex={activeIndex}
                suggestionsCount={suggestions.length}
                onSelectProduct={handleSelectProduct}
                onViewAllResults={handleViewAllResults}
              />
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AdvancedSearchBar;
