import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Share2, 
  Co<PERSON>, 
  CheckCircle, 
  MessageCircle,
  Send,
  Link as LinkIcon,
  X
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { pwaManager } from '../../utils/pwaUtils';

interface SocialShareProps {
  url?: string;
  title?: string;
  description?: string;
  image?: string;
  hashtags?: string[];
  className?: string;
  variant?: 'button' | 'dropdown' | 'modal';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

interface SocialPlatform {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  shareUrl: (data: ShareData) => string;
  persianName: string;
}

interface ShareData {
  url: string;
  title: string;
  description: string;
  hashtags: string;
}

const SocialShare: React.FC<SocialShareProps> = ({
  url = window.location.href,
  title = document.title,
  description = '',
  image = '',
  hashtags = [],
  className = '',
  variant = 'button',
  size = 'md',
  showLabel = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  // Social media platforms
  const platforms: SocialPlatform[] = [
    {
      id: 'telegram',
      name: 'Telegram',
      persianName: 'تلگرام',
      icon: <Send className="w-5 h-5" />,
      color: 'bg-blue-500',
      shareUrl: (data) => `https://t.me/share/url?url=${encodeURIComponent(data.url)}&text=${encodeURIComponent(data.title)}`
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      persianName: 'واتساپ',
      icon: <MessageCircle className="w-5 h-5" />,
      color: 'bg-green-500',
      shareUrl: (data) => `https://wa.me/?text=${encodeURIComponent(`${data.title}\n${data.url}`)}`
    },
    {
      id: 'twitter',
      name: 'Twitter',
      persianName: 'توییتر',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
        </svg>
      ),
      color: 'bg-blue-400',
      shareUrl: (data) => `https://twitter.com/intent/tweet?text=${encodeURIComponent(data.title)}&url=${encodeURIComponent(data.url)}&hashtags=${data.hashtags}`
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      persianName: 'لینکدین',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      ),
      color: 'bg-blue-600',
      shareUrl: (data) => `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(data.url)}`
    },
    {
      id: 'facebook',
      name: 'Facebook',
      persianName: 'فیسبوک',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      ),
      color: 'bg-blue-600',
      shareUrl: (data) => `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(data.url)}`
    }
  ];

  const shareData: ShareData = {
    url,
    title,
    description,
    hashtags: hashtags.join(',')
  };

  // Handle native share
  const handleNativeShare = async () => {
    const success = await pwaManager.shareContent({
      title,
      text: description,
      url
    });

    if (success) {
      toast.success('با موفقیت اشتراک‌گذاری شد');
    }
  };

  // Handle platform share
  const handlePlatformShare = (platform: SocialPlatform) => {
    const shareUrl = platform.shareUrl(shareData);
    window.open(shareUrl, '_blank', 'width=600,height=400');
    setIsOpen(false);
  };

  // Handle copy link
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success('لینک کپی شد');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('خطا در کپی کردن لینک');
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg'
  };

  // Button variant
  if (variant === 'button') {
    return (
      <button
        onClick={handleNativeShare}
        className={`
          inline-flex items-center gap-2 px-4 py-2 bg-primary-500 text-white 
          rounded-lg hover:bg-primary-600 transition-colors ${className}
        `}
      >
        <Share2 className="w-4 h-4" />
        {showLabel && 'اشتراک‌گذاری'}
      </button>
    );
  }

  // Dropdown variant
  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`
            ${sizeClasses[size]} flex items-center justify-center
            bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors
          `}
        >
          <Share2 className="w-4 h-4" />
        </button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              className="
                absolute top-full left-0 mt-2 bg-white rounded-lg shadow-lg border
                p-3 min-w-[200px] z-50
              "
            >
              <div className="space-y-2">
                {platforms.map((platform) => (
                  <button
                    key={platform.id}
                    onClick={() => handlePlatformShare(platform)}
                    className="
                      w-full flex items-center gap-3 px-3 py-2 text-right
                      hover:bg-gray-50 rounded-lg transition-colors
                    "
                  >
                    <div className={`${platform.color} text-white p-1.5 rounded`}>
                      {platform.icon}
                    </div>
                    <span className="text-gray-700">{platform.persianName}</span>
                  </button>
                ))}
                
                <hr className="my-2" />
                
                <button
                  onClick={handleCopyLink}
                  className="
                    w-full flex items-center gap-3 px-3 py-2 text-right
                    hover:bg-gray-50 rounded-lg transition-colors
                  "
                >
                  <div className="bg-gray-500 text-white p-1.5 rounded">
                    {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  </div>
                  <span className="text-gray-700">
                    {copied ? 'کپی شد!' : 'کپی لینک'}
                  </span>
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Modal variant
  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className={`
          ${sizeClasses[size]} flex items-center justify-center
          bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors
          ${className}
        `}
      >
        <Share2 className="w-4 h-4" />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-xl shadow-xl max-w-md w-full p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  اشتراک‌گذاری
                </h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-3">
                {platforms.map((platform) => (
                  <button
                    key={platform.id}
                    onClick={() => handlePlatformShare(platform)}
                    className="
                      w-full flex items-center gap-4 px-4 py-3 text-right
                      hover:bg-gray-50 rounded-lg transition-colors border
                    "
                  >
                    <div className={`${platform.color} text-white p-2 rounded-lg`}>
                      {platform.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {platform.persianName}
                      </div>
                      <div className="text-sm text-gray-500">
                        اشتراک‌گذاری در {platform.persianName}
                      </div>
                    </div>
                  </button>
                ))}
                
                <button
                  onClick={handleCopyLink}
                  className="
                    w-full flex items-center gap-4 px-4 py-3 text-right
                    hover:bg-gray-50 rounded-lg transition-colors border
                  "
                >
                  <div className="bg-gray-500 text-white p-2 rounded-lg">
                    {copied ? <CheckCircle className="w-5 h-5" /> : <LinkIcon className="w-5 h-5" />}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {copied ? 'کپی شد!' : 'کپی لینک'}
                    </div>
                    <div className="text-sm text-gray-500">
                      کپی کردن لینک در کلیپ‌بورد
                    </div>
                  </div>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default SocialShare;
