import React, { useEffect, useRef } from 'react';
import { useAnalytics } from '../../hooks/useAnalytics';
import { debounceAnalyticsEvent, throttleAnalyticsEvent } from '../../utils/analyticsUtils';

interface EventTrackingProps {
  children: React.ReactNode;
  enableClickTracking?: boolean;
  enableScrollTracking?: boolean;
  enableFormTracking?: boolean;
  enableVideoTracking?: boolean;
  enableFileDownloadTracking?: boolean;
  enableOutboundLinkTracking?: boolean;
  enableErrorTracking?: boolean;
  className?: string;
}

const EventTracking: React.FC<EventTrackingProps> = ({
  children,
  enableClickTracking = true,
  enableScrollTracking = true,
  enableFormTracking = true,
  enableVideoTracking = true,
  enableFileDownloadTracking = true,
  enableOutboundLinkTracking = true,
  enableErrorTracking = true,
  className = ''
}) => {
  const { trackEvent, trackCustomEvent } = useAnalytics();
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollDepthRef = useRef<number>(0);
  const formInteractionsRef = useRef<Set<string>>(new Set());

  // Click tracking
  useEffect(() => {
    if (!enableClickTracking) return;

    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target) return;

      // Track button clicks
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        const button = target.tagName === 'BUTTON' ? target : target.closest('button');
        const buttonText = button?.textContent?.trim() || 'Unknown Button';
        const buttonType = button?.getAttribute('type') || 'button';
        
        trackEvent('click', {
          element_type: 'button',
          element_text: buttonText,
          button_type: buttonType,
          page_location: window.location.href
        });
      }

      // Track link clicks
      if (target.tagName === 'A' || target.closest('a')) {
        const link = target.tagName === 'A' ? target as HTMLAnchorElement : target.closest('a');
        const href = link?.href;
        const linkText = link?.textContent?.trim() || 'Unknown Link';
        
        if (href) {
          // Check if it's an outbound link
          const isOutbound = enableOutboundLinkTracking && 
            href.startsWith('http') && 
            !href.includes(window.location.hostname);
          
          // Check if it's a file download
          const isDownload = enableFileDownloadTracking && 
            /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|mp3|mp4|avi|mov)$/i.test(href);
          
          if (isOutbound) {
            trackEvent('click', {
              element_type: 'outbound_link',
              link_url: href,
              link_text: linkText,
              link_domain: new URL(href).hostname
            });
          } else if (isDownload) {
            const fileName = href.split('/').pop() || 'unknown_file';
            const fileExtension = fileName.split('.').pop() || 'unknown';
            
            trackEvent('file_download', {
              file_name: fileName,
              file_extension: fileExtension,
              link_url: href,
              link_text: linkText
            });
          } else {
            trackEvent('click', {
              element_type: 'internal_link',
              link_url: href,
              link_text: linkText
            });
          }
        }
      }

      // Track image clicks
      if (target.tagName === 'IMG') {
        const img = target as HTMLImageElement;
        trackEvent('click', {
          element_type: 'image',
          image_src: img.src,
          image_alt: img.alt || 'No alt text'
        });
      }

      // Track custom data-track attributes
      const trackData = target.getAttribute('data-track') || target.closest('[data-track]')?.getAttribute('data-track');
      if (trackData) {
        try {
          const trackInfo = JSON.parse(trackData);
          trackCustomEvent({
            event_name: trackInfo.event || 'custom_click',
            event_category: trackInfo.category || 'interaction',
            event_label: trackInfo.label,
            value: trackInfo.value,
            custom_parameters: trackInfo.parameters
          });
        } catch (error) {
          // Fallback for simple string tracking
          trackEvent('click', {
            element_type: 'custom',
            custom_action: trackData
          });
        }
      }
    };

    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, [enableClickTracking, enableOutboundLinkTracking, enableFileDownloadTracking, trackEvent, trackCustomEvent]);

  // Scroll tracking
  useEffect(() => {
    if (!enableScrollTracking) return;

    const handleScroll = throttleAnalyticsEvent(() => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      // Track scroll milestones (25%, 50%, 75%, 100%)
      const milestones = [25, 50, 75, 100];
      const currentMilestone = milestones.find(milestone => 
        scrollPercent >= milestone && scrollDepthRef.current < milestone
      );
      
      if (currentMilestone) {
        scrollDepthRef.current = currentMilestone;
        trackEvent('scroll', {
          scroll_depth: currentMilestone,
          page_location: window.location.href,
          page_title: document.title
        });
      }
    }, 1000);

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [enableScrollTracking, trackEvent]);

  // Form tracking
  useEffect(() => {
    if (!enableFormTracking) return;

    const handleFormStart = (event: Event) => {
      const form = event.target as HTMLFormElement;
      const formId = form.id || form.className || 'unknown_form';
      
      if (!formInteractionsRef.current.has(formId)) {
        formInteractionsRef.current.add(formId);
        trackEvent('form_start', {
          form_id: formId,
          form_name: form.getAttribute('name') || formId
        });
      }
    };

    const handleFormSubmit = (event: Event) => {
      const form = event.target as HTMLFormElement;
      const formId = form.id || form.className || 'unknown_form';
      
      trackEvent('form_submit', {
        form_id: formId,
        form_name: form.getAttribute('name') || formId,
        form_method: form.method || 'get'
      });
    };

    const handleInputFocus = (event: Event) => {
      const input = event.target as HTMLInputElement;
      const form = input.closest('form');
      
      if (form) {
        handleFormStart({ target: form } as Event);
      }
    };

    // Add event listeners
    document.addEventListener('submit', handleFormSubmit);
    document.addEventListener('focusin', handleInputFocus);

    return () => {
      document.removeEventListener('submit', handleFormSubmit);
      document.removeEventListener('focusin', handleInputFocus);
    };
  }, [enableFormTracking, trackEvent]);

  // Video tracking
  useEffect(() => {
    if (!enableVideoTracking) return;

    const handleVideoEvent = (event: Event) => {
      const video = event.target as HTMLVideoElement;
      const videoSrc = video.src || video.currentSrc || 'unknown_video';
      const videoDuration = video.duration || 0;
      const currentTime = video.currentTime || 0;
      const videoTitle = video.getAttribute('title') || video.getAttribute('data-title') || 'Unknown Video';

      switch (event.type) {
        case 'play':
          trackEvent('video_start', {
            video_title: videoTitle,
            video_url: videoSrc,
            video_duration: Math.round(videoDuration)
          });
          break;
          
        case 'pause':
          trackEvent('video_pause', {
            video_title: videoTitle,
            video_url: videoSrc,
            video_current_time: Math.round(currentTime),
            video_percent: Math.round((currentTime / videoDuration) * 100)
          });
          break;
          
        case 'ended':
          trackEvent('video_complete', {
            video_title: videoTitle,
            video_url: videoSrc,
            video_duration: Math.round(videoDuration)
          });
          break;
          
        case 'timeupdate':
          // Track video progress at 25%, 50%, 75%
          const percent = Math.round((currentTime / videoDuration) * 100);
          const progressMilestones = [25, 50, 75];
          const milestone = progressMilestones.find(m => 
            percent >= m && !video.dataset[`tracked${m}`]
          );
          
          if (milestone) {
            video.dataset[`tracked${milestone}`] = 'true';
            trackEvent('video_progress', {
              video_title: videoTitle,
              video_url: videoSrc,
              video_percent: milestone,
              video_current_time: Math.round(currentTime)
            });
          }
          break;
      }
    };

    // Add event listeners to all videos
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      video.addEventListener('play', handleVideoEvent);
      video.addEventListener('pause', handleVideoEvent);
      video.addEventListener('ended', handleVideoEvent);
      video.addEventListener('timeupdate', debounceAnalyticsEvent(handleVideoEvent, 1000));
    });

    // Observer for dynamically added videos
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            const videos = element.tagName === 'VIDEO' ? [element] : element.querySelectorAll('video');
            
            videos.forEach((video: Element) => {
              const videoElement = video as HTMLVideoElement;
              videoElement.addEventListener('play', handleVideoEvent);
              videoElement.addEventListener('pause', handleVideoEvent);
              videoElement.addEventListener('ended', handleVideoEvent);
              videoElement.addEventListener('timeupdate', debounceAnalyticsEvent(handleVideoEvent, 1000));
            });
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      videos.forEach(video => {
        video.removeEventListener('play', handleVideoEvent);
        video.removeEventListener('pause', handleVideoEvent);
        video.removeEventListener('ended', handleVideoEvent);
        video.removeEventListener('timeupdate', handleVideoEvent);
      });
      observer.disconnect();
    };
  }, [enableVideoTracking, trackEvent]);

  // Error tracking
  useEffect(() => {
    if (!enableErrorTracking) return;

    const handleError = (event: ErrorEvent) => {
      trackEvent('exception', {
        description: event.error?.message || event.message || 'Unknown error',
        fatal: false,
        file_name: event.filename,
        line_number: event.lineno,
        column_number: event.colno,
        stack_trace: event.error?.stack
      });
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      trackEvent('exception', {
        description: `Unhandled Promise Rejection: ${event.reason}`,
        fatal: false,
        error_type: 'promise_rejection'
      });
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [enableErrorTracking, trackEvent]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
};

export default EventTracking;
