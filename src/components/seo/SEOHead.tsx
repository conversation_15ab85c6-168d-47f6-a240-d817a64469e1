import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'product' | 'article';
  price?: number;
  currency?: string;
  availability?: 'in_stock' | 'out_of_stock' | 'limited_stock';
  brand?: string;
  category?: string;
  noIndex?: boolean;
  canonicalUrl?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  price,
  currency = 'IRR',
  availability = 'in_stock',
  brand,
  category,
  noIndex = false,
  canonicalUrl
}) => {
  // Default values
  const defaultTitle = 'آرامش پوست | محصولات مراقبت از پوست و زیبایی';
  const defaultDescription = 'فروشگاه آنلاین محصولات مراقبت از پوست و زیبایی با بهترین برندهای ایرانی و خارجی. سرم، کرم، ماسک و محصولات طبیعی برای پوست سالم و درخشان.';
  const defaultImage = '/images/og-image.jpg';
  const siteUrl = window.location.origin;
  const currentUrl = url || window.location.href;

  // Construct full title
  const fullTitle = title ? `${title} | آرامش پوست` : defaultTitle;
  
  // Construct meta description
  const metaDescription = description || defaultDescription;
  
  // Construct keywords
  const defaultKeywords = [
    'محصولات مراقبت از پوست',
    'کرم صورت',
    'سرم پوست',
    'ماسک صورت',
    'آرایشی بهداشتی',
    'زیبایی',
    'پوست سالم',
    'مراقبت از پوست',
    'کرم ضد پیری',
    'ضد آفتاب',
    'فروشگاه آنلاین',
    'ایران'
  ];
  
  const allKeywords = [...defaultKeywords, ...keywords].join(', ');

  // Structured data for products
  const productStructuredData = type === 'product' && price ? {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": title,
    "description": metaDescription,
    "image": image || defaultImage,
    "brand": {
      "@type": "Brand",
      "name": brand || "آرامش پوست"
    },
    "category": category,
    "offers": {
      "@type": "Offer",
      "price": price,
      "priceCurrency": currency,
      "availability": `https://schema.org/${availability === 'in_stock' ? 'InStock' : availability === 'out_of_stock' ? 'OutOfStock' : 'LimitedAvailability'}`,
      "seller": {
        "@type": "Organization",
        "name": "آرامش پوست"
      }
    }
  } : null;

  // Website structured data
  const websiteStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "آرامش پوست",
    "alternateName": "GlowRoya Skincare",
    "url": siteUrl,
    "description": defaultDescription,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${siteUrl}/products?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={allKeywords} />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Language and Direction */}
      <html lang="fa" dir="rtl" />
      
      {/* Open Graph Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={image || defaultImage} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:site_name" content="آرامش پوست" />
      <meta property="og:locale" content="fa_IR" />
      
      {/* Product specific Open Graph */}
      {type === 'product' && price && (
        <>
          <meta property="product:price:amount" content={price.toString()} />
          <meta property="product:price:currency" content={currency} />
          <meta property="product:availability" content={availability} />
          {brand && <meta property="product:brand" content={brand} />}
          {category && <meta property="product:category" content={category} />}
        </>
      )}
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={image || defaultImage} />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="آرامش پوست" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#8B5CF6" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(websiteStructuredData)}
      </script>
      
      {productStructuredData && (
        <script type="application/ld+json">
          {JSON.stringify(productStructuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOHead;
