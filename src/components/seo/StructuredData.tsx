import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Product } from '../../types';

interface StructuredDataProps {
  type: 'organization' | 'breadcrumb' | 'product' | 'review' | 'faq';
  data: any;
}

const StructuredData: React.FC<StructuredDataProps> = ({ type, data }) => {
  const generateStructuredData = () => {
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "آرامش پوست",
          "alternateName": "GlowRoya Skincare",
          "url": "https://glowroya.com",
          "logo": "https://glowroya.com/logo.png",
          "description": "فروشگاه آنلاین محصولات مراقبت از پوست و زیبایی",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": data.address || "تهران، ایران",
            "addressLocality": "تهران",
            "addressCountry": "IR"
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": data.phone || "+98-21-12345678",
            "contactType": "customer service",
            "availableLanguage": "Persian"
          },
          "sameAs": [
            "https://instagram.com/glowroya",
            "https://telegram.me/glowroya"
          ]
        };

      case 'breadcrumb':
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": data.items.map((item: any, index: number) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": item.url
          }))
        };

      case 'product':
        const product: Product = data.product;
        return {
          "@context": "https://schema.org/",
          "@type": "Product",
          "name": product.name,
          "description": product.description,
          "image": product.images || [product.imageSrc],
          "brand": {
            "@type": "Brand",
            "name": product.brand || "آرامش پوست"
          },
          "category": product.category,
          "sku": `SKU-${product.id}`,
          "gtin": `GTIN-${product.id}`,
          "offers": {
            "@type": "Offer",
            "price": product.discountedPrice || product.price,
            "priceCurrency": "IRR",
            "availability": product.stock > 0 ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
            "seller": {
              "@type": "Organization",
              "name": "آرامش پوست"
            },
            "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": product.rating,
            "reviewCount": product.reviewCount,
            "bestRating": 5,
            "worstRating": 1
          },
          "review": data.reviews?.map((review: any) => ({
            "@type": "Review",
            "author": {
              "@type": "Person",
              "name": review.author
            },
            "reviewRating": {
              "@type": "Rating",
              "ratingValue": review.rating,
              "bestRating": 5,
              "worstRating": 1
            },
            "reviewBody": review.text,
            "datePublished": review.date
          })) || []
        };

      case 'review':
        return {
          "@context": "https://schema.org",
          "@type": "Review",
          "itemReviewed": {
            "@type": "Product",
            "name": data.productName
          },
          "author": {
            "@type": "Person",
            "name": data.authorName
          },
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": data.rating,
            "bestRating": 5,
            "worstRating": 1
          },
          "reviewBody": data.reviewText,
          "datePublished": data.date
        };

      case 'faq':
        return {
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": data.faqs.map((faq: any) => ({
            "@type": "Question",
            "name": faq.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": faq.answer
            }
          }))
        };

      default:
        return null;
    }
  };

  const structuredData = generateStructuredData();

  if (!structuredData) {
    return null;
  }

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

export default StructuredData;
