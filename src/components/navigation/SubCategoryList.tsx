import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { SubCategoryListProps } from '../../types/navigation';

const SubCategoryList: React.FC<SubCategoryListProps> = ({ 
  subcategories, 
  categorySlug 
}) => {
  return (
    <div className="grid grid-cols-1 gap-1">
      {subcategories.map((subcategory, index) => (
        <motion.div
          key={subcategory.id}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ 
            duration: 0.2, 
            delay: index * 0.05,
            ease: 'easeOut'
          }}
        >
          <Link
            to={`/products?category=${categorySlug}&subcategory=${subcategory.slug}`}
            className="block py-2 px-3 text-sm text-text-secondary hover:text-primary-600 hover:bg-primary-50 rounded-md transition-all duration-200 group"
          >
            <div className="flex items-center justify-between">
              <span className="group-hover:translate-x-1 transition-transform duration-200">
                {subcategory.name}
              </span>
              <svg 
                className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M15 19l-7-7 7-7" 
                />
              </svg>
            </div>
            {subcategory.description && (
              <p className="text-xs text-text-muted mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                {subcategory.description}
              </p>
            )}
          </Link>
        </motion.div>
      ))}
    </div>
  );
};

export default SubCategoryList;
