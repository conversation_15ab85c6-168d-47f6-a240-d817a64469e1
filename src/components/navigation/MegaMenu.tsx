import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { MegaMenuProps } from '../../types/navigation';
import CategoryDropdown from './CategoryDropdown';

const MegaMenu: React.FC<MegaMenuProps> = ({ categories, isOpen, onClose }) => {
  const [activeCategory, setActiveCategory] = useState<number | null>(null);

  const handleCategoryHover = (categoryId: number) => {
    setActiveCategory(categoryId);
  };

  const handleCategoryLeave = () => {
    setActiveCategory(null);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2, ease: 'easeOut' }}
        className="absolute top-full left-0 right-0 bg-white shadow-xl border-t border-gray-100 z-40"
        onMouseLeave={onClose}
      >
        <div className="container-custom py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Categories Grid */}
            <div className="lg:col-span-3">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {categories.map((category) => (
                  <div key={category.id} className="relative">
                    <CategoryDropdown
                      category={category}
                      isOpen={activeCategory === category.id}
                      onMouseEnter={() => handleCategoryHover(category.id)}
                      onMouseLeave={handleCategoryLeave}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Featured Section */}
            <div className="lg:col-span-1">
              <div className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-xl p-6 h-full">
                <h3 className="text-lg font-bold text-text-primary mb-4">
                  پیشنهاد ویژه
                </h3>
                
                {/* Featured Product */}
                {categories[0]?.featured && (
                  <Link
                    to={categories[0].featured.link}
                    className="block group"
                  >
                    <div className="relative overflow-hidden rounded-lg mb-4">
                      <img
                        src={categories[0].featured.imageSrc}
                        alt={categories[0].featured.title}
                        className="w-full h-32 object-cover transition-transform duration-300 group-hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <h4 className="font-semibold text-text-primary mb-2 group-hover:text-primary-600 transition-colors">
                      {categories[0].featured.title}
                    </h4>
                    <p className="text-sm text-text-secondary mb-4">
                      {categories[0].featured.description}
                    </p>
                    <div className="flex items-center text-primary-600 text-sm font-medium group-hover:text-primary-700 transition-colors">
                      مشاهده محصولات
                      <svg className="w-4 h-4 mr-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </div>
                  </Link>
                )}

                {/* Quick Links */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h4 className="font-medium text-text-primary mb-3">دسترسی سریع</h4>
                  <div className="space-y-2">
                    <Link
                      to="/products?featured=new"
                      className="block text-sm text-text-secondary hover:text-primary-600 transition-colors"
                    >
                      جدیدترین محصولات
                    </Link>
                    <Link
                      to="/products?featured=bestseller"
                      className="block text-sm text-text-secondary hover:text-primary-600 transition-colors"
                    >
                      پرفروش‌ترین‌ها
                    </Link>
                    <Link
                      to="/products?discount=true"
                      className="block text-sm text-text-secondary hover:text-primary-600 transition-colors"
                    >
                      تخفیف‌دار
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default MegaMenu;
