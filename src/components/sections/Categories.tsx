import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { categories } from '../../data/categories';

const Categories: React.FC = () => {
  return (
    <section className="py-16 bg-gradient-to-b from-white to-primary-50">
      <div className="container-custom">
        <h2 className="section-title text-center mx-auto mb-12">دسته‌بندی محصولات</h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.5, 
                delay: index * 0.1,
                ease: 'easeOut'
              }}
              viewport={{ once: true }}
            >
              <Link 
                to={`/products?category=${category.slug}`}
                className="group relative block overflow-hidden rounded-2xl shadow-md hover:shadow-lg transition-all duration-300"
              >
                <div className="relative aspect-[4/3]">
                  <img
                    src={category.imageSrc}
                    alt={category.name}
                    className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-70 group-hover:opacity-90 transition-opacity duration-300"></div>
                  
                  <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                    <h3 className="text-xl font-bold mb-2 group-hover:text-primary-200 transition-colors">{category.name}</h3>
                    <p className="text-sm text-white/80 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">{category.description}</p>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Categories;