import React from 'react';
import { motion } from 'framer-motion';
import { Spark<PERSON>, Leaf, Shield, Truck, RefreshCcw, Heart } from 'lucide-react';

interface FeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay: number;
}

const Feature: React.FC<FeatureProps> = ({ icon, title, description, delay }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="bg-white rounded-2xl shadow-soft p-6 transition-all duration-300 hover:shadow-medium hover:-translate-y-1"
    >
      <div className="w-12 h-12 bg-primary-50 rounded-xl flex items-center justify-center mb-4 text-primary-500">
        {icon}
      </div>
      <h3 className="text-lg font-semibold mb-2 text-text-primary">{title}</h3>
      <p className="text-text-secondary">{description}</p>
    </motion.div>
  );
};

const Features: React.FC = () => {
  const features = [
    {
      icon: <Sparkles className="h-6 w-6" />,
      title: 'کیفیت برتر',
      description: 'محصولات با کیفیت بالا و مواد اولیه طبیعی برای نتایج قابل مشاهده',
    },
    {
      icon: <Leaf className="h-6 w-6" />,
      title: 'مواد طبیعی',
      description: 'استفاده از مواد طبیعی و ارگانیک بدون مواد شیمیایی مضر',
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: 'تضمین اصالت',
      description: 'تضمین اصالت و کیفیت تمامی محصولات ارائه شده در فروشگاه',
    },
    {
      icon: <Truck className="h-6 w-6" />,
      title: 'ارسال سریع',
      description: 'ارسال سریع و مطمئن به سراسر کشور با بسته‌بندی ایمن',
    },
    {
      icon: <RefreshCcw className="h-6 w-6" />,
      title: 'تعویض آسان',
      description: 'امکان تعویض و بازگشت محصول تا ۷ روز در صورت عدم رضایت',
    },
    {
      icon: <Heart className="h-6 w-6" />,
      title: 'مشاوره تخصصی',
      description: 'ارائه مشاوره تخصصی رایگان برای انتخاب محصول مناسب پوست شما',
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-12"
        >
          <h2 className="section-title inline-block mb-6">چرا گلورویا؟</h2>
          <p className="text-text-secondary">ما به بهترین‌ها برای پوست شما اهمیت می‌دهیم</p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Feature
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              delay={index * 0.1}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;