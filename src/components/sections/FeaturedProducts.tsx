import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import { ArrowLeft } from 'lucide-react';
import ProductCard from '../common/ProductCard';
import { Link } from 'react-router-dom';
import { getBestSellers } from '../../data/products';

const FeaturedProducts: React.FC = () => {
  const bestSellers = getBestSellers();
  
  return (
    <section className="py-16 bg-white">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h2 className="section-title">محصولات پرفروش</h2>
          <Link to="/products" className="text-primary-500 hover:text-primary-600 flex items-center gap-1">
            <span>مشاهده همه</span>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </div>

        <div className="relative">
          <Swiper
            modules={[Pagination, Autoplay]}
            spaceBetween={24}
            slidesPerView={1}
            pagination={{ clickable: true }}
            autoplay={{
              delay: 5000,
              disableOnInteraction: false,
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
              },
              1024: {
                slidesPerView: 3,
              },
              1280: {
                slidesPerView: 4,
              },
            }}
            className="pb-12"
          >
            {bestSellers.map(product => (
              <SwiperSlide key={product.id}>
                <ProductCard product={product} />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;