import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Minus, ShoppingBag, Building2, Spa<PERSON><PERSON>, Star } from 'lucide-react';
import { useCart } from '../../context/CartContext';
import { useAdvancedCart } from '../../hooks/useAdvancedCart';
import { Link, useNavigate } from 'react-router-dom';
import { getBrandInfo } from '../../utils/brandUtils';
import { formatPrice, formatNumber } from '../../utils/formatters';
import AdvancedCartDrawer from '../cart/AdvancedCartDrawer';

const Cart: React.FC = () => {
  const navigate = useNavigate();
  const [useAdvanced, setUseAdvanced] = useState(true);

  // Use basic cart for compatibility
  const {
    items,
    totalItems,
    totalPrice,
    isOpen,
    toggleCart,
    updateQuantity,
    removeItem
  } = useCart();

  // Advanced cart features
  const advancedCart = useAdvancedCart();

  const handleCheckout = () => {
    toggleCart();
    navigate('/checkout');
  };

  // Use advanced cart drawer if enabled and has items
  if (useAdvanced && items.length > 0) {
    return (
      <AdvancedCartDrawer
        isOpen={isOpen}
        onClose={toggleCart}
        onCheckout={handleCheckout}
      />
    );
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={toggleCart}
          />
          
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 20 }}
            className="fixed top-0 left-0 h-full w-full max-w-md bg-white shadow-xl z-50 overflow-hidden flex flex-col"
          >
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-xl font-semibold text-text-primary flex items-center">
                <ShoppingBag className="ml-2 h-5 w-5" />
                سبد خرید
                {totalItems > 0 && (
                  <span className="bg-primary-100 text-primary-600 text-sm rounded-full px-2 py-0.5 mr-2">
                    {totalItems}
                  </span>
                )}
              </h3>
              <button 
                onClick={toggleCart}
                className="text-text-secondary hover:text-text-primary p-1 rounded-full hover:bg-gray-100"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <div className="flex-grow overflow-y-auto custom-scroll p-4">
              {items.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <ShoppingBag className="h-16 w-16 text-gray-300 mb-4" />
                  <p className="text-text-secondary mb-4">سبد خرید شما خالی است!</p>
                  <button 
                    onClick={toggleCart}
                    className="btn-primary"
                  >
                    ادامه خرید
                  </button>
                </div>
              ) : (
                <ul className="space-y-4">
                  {items.map((item) => (
                    <motion.li 
                      key={item.product.id}
                      layout
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, height: 0 }}
                      className="flex border-b border-gray-100 pb-4"
                    >
                      <Link 
                        to={`/product/${item.product.id}`}
                        className="w-20 h-20 rounded-lg overflow-hidden bg-gray-50 flex-shrink-0"
                        onClick={toggleCart}
                      >
                        <img 
                          src={item.product.imageSrc} 
                          alt={item.product.name} 
                          className="w-full h-full object-cover"
                        />
                      </Link>
                      
                      <div className="flex-grow mr-3">
                        <div className="flex justify-between">
                          <Link 
                            to={`/product/${item.product.id}`}
                            className="font-medium text-text-primary hover:text-primary-500"
                            onClick={toggleCart}
                          >
                            {item.product.name}
                          </Link>
                          <button 
                            onClick={() => removeItem(item.product.id)}
                            className="text-text-secondary hover:text-red-500 p-1"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                        
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-sm text-text-secondary">{item.product.category}</p>
                          {item.product.brand && (
                            <div className="flex items-center gap-1">
                              {(() => {
                                const brandInfo = getBrandInfo(item.product.brand);
                                return brandInfo.logo ? (
                                  <img
                                    src={brandInfo.logo}
                                    alt={`لوگو ${brandInfo.name}`}
                                    className="w-4 h-4 rounded object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.style.display = 'none';
                                      target.nextElementSibling?.classList.remove('hidden');
                                    }}
                                  />
                                ) : null;
                              })()}
                              <Building2 className={`w-3 h-3 text-gray-400 ${item.product.brand && getBrandInfo(item.product.brand).logo ? 'hidden' : ''}`} />
                              <span className="text-xs text-text-muted">{item.product.brand}</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div className="flex items-center border border-gray-200 rounded-full overflow-hidden">
                            <button 
                              onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                              className="p-1 hover:bg-gray-100 text-text-secondary"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                            <span className="w-8 text-center text-sm">{item.quantity}</span>
                            <button 
                              onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                              className="p-1 hover:bg-gray-100 text-text-secondary"
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </div>
                          
                          <span className="font-medium text-text-primary">
                            {((item.product.discountedPrice || item.product.price) * item.quantity).toLocaleString()} تومان
                          </span>
                        </div>
                      </div>
                    </motion.li>
                  ))}
                </ul>
              )}
            </div>
            
            {items.length > 0 && (
              <div className="border-t border-gray-100 p-4">
                <div className="flex justify-between mb-4">
                  <span className="text-text-secondary">جمع کل:</span>
                  <span className="font-bold text-text-primary">{totalPrice.toLocaleString()} تومان</span>
                </div>
                
                <Link 
                  to="/checkout"
                  className="btn-primary w-full"
                  onClick={toggleCart}
                >
                  تکمیل خرید
                </Link>
                
                <button 
                  onClick={toggleCart}
                  className="btn-secondary w-full mt-2"
                >
                  ادامه خرید
                </button>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default Cart;