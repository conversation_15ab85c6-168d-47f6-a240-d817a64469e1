import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Star, ShoppingBag, Building2, Heart, Eye } from 'lucide-react';
import { Product } from '../../types';
import { useCart } from '../../context/CartContext';
import { useWishlist } from '../../hooks/useWishlist';
import { useProductComparison } from '../../hooks/useProductComparison';
import { useAnalytics } from '../../hooks/useAnalytics';
import { getBrandInfo } from '../../utils/brandUtils';
import { OptimizedProductImage } from './ImageOptimizer';

interface ProductCardProps {
  product: Product;
  showWishlistButton?: boolean;
  showComparisonButton?: boolean;
  showQuickActions?: boolean;
  compact?: boolean;
  variant?: 'vertical' | 'horizontal';
  className?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  showWishlistButton = true,
  showComparisonButton = true,
  showQuickActions = true,
  compact = false,
  variant = 'vertical',
  className = ''
}) => {
  const { addItem } = useCart();
  const { toggleWishlist, isInWishlist } = useWishlist();
  const { addItem: addToComparison, isInComparison, canAddMore } = useProductComparison();
  const { trackProductView, trackAddToCart, trackEvent } = useAnalytics();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addItem(product, 1);
    trackAddToCart(product, 1);
  };

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleWishlist(product);
  };

  const handleAddToComparison = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isInComparison(product.id) && canAddMore) {
      addToComparison(product);
    }
  };

  return (
    <motion.div
      className={`card card-hover ${variant === 'horizontal' ? 'flex' : ''} ${className}`}
      whileHover={{ y: variant === 'vertical' ? -5 : 0 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <Link
        to={`/product/${product.id}`}
        className={`block ${variant === 'horizontal' ? 'flex w-full' : ''}`}
        onClick={() => trackProductView(product)}
      >
        <div className={`relative overflow-hidden ${variant === 'horizontal' ? 'w-32 h-32 flex-shrink-0' : ''}`}>
          <OptimizedProductImage
            src={product.imageSrc}
            alt={product.name}
            className={`w-full transition-transform duration-500 hover:scale-105 ${
              variant === 'horizontal' ? 'h-32' : 'h-60'
            }`}
            width={variant === 'horizontal' ? 128 : 400}
            height={variant === 'horizontal' ? 128 : 240}
            quality={85}
            lazy={true}
            responsive={variant === 'vertical'}
          />
          
          {product.isNew && (
            <div className="absolute top-3 right-3 bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
              جدید
            </div>
          )}
          
          {product.isBestSeller && (
            <div className="absolute top-3 right-3 bg-accent-500 text-white text-xs px-2 py-1 rounded-full">
              پرفروش
            </div>
          )}
          
          {product.discountedPrice && (
            <div className="absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {Math.round((1 - product.discountedPrice / product.price) * 100)}% تخفیف
            </div>
          )}

          {product.hasVariants && (
            <div className="absolute bottom-3 left-3 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
              {product.variants?.length === 1 && product.variants[0].type === 'color' ?
                `${product.variants[0].variants.length} رنگ` :
                product.variants?.length === 1 && product.variants[0].type === 'volume' ?
                `${product.variants[0].variants.length} حجم` :
                'چند گزینه'
              }
            </div>
          )}
          
          {/* Action Buttons */}
          {showQuickActions && (
            <div className={`absolute flex gap-2 ${
              variant === 'horizontal' ? 'top-2 right-2' : 'bottom-3 right-3'
            }`}>
              <button
                onClick={handleAddToCart}
                disabled={product.stock === 0}
                className="bg-white rounded-full p-2 shadow-md hover:bg-primary-500 hover:text-white transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation"
                aria-label="افزودن به سبد خرید"
              >
                <ShoppingBag className="h-4 w-4" />
              </button>

              {showWishlistButton && (
                <button
                  onClick={handleToggleWishlist}
                  className={`rounded-full p-2 shadow-md transition-colors duration-300 touch-manipulation ${
                    isInWishlist(product.id)
                      ? 'bg-red-500 text-white hover:bg-red-600'
                      : 'bg-white text-gray-600 hover:bg-red-500 hover:text-white'
                  }`}
                  aria-label={isInWishlist(product.id) ? 'حذف از علاقه‌مندی‌ها' : 'افزودن به علاقه‌مندی‌ها'}
                >
                  <Heart className={`h-4 w-4 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                </button>
              )}

              {showComparisonButton && (
                <button
                  onClick={handleAddToComparison}
                  disabled={isInComparison(product.id) || !canAddMore}
                  className={`rounded-full p-2 shadow-md transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation ${
                    isInComparison(product.id)
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-600 hover:bg-blue-500 hover:text-white'
                  }`}
                  aria-label={isInComparison(product.id) ? 'در مقایسه' : 'افزودن به مقایسه'}
                >
                  <Eye className="h-4 w-4" />
                </button>
              )}
            </div>
          )}
        </div>

        <div className={`${variant === 'horizontal' ? 'flex-1 p-3' : 'p-4'}`}>
          <div className="flex items-center mb-2">
            <div className="flex items-center text-accent-500">
              <Star className="h-4 w-4 fill-current" />
              <span className="mr-1 text-sm font-medium">{product.rating}</span>
            </div>
            <span className="mr-2 text-xs text-text-secondary">({product.reviewCount} نظر)</span>
          </div>

          <h3 className="font-semibold text-lg mb-1 text-text-primary">{product.name}</h3>

          <div className="flex items-center justify-between mb-2">
            <p className="text-sm text-text-secondary">{product.category}</p>
            {product.brand && (
              <div className="flex items-center gap-1">
                {(() => {
                  const brandInfo = getBrandInfo(product.brand);
                  return brandInfo.logo ? (
                    <img
                      src={brandInfo.logo}
                      alt={`لوگو ${brandInfo.name}`}
                      className="w-5 h-5 rounded object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                  ) : null;
                })()}
                <Building2 className={`w-4 h-4 text-gray-400 ${product.brand && getBrandInfo(product.brand).logo ? 'hidden' : ''}`} />
                <span className="text-xs text-text-muted">{product.brand}</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center justify-between mt-3">
            {product.discountedPrice ? (
              <div className="flex items-center">
                <span className="font-bold text-primary-600">{product.discountedPrice.toLocaleString()} تومان</span>
                <span className="line-through text-text-secondary text-sm mr-2">{product.price.toLocaleString()}</span>
              </div>
            ) : (
              <span className="font-bold text-text-primary">{product.price.toLocaleString()} تومان</span>
            )}
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default ProductCard;