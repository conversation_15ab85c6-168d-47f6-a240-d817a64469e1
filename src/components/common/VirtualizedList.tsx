import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { throttle } from '../../utils/performanceUtils';

export interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number | ((index: number, item: T) => number);
  containerHeight: number;
  renderItem: (item: T, index: number, style: React.CSSProperties) => React.ReactNode;
  overscan?: number;
  className?: string;
  onScroll?: (scrollTop: number) => void;
  scrollToIndex?: number;
  scrollToAlignment?: 'start' | 'center' | 'end' | 'auto';
  estimatedItemSize?: number;
  getItemKey?: (item: T, index: number) => string | number;
}

interface VirtualizedListState {
  scrollTop: number;
  isScrolling: boolean;
}

const VirtualizedList = <T,>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll,
  scrollToIndex,
  scrollToAlignment = 'auto',
  estimatedItemSize = 50,
  getItemKey
}: VirtualizedListProps<T>) => {
  const [state, setState] = useState<VirtualizedListState>({
    scrollTop: 0,
    isScrolling: false
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const itemHeightsRef = useRef<Map<number, number>>(new Map());

  // Get item height (supports both fixed and dynamic heights)
  const getItemHeight = useCallback((index: number): number => {
    if (typeof itemHeight === 'function') {
      const cachedHeight = itemHeightsRef.current.get(index);
      if (cachedHeight !== undefined) {
        return cachedHeight;
      }
      return estimatedItemSize;
    }
    return itemHeight;
  }, [itemHeight, estimatedItemSize]);

  // Calculate total height
  const totalHeight = useMemo(() => {
    if (typeof itemHeight === 'number') {
      return items.length * itemHeight;
    }
    
    let height = 0;
    for (let i = 0; i < items.length; i++) {
      height += getItemHeight(i);
    }
    return height;
  }, [items.length, itemHeight, getItemHeight]);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const { scrollTop } = state;
    
    if (typeof itemHeight === 'number') {
      const startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(
        items.length - 1,
        Math.ceil((scrollTop + containerHeight) / itemHeight)
      );
      
      return {
        start: Math.max(0, startIndex - overscan),
        end: Math.min(items.length - 1, endIndex + overscan)
      };
    }

    // Dynamic height calculation
    let currentHeight = 0;
    let startIndex = 0;
    let endIndex = items.length - 1;

    // Find start index
    for (let i = 0; i < items.length; i++) {
      const height = getItemHeight(i);
      if (currentHeight + height > scrollTop) {
        startIndex = i;
        break;
      }
      currentHeight += height;
    }

    // Find end index
    currentHeight = 0;
    for (let i = 0; i < items.length; i++) {
      const height = getItemHeight(i);
      currentHeight += height;
      if (currentHeight > scrollTop + containerHeight) {
        endIndex = i;
        break;
      }
    }

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan)
    };
  }, [state.scrollTop, containerHeight, itemHeight, items.length, overscan, getItemHeight]);

  // Calculate item positions
  const getItemOffset = useCallback((index: number): number => {
    if (typeof itemHeight === 'number') {
      return index * itemHeight;
    }

    let offset = 0;
    for (let i = 0; i < index; i++) {
      offset += getItemHeight(i);
    }
    return offset;
  }, [itemHeight, getItemHeight]);

  // Throttled scroll handler
  const handleScroll = useCallback(
    throttle((event: React.UIEvent<HTMLDivElement>) => {
      const scrollTop = event.currentTarget.scrollTop;
      
      setState(prev => ({
        ...prev,
        scrollTop,
        isScrolling: true
      }));

      onScroll?.(scrollTop);

      // Clear scrolling state after scroll ends
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      
      scrollTimeoutRef.current = setTimeout(() => {
        setState(prev => ({
          ...prev,
          isScrolling: false
        }));
      }, 150);
    }, 16), // ~60fps
    [onScroll]
  );

  // Scroll to specific index
  const scrollToItem = useCallback((index: number, alignment: string = scrollToAlignment) => {
    if (!containerRef.current) return;

    const itemOffset = getItemOffset(index);
    const itemSize = getItemHeight(index);
    
    let scrollTop = itemOffset;

    switch (alignment) {
      case 'center':
        scrollTop = itemOffset - (containerHeight - itemSize) / 2;
        break;
      case 'end':
        scrollTop = itemOffset - containerHeight + itemSize;
        break;
      case 'auto':
        const currentScrollTop = containerRef.current.scrollTop;
        if (itemOffset < currentScrollTop) {
          scrollTop = itemOffset;
        } else if (itemOffset + itemSize > currentScrollTop + containerHeight) {
          scrollTop = itemOffset - containerHeight + itemSize;
        } else {
          return; // Item is already visible
        }
        break;
    }

    containerRef.current.scrollTop = Math.max(0, Math.min(scrollTop, totalHeight - containerHeight));
  }, [getItemOffset, getItemHeight, containerHeight, scrollToAlignment, totalHeight]);

  // Handle scroll to index prop
  useEffect(() => {
    if (scrollToIndex !== undefined && scrollToIndex >= 0 && scrollToIndex < items.length) {
      scrollToItem(scrollToIndex);
    }
  }, [scrollToIndex, scrollToItem, items.length]);

  // Render visible items
  const visibleItems = useMemo(() => {
    const items_to_render = [];
    
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      const item = items[i];
      if (!item) continue;

      const offset = getItemOffset(i);
      const height = getItemHeight(i);
      
      const style: React.CSSProperties = {
        position: 'absolute',
        top: offset,
        left: 0,
        right: 0,
        height: typeof itemHeight === 'number' ? itemHeight : height,
        zIndex: state.isScrolling ? 1 : 'auto'
      };

      const key = getItemKey ? getItemKey(item, i) : i;

      items_to_render.push(
        <div key={key} style={style}>
          {renderItem(item, i, style)}
        </div>
      );
    }

    return items_to_render;
  }, [visibleRange, items, getItemOffset, getItemHeight, itemHeight, state.isScrolling, renderItem, getItemKey]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`virtualized-list ${className}`}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }}
      onScroll={handleScroll}
    >
      {/* Total height spacer */}
      <div
        style={{
          height: totalHeight,
          position: 'relative'
        }}
      >
        {visibleItems}
      </div>
    </div>
  );
};

// Hook for virtualized list with dynamic heights
export const useVirtualizedList = <T,>(
  items: T[],
  estimatedItemSize: number = 50
) => {
  const [itemHeights, setItemHeights] = useState<Map<number, number>>(new Map());
  const measureRef = useRef<Map<number, HTMLElement>>(new Map());

  const measureItem = useCallback((index: number, element: HTMLElement | null) => {
    if (!element) return;
    
    measureRef.current.set(index, element);
    
    const height = element.getBoundingClientRect().height;
    setItemHeights(prev => {
      const newMap = new Map(prev);
      newMap.set(index, height);
      return newMap;
    });
  }, []);

  const getItemHeight = useCallback((index: number): number => {
    return itemHeights.get(index) || estimatedItemSize;
  }, [itemHeights, estimatedItemSize]);

  return {
    measureItem,
    getItemHeight,
    itemHeights
  };
};

// Preset configurations for common use cases
export const ProductVirtualizedList: React.FC<Omit<VirtualizedListProps<any>, 'itemHeight'>> = (props) => (
  <VirtualizedList
    {...props}
    itemHeight={120}
    overscan={3}
  />
);

export const CommentVirtualizedList: React.FC<Omit<VirtualizedListProps<any>, 'itemHeight' | 'estimatedItemSize'>> = (props) => (
  <VirtualizedList
    {...props}
    itemHeight={(index, item) => item.height || 80}
    estimatedItemSize={80}
    overscan={5}
  />
);

export default VirtualizedList;
