import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useProgressiveImage } from '../../hooks/useIntersectionObserver';
import { 
  generatePlaceholderImage, 
  createBase64Placeholder,
  generateOptimizedImageUrl,
  generateSrcSet,
  getResponsiveImageSizes,
  DEFAULT_RESPONSIVE_SIZES
} from '../../utils/imageOptimization';
import { ImageOptimizationOptions, ResponsiveImageSizes } from '../../utils/imageOptimization';

export interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  placeholderSrc?: string;
  placeholderColor?: string;
  showPlaceholder?: boolean;
  enableProgressiveLoading?: boolean;
  optimizationOptions?: Partial<ImageOptimizationOptions>;
  responsiveSizes?: Partial<ResponsiveImageSizes>;
  enableResponsive?: boolean;
  fadeInDuration?: number;
  blurDuration?: number;
  onLoad?: () => void;
  onError?: () => void;
  className?: string;
  containerClassName?: string;
  aspectRatio?: number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholderSrc,
  placeholderColor = '#f3f4f6',
  showPlaceholder = true,
  enableProgressiveLoading = true,
  optimizationOptions = {},
  responsiveSizes = {},
  enableResponsive = true,
  fadeInDuration = 300,
  blurDuration = 200,
  onLoad,
  onError,
  className = '',
  containerClassName = '',
  aspectRatio,
  objectFit = 'cover',
  width,
  height,
  ...props
}) => {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);
  
  // Merge responsive sizes with defaults
  const sizes = { ...DEFAULT_RESPONSIVE_SIZES, ...responsiveSizes };
  
  // Generate placeholder
  const placeholder = placeholderSrc || 
    (showPlaceholder ? createBase64Placeholder(
      typeof width === 'number' ? width : 400,
      typeof height === 'number' ? height : 300,
      placeholderColor
    ) : '');

  // Use progressive image loading if enabled
  const {
    ref: lazyRef,
    src: progressiveSrc,
    isLoading: isProgressiveLoading,
    isLoaded: isProgressiveLoaded,
    blur
  } = useProgressiveImage(
    placeholder,
    enableProgressiveLoading ? generateOptimizedImageUrl(src, optimizationOptions) : src,
    {
      threshold: 0.1,
      rootMargin: '50px'
    }
  );

  // Update current src based on progressive loading
  useEffect(() => {
    if (enableProgressiveLoading) {
      setCurrentSrc(progressiveSrc);
      setImageState(isProgressiveLoaded ? 'loaded' : 'loading');
    } else {
      setCurrentSrc(generateOptimizedImageUrl(src, optimizationOptions));
    }
  }, [progressiveSrc, isProgressiveLoaded, enableProgressiveLoading, src, optimizationOptions]);

  // Handle image load
  const handleLoad = () => {
    setImageState('loaded');
    onLoad?.();
  };

  // Handle image error
  const handleError = () => {
    setImageState('error');
    onError?.();
  };

  // Generate responsive attributes
  const responsiveAttributes = enableResponsive ? {
    srcSet: generateSrcSet(src, sizes, optimizationOptions),
    sizes: getResponsiveImageSizes(sizes)
  } : {};

  // Calculate container styles
  const containerStyles: React.CSSProperties = {
    position: 'relative',
    overflow: 'hidden',
    ...(aspectRatio && {
      aspectRatio: aspectRatio.toString(),
      width: '100%'
    })
  };

  // Calculate image styles
  const imageStyles: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit,
    transition: `filter ${blurDuration}ms ease, opacity ${fadeInDuration}ms ease`
  };

  return (
    <div 
      ref={lazyRef}
      className={`lazy-image-container ${containerClassName}`}
      style={containerStyles}
    >
      <AnimatePresence>
        {imageState === 'loading' && showPlaceholder && (
          <motion.div
            key="placeholder"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: fadeInDuration / 1000 }}
            className="absolute inset-0 flex items-center justify-center bg-gray-100"
            style={{ backgroundColor: placeholderColor }}
          >
            {placeholder && (
              <img
                src={placeholder}
                alt=""
                className="w-full h-full object-cover"
                style={{ filter: 'blur(5px)' }}
              />
            )}
            
            {/* Loading spinner */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
            </div>
          </motion.div>
        )}

        {imageState === 'error' && (
          <motion.div
            key="error"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: fadeInDuration / 1000 }}
            className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500"
          >
            <div className="text-center">
              <div className="text-2xl mb-2">📷</div>
              <div className="text-sm">تصویر بارگذاری نشد</div>
            </div>
          </motion.div>
        )}

        {currentSrc && (
          <motion.img
            key="image"
            ref={imgRef}
            src={currentSrc}
            alt={alt}
            className={`lazy-image ${className}`}
            style={{
              ...imageStyles,
              filter: blur && enableProgressiveLoading ? 'blur(5px)' : 'none',
              opacity: imageState === 'loaded' ? 1 : 0
            }}
            onLoad={handleLoad}
            onError={handleError}
            width={width}
            height={height}
            {...responsiveAttributes}
            {...props}
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ 
              opacity: imageState === 'loaded' ? 1 : 0,
              scale: 1
            }}
            transition={{ 
              duration: fadeInDuration / 1000,
              ease: 'easeOut'
            }}
          />
        )}
      </AnimatePresence>

      {/* Loading progress indicator */}
      {(imageState === 'loading' || isProgressiveLoading) && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
          <motion.div
            className="h-full bg-blue-500"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 2, ease: 'easeInOut' }}
          />
        </div>
      )}
    </div>
  );
};

// Higher-order component for lazy loading existing images
export const withLazyLoading = <P extends object>(
  Component: React.ComponentType<P & { src: string }>
) => {
  return React.forwardRef<HTMLElement, P & LazyImageProps>((props, ref) => {
    const { src, ...lazyProps } = props;
    
    return (
      <LazyImage {...lazyProps} src={src}>
        <Component {...(props as P)} ref={ref} />
      </LazyImage>
    );
  });
};

// Preset configurations for common use cases
export const ProductImage: React.FC<Omit<LazyImageProps, 'optimizationOptions'>> = (props) => (
  <LazyImage
    {...props}
    optimizationOptions={{
      quality: 85,
      format: 'auto',
      progressive: true
    }}
    responsiveSizes={{
      mobile: 300,
      tablet: 400,
      desktop: 500,
      wide: 600
    }}
    aspectRatio={1}
    objectFit="cover"
  />
);

export const HeroImage: React.FC<Omit<LazyImageProps, 'optimizationOptions'>> = (props) => (
  <LazyImage
    {...props}
    optimizationOptions={{
      quality: 90,
      format: 'auto',
      progressive: true
    }}
    responsiveSizes={{
      mobile: 800,
      tablet: 1200,
      desktop: 1600,
      wide: 2000
    }}
    aspectRatio={16/9}
    objectFit="cover"
    fadeInDuration={500}
  />
);

export const ThumbnailImage: React.FC<Omit<LazyImageProps, 'optimizationOptions'>> = (props) => (
  <LazyImage
    {...props}
    optimizationOptions={{
      quality: 75,
      format: 'auto',
      progressive: false
    }}
    responsiveSizes={{
      mobile: 100,
      tablet: 150,
      desktop: 200,
      wide: 250
    }}
    aspectRatio={1}
    objectFit="cover"
    fadeInDuration={200}
  />
);

export default LazyImage;
