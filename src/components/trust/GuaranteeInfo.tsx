import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Shield, 
  RotateCcw, 
  Truck, 
  Clock, 
  CheckCircle, 
  Info, 
  ChevronDown,
  Phone,
  Mail,
  MapPin
} from 'lucide-react';

interface GuaranteeItem {
  id: string;
  title: string;
  description: string;
  details: string[];
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}

interface GuaranteeInfoProps {
  variant?: 'full' | 'compact' | 'product';
  showContactInfo?: boolean;
}

const GuaranteeInfo: React.FC<GuaranteeInfoProps> = ({
  variant = 'full',
  showContactInfo = true
}) => {
  const [expandedItem, setExpandedItem] = useState<string | null>(null);

  const guarantees: GuaranteeItem[] = [
    {
      id: 'authenticity',
      title: 'ضمانت اصالت کالا',
      description: 'تمامی محصولات ما اصل و با ضمانت معتبر هستند',
      details: [
        'واردات مستقیم از شرکت‌های معتبر',
        'دارای هولوگرام و کد تأیید اصالت',
        'ضمانت بازپرداخت در صورت عدم اصالت',
        'گواهی‌نامه اصالت همراه محصول'
      ],
      icon: <Shield className="w-6 h-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      id: 'return',
      title: 'ضمانت بازگشت کالا',
      description: '۷ روز ضمانت بازگشت بدون قید و شرط',
      details: [
        'بازگشت کالا تا ۷ روز پس از خرید',
        'بدون نیاز به ذکر دلیل',
        'بازپرداخت کامل وجه',
        'هزینه ارسال بازگشت بر عهده ما'
      ],
      icon: <RotateCcw className="w-6 h-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      id: 'shipping',
      title: 'ضمانت ارسال',
      description: 'ارسال سریع و ایمن به سراسر کشور',
      details: [
        'ارسال رایگان برای خریدهای بالای ۵۰۰ هزار تومان',
        'ارسال در کمترین زمان ممکن',
        'بسته‌بندی مقاوم و ایمن',
        'پیگیری آنلاین مرسوله'
      ],
      icon: <Truck className="w-6 h-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      id: 'support',
      title: 'پشتیبانی ۲۴/۷',
      description: 'پشتیبانی همه روزه برای راهنمایی شما',
      details: [
        'پاسخگویی از ساعت ۸ صبح تا ۲۲ شب',
        'مشاوره رایگان انتخاب محصول',
        'پیگیری سفارشات',
        'حل مشکلات پس از فروش'
      ],
      icon: <Clock className="w-6 h-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  const toggleExpanded = (itemId: string) => {
    setExpandedItem(expandedItem === itemId ? null : itemId);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  if (variant === 'compact') {
    return (
      <div className="flex flex-wrap justify-center gap-4">
        {guarantees.slice(0, 2).map((guarantee) => (
          <div
            key={guarantee.id}
            className={`
              ${guarantee.bgColor} p-3 rounded-lg 
              flex items-center gap-2 text-sm
            `}
          >
            <div className={guarantee.color}>
              {guarantee.icon}
            </div>
            <span className="font-medium text-text-primary">
              {guarantee.title}
            </span>
          </div>
        ))}
      </div>
    );
  }

  if (variant === 'product') {
    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold text-text-primary mb-3 flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          تضمین‌های این محصول
        </h4>
        <div className="space-y-2">
          {guarantees.map((guarantee) => (
            <div key={guarantee.id} className="flex items-center gap-2 text-sm">
              <div className={`${guarantee.color} flex-shrink-0`}>
                {React.cloneElement(guarantee.icon as React.ReactElement, {
                  className: 'w-4 h-4'
                })}
              </div>
              <span className="text-text-secondary">{guarantee.description}</span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="guarantee-info">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-text-primary mb-2">
          تضمین‌های آرامش پوست
        </h2>
        <p className="text-text-secondary">
          ما متعهد به ارائه بهترین خدمات و کیفیت به شما هستیم
        </p>
      </div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {guarantees.map((guarantee) => (
          <motion.div
            key={guarantee.id}
            variants={itemVariants}
            className={`
              ${guarantee.bgColor} rounded-lg border border-gray-100
              hover:shadow-md transition-all duration-200
            `}
          >
            <div
              className="p-6 cursor-pointer"
              onClick={() => toggleExpanded(guarantee.id)}
            >
              <div className="flex items-start gap-4">
                <div className={`${guarantee.color} flex-shrink-0`}>
                  {guarantee.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-text-primary mb-1">
                      {guarantee.title}
                    </h3>
                    <ChevronDown
                      className={`w-5 h-5 text-text-muted transition-transform duration-200 ${
                        expandedItem === guarantee.id ? 'rotate-180' : ''
                      }`}
                    />
                  </div>
                  <p className="text-text-secondary text-sm">
                    {guarantee.description}
                  </p>
                </div>
              </div>
            </div>

            <AnimatePresence>
              {expandedItem === guarantee.id && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <div className="px-6 pb-6">
                    <div className="border-t border-gray-200 pt-4">
                      <ul className="space-y-2">
                        {guarantee.details.map((detail, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                            <span className="text-text-secondary">{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </motion.div>

      {/* Contact Information */}
      {showContactInfo && (
        <div className="bg-primary-50 rounded-lg p-6">
          <h3 className="font-semibold text-primary-700 mb-4 flex items-center gap-2">
            <Info className="w-5 h-5" />
            اطلاعات تماس برای پیگیری
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4 text-primary-600" />
              <span className="text-primary-700">۰۲۱-۱۲۳۴۵۶۷۸</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-primary-600" />
              <span className="text-primary-700"><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-primary-600" />
              <span className="text-primary-700">تهران، ایران</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GuaranteeInfo;
