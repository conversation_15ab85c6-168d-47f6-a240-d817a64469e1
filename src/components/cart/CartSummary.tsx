import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  ShoppingBag, 
  Truck, 
  Gift, 
  Star, 
  CreditCard, 
  Calendar,
  Info,
  ChevronDown,
  ChevronUp,
  Tag
} from 'lucide-react';
import { AdvancedCartSummary } from '../../types/advancedCart';
import { formatPrice, formatNumber } from '../../utils/formatters';
import { PERSIAN_ADVANCED_CART_MESSAGES } from '../../types/advancedCart';

interface CartSummaryProps {
  summary: AdvancedCartSummary;
  onCheckout: () => void;
  className?: string;
}

const CartSummary: React.FC<CartSummaryProps> = ({
  summary,
  onCheckout,
  className = ''
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(summary.paymentOptions[0]?.id || '');

  const selectedPaymentOption = summary.paymentOptions.find(p => p.id === selectedPayment);

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <ShoppingBag className="w-5 h-5 ml-2 text-primary-600" />
          خلاصه سفارش
        </h3>
      </div>

      <div className="p-4 space-y-4">
        {/* Subtotal */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">
            {PERSIAN_ADVANCED_CART_MESSAGES.summary.subtotal}
          </span>
          <span className="font-medium text-gray-900">
            {formatPrice(summary.subtotal)}
          </span>
        </div>

        {/* Discounts */}
        {summary.discounts.length > 0 && (
          <div className="space-y-2">
            {summary.discounts.map((discount, index) => (
              <div key={index} className="flex items-center justify-between text-green-600">
                <div className="flex items-center">
                  <Tag className="w-4 h-4 ml-1" />
                  <span className="text-sm">{discount.description}</span>
                </div>
                <span className="font-medium">
                  -{formatPrice(discount.type === 'percentage' 
                    ? (summary.subtotal * discount.value / 100) 
                    : discount.value
                  )}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Shipping */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Truck className="w-4 h-4 ml-1 text-gray-500" />
              <span className="text-gray-600">
                {PERSIAN_ADVANCED_CART_MESSAGES.summary.shipping}
              </span>
            </div>
            <span className={`font-medium ${summary.shipping.isFree ? 'text-green-600' : 'text-gray-900'}`}>
              {summary.shipping.isFree 
                ? PERSIAN_ADVANCED_CART_MESSAGES.summary.freeShipping
                : formatPrice(summary.shipping.cost)
              }
            </span>
          </div>

          {/* Free shipping progress */}
          {!summary.shipping.isFree && summary.shipping.remainingForFreeShipping && summary.shipping.remainingForFreeShipping > 0 && (
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="flex items-center justify-between text-sm text-blue-700 mb-2">
                <span>برای ارسال رایگان</span>
                <span className="font-medium">
                  {formatPrice(summary.shipping.remainingForFreeShipping)} مانده
                </span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.max(0, Math.min(100, 
                      ((summary.shipping.freeShippingThreshold! - summary.shipping.remainingForFreeShipping) / 
                       summary.shipping.freeShippingThreshold!) * 100
                    ))}%`
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Loyalty Points */}
        {summary.loyaltyPoints.pointsToEarn > 0 && (
          <div className="bg-purple-50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Star className="w-4 h-4 ml-1 text-purple-600" />
                <span className="text-sm text-purple-700">
                  {PERSIAN_ADVANCED_CART_MESSAGES.summary.loyaltyPoints}
                </span>
              </div>
              <span className="font-medium text-purple-700">
                +{formatNumber(summary.loyaltyPoints.pointsToEarn)} امتیاز
              </span>
            </div>

            {/* Next tier progress */}
            {summary.loyaltyPoints.nextTierProgress && (
              <div className="mt-2 pt-2 border-t border-purple-200">
                <div className="text-xs text-purple-600">
                  تا رسیدن به سطح {summary.loyaltyPoints.nextTierProgress.next}:
                  <span className="font-medium mr-1">
                    {formatNumber(summary.loyaltyPoints.nextTierProgress.pointsNeeded)} امتیاز
                  </span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Total */}
        <div className="border-t border-gray-200 pt-4">
          <div className="flex items-center justify-between text-lg font-semibold">
            <span className="text-gray-900">
              {PERSIAN_ADVANCED_CART_MESSAGES.summary.total}
            </span>
            <span className="text-primary-600">
              {formatPrice(summary.total)}
            </span>
          </div>

          {/* Savings */}
          {summary.savings > 0 && (
            <div className="flex items-center justify-between text-sm text-green-600 mt-1">
              <span>{PERSIAN_ADVANCED_CART_MESSAGES.summary.savings}</span>
              <span className="font-medium">
                {formatPrice(summary.savings)}
              </span>
            </div>
          )}
        </div>

        {/* Payment Options */}
        <div className="space-y-3">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center justify-between w-full text-sm text-gray-600 hover:text-gray-900"
          >
            <span>گزینه‌های پرداخت</span>
            {showDetails ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </button>

          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-2"
            >
              {summary.paymentOptions.filter(p => p.available).map((option) => (
                <label
                  key={option.id}
                  className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedPayment === option.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="payment"
                    value={option.id}
                    checked={selectedPayment === option.id}
                    onChange={(e) => setSelectedPayment(e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      <CreditCard className="w-4 h-4 ml-2 text-gray-500" />
                      <span className="text-sm font-medium">{option.name}</span>
                    </div>

                  </div>
                </label>
              ))}
            </motion.div>
          )}
        </div>

        {/* Estimated Delivery */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center">
            <Calendar className="w-4 h-4 ml-2 text-gray-500" />
            <span className="text-sm text-gray-600">
              {PERSIAN_ADVANCED_CART_MESSAGES.summary.estimatedDelivery}:
            </span>
            <span className="text-sm font-medium text-gray-900 mr-2">
              {summary.estimatedDelivery.toLocaleDateString('fa-IR')}
            </span>
          </div>
        </div>

        {/* Checkout Button */}
        <button
          onClick={onCheckout}
          className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center"
        >
          <ShoppingBag className="w-5 h-5 ml-2" />
          ادامه خرید
        </button>

        {/* Security Note */}
        <div className="flex items-start text-xs text-gray-500">
          <Info className="w-3 h-3 ml-1 mt-0.5 flex-shrink-0" />
          <span>
            تمامی پرداخت‌ها از طریق درگاه‌های امن بانکی انجام می‌شود و اطلاعات شما محفوظ است.
          </span>
        </div>
      </div>
    </div>
  );
};

export default CartSummary;
