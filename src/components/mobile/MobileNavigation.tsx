import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  Search,
  ShoppingBag,
  User,
  Menu,
  X,
  Heart,
  Eye,
  Star,
  Gift,
  Percent
} from 'lucide-react';
import { useCart } from '../../context/CartContext';
import { useWishlist } from '../../hooks/useWishlist';
import { useProductComparison } from '../../hooks/useProductComparison';
import { useAuth } from '../../context/AuthContext';
import { useMobileDetection } from '../../hooks/useMobileDetection';
import { MobileNavigationItem, PERSIAN_MOBILE_MESSAGES } from '../../types/mobile';

interface MobileNavigationProps {
  className?: string;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({ className = '' }) => {
  const location = useLocation();
  const { items: cartItems } = useCart();
  const { items: wishlistItems } = useWishlist();
  const { items: comparisonItems } = useProductComparison();
  const { user } = useAuth();
  const { isMobile } = useMobileDetection();
  
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Close menu on route change
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  // Don't render on desktop
  if (!isMobile) return null;

  // Main navigation items
  const mainNavItems: MobileNavigationItem[] = [
    {
      id: 'home',
      title: PERSIAN_MOBILE_MESSAGES.navigation.home,
      icon: Home,
      path: '/',
      isActive: location.pathname === '/'
    },
    {
      id: 'search',
      title: PERSIAN_MOBILE_MESSAGES.navigation.search,
      icon: Search,
      path: '/products',
      isActive: location.pathname === '/products'
    },
    {
      id: 'cart',
      title: PERSIAN_MOBILE_MESSAGES.navigation.cart,
      icon: ShoppingBag,
      path: '/cart',
      badge: cartItems.length,
      isActive: location.pathname === '/cart'
    },
    {
      id: 'profile',
      title: PERSIAN_MOBILE_MESSAGES.navigation.profile,
      icon: User,
      path: user ? '/profile' : '/auth',
      isActive: location.pathname === '/profile' || location.pathname === '/auth'
    },
    {
      id: 'menu',
      title: PERSIAN_MOBILE_MESSAGES.navigation.menu,
      icon: Menu,
      path: '#',
      isActive: isMenuOpen
    }
  ];

  // Extended menu items
  const extendedMenuItems: MobileNavigationItem[] = [
    {
      id: 'wishlist',
      title: 'علاقه‌مندی‌ها',
      icon: Heart,
      path: '/wishlist',
      badge: wishlistItems.length
    },
    {
      id: 'comparison',
      title: 'مقایسه محصولات',
      icon: Eye,
      path: '/comparison',
      badge: comparisonItems.length
    },
    {
      id: 'loyalty',
      title: 'باشگاه مشتریان',
      icon: Star,
      path: '/loyalty'
    },
    {
      id: 'offers',
      title: 'پیشنهادات ویژه',
      icon: Percent,
      path: '/offers'
    },
    {
      id: 'gift-cards',
      title: 'کارت هدیه',
      icon: Gift,
      path: '/gift-cards'
    }
  ];

  const handleMenuToggle = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleNavItemClick = (item: MobileNavigationItem) => {
    if (item.id === 'menu') {
      handleMenuToggle();
    } else {
      setIsMenuOpen(false);
    }
  };

  return (
    <>
      {/* Bottom Navigation Bar */}
      <motion.nav
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        className={`fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 ${className}`}
        style={{
          paddingBottom: 'env(safe-area-inset-bottom)',
        }}
      >
        <div className="flex items-center justify-around px-2 py-2">
          {mainNavItems.map((item) => (
            <NavItem
              key={item.id}
              item={item}
              onClick={() => handleNavItemClick(item)}
            />
          ))}
        </div>
      </motion.nav>

      {/* Extended Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setIsMenuOpen(false)}
            />

            {/* Menu Panel */}
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              className="fixed bottom-0 left-0 right-0 z-50 bg-white rounded-t-3xl shadow-2xl"
              style={{
                paddingBottom: 'env(safe-area-inset-bottom)',
              }}
            >
              {/* Menu Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-100">
                <h3 className="text-lg font-bold text-gray-900">
                  {PERSIAN_MOBILE_MESSAGES.navigation.menu}
                </h3>
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Menu Items */}
              <div className="p-6 space-y-4 max-h-96 overflow-y-auto">
                {extendedMenuItems.map((item) => (
                  <Link
                    key={item.id}
                    to={item.path}
                    className="flex items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className="p-2 rounded-lg bg-white shadow-sm">
                        <item.icon className="w-5 h-5 text-rose-600" />
                      </div>
                      <span className="font-medium text-gray-900">{item.title}</span>
                    </div>
                    {item.badge && item.badge > 0 && (
                      <span className="px-2 py-1 text-xs font-bold text-white bg-rose-600 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

interface NavItemProps {
  item: MobileNavigationItem;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ item, onClick }) => {
  const content = (
    <motion.button
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={`flex flex-col items-center justify-center p-3 rounded-xl transition-all duration-200 min-w-[60px] relative ${
        item.isActive
          ? 'text-rose-600 bg-rose-50'
          : 'text-gray-600 hover:text-rose-600 hover:bg-gray-50'
      }`}
    >
      <div className="relative">
        <item.icon className="w-6 h-6" />
        {item.badge && item.badge > 0 && (
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-2 -right-2 px-1.5 py-0.5 text-xs font-bold text-white bg-rose-600 rounded-full min-w-[18px] text-center"
          >
            {item.badge > 99 ? '99+' : item.badge}
          </motion.span>
        )}
      </div>
      <span className="text-xs font-medium mt-1 leading-none">
        {item.title}
      </span>
    </motion.button>
  );

  if (item.path === '#') {
    return content;
  }

  return (
    <Link to={item.path} className="block">
      {content}
    </Link>
  );
};

export default MobileNavigation;
