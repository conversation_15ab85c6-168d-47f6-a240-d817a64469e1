import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { LucideIcon, AlertCircle, CheckCircle, Info } from 'lucide-react';

interface AdminFormProps {
  children: ReactNode;
  onSubmit?: (e: React.FormEvent) => void;
  className?: string;
  loading?: boolean;
}

const AdminForm: React.FC<AdminFormProps> = ({
  children,
  onSubmit,
  className = '',
  loading = false
}) => {
  return (
    <form
      onSubmit={onSubmit}
      className={`space-y-6 ${loading ? 'opacity-50 pointer-events-none' : ''} ${className}`}
    >
      {children}
    </form>
  );
};

// Form Field Components
export const AdminFormField: React.FC<{
  label?: string;
  required?: boolean;
  error?: string;
  help?: string;
  children: ReactNode;
  className?: string;
}> = ({ label, required, error, help, children, className = '' }) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}
      {children}
      {error && (
        <motion.p
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-sm text-red-600 flex items-center gap-1"
        >
          <AlertCircle className="w-4 h-4" />
          {error}
        </motion.p>
      )}
      {help && !error && (
        <p className="text-sm text-gray-500">{help}</p>
      )}
    </div>
  );
};

export const AdminInput: React.FC<{
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  error?: boolean;
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  className?: string;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'>> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  disabled,
  error,
  icon: Icon,
  iconPosition = 'right',
  className = '',
  ...props
}) => {
  const baseClasses = `
    w-full px-4 py-3 border rounded-lg transition-colors
    focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent
    disabled:bg-gray-50 disabled:cursor-not-allowed
    ${error ? 'border-red-300 bg-red-50' : 'border-gray-300'}
    ${Icon ? (iconPosition === 'right' ? 'pr-12' : 'pl-12') : ''}
    ${className}
  `.trim();

  return (
    <div className="relative">
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        className={baseClasses}
        {...props}
      />
      {Icon && (
        <div className={`
          absolute top-1/2 transform -translate-y-1/2 text-gray-400
          ${iconPosition === 'right' ? 'right-3' : 'left-3'}
        `}>
          <Icon className="w-5 h-5" />
        </div>
      )}
    </div>
  );
};

export const AdminTextarea: React.FC<{
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  error?: boolean;
  rows?: number;
  className?: string;
} & Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'>> = ({
  placeholder,
  value,
  onChange,
  disabled,
  error,
  rows = 4,
  className = '',
  ...props
}) => {
  const baseClasses = `
    w-full px-4 py-3 border rounded-lg transition-colors resize-vertical
    focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent
    disabled:bg-gray-50 disabled:cursor-not-allowed
    ${error ? 'border-red-300 bg-red-50' : 'border-gray-300'}
    ${className}
  `.trim();

  return (
    <textarea
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      rows={rows}
      className={baseClasses}
      {...props}
    />
  );
};

export const AdminSelect: React.FC<{
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string; disabled?: boolean }>;
  children?: ReactNode;
  className?: string;
} & Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'>> = ({
  value,
  onChange,
  disabled,
  error,
  placeholder,
  options,
  children,
  className = '',
  ...props
}) => {
  const baseClasses = `
    w-full px-4 py-3 border rounded-lg transition-colors
    focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent
    disabled:bg-gray-50 disabled:cursor-not-allowed
    ${error ? 'border-red-300 bg-red-50' : 'border-gray-300'}
    ${className}
  `.trim();

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <select
      value={value}
      onChange={handleChange}
      disabled={disabled}
      className={baseClasses}
      {...props}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options ? (
        options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))
      ) : (
        children
      )}
    </select>
  );
};

export const AdminCheckbox: React.FC<{
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  label?: string;
  description?: string;
  className?: string;
}> = ({
  checked,
  onChange,
  disabled,
  label,
  description,
  className = ''
}) => {
  return (
    <div className={`flex items-start gap-3 ${className}`}>
      <div className="flex items-center h-5">
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onChange?.(e.target.checked)}
          disabled={disabled}
          className="w-4 h-4 text-admin-600 border-gray-300 rounded focus:ring-admin-500 focus:ring-2"
        />
      </div>
      {(label || description) && (
        <div className="text-sm">
          {label && (
            <label className="font-medium text-gray-700">
              {label}
            </label>
          )}
          {description && (
            <p className="text-gray-500 mt-1">
              {description}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export const AdminRadioGroup: React.FC<{
  value?: string;
  onChange?: (value: string) => void;
  options: Array<{
    value: string;
    label: string;
    description?: string;
    disabled?: boolean;
  }>;
  disabled?: boolean;
  className?: string;
}> = ({
  value,
  onChange,
  options,
  disabled,
  className = ''
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {options.map((option) => (
        <div key={option.value} className="flex items-start gap-3">
          <div className="flex items-center h-5">
            <input
              type="radio"
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange?.(e.target.value)}
              disabled={disabled || option.disabled}
              className="w-4 h-4 text-admin-600 border-gray-300 focus:ring-admin-500 focus:ring-2"
            />
          </div>
          <div className="text-sm">
            <label className="font-medium text-gray-700">
              {option.label}
            </label>
            {option.description && (
              <p className="text-gray-500 mt-1">
                {option.description}
              </p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export const AdminFormAlert: React.FC<{
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  className?: string;
}> = ({ type, title, message, className = '' }) => {
  const typeConfig = {
    success: {
      icon: CheckCircle,
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      iconColor: 'text-green-400',
      titleColor: 'text-green-800',
      messageColor: 'text-green-700'
    },
    error: {
      icon: AlertCircle,
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      iconColor: 'text-red-400',
      titleColor: 'text-red-800',
      messageColor: 'text-red-700'
    },
    warning: {
      icon: AlertCircle,
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      iconColor: 'text-yellow-400',
      titleColor: 'text-yellow-800',
      messageColor: 'text-yellow-700'
    },
    info: {
      icon: Info,
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      iconColor: 'text-blue-400',
      titleColor: 'text-blue-800',
      messageColor: 'text-blue-700'
    }
  };

  const config = typeConfig[type];
  const Icon = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`
        rounded-md p-4 border
        ${config.bgColor} ${config.borderColor} ${className}
      `}
    >
      <div className="flex">
        <div className="flex-shrink-0">
          <Icon className={`h-5 w-5 ${config.iconColor}`} />
        </div>
        <div className="mr-3">
          {title && (
            <h3 className={`text-sm font-medium ${config.titleColor}`}>
              {title}
            </h3>
          )}
          <div className={`text-sm ${title ? 'mt-2' : ''} ${config.messageColor}`}>
            {message}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default AdminForm;
