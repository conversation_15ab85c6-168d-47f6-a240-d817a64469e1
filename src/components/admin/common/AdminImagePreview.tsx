import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Building2, Image as ImageIcon, AlertCircle } from 'lucide-react';

interface AdminImagePreviewProps {
  src?: string;
  alt?: string;
  fallbackIcon?: React.ComponentType<any>;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'square' | 'circle' | 'rounded';
  className?: string;
  showError?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

const AdminImagePreview: React.FC<AdminImagePreviewProps> = ({
  src,
  alt = 'تصویر',
  fallbackIcon: FallbackIcon = Building2,
  size = 'md',
  shape = 'rounded',
  className = '',
  showError = false,
  loading = false,
  onClick
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const getSizeClasses = () => {
    switch (size) {
      case 'xs': return 'w-6 h-6';
      case 'sm': return 'w-8 h-8';
      case 'md': return 'w-12 h-12';
      case 'lg': return 'w-16 h-16';
      case 'xl': return 'w-24 h-24';
      default: return 'w-12 h-12';
    }
  };

  const getShapeClasses = () => {
    switch (shape) {
      case 'circle': return 'rounded-full';
      case 'square': return 'rounded-none';
      case 'rounded': return 'rounded-lg';
      default: return 'rounded-lg';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'xs': return 'w-3 h-3';
      case 'sm': return 'w-4 h-4';
      case 'md': return 'w-6 h-6';
      case 'lg': return 'w-8 h-8';
      case 'xl': return 'w-12 h-12';
      default: return 'w-6 h-6';
    }
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const shouldShowImage = src && !imageError;
  const shouldShowFallback = !src || imageError;
  const isLoading = loading || (shouldShowImage && imageLoading);

  return (
    <div
      className={`
        relative bg-gray-100 flex items-center justify-center overflow-hidden
        ${getSizeClasses()} ${getShapeClasses()}
        ${onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full border-2 border-gray-300 border-t-gray-600 w-4 h-4"></div>
        </div>
      )}

      {/* Image */}
      {shouldShowImage && (
        <motion.img
          src={src}
          alt={alt}
          className={`w-full h-full object-cover ${isLoading ? 'opacity-0' : 'opacity-100'}`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoading ? 0 : 1 }}
          transition={{ duration: 0.2 }}
        />
      )}

      {/* Fallback icon */}
      {shouldShowFallback && !isLoading && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
          className="flex items-center justify-center"
        >
          {showError && imageError ? (
            <AlertCircle className={`text-red-500 ${getIconSize()}`} />
          ) : (
            <FallbackIcon className={`text-gray-500 ${getIconSize()}`} />
          )}
        </motion.div>
      )}
    </div>
  );
};

export default AdminImagePreview;
