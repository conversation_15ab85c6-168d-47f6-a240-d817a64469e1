import React, { useState, useRef, useEffect } from 'react';
import { X, Plus } from 'lucide-react';

interface AdminTagInputProps {
  value: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  maxTags?: number;
  suggestions?: string[];
  disabled?: boolean;
  className?: string;
}

const AdminTagInput: React.FC<AdminTagInputProps> = ({
  value = [],
  onChange,
  placeholder = 'تگ جدید اضافه کنید...',
  maxTags = 10,
  suggestions = [],
  disabled = false,
  className = ''
}) => {
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (inputValue.trim() && suggestions.length > 0) {
      const filtered = suggestions.filter(
        suggestion => 
          suggestion.toLowerCase().includes(inputValue.toLowerCase()) &&
          !value.includes(suggestion)
      );
      setFilteredSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
    } else {
      setShowSuggestions(false);
      setFilteredSuggestions([]);
    }
  }, [inputValue, suggestions, value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (
      trimmedTag &&
      !value.includes(trimmedTag) &&
      value.length < maxTags
    ) {
      onChange([...value, trimmedTag]);
      setInputValue('');
      setShowSuggestions(false);
    }
  };

  const removeTag = (indexToRemove: number) => {
    onChange(value.filter((_, index) => index !== indexToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      removeTag(value.length - 1);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleSuggestionClick = (suggestion: string) => {
    addTag(suggestion);
    inputRef.current?.focus();
  };

  const canAddMore = value.length < maxTags;

  return (
    <div className={`relative ${className}`} ref={containerRef}>
      {/* Tags Container */}
      <div className={`
        min-h-[42px] p-2 border border-gray-300 rounded-lg bg-white
        focus-within:ring-2 focus-within:ring-admin-500 focus-within:border-admin-500
        ${disabled ? 'opacity-50 cursor-not-allowed bg-gray-50' : ''}
        transition-colors duration-200
      `}>
        <div className="flex flex-wrap gap-2 items-center">
          {/* Existing Tags */}
          {value.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1 px-3 py-1 bg-admin-100 text-admin-800 rounded-full text-sm"
            >
              <span>{tag}</span>
              {!disabled && (
                <button
                  type="button"
                  onClick={() => removeTag(index)}
                  className="p-0.5 hover:bg-admin-200 rounded-full transition-colors"
                  title="حذف تگ"
                >
                  <X className="w-3 h-3" />
                </button>
              )}
            </span>
          ))}

          {/* Input Field */}
          {canAddMore && !disabled && (
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onFocus={() => {
                if (filteredSuggestions.length > 0) {
                  setShowSuggestions(true);
                }
              }}
              placeholder={value.length === 0 ? placeholder : ''}
              className="flex-1 min-w-[120px] outline-none bg-transparent text-sm"
            />
          )}

          {/* Add Button */}
          {inputValue.trim() && canAddMore && !disabled && (
            <button
              type="button"
              onClick={() => addTag(inputValue)}
              className="p-1 text-admin-600 hover:bg-admin-100 rounded transition-colors"
              title="افزودن تگ"
            >
              <Plus className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-48 overflow-y-auto">
          {filteredSuggestions.map((suggestion, index) => (
            <button
              key={index}
              type="button"
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full px-3 py-2 text-right hover:bg-gray-50 transition-colors text-sm border-b border-gray-100 last:border-b-0"
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}

      {/* Helper Text */}
      <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
        <span>
          {value.length}/{maxTags} تگ
        </span>
        {!canAddMore && (
          <span className="text-yellow-600">
            حداکثر تعداد تگ اضافه شده
          </span>
        )}
      </div>
    </div>
  );
};

export default AdminTagInput;
