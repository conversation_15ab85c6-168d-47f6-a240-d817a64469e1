import React, { useState, useRef, useCallback } from 'react';

interface AdminSliderProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  label?: string;
  disabled?: boolean;
  className?: string;
  showInput?: boolean;
  formatLabel?: (value: number) => string;
}

const AdminSlider: React.FC<AdminSliderProps> = ({
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  label,
  disabled = false,
  className = '',
  showInput = false,
  formatLabel
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  const percentage = ((value - min) / (max - min)) * 100;

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (disabled) return;
    
    setIsDragging(true);
    updateValue(e);
  }, [disabled]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || disabled) return;
    updateValue(e);
  }, [isDragging, disabled]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const updateValue = (e: MouseEvent | React.MouseEvent) => {
    if (!sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    const newValue = min + (percentage / 100) * (max - min);
    
    // Round to nearest step
    const steppedValue = Math.round(newValue / step) * step;
    const clampedValue = Math.max(min, Math.min(max, steppedValue));
    
    onChange(clampedValue);
  };

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseFloat(e.target.value);
    if (!isNaN(newValue)) {
      const clampedValue = Math.max(min, Math.min(max, newValue));
      onChange(clampedValue);
    }
  };

  const displayLabel = formatLabel ? formatLabel(value) : (label || value.toString());

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label and Value */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">
            {displayLabel}
          </span>
        </div>
        
        {showInput && (
          <input
            type="number"
            value={value}
            onChange={handleInputChange}
            min={min}
            max={max}
            step={step}
            disabled={disabled}
            className={`
              w-20 px-2 py-1 text-sm border border-gray-300 rounded
              focus:ring-2 focus:ring-admin-500 focus:border-admin-500
              ${disabled ? 'opacity-50 cursor-not-allowed bg-gray-50' : ''}
            `}
          />
        )}
      </div>

      {/* Slider Track */}
      <div
        ref={sliderRef}
        className={`
          relative h-2 bg-gray-200 rounded-full cursor-pointer
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-300'}
          transition-colors duration-200
        `}
        onMouseDown={handleMouseDown}
      >
        {/* Progress Fill */}
        <div
          className={`
            absolute top-0 left-0 h-full bg-admin-500 rounded-full
            transition-all duration-200
            ${isDragging ? 'bg-admin-600' : ''}
          `}
          style={{ width: `${percentage}%` }}
        />

        {/* Thumb */}
        <div
          className={`
            absolute top-1/2 w-5 h-5 bg-white border-2 border-admin-500 rounded-full
            transform -translate-y-1/2 -translate-x-1/2 cursor-pointer
            transition-all duration-200 shadow-sm
            ${isDragging 
              ? 'scale-110 border-admin-600 shadow-md' 
              : 'hover:scale-105 hover:shadow-md'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          style={{ left: `${percentage}%` }}
        />
      </div>

      {/* Min/Max Labels */}
      <div className="flex justify-between text-xs text-gray-500">
        <span>{formatLabel ? formatLabel(min) : min}</span>
        <span>{formatLabel ? formatLabel(max) : max}</span>
      </div>
    </div>
  );
};

export default AdminSlider;
