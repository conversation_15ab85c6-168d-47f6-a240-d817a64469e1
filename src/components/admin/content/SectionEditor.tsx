import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Save, Eye, Settings } from 'lucide-react';
import { HomePageSection } from '../../../types/homePageContent';
import AdminCard from '../common/AdminCard';
import AdminButton from '../common/AdminButton';
import AdminForm, { AdminFormField, AdminInput, AdminTextarea, AdminSelect } from '../common/AdminForm';
import AdminToggle from '../common/AdminToggle';
import HeroSectionEditor from './sections/HeroSectionEditor';
import FeaturedProductsSectionEditor from './sections/FeaturedProductsSectionEditor';
import CategoriesSectionEditor from './sections/CategoriesSectionEditor';
import TestimonialsSectionEditor from './sections/TestimonialsSectionEditor';
import NewsletterSectionEditor from './sections/NewsletterSectionEditor';
import AboutBrandSectionEditor from './sections/AboutBrandSectionEditor';
import SpecialOffersSectionEditor from './sections/SpecialOffersSectionEditor';

interface SectionEditorProps {
  section: HomePageSection | undefined;
  onUpdate: (updates: Partial<HomePageSection>) => void;
  onClose: () => void;
}

const SectionEditor: React.FC<SectionEditorProps> = ({
  section,
  onUpdate,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<'content' | 'settings'>('content');
  const [localSection, setLocalSection] = useState<HomePageSection | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (section) {
      setLocalSection({ ...section });
      setHasChanges(false);
    }
  }, [section]);

  if (!section || !localSection) {
    return null;
  }

  const handleFieldChange = (field: string, value: any) => {
    setLocalSection(prev => {
      if (!prev) return null;
      
      const updated = { ...prev, [field]: value };
      setHasChanges(true);
      return updated;
    });
  };

  const handleContentChange = (contentUpdates: any) => {
    setLocalSection(prev => {
      if (!prev) return null;
      
      const updated = {
        ...prev,
        content: { ...prev.content, ...contentUpdates }
      } as HomePageSection;
      setHasChanges(true);
      return updated;
    });
  };

  const handleSave = () => {
    if (localSection && hasChanges) {
      onUpdate(localSection);
      setHasChanges(false);
    }
  };

  const renderSectionEditor = () => {
    switch (section.type) {
      case 'hero':
        return (
          <HeroSectionEditor
            section={localSection as any}
            onChange={handleContentChange}
          />
        );
      case 'featured-products':
        return (
          <FeaturedProductsSectionEditor
            section={localSection as any}
            onChange={handleContentChange}
          />
        );
      case 'categories':
        return (
          <CategoriesSectionEditor
            section={localSection as any}
            onChange={handleContentChange}
          />
        );
      case 'testimonials':
        return (
          <TestimonialsSectionEditor
            section={localSection as any}
            onChange={handleContentChange}
          />
        );
      case 'newsletter':
        return (
          <NewsletterSectionEditor
            section={localSection as any}
            onChange={handleContentChange}
          />
        );
      case 'about-brand':
        return (
          <AboutBrandSectionEditor
            section={localSection as any}
            onChange={handleContentChange}
          />
        );
      case 'special-offers':
        return (
          <SpecialOffersSectionEditor
            section={localSection as any}
            onChange={handleContentChange}
          />
        );
      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">ویرایشگر برای این نوع بخش هنوز آماده نیست</p>
          </div>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="fixed inset-0 z-50 overflow-hidden"
    >
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="absolute inset-y-0 right-0 w-full max-w-4xl bg-white shadow-xl">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                ویرایش بخش: {section.title}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                نوع: {section.type}
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              {hasChanges && (
                <AdminButton
                  variant="primary"
                  icon={Save}
                  onClick={handleSave}
                >
                  ذخیره تغییرات
                </AdminButton>
              )}
              
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex px-6">
              <button
                onClick={() => setActiveTab('content')}
                className={`py-3 px-4 border-b-2 font-medium text-sm ${
                  activeTab === 'content'
                    ? 'border-admin-500 text-admin-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                محتوا
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`py-3 px-4 border-b-2 font-medium text-sm ${
                  activeTab === 'settings'
                    ? 'border-admin-500 text-admin-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                تنظیمات
              </button>
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {activeTab === 'content' ? (
              <div className="p-6">
                {renderSectionEditor()}
              </div>
            ) : (
              <div className="p-6 space-y-6">
                {/* General Settings */}
                <AdminCard title="تنظیمات عمومی">
                  <div className="space-y-4">
                    <AdminFormField label="عنوان بخش" required>
                      <AdminInput
                        value={localSection.title}
                        onChange={(e) => handleFieldChange('title', e.target.value)}
                        placeholder="عنوان بخش را وارد کنید"
                      />
                    </AdminFormField>

                    <AdminFormField label="وضعیت نمایش">
                      <AdminToggle
                        checked={localSection.isActive}
                        onChange={(checked) => handleFieldChange('isActive', checked)}
                        label={localSection.isActive ? 'فعال' : 'غیرفعال'}
                      />
                    </AdminFormField>

                    <AdminFormField label="ترتیب نمایش">
                      <AdminInput
                        type="number"
                        value={localSection.order}
                        onChange={(e) => handleFieldChange('order', parseInt(e.target.value))}
                        min={1}
                      />
                    </AdminFormField>
                  </div>
                </AdminCard>

                {/* Advanced Settings */}
                <AdminCard title="تنظیمات پیشرفته">
                  <div className="space-y-4">
                    <AdminFormField 
                      label="شناسه CSS سفارشی"
                      help="برای استایل‌دهی سفارشی استفاده می‌شود"
                    >
                      <AdminInput
                        value={(localSection as any).customId || ''}
                        onChange={(e) => handleFieldChange('customId', e.target.value)}
                        placeholder="custom-section-id"
                      />
                    </AdminFormField>

                    <AdminFormField 
                      label="کلاس CSS سفارشی"
                      help="کلاس‌های CSS اضافی برای این بخش"
                    >
                      <AdminInput
                        value={(localSection as any).customClass || ''}
                        onChange={(e) => handleFieldChange('customClass', e.target.value)}
                        placeholder="custom-class-name"
                      />
                    </AdminFormField>

                    <AdminFormField 
                      label="انیمیشن ورود"
                      help="نوع انیمیشن هنگام نمایش بخش"
                    >
                      <AdminSelect
                        value={(localSection as any).animation || 'fade'}
                        onChange={(value) => handleFieldChange('animation', value)}
                        options={[
                          { value: 'none', label: 'بدون انیمیشن' },
                          { value: 'fade', label: 'محو شدن' },
                          { value: 'slide', label: 'لغزش' },
                          { value: 'zoom', label: 'زوم' },
                          { value: 'bounce', label: 'پرش' }
                        ]}
                      />
                    </AdminFormField>
                  </div>
                </AdminCard>

                {/* Responsive Settings */}
                <AdminCard title="تنظیمات ریسپانسیو">
                  <div className="space-y-4">
                    <AdminFormField label="نمایش در موبایل">
                      <AdminToggle
                        checked={(localSection as any).showOnMobile !== false}
                        onChange={(checked) => handleFieldChange('showOnMobile', checked)}
                        label="نمایش در دستگاه‌های موبایل"
                      />
                    </AdminFormField>

                    <AdminFormField label="نمایش در تبلت">
                      <AdminToggle
                        checked={(localSection as any).showOnTablet !== false}
                        onChange={(checked) => handleFieldChange('showOnTablet', checked)}
                        label="نمایش در دستگاه‌های تبلت"
                      />
                    </AdminFormField>

                    <AdminFormField label="نمایش در دسکتاپ">
                      <AdminToggle
                        checked={(localSection as any).showOnDesktop !== false}
                        onChange={(checked) => handleFieldChange('showOnDesktop', checked)}
                        label="نمایش در دستگاه‌های دسکتاپ"
                      />
                    </AdminFormField>
                  </div>
                </AdminCard>
              </div>
            )}
          </div>

          {/* Footer */}
          {hasChanges && (
            <div className="border-t border-gray-200 p-6 bg-gray-50">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  تغییرات ذخیره نشده وجود دارد
                </p>
                <div className="flex gap-3">
                  <AdminButton
                    variant="outline"
                    onClick={onClose}
                  >
                    انصراف
                  </AdminButton>
                  <AdminButton
                    variant="primary"
                    icon={Save}
                    onClick={handleSave}
                  >
                    ذخیره تغییرات
                  </AdminButton>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default SectionEditor;
