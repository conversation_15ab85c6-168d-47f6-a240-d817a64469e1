import React from 'react';
import { NewsletterSectionData } from '../../../../types/homePageContent';
import AdminCard from '../../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminTextarea, AdminSelect } from '../../common/AdminForm';
import AdminToggle from '../../common/AdminToggle';

interface NewsletterSectionEditorProps {
  section: NewsletterSectionData;
  onChange: (updates: Partial<NewsletterSectionData['content']>) => void;
}

const NewsletterSectionEditor: React.FC<NewsletterSectionEditorProps> = ({
  section,
  onChange
}) => {
  const handleChange = (field: keyof NewsletterSectionData['content'], value: any) => {
    onChange({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <AdminCard title="محتوای بخش خبرنامه" icon="Mail">
        <div className="space-y-4">
          <AdminFormField label="عنوان بخش" required>
            <AdminInput
              value={section.content.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder="عضویت در خبرنامه"
            />
          </AdminFormField>

          <AdminFormField label="توضیحات">
            <AdminTextarea
              value={section.content.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="از آخرین اخبار و تخفیف‌های ویژه باخبر شوید"
              rows={3}
            />
          </AdminFormField>

          <AdminFormField label="متن فیلد ایمیل">
            <AdminInput
              value={section.content.inputPlaceholder}
              onChange={(e) => handleChange('inputPlaceholder', e.target.value)}
              placeholder="آدرس ایمیل خود را وارد کنید"
            />
          </AdminFormField>

          <AdminFormField label="متن دکمه">
            <AdminInput
              value={section.content.buttonText}
              onChange={(e) => handleChange('buttonText', e.target.value)}
              placeholder="عضویت"
            />
          </AdminFormField>

          <AdminFormField label="پیام موفقیت">
            <AdminInput
              value={section.content.successMessage}
              onChange={(e) => handleChange('successMessage', e.target.value)}
              placeholder="با موفقیت در خبرنامه عضو شدید"
            />
          </AdminFormField>

          <AdminFormField label="استایل نمایش">
            <AdminSelect
              value={section.content.style}
              onChange={(value) => handleChange('style', value)}
              options={[
                { value: 'default', label: 'پیش‌فرض' },
                { value: 'minimal', label: 'مینیمال' },
                { value: 'card', label: 'کارتی' },
                { value: 'fullwidth', label: 'تمام عرض' }
              ]}
            />
          </AdminFormField>
        </div>
      </AdminCard>
    </div>
  );
};

export default NewsletterSectionEditor;
