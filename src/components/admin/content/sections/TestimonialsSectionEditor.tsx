import React from 'react';
import { TestimonialsSectionData } from '../../../../types/homePageContent';
import AdminCard from '../../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminSelect } from '../../common/AdminForm';
import AdminToggle from '../../common/AdminToggle';

interface TestimonialsSectionEditorProps {
  section: TestimonialsSectionData;
  onChange: (updates: Partial<TestimonialsSectionData['content']>) => void;
}

const TestimonialsSectionEditor: React.FC<TestimonialsSectionEditorProps> = ({
  section,
  onChange
}) => {
  const handleChange = (field: keyof TestimonialsSectionData['content'], value: any) => {
    onChange({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <AdminCard title="محتوای بخش نظرات" icon="MessageSquare">
        <div className="space-y-4">
          <AdminFormField label="عنوان بخش" required>
            <AdminInput
              value={section.content.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder="نظرات مشتریان"
            />
          </AdminFormField>

          <AdminFormField label="نوع نمایش">
            <AdminSelect
              value={section.content.displayType}
              onChange={(value) => handleChange('displayType', value)}
              options={[
                { value: 'carousel', label: 'اسلایدر' },
                { value: 'grid', label: 'شبکه‌ای' },
                { value: 'masonry', label: 'آجری' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="تعداد نظر در هر نمایش">
            <AdminSelect
              value={section.content.testimonialsPerView}
              onChange={(value) => handleChange('testimonialsPerView', parseInt(value))}
              options={[
                { value: '1', label: '1 نظر' },
                { value: '2', label: '2 نظر' },
                { value: '3', label: '3 نظر' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="پخش خودکار">
            <AdminToggle
              checked={section.content.autoplay}
              onChange={(checked) => handleChange('autoplay', checked)}
              label="اسلایدر به صورت خودکار حرکت کند"
            />
          </AdminFormField>

          <AdminFormField label="نمایش دکمه‌های ناوبری">
            <AdminToggle
              checked={section.content.showNavigation}
              onChange={(checked) => handleChange('showNavigation', checked)}
              label="دکمه‌های قبلی/بعدی نمایش داده شود"
            />
          </AdminFormField>

          <AdminFormField label="نمایش نقاط صفحه‌بندی">
            <AdminToggle
              checked={section.content.showPagination}
              onChange={(checked) => handleChange('showPagination', checked)}
              label="نقاط صفحه‌بندی نمایش داده شود"
            />
          </AdminFormField>
        </div>
      </AdminCard>
    </div>
  );
};

export default TestimonialsSectionEditor;
