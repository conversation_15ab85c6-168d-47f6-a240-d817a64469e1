import React from 'react';
import { CategoriesSectionData } from '../../../../types/homePageContent';
import AdminCard from '../../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminTextarea, AdminSelect } from '../../common/AdminForm';
import AdminToggle from '../../common/AdminToggle';

interface CategoriesSectionEditorProps {
  section: CategoriesSectionData;
  onChange: (updates: Partial<CategoriesSectionData['content']>) => void;
}

const CategoriesSectionEditor: React.FC<CategoriesSectionEditorProps> = ({
  section,
  onChange
}) => {
  const handleChange = (field: keyof CategoriesSectionData['content'], value: any) => {
    onChange({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <AdminCard title="محتوای بخش دسته‌بندی‌ها" icon="Grid">
        <div className="space-y-4">
          <AdminFormField label="عنوان بخش" required>
            <AdminInput
              value={section.content.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder="دسته‌بندی محصولات"
            />
          </AdminFormField>

          <AdminFormField label="زیرعنوان">
            <AdminInput
              value={section.content.subtitle || ''}
              onChange={(e) => handleChange('subtitle', e.target.value)}
              placeholder="انتخاب بر اساس نیاز پوست شما"
            />
          </AdminFormField>

          <AdminFormField label="نوع نمایش">
            <AdminSelect
              value={section.content.displayType}
              onChange={(value) => handleChange('displayType', value)}
              options={[
                { value: 'grid', label: 'شبکه‌ای' },
                { value: 'carousel', label: 'اسلایدر' },
                { value: 'masonry', label: 'آجری' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="تعداد دسته‌بندی در هر ردیف">
            <AdminSelect
              value={section.content.categoriesPerRow}
              onChange={(value) => handleChange('categoriesPerRow', parseInt(value))}
              options={[
                { value: '2', label: '2 دسته‌بندی' },
                { value: '3', label: '3 دسته‌بندی' },
                { value: '4', label: '4 دسته‌بندی' },
                { value: '6', label: '6 دسته‌بندی' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="نمایش تعداد محصولات">
            <AdminToggle
              checked={section.content.showProductCount}
              onChange={(checked) => handleChange('showProductCount', checked)}
              label="تعداد محصولات هر دسته‌بندی نمایش داده شود"
            />
          </AdminFormField>

          <AdminFormField label="استایل تصاویر">
            <AdminSelect
              value={section.content.imageStyle}
              onChange={(value) => handleChange('imageStyle', value)}
              options={[
                { value: 'square', label: 'مربعی' },
                { value: 'circle', label: 'دایره‌ای' },
                { value: 'rounded', label: 'گوشه گرد' }
              ]}
            />
          </AdminFormField>
        </div>
      </AdminCard>
    </div>
  );
};

export default CategoriesSectionEditor;
