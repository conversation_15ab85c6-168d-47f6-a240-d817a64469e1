import React from 'react';
import { Search, Globe, Image, AlertCircle, CheckCircle } from 'lucide-react';
import AdminCard from '../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminTextarea } from '../common/AdminForm';
import AdminFileUpload from '../common/AdminFileUpload';
import AdminTagInput from '../common/AdminTagInput';

interface SEOSettingsProps {
  settings: {
    title: string;
    description: string;
    keywords: string[];
    ogImage?: string;
  } | undefined;
  onChange: (settings: any) => void;
}

const SEOSettings: React.FC<SEOSettingsProps> = ({
  settings,
  onChange
}) => {
  if (!settings) return null;

  const handleChange = (field: string, value: any) => {
    onChange({ ...settings, [field]: value });
  };

  const getTitleLength = () => settings.title.length;
  const getDescriptionLength = () => settings.description.length;

  const getTitleStatus = () => {
    const length = getTitleLength();
    if (length === 0) return { type: 'error', message: 'عنوان الزامی است' };
    if (length < 30) return { type: 'warning', message: 'عنوان کوتاه است' };
    if (length > 60) return { type: 'warning', message: 'عنوان طولانی است' };
    return { type: 'success', message: 'طول عنوان مناسب است' };
  };

  const getDescriptionStatus = () => {
    const length = getDescriptionLength();
    if (length === 0) return { type: 'error', message: 'توضیحات الزامی است' };
    if (length < 120) return { type: 'warning', message: 'توضیحات کوتاه است' };
    if (length > 160) return { type: 'warning', message: 'توضیحات طولانی است' };
    return { type: 'success', message: 'طول توضیحات مناسب است' };
  };

  const titleStatus = getTitleStatus();
  const descriptionStatus = getDescriptionStatus();

  return (
    <div className="space-y-6">
      {/* Basic SEO */}
      <AdminCard title="تنظیمات پایه SEO" icon={Search}>
        <div className="space-y-4">
          <AdminFormField 
            label="عنوان صفحه (Title Tag)" 
            required
            help="عنوان اصلی که در نتایج جستجو و تب مرورگر نمایش داده می‌شود"
          >
            <AdminInput
              value={settings.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder="فروشگاه آنلاین محصولات مراقبت از پوست گلورویا"
              maxLength={60}
            />
            <div className="flex items-center justify-between mt-2">
              <div className={`flex items-center gap-2 text-sm ${
                titleStatus.type === 'error' ? 'text-red-600' :
                titleStatus.type === 'warning' ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {titleStatus.type === 'error' ? <AlertCircle className="w-4 h-4" /> :
                 titleStatus.type === 'warning' ? <AlertCircle className="w-4 h-4" /> :
                 <CheckCircle className="w-4 h-4" />}
                <span>{titleStatus.message}</span>
              </div>
              <span className={`text-sm ${
                getTitleLength() > 60 ? 'text-red-600' : 'text-gray-500'
              }`}>
                {getTitleLength()}/60
              </span>
            </div>
          </AdminFormField>

          <AdminFormField 
            label="توضیحات صفحه (Meta Description)" 
            required
            help="توضیح کوتاه که در نتایج جستجو نمایش داده می‌شود"
          >
            <AdminTextarea
              value={settings.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="بهترین محصولات مراقبت از پوست، سرم، کرم و ماسک با کیفیت بالا و قیمت مناسب. ارسال رایگان به سراسر کشور."
              rows={3}
              maxLength={160}
            />
            <div className="flex items-center justify-between mt-2">
              <div className={`flex items-center gap-2 text-sm ${
                descriptionStatus.type === 'error' ? 'text-red-600' :
                descriptionStatus.type === 'warning' ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {descriptionStatus.type === 'error' ? <AlertCircle className="w-4 h-4" /> :
                 descriptionStatus.type === 'warning' ? <AlertCircle className="w-4 h-4" /> :
                 <CheckCircle className="w-4 h-4" />}
                <span>{descriptionStatus.message}</span>
              </div>
              <span className={`text-sm ${
                getDescriptionLength() > 160 ? 'text-red-600' : 'text-gray-500'
              }`}>
                {getDescriptionLength()}/160
              </span>
            </div>
          </AdminFormField>

          <AdminFormField 
            label="کلمات کلیدی" 
            help="کلمات مهم مرتبط با محتوای سایت (حداکثر 10 کلمه)"
          >
            <AdminTagInput
              value={settings.keywords}
              onChange={(keywords) => handleChange('keywords', keywords)}
              placeholder="کلمه کلیدی را وارد کنید و Enter بزنید"
              maxTags={10}
              suggestions={[
                'مراقبت از پوست',
                'سرم',
                'کرم',
                'ماسک',
                'زیبایی',
                'آرایشی',
                'بهداشتی',
                'طبیعی',
                'ارگانیک',
                'ضد پیری'
              ]}
            />
            <p className="text-xs text-gray-500 mt-1">
              {settings.keywords.length}/10 کلمه
            </p>
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Social Media SEO */}
      <AdminCard title="تنظیمات شبکه‌های اجتماعی" icon={Globe}>
        <div className="space-y-4">
          <AdminFormField 
            label="تصویر شبکه‌های اجتماعی (OG Image)"
            help="تصویری که هنگام اشتراک‌گذاری در شبکه‌های اجتماعی نمایش داده می‌شود. ابعاد توصیه شده: 1200x630 پیکسل"
          >
            <AdminFileUpload
              value={settings.ogImage || ''}
              onChange={(file, url) => handleChange('ogImage', url)}
              accept="image/*"
              aspectRatio="1200:630"
              placeholder="تصویر شبکه‌های اجتماعی را انتخاب کنید"
            />
          </AdminFormField>

          {/* Preview */}
          {settings.title && settings.description && (
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                پیش‌نمایش در شبکه‌های اجتماعی:
              </h4>
              <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div className="flex gap-4">
                  {settings.ogImage && (
                    <div className="w-24 h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                      <img 
                        src={settings.ogImage} 
                        alt="OG Preview" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h5 className="font-medium text-gray-900 truncate">
                      {settings.title}
                    </h5>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {settings.description}
                    </p>
                    <p className="text-xs text-gray-500 mt-2">
                      glowroya.com
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </AdminCard>

      {/* Advanced SEO */}
      <AdminCard title="تنظیمات پیشرفته" icon={Search}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <AdminFormField label="Canonical URL">
              <AdminInput
                value={(settings as any).canonicalUrl || ''}
                onChange={(e) => handleChange('canonicalUrl', e.target.value)}
                placeholder="https://glowroya.com/"
              />
            </AdminFormField>

            <AdminFormField label="Robots Meta Tag">
              <AdminInput
                value={(settings as any).robots || 'index, follow'}
                onChange={(e) => handleChange('robots', e.target.value)}
                placeholder="index, follow"
              />
            </AdminFormField>
          </div>

          <AdminFormField 
            label="Schema Markup"
            help="کد JSON-LD برای بهبود نمایش در نتایج جستجو"
          >
            <AdminTextarea
              value={(settings as any).schemaMarkup || ''}
              onChange={(e) => handleChange('schemaMarkup', e.target.value)}
              placeholder='{"@context": "https://schema.org", "@type": "WebSite", ...}'
              rows={6}
              className="font-mono text-sm"
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* SEO Score */}
      <AdminCard title="امتیاز SEO" icon={CheckCircle}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">85</div>
              <div className="text-sm text-green-700">امتیاز کلی</div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">92</div>
              <div className="text-sm text-blue-700">محتوا</div>
            </div>
            
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600 mb-1">78</div>
              <div className="text-sm text-yellow-700">تکنیکی</div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                عنوان صفحه مناسب است
              </span>
              <span className="text-green-600">✓</span>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                توضیحات متا مناسب است
              </span>
              <span className="text-green-600">✓</span>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-yellow-500" />
                تصویر OG تنظیم نشده
              </span>
              <span className="text-yellow-600">!</span>
            </div>
          </div>
        </div>
      </AdminCard>
    </div>
  );
};

export default SEOSettings;
