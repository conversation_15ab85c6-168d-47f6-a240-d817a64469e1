import React, { useState } from 'react';
import { motion, Reorder } from 'framer-motion';
import { 
  GripVertical, 
  Eye, 
  EyeOff, 
  Edit, 
  Trash2, 
  Plus,
  Image,
  Star,
  Grid3X3,
  MessageSquare,
  Mail,
  Info,
  Tag,
  Copy
} from 'lucide-react';
import { HomePageSection, SectionType } from '../../../types/homePageContent';
import AdminButton from '../common/AdminButton';
import AdminModal from '../common/AdminModal';
import { useHomePageContent } from '../../../hooks/useHomePageContent';
import toast from 'react-hot-toast';

interface SectionListProps {
  sections: HomePageSection[];
  selectedSectionId: string | null;
  onSelectSection: (sectionId: string | null) => void;
}

const SectionList: React.FC<SectionListProps> = ({
  sections,
  selectedSectionId,
  onSelectSection
}) => {
  const { 
    addSection, 
    removeSection, 
    reorderSections, 
    updateSection,
    sectionTemplates 
  } = useHomePageContent();
  
  const [showAddModal, setShowAddModal] = useState(false);
  const [sectionToDelete, setSectionToDelete] = useState<string | null>(null);

  const getSectionIcon = (type: SectionType) => {
    switch (type) {
      case 'hero': return Image;
      case 'featured-products': return Star;
      case 'categories': return Grid3X3;
      case 'testimonials': return MessageSquare;
      case 'newsletter': return Mail;
      case 'about-brand': return Info;
      case 'special-offers': return Tag;
      default: return Grid3X3;
    }
  };

  const getSectionTypeLabel = (type: SectionType): string => {
    const labels: Record<SectionType, string> = {
      'hero': 'بخش اصلی',
      'featured-products': 'محصولات ویژه',
      'categories': 'دسته‌بندی‌ها',
      'testimonials': 'نظرات مشتریان',
      'newsletter': 'خبرنامه',
      'about-brand': 'درباره برند',
      'special-offers': 'پیشنهادات ویژه',
      'new-products': 'محصولات جدید',
      'discounted-products': 'محصولات تخفیف‌دار'
    };
    return labels[type] || type;
  };

  const handleReorder = (newOrder: HomePageSection[]) => {
    const oldOrder = sections.map(s => s.order);
    const newOrderValues = newOrder.map((_, index) => index + 1);
    
    if (JSON.stringify(oldOrder) !== JSON.stringify(newOrderValues)) {
      newOrder.forEach((section, index) => {
        if (section.order !== index + 1) {
          reorderSections(section.order - 1, index);
        }
      });
    }
  };

  const handleToggleVisibility = (sectionId: string, isActive: boolean) => {
    updateSection(sectionId, { isActive: !isActive });
    toast.success(isActive ? 'بخش مخفی شد' : 'بخش نمایش داده می‌شود');
  };

  const handleDuplicateSection = (section: HomePageSection) => {
    const duplicatedSection = {
      ...section,
      id: `${section.id}-copy-${Date.now()}`,
      title: `${section.title} (کپی)`,
      order: sections.length + 1
    };
    
    addSection(duplicatedSection);
    toast.success('بخش کپی شد');
  };

  const handleDeleteSection = (sectionId: string) => {
    removeSection(sectionId);
    setSectionToDelete(null);
    if (selectedSectionId === sectionId) {
      onSelectSection(null);
    }
    toast.success('بخش حذف شد');
  };

  const handleAddSection = (templateId: string) => {
    const template = sectionTemplates.find(t => t.id === templateId);
    if (!template) return;

    const newSection: Partial<HomePageSection> = {
      type: template.type,
      title: template.name,
      isActive: true,
      ...template.defaultData
    };

    addSection(newSection);
    setShowAddModal(false);
    toast.success('بخش جدید اضافه شد');
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">بخش‌های صفحه</h3>
          <p className="text-sm text-gray-600">
            {sections.length} بخش • {sections.filter(s => s.isActive).length} فعال
          </p>
        </div>
        <AdminButton
          variant="primary"
          icon={Plus}
          onClick={() => setShowAddModal(true)}
        >
          افزودن بخش
        </AdminButton>
      </div>

      {/* Sections List */}
      {sections.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <Grid3X3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">هیچ بخشی وجود ندارد</h3>
          <p className="text-gray-600 mb-4">برای شروع، اولین بخش صفحه اصلی را اضافه کنید</p>
          <AdminButton
            variant="primary"
            icon={Plus}
            onClick={() => setShowAddModal(true)}
          >
            افزودن اولین بخش
          </AdminButton>
        </div>
      ) : (
        <Reorder.Group
          axis="y"
          values={sections}
          onReorder={handleReorder}
          className="space-y-3"
        >
          {sections.map((section) => {
            const Icon = getSectionIcon(section.type);
            const isSelected = selectedSectionId === section.id;
            
            return (
              <Reorder.Item
                key={section.id}
                value={section}
                className={`
                  bg-white border rounded-lg p-4 cursor-pointer transition-all duration-200
                  ${isSelected 
                    ? 'border-admin-500 shadow-md ring-2 ring-admin-500 ring-opacity-20' 
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                  }
                  ${!section.isActive ? 'opacity-60' : ''}
                `}
                onClick={() => onSelectSection(section.id)}
              >
                <div className="flex items-center gap-4">
                  {/* Drag Handle */}
                  <div className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600">
                    <GripVertical className="w-5 h-5" />
                  </div>

                  {/* Section Icon */}
                  <div className={`
                    p-2 rounded-lg
                    ${isSelected ? 'bg-admin-100 text-admin-600' : 'bg-gray-100 text-gray-600'}
                  `}>
                    <Icon className="w-5 h-5" />
                  </div>

                  {/* Section Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-gray-900 truncate">
                        {section.title}
                      </h4>
                      <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                        {getSectionTypeLabel(section.type)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      آخرین ویرایش: {new Date(section.lastModified).toLocaleDateString('fa-IR')}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleVisibility(section.id, section.isActive);
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                      title={section.isActive ? 'مخفی کردن' : 'نمایش'}
                    >
                      {section.isActive ? (
                        <Eye className="w-4 h-4" />
                      ) : (
                        <EyeOff className="w-4 h-4" />
                      )}
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDuplicateSection(section);
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                      title="کپی کردن"
                    >
                      <Copy className="w-4 h-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onSelectSection(section.id);
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                      title="ویرایش"
                    >
                      <Edit className="w-4 h-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSectionToDelete(section.id);
                      }}
                      className="p-2 text-red-400 hover:text-red-600 rounded-lg hover:bg-red-50"
                      title="حذف"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </Reorder.Item>
            );
          })}
        </Reorder.Group>
      )}

      {/* Add Section Modal */}
      <AdminModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="افزودن بخش جدید"
        size="lg"
      >
        <div className="space-y-6">
          <p className="text-gray-600">
            یکی از قالب‌های آماده را انتخاب کنید یا بخش سفارشی ایجاد کنید
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {sectionTemplates.map((template) => {
              const Icon = getSectionIcon(template.type);
              
              return (
                <button
                  key={template.id}
                  onClick={() => handleAddSection(template.id)}
                  className="p-4 border border-gray-200 rounded-lg hover:border-admin-300 hover:shadow-sm transition-all text-left"
                >
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-admin-100 text-admin-600 rounded-lg">
                      <Icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 mb-1">
                        {template.name}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {template.description}
                      </p>
                      {template.isPopular && (
                        <span className="inline-block mt-2 text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full">
                          محبوب
                        </span>
                      )}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </AdminModal>

      {/* Delete Confirmation Modal */}
      <AdminModal
        isOpen={!!sectionToDelete}
        onClose={() => setSectionToDelete(null)}
        title="حذف بخش"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            آیا مطمئن هستید که می‌خواهید این بخش را حذف کنید؟ این عمل قابل بازگشت نیست.
          </p>
          
          <div className="flex gap-3 justify-end">
            <AdminButton
              variant="outline"
              onClick={() => setSectionToDelete(null)}
            >
              انصراف
            </AdminButton>
            <AdminButton
              variant="danger"
              onClick={() => sectionToDelete && handleDeleteSection(sectionToDelete)}
            >
              حذف
            </AdminButton>
          </div>
        </div>
      </AdminModal>
    </div>
  );
};

export default SectionList;
