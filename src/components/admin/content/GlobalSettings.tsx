import React from 'react';
import { Settings, Zap, Smartphone, Globe, Palette, Code } from 'lucide-react';
import AdminCard from '../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminTextarea, AdminSelect } from '../common/AdminForm';
import AdminToggle from '../common/AdminToggle';

interface GlobalSettingsProps {
  settings: {
    enableAnimations: boolean;
    lazyLoading: boolean;
    mobileOptimized: boolean;
  } | undefined;
  onChange: (settings: any) => void;
}

const GlobalSettings: React.FC<GlobalSettingsProps> = ({
  settings,
  onChange
}) => {
  if (!settings) return null;

  const handleChange = (field: string, value: any) => {
    onChange({ ...settings, [field]: value });
  };

  return (
    <div className="space-y-6">
      {/* Performance Settings */}
      <AdminCard title="تنظیمات عملکرد" icon={Zap}>
        <div className="space-y-4">
          <AdminFormField 
            label="انیمیشن‌ها"
            help="فعال/غیرفعال کردن انیمیشن‌های صفحه"
          >
            <AdminToggle
              checked={settings.enableAnimations}
              onChange={(checked) => handleChange('enableAnimations', checked)}
              label={settings.enableAnimations ? 'انیمیشن‌ها فعال' : 'انیمیشن‌ها غیرفعال'}
            />
          </AdminFormField>

          <AdminFormField 
            label="بارگذاری تنبل (Lazy Loading)"
            help="بارگذاری تصاویر و محتوا هنگام نیاز"
          >
            <AdminToggle
              checked={settings.lazyLoading}
              onChange={(checked) => handleChange('lazyLoading', checked)}
              label={settings.lazyLoading ? 'بارگذاری تنبل فعال' : 'بارگذاری تنبل غیرفعال'}
            />
          </AdminFormField>

          <AdminFormField 
            label="بهینه‌سازی موبایل"
            help="بهینه‌سازی خاص برای دستگاه‌های موبایل"
          >
            <AdminToggle
              checked={settings.mobileOptimized}
              onChange={(checked) => handleChange('mobileOptimized', checked)}
              label={settings.mobileOptimized ? 'بهینه‌سازی موبایل فعال' : 'بهینه‌سازی موبایل غیرفعال'}
            />
          </AdminFormField>

          <AdminFormField 
            label="کیفیت تصاویر"
            help="کیفیت پیش‌فرض تصاویر در سایت"
          >
            <AdminSelect
              value={(settings as any).imageQuality || 'high'}
              onChange={(value) => handleChange('imageQuality', value)}
              options={[
                { value: 'low', label: 'پایین (سریع‌تر)' },
                { value: 'medium', label: 'متوسط' },
                { value: 'high', label: 'بالا (کیفیت بهتر)' },
                { value: 'auto', label: 'خودکار' }
              ]}
            />
          </AdminFormField>

          <AdminFormField 
            label="حافظه نهان (Cache)"
            help="مدت زمان نگهداری فایل‌ها در حافظه نهان"
          >
            <AdminSelect
              value={(settings as any).cacheTimeout || '1hour'}
              onChange={(value) => handleChange('cacheTimeout', value)}
              options={[
                { value: '15min', label: '15 دقیقه' },
                { value: '1hour', label: '1 ساعت' },
                { value: '6hours', label: '6 ساعت' },
                { value: '1day', label: '1 روز' },
                { value: '1week', label: '1 هفته' }
              ]}
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Mobile Settings */}
      <AdminCard title="تنظیمات موبایل" icon={Smartphone}>
        <div className="space-y-4">
          <AdminFormField 
            label="نمایش منوی همبرگری"
            help="نمایش منوی همبرگری در دستگاه‌های موبایل"
          >
            <AdminToggle
              checked={(settings as any).showMobileMenu !== false}
              onChange={(checked) => handleChange('showMobileMenu', checked)}
              label="منوی موبایل فعال"
            />
          </AdminFormField>

          <AdminFormField 
            label="سایز فونت موبایل"
            help="اندازه پیش‌فرض فونت در موبایل"
          >
            <AdminSelect
              value={(settings as any).mobileFontSize || 'medium'}
              onChange={(value) => handleChange('mobileFontSize', value)}
              options={[
                { value: 'small', label: 'کوچک' },
                { value: 'medium', label: 'متوسط' },
                { value: 'large', label: 'بزرگ' }
              ]}
            />
          </AdminFormField>

          <AdminFormField 
            label="فاصله بین عناصر"
            help="فاصله بین عناصر در نمایش موبایل"
          >
            <AdminSelect
              value={(settings as any).mobileSpacing || 'normal'}
              onChange={(value) => handleChange('mobileSpacing', value)}
              options={[
                { value: 'compact', label: 'فشرده' },
                { value: 'normal', label: 'عادی' },
                { value: 'comfortable', label: 'راحت' }
              ]}
            />
          </AdminFormField>

          <AdminFormField 
            label="نمایش دکمه بازگشت به بالا"
            help="نمایش دکمه شناور برای بازگشت به بالای صفحه"
          >
            <AdminToggle
              checked={(settings as any).showBackToTop !== false}
              onChange={(checked) => handleChange('showBackToTop', checked)}
              label="دکمه بازگشت به بالا فعال"
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Accessibility Settings */}
      <AdminCard title="تنظیمات دسترسی" icon={Globe}>
        <div className="space-y-4">
          <AdminFormField 
            label="پشتیبانی از صفحه‌خوان"
            help="بهینه‌سازی برای نرم‌افزارهای صفحه‌خوان"
          >
            <AdminToggle
              checked={(settings as any).screenReaderSupport !== false}
              onChange={(checked) => handleChange('screenReaderSupport', checked)}
              label="پشتیبانی از صفحه‌خوان فعال"
            />
          </AdminFormField>

          <AdminFormField 
            label="کنتراست بالا"
            help="حالت کنتراست بالا برای کاربران کم‌بینا"
          >
            <AdminToggle
              checked={(settings as any).highContrast === true}
              onChange={(checked) => handleChange('highContrast', checked)}
              label="کنتراست بالا فعال"
            />
          </AdminFormField>

          <AdminFormField 
            label="اندازه فونت قابل تنظیم"
            help="امکان تغییر اندازه فونت توسط کاربر"
          >
            <AdminToggle
              checked={(settings as any).adjustableFontSize !== false}
              onChange={(checked) => handleChange('adjustableFontSize', checked)}
              label="تنظیم اندازه فونت فعال"
            />
          </AdminFormField>

          <AdminFormField 
            label="کلیدهای میانبر"
            help="فعال کردن کلیدهای میانبر کیبورد"
          >
            <AdminToggle
              checked={(settings as any).keyboardShortcuts !== false}
              onChange={(checked) => handleChange('keyboardShortcuts', checked)}
              label="کلیدهای میانبر فعال"
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Theme Settings */}
      <AdminCard title="تنظیمات ظاهری" icon={Palette}>
        <div className="space-y-4">
          <AdminFormField 
            label="حالت تاریک"
            help="پشتیبانی از حالت تاریک سایت"
          >
            <AdminToggle
              checked={(settings as any).darkModeSupport === true}
              onChange={(checked) => handleChange('darkModeSupport', checked)}
              label="حالت تاریک فعال"
            />
          </AdminFormField>

          <AdminFormField 
            label="رنگ اصلی سایت"
            help="رنگ اصلی که در سراسر سایت استفاده می‌شود"
          >
            <AdminInput
              type="color"
              value={(settings as any).primaryColor || '#3b82f6'}
              onChange={(e) => handleChange('primaryColor', e.target.value)}
            />
          </AdminFormField>

          <AdminFormField 
            label="فونت اصلی"
            help="فونت پیش‌فرض سایت"
          >
            <AdminSelect
              value={(settings as any).primaryFont || 'vazir'}
              onChange={(value) => handleChange('primaryFont', value)}
              options={[
                { value: 'vazir', label: 'وزیر' },
                { value: 'iran-sans', label: 'ایران سنس' },
                { value: 'shabnam', label: 'شبنم' },
                { value: 'yekan', label: 'یکان' }
              ]}
            />
          </AdminFormField>

          <AdminFormField 
            label="شعاع گوشه‌ها"
            help="میزان گردی گوشه‌های عناصر"
          >
            <AdminSelect
              value={(settings as any).borderRadius || 'medium'}
              onChange={(value) => handleChange('borderRadius', value)}
              options={[
                { value: 'none', label: 'بدون گردی' },
                { value: 'small', label: 'کم' },
                { value: 'medium', label: 'متوسط' },
                { value: 'large', label: 'زیاد' }
              ]}
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Advanced Settings */}
      <AdminCard title="تنظیمات پیشرفته" icon={Code}>
        <div className="space-y-4">
          <AdminFormField 
            label="کد Google Analytics"
            help="شناسه Google Analytics برای ردیابی بازدیدکنندگان"
          >
            <AdminInput
              value={(settings as any).googleAnalyticsId || ''}
              onChange={(e) => handleChange('googleAnalyticsId', e.target.value)}
              placeholder="GA-XXXXXXXXX-X"
            />
          </AdminFormField>

          <AdminFormField 
            label="کد Google Tag Manager"
            help="شناسه Google Tag Manager"
          >
            <AdminInput
              value={(settings as any).googleTagManagerId || ''}
              onChange={(e) => handleChange('googleTagManagerId', e.target.value)}
              placeholder="GTM-XXXXXXX"
            />
          </AdminFormField>

          <AdminFormField 
            label="کد سفارشی Header"
            help="کد HTML/CSS/JS که در بخش head قرار می‌گیرد"
          >
            <AdminTextarea
              value={(settings as any).customHeaderCode || ''}
              onChange={(e) => handleChange('customHeaderCode', e.target.value)}
              placeholder="<script>...</script>"
              rows={4}
              className="font-mono text-sm"
            />
          </AdminFormField>

          <AdminFormField 
            label="کد سفارشی Footer"
            help="کد HTML/CSS/JS که در انتهای صفحه قرار می‌گیرد"
          >
            <AdminTextarea
              value={(settings as any).customFooterCode || ''}
              onChange={(e) => handleChange('customFooterCode', e.target.value)}
              placeholder="<script>...</script>"
              rows={4}
              className="font-mono text-sm"
            />
          </AdminFormField>

          <AdminFormField 
            label="حالت توسعه"
            help="فعال کردن ابزارهای توسعه و دیباگ"
          >
            <AdminToggle
              checked={(settings as any).developmentMode === true}
              onChange={(checked) => handleChange('developmentMode', checked)}
              label="حالت توسعه فعال"
            />
          </AdminFormField>
        </div>
      </AdminCard>
    </div>
  );
};

export default GlobalSettings;
