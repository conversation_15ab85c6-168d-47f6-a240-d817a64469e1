import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Eye, Upload, X, ExternalLink, FileText, Settings, Search } from 'lucide-react';
import AdminForm, { 
  AdminFormField, 
  AdminInput, 
  AdminTextarea, 
  AdminSelect, 
  AdminCheckbox,
  AdminFormAlert 
} from '../common/AdminForm';
import AdminButton from '../common/AdminButton';
import AdminCard from '../common/AdminCard';
import { PageContentFormData, ContentStatus } from '../../../types/adminContent';
import { validatePageForm } from '../../../utils/contentUtils';

interface PageEditorProps {
  initialData?: Partial<PageContentFormData>;
  onSave: (data: PageContentFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
}

const PageEditor: React.FC<PageEditorProps> = ({
  initialData,
  onSave,
  onCancel,
  loading = false,
  mode = 'create'
}) => {
  const [formData, setFormData] = useState<PageContentFormData>({
    title: '',
    content: '',
    excerpt: '',
    template: 'default',
    parentPage: '',
    menuOrder: 0,
    showInMenu: true,
    metaTitle: '',
    metaDescription: '',
    canonicalUrl: '',
    noIndex: false,
    noFollow: false,
    featuredImage: '',
    featuredImageAlt: '',
    customFields: {},
    status: 'draft',
    isActive: true,
    startDate: '',
    endDate: '',
    seoTitle: '',
    seoDescription: '',
    seoKeywords: [],
    ...initialData
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'content' | 'seo' | 'settings'>('content');

  const handleInputChange = (field: keyof PageContentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear errors when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validatePageForm(formData);
    
    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message;
      });
      setErrors(errorMap);
      
      const warningMap: Record<string, string> = {};
      validation.warnings.forEach(warning => {
        warningMap[warning.field] = warning.message;
      });
      setWarnings(warningMap);
      
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving page:', error);
    }
  };

  const statusOptions = [
    { value: 'draft', label: 'پیش‌نویس' },
    { value: 'published', label: 'منتشر شده' },
    { value: 'scheduled', label: 'زمان‌بندی شده' },
    { value: 'archived', label: 'بایگانی شده' }
  ];

  const templateOptions = [
    { value: 'default', label: 'پیش‌فرض' },
    { value: 'landing', label: 'صفحه فرود' },
    { value: 'about', label: 'درباره ما' },
    { value: 'contact', label: 'تماس با ما' },
    { value: 'custom', label: 'سفارشی' }
  ];

  const tabs = [
    { id: 'content', label: 'محتوا', icon: FileText },
    { id: 'seo', label: 'سئو', icon: Search },
    { id: 'settings', label: 'تنظیمات', icon: Settings }
  ];

  return (
    <div className="max-w-7xl mx-auto">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8 space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-admin-500 text-admin-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      <AdminForm onSubmit={handleSubmit} loading={loading}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Content Tab */}
            {activeTab === 'content' && (
              <AdminCard title="محتوای صفحه">
                <div className="space-y-4">
                  <AdminFormField label="عنوان صفحه" required error={errors.title}>
                    <AdminInput
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="عنوان صفحه را وارد کنید"
                      error={!!errors.title}
                    />
                  </AdminFormField>

                  <AdminFormField label="خلاصه" error={errors.excerpt}>
                    <AdminTextarea
                      value={formData.excerpt || ''}
                      onChange={(e) => handleInputChange('excerpt', e.target.value)}
                      placeholder="خلاصه‌ای از محتوای صفحه"
                      rows={3}
                      error={!!errors.excerpt}
                    />
                  </AdminFormField>

                  <AdminFormField label="محتوای صفحه" required error={errors.content}>
                    <AdminTextarea
                      value={formData.content}
                      onChange={(e) => handleInputChange('content', e.target.value)}
                      placeholder="محتوای کامل صفحه را وارد کنید"
                      rows={12}
                      error={!!errors.content}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      از HTML و Markdown پشتیبانی می‌شود
                    </div>
                  </AdminFormField>

                  <AdminFormField label="تصویر شاخص" error={errors.featuredImage}>
                    <div className="space-y-2">
                      <AdminInput
                        value={formData.featuredImage || ''}
                        onChange={(e) => handleInputChange('featuredImage', e.target.value)}
                        placeholder="URL تصویر شاخص"
                        error={!!errors.featuredImage}
                      />
                      <AdminButton variant="outline" icon={Upload} size="sm">
                        انتخاب تصویر
                      </AdminButton>
                    </div>
                  </AdminFormField>

                  {formData.featuredImage && (
                    <AdminFormField label="متن جایگزین تصویر" error={errors.featuredImageAlt}>
                      <AdminInput
                        value={formData.featuredImageAlt || ''}
                        onChange={(e) => handleInputChange('featuredImageAlt', e.target.value)}
                        placeholder="توضیح تصویر برای بهبود دسترسی"
                        error={!!errors.featuredImageAlt}
                      />
                    </AdminFormField>
                  )}
                </div>
              </AdminCard>
            )}

            {/* SEO Tab */}
            {activeTab === 'seo' && (
              <AdminCard title="تنظیمات سئو" icon={Search}>
                <div className="space-y-4">
                  <AdminFormField label="عنوان سئو" error={errors.metaTitle}>
                    <AdminInput
                      value={formData.metaTitle || ''}
                      onChange={(e) => handleInputChange('metaTitle', e.target.value)}
                      placeholder="عنوان برای موتورهای جستجو"
                      error={!!errors.metaTitle}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      توصیه می‌شود کمتر از 60 کاراکتر باشد
                    </div>
                  </AdminFormField>

                  <AdminFormField label="توضیحات سئو" error={errors.metaDescription}>
                    <AdminTextarea
                      value={formData.metaDescription || ''}
                      onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                      placeholder="توضیح مختصر برای موتورهای جستجو"
                      rows={3}
                      error={!!errors.metaDescription}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      توصیه می‌شود بین 150-160 کاراکتر باشد
                    </div>
                  </AdminFormField>

                  <AdminFormField label="URL کانونیکال" error={errors.canonicalUrl}>
                    <AdminInput
                      value={formData.canonicalUrl || ''}
                      onChange={(e) => handleInputChange('canonicalUrl', e.target.value)}
                      placeholder="https://example.com/page"
                      error={!!errors.canonicalUrl}
                    />
                  </AdminFormField>

                  <div className="grid grid-cols-2 gap-4">
                    <AdminFormField>
                      <AdminCheckbox
                        checked={formData.noIndex || false}
                        onChange={(checked) => handleInputChange('noIndex', checked)}
                        label="عدم نمایه‌سازی (noindex)"
                      />
                    </AdminFormField>

                    <AdminFormField>
                      <AdminCheckbox
                        checked={formData.noFollow || false}
                        onChange={(checked) => handleInputChange('noFollow', checked)}
                        label="عدم دنبال کردن لینک‌ها (nofollow)"
                      />
                    </AdminFormField>
                  </div>
                </div>
              </AdminCard>
            )}

            {/* Settings Tab */}
            {activeTab === 'settings' && (
              <AdminCard title="تنظیمات صفحه" icon={Settings}>
                <div className="space-y-4">
                  <AdminFormField label="قالب صفحه" required error={errors.template}>
                    <AdminSelect
                      value={formData.template}
                      onChange={(e) => handleInputChange('template', e.target.value)}
                      error={!!errors.template}
                    >
                      {templateOptions.map(template => (
                        <option key={template.value} value={template.value}>
                          {template.label}
                        </option>
                      ))}
                    </AdminSelect>
                  </AdminFormField>

                  <div className="grid grid-cols-2 gap-4">
                    <AdminFormField label="ترتیب در منو" error={errors.menuOrder}>
                      <AdminInput
                        type="number"
                        value={formData.menuOrder}
                        onChange={(e) => handleInputChange('menuOrder', parseInt(e.target.value) || 0)}
                        placeholder="0"
                        error={!!errors.menuOrder}
                      />
                    </AdminFormField>

                    <AdminFormField>
                      <AdminCheckbox
                        checked={formData.showInMenu}
                        onChange={(checked) => handleInputChange('showInMenu', checked)}
                        label="نمایش در منو"
                      />
                    </AdminFormField>
                  </div>

                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.isActive}
                      onChange={(checked) => handleInputChange('isActive', checked)}
                      label="فعال"
                    />
                  </AdminFormField>

                  {formData.status === 'scheduled' && (
                    <div className="grid grid-cols-2 gap-4">
                      <AdminFormField label="تاریخ شروع" error={errors.startDate}>
                        <AdminInput
                          type="datetime-local"
                          value={formData.startDate}
                          onChange={(e) => handleInputChange('startDate', e.target.value)}
                          error={!!errors.startDate}
                        />
                      </AdminFormField>

                      <AdminFormField label="تاریخ پایان" error={errors.endDate}>
                        <AdminInput
                          type="datetime-local"
                          value={formData.endDate || ''}
                          onChange={(e) => handleInputChange('endDate', e.target.value)}
                          error={!!errors.endDate}
                        />
                      </AdminFormField>
                    </div>
                  )}
                </div>
              </AdminCard>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status and Actions */}
            <AdminCard title="انتشار">
              <div className="space-y-4">
                <AdminFormField label="وضعیت" required>
                  <AdminSelect
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                  >
                    {statusOptions.map(status => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </AdminSelect>
                </AdminFormField>

                <div className="flex gap-2">
                  <AdminButton 
                    type="submit" 
                    variant="primary" 
                    icon={Save}
                    loading={loading}
                    className="flex-1"
                  >
                    {mode === 'create' ? 'ایجاد صفحه' : 'به‌روزرسانی'}
                  </AdminButton>
                  
                  <AdminButton 
                    variant="outline" 
                    icon={Eye}
                    onClick={() => {
                      // Implement preview functionality
                      console.log('Preview page');
                    }}
                  >
                    پیش‌نمایش
                  </AdminButton>
                </div>

                <AdminButton 
                  variant="ghost" 
                  onClick={onCancel}
                  className="w-full"
                >
                  انصراف
                </AdminButton>
              </div>
            </AdminCard>

            {/* Page Info */}
            <AdminCard title="اطلاعات صفحه">
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">قالب:</span>
                  <span className="font-medium">
                    {templateOptions.find(t => t.value === formData.template)?.label}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">وضعیت:</span>
                  <span className="font-medium">
                    {statusOptions.find(s => s.value === formData.status)?.label}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">نمایش در منو:</span>
                  <span className="font-medium">
                    {formData.showInMenu ? 'بله' : 'خیر'}
                  </span>
                </div>
              </div>
            </AdminCard>
          </div>
        </div>
      </AdminForm>
    </div>
  );
};

export default PageEditor;
