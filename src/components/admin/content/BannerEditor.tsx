import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Eye, Upload, X, ExternalLink, Palette, Settings } from 'lucide-react';
import AdminForm, { 
  AdminFormField, 
  AdminInput, 
  AdminTextarea, 
  AdminSelect, 
  AdminCheckbox,
  AdminFormAlert 
} from '../common/AdminForm';
import AdminButton from '../common/AdminButton';
import AdminCard from '../common/AdminCard';
import { BannerFormData, ContentStatus, BannerType } from '../../../types/adminContent';
import { validateBannerForm } from '../../../utils/contentUtils';

interface BannerEditorProps {
  initialData?: Partial<BannerFormData>;
  onSave: (data: BannerFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
}

const BannerEditor: React.FC<BannerEditorProps> = ({
  initialData,
  onSave,
  onCancel,
  loading = false,
  mode = 'create'
}) => {
  const [formData, setFormData] = useState<BannerFormData>({
    title: '',
    subtitle: '',
    description: '',
    type: 'promotional',
    image: '',
    mobileImage: '',
    altText: '',
    ctaText: '',
    ctaUrl: '',
    ctaType: 'internal',
    position: 1,
    showOnPages: ['home'],
    backgroundColor: '#ffffff',
    textColor: '#333333',
    overlayOpacity: 0.3,
    animationType: 'fade',
    autoplay: false,
    duration: 5,
    status: 'draft',
    isActive: true,
    startDate: '',
    endDate: '',
    seoTitle: '',
    seoDescription: '',
    seoKeywords: [],
    ...initialData
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'design' | 'seo' | 'advanced'>('basic');

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData]);

  const handleInputChange = (field: keyof BannerFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateBannerForm(formData);
    
    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message;
      });
      setErrors(errorMap);
      
      const warningMap: Record<string, string> = {};
      validation.warnings.forEach(warning => {
        warningMap[warning.field] = warning.message;
      });
      setWarnings(warningMap);
      
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving banner:', error);
    }
  };

  const bannerTypes: { value: BannerType; label: string }[] = [
    { value: 'hero', label: 'بنر اصلی' },
    { value: 'promotional', label: 'تبلیغاتی' },
    { value: 'announcement', label: 'اعلان' },
    { value: 'category', label: 'دسته‌بندی' }
  ];

  const statusOptions: { value: ContentStatus; label: string }[] = [
    { value: 'draft', label: 'پیش‌نویس' },
    { value: 'published', label: 'منتشر شده' },
    { value: 'scheduled', label: 'زمان‌بندی شده' },
    { value: 'archived', label: 'بایگانی شده' }
  ];

  const animationTypes = [
    { value: 'none', label: 'بدون انیمیشن' },
    { value: 'fade', label: 'محو شدن' },
    { value: 'slide', label: 'کشیدن' },
    { value: 'zoom', label: 'زوم' }
  ];

  const pageOptions = [
    { value: 'home', label: 'صفحه اصلی' },
    { value: 'products', label: 'محصولات' },
    { value: 'categories', label: 'دسته‌بندی‌ها' },
    { value: 'about', label: 'درباره ما' },
    { value: 'contact', label: 'تماس با ما' }
  ];

  const tabs = [
    { id: 'basic', label: 'اطلاعات پایه', icon: Settings },
    { id: 'design', label: 'طراحی', icon: Palette },
    { id: 'seo', label: 'سئو', icon: ExternalLink },
    { id: 'advanced', label: 'پیشرفته', icon: Settings }
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <AdminCard>
        <div className="flex space-x-1 space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-admin-100 text-admin-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </AdminCard>

      <AdminForm onSubmit={handleSubmit} loading={loading}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information Tab */}
            {activeTab === 'basic' && (
              <AdminCard title="اطلاعات پایه">
                <div className="space-y-4">
                  <AdminFormField label="عنوان بنر" required error={errors.title}>
                    <AdminInput
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="عنوان بنر را وارد کنید"
                      error={!!errors.title}
                    />
                  </AdminFormField>

                  <AdminFormField label="زیرعنوان" error={errors.subtitle}>
                    <AdminInput
                      value={formData.subtitle}
                      onChange={(e) => handleInputChange('subtitle', e.target.value)}
                      placeholder="زیرعنوان بنر (اختیاری)"
                      error={!!errors.subtitle}
                    />
                  </AdminFormField>

                  <AdminFormField label="توضیحات" error={errors.description}>
                    <AdminTextarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="توضیحات کامل بنر"
                      rows={3}
                      error={!!errors.description}
                    />
                  </AdminFormField>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="نوع بنر" required error={errors.type}>
                      <AdminSelect
                        value={formData.type}
                        onChange={(e) => handleInputChange('type', e.target.value)}
                        error={!!errors.type}
                      >
                        {bannerTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </AdminSelect>
                    </AdminFormField>

                    <AdminFormField label="موقعیت" required error={errors.position}>
                      <AdminInput
                        type="number"
                        value={formData.position}
                        onChange={(e) => handleInputChange('position', parseInt(e.target.value) || 1)}
                        placeholder="1"
                        min="1"
                        error={!!errors.position}
                      />
                    </AdminFormField>
                  </div>

                  <AdminFormField label="تصویر بنر" required error={errors.image}>
                    <div className="space-y-2">
                      <AdminInput
                        value={formData.image}
                        onChange={(e) => handleInputChange('image', e.target.value)}
                        placeholder="آدرس تصویر بنر"
                        error={!!errors.image}
                      />
                      <AdminButton variant="outline" size="sm" icon={Upload}>
                        آپلود تصویر
                      </AdminButton>
                    </div>
                  </AdminFormField>

                  <AdminFormField label="متن جایگزین تصویر" required error={errors.altText}>
                    <AdminInput
                      value={formData.altText}
                      onChange={(e) => handleInputChange('altText', e.target.value)}
                      placeholder="توضیح تصویر برای موتورهای جستجو"
                      error={!!errors.altText}
                    />
                  </AdminFormField>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="متن دکمه عمل" error={errors.ctaText}>
                      <AdminInput
                        value={formData.ctaText}
                        onChange={(e) => handleInputChange('ctaText', e.target.value)}
                        placeholder="مثال: خرید کنید"
                        error={!!errors.ctaText}
                      />
                    </AdminFormField>

                    <AdminFormField label="لینک دکمه عمل" error={errors.ctaUrl}>
                      <AdminInput
                        value={formData.ctaUrl}
                        onChange={(e) => handleInputChange('ctaUrl', e.target.value)}
                        placeholder="/products"
                        error={!!errors.ctaUrl}
                      />
                    </AdminFormField>
                  </div>
                </div>
              </AdminCard>
            )}

            {/* Design Tab */}
            {activeTab === 'design' && (
              <AdminCard title="تنظیمات طراحی">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="رنگ پس‌زمینه">
                      <AdminInput
                        type="color"
                        value={formData.backgroundColor}
                        onChange={(e) => handleInputChange('backgroundColor', e.target.value)}
                      />
                    </AdminFormField>

                    <AdminFormField label="رنگ متن">
                      <AdminInput
                        type="color"
                        value={formData.textColor}
                        onChange={(e) => handleInputChange('textColor', e.target.value)}
                      />
                    </AdminFormField>
                  </div>

                  <AdminFormField label="شفافیت پوشش">
                    <AdminInput
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={formData.overlayOpacity}
                      onChange={(e) => handleInputChange('overlayOpacity', parseFloat(e.target.value))}
                    />
                    <div className="text-sm text-gray-500 mt-1">
                      {Math.round((formData.overlayOpacity || 0) * 100)}%
                    </div>
                  </AdminFormField>

                  <AdminFormField label="نوع انیمیشن">
                    <AdminSelect
                      value={formData.animationType}
                      onChange={(e) => handleInputChange('animationType', e.target.value)}
                    >
                      {animationTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </AdminSelect>
                  </AdminFormField>

                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.autoplay}
                      onChange={(checked) => handleInputChange('autoplay', checked)}
                      label="پخش خودکار"
                      description="بنر به صورت خودکار تغییر کند"
                    />
                  </AdminFormField>

                  {formData.autoplay && (
                    <AdminFormField label="مدت زمان نمایش (ثانیه)">
                      <AdminInput
                        type="number"
                        value={formData.duration}
                        onChange={(e) => handleInputChange('duration', parseInt(e.target.value) || 5)}
                        min="1"
                        max="30"
                      />
                    </AdminFormField>
                  )}
                </div>
              </AdminCard>
            )}

            {/* SEO Tab */}
            {activeTab === 'seo' && (
              <AdminCard title="تنظیمات سئو">
                <div className="space-y-4">
                  <AdminFormField label="عنوان سئو">
                    <AdminInput
                      value={formData.seoTitle}
                      onChange={(e) => handleInputChange('seoTitle', e.target.value)}
                      placeholder="عنوان برای موتورهای جستجو"
                    />
                  </AdminFormField>

                  <AdminFormField label="توضیحات سئو">
                    <AdminTextarea
                      value={formData.seoDescription}
                      onChange={(e) => handleInputChange('seoDescription', e.target.value)}
                      placeholder="توضیحات برای موتورهای جستجو"
                      rows={3}
                    />
                  </AdminFormField>

                  <AdminFormField label="کلمات کلیدی">
                    <AdminInput
                      value={formData.seoKeywords?.join(', ')}
                      onChange={(e) => handleInputChange('seoKeywords', e.target.value.split(',').map(k => k.trim()))}
                      placeholder="کلمات کلیدی را با کاما جدا کنید"
                    />
                  </AdminFormField>
                </div>
              </AdminCard>
            )}

            {/* Advanced Tab */}
            {activeTab === 'advanced' && (
              <AdminCard title="تنظیمات پیشرفته">
                <div className="space-y-4">
                  <AdminFormField label="نمایش در صفحات">
                    <div className="space-y-2">
                      {pageOptions.map(page => (
                        <AdminCheckbox
                          key={page.value}
                          checked={formData.showOnPages.includes(page.value)}
                          onChange={(checked) => {
                            const newPages = checked
                              ? [...formData.showOnPages, page.value]
                              : formData.showOnPages.filter(p => p !== page.value);
                            handleInputChange('showOnPages', newPages);
                          }}
                          label={page.label}
                        />
                      ))}
                    </div>
                  </AdminFormField>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="تاریخ شروع">
                      <AdminInput
                        type="date"
                        value={formData.startDate}
                        onChange={(e) => handleInputChange('startDate', e.target.value)}
                      />
                    </AdminFormField>

                    <AdminFormField label="تاریخ پایان">
                      <AdminInput
                        type="date"
                        value={formData.endDate}
                        onChange={(e) => handleInputChange('endDate', e.target.value)}
                      />
                    </AdminFormField>
                  </div>

                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.isActive}
                      onChange={(checked) => handleInputChange('isActive', checked)}
                      label="فعال"
                      description="بنر در سایت نمایش داده شود"
                    />
                  </AdminFormField>
                </div>
              </AdminCard>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status and Actions */}
            <AdminCard title="انتشار">
              <div className="space-y-4">
                <AdminFormField label="وضعیت" required>
                  <AdminSelect
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                  >
                    {statusOptions.map(status => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </AdminSelect>
                </AdminFormField>

                <div className="flex gap-2">
                  <AdminButton 
                    type="submit" 
                    variant="primary" 
                    icon={Save}
                    loading={loading}
                    className="flex-1"
                  >
                    {mode === 'create' ? 'ایجاد بنر' : 'به‌روزرسانی'}
                  </AdminButton>
                  
                  <AdminButton 
                    variant="outline" 
                    icon={Eye}
                    onClick={() => {
                      // Implement preview functionality
                      console.log('Preview banner');
                    }}
                  >
                    پیش‌نمایش
                  </AdminButton>
                </div>

                <AdminButton 
                  variant="ghost" 
                  onClick={onCancel}
                  className="w-full"
                >
                  انصراف
                </AdminButton>
              </div>
            </AdminCard>

            {/* Preview */}
            {formData.image && (
              <AdminCard title="پیش‌نمایش">
                <div className="space-y-2">
                  <div className="relative rounded-lg overflow-hidden bg-gray-100">
                    <img 
                      src={formData.image} 
                      alt={formData.altText}
                      className="w-full h-32 object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/api/placeholder/300/128';
                      }}
                    />
                    {formData.overlayOpacity && formData.overlayOpacity > 0 && (
                      <div 
                        className="absolute inset-0"
                        style={{ 
                          backgroundColor: 'rgba(0,0,0,' + formData.overlayOpacity + ')' 
                        }}
                      />
                    )}
                    <div className="absolute inset-0 p-4 flex flex-col justify-center">
                      <h3 
                        className="font-bold text-sm mb-1"
                        style={{ color: formData.textColor }}
                      >
                        {formData.title}
                      </h3>
                      {formData.subtitle && (
                        <p 
                          className="text-xs mb-2"
                          style={{ color: formData.textColor }}
                        >
                          {formData.subtitle}
                        </p>
                      )}
                      {formData.ctaText && (
                        <button 
                          className="text-xs px-2 py-1 bg-white text-gray-900 rounded self-start"
                        >
                          {formData.ctaText}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </AdminCard>
            )}

            {/* Validation Messages */}
            {Object.keys(errors).length > 0 && (
              <AdminFormAlert
                type="error"
                title="خطاهای فرم"
                message={`لطفاً ${Object.keys(errors).length} خطای موجود را برطرف کنید.`}
              />
            )}

            {Object.keys(warnings).length > 0 && (
              <AdminFormAlert
                type="warning"
                title="هشدارها"
                message={`${Object.keys(warnings).length} هشدار برای بهبود بنر وجود دارد.`}
              />
            )}
          </div>
        </div>
      </AdminForm>
    </div>
  );
};

export default BannerEditor;
