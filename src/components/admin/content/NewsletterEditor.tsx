import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Send, Users, Mail, Settings, Target, Calendar, Eye } from 'lucide-react';
import AdminForm, { 
  AdminFormField, 
  AdminInput, 
  AdminTextarea, 
  AdminSelect, 
  AdminCheckbox,
  AdminFormAlert 
} from '../common/AdminForm';
import AdminButton from '../common/AdminButton';
import AdminCard from '../common/AdminCard';
import { NewsletterCampaignFormData, ContentStatus } from '../../../types/adminContent';
import { validateNewsletterForm } from '../../../utils/contentUtils';

interface NewsletterEditorProps {
  initialData?: Partial<NewsletterCampaignFormData>;
  onSave: (data: NewsletterCampaignFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
}

const NewsletterEditor: React.FC<NewsletterEditorProps> = ({
  initialData,
  onSave,
  onCancel,
  loading = false,
  mode = 'create'
}) => {
  const [formData, setFormData] = useState<NewsletterCampaignFormData>({
    title: '',
    subject: '',
    preheader: '',
    content: '',
    htmlContent: '',
    campaignType: 'promotional',
    template: '',
    recipientSegments: ['all_subscribers'],
    sendAt: '',
    timezone: 'Asia/Tehran',
    isABTest: false,
    abTestVariant: 'A',
    status: 'draft',
    ...initialData
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'content' | 'recipients' | 'schedule' | 'testing'>('content');

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData]);

  const handleInputChange = (field: keyof NewsletterCampaignFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateNewsletterForm(formData);
    
    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message;
      });
      setErrors(errorMap);
      
      const warningMap: Record<string, string> = {};
      validation.warnings.forEach(warning => {
        warningMap[warning.field] = warning.message;
      });
      setWarnings(warningMap);
      
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving newsletter campaign:', error);
    }
  };

  const campaignTypes = [
    { value: 'promotional', label: 'تبلیغاتی' },
    { value: 'informational', label: 'اطلاع‌رسانی' },
    { value: 'product_update', label: 'به‌روزرسانی محصول' },
    { value: 'seasonal', label: 'فصلی' }
  ];

  const statusOptions: { value: ContentStatus; label: string }[] = [
    { value: 'draft', label: 'پیش‌نویس' },
    { value: 'scheduled', label: 'زمان‌بندی شده' },
    { value: 'published', label: 'ارسال شده' }
  ];

  const recipientSegments = [
    { value: 'all_subscribers', label: 'همه مشترکین' },
    { value: 'vip_customers', label: 'مشتریان ویژه' },
    { value: 'new_subscribers', label: 'مشترکین جدید' },
    { value: 'active_customers', label: 'مشتریان فعال' },
    { value: 'inactive_customers', label: 'مشتریان غیرفعال' }
  ];

  const templates = [
    { value: '', label: 'بدون قالب' },
    { value: 'promotional', label: 'قالب تبلیغاتی' },
    { value: 'newsletter', label: 'قالب خبرنامه' },
    { value: 'product-announcement', label: 'قالب معرفی محصول' },
    { value: 'seasonal', label: 'قالب فصلی' }
  ];

  const tabs = [
    { id: 'content', label: 'محتوا', icon: Mail },
    { id: 'recipients', label: 'مخاطبان', icon: Users },
    { id: 'schedule', label: 'زمان‌بندی', icon: Calendar },
    { id: 'testing', label: 'تست A/B', icon: Target }
  ];

  const estimatedRecipients = formData.recipientSegments.includes('all_subscribers') ? 15420 :
    formData.recipientSegments.includes('vip_customers') ? 2340 :
    formData.recipientSegments.includes('new_subscribers') ? 1250 :
    formData.recipientSegments.includes('active_customers') ? 8900 :
    formData.recipientSegments.includes('inactive_customers') ? 3200 : 0;

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <AdminCard>
        <div className="flex space-x-1 space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-admin-100 text-admin-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </AdminCard>

      <AdminForm onSubmit={handleSubmit} loading={loading}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Content Tab */}
            {activeTab === 'content' && (
              <AdminCard title="محتوای کمپین">
                <div className="space-y-4">
                  <AdminFormField label="عنوان کمپین" required error={errors.title}>
                    <AdminInput
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="عنوان کمپین را وارد کنید"
                      error={!!errors.title}
                    />
                  </AdminFormField>

                  <AdminFormField label="موضوع ایمیل" required error={errors.subject}>
                    <AdminInput
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      placeholder="موضوع ایمیل که مشترکین خواهند دید"
                      error={!!errors.subject}
                    />
                    {formData.subject && (
                      <div className="text-xs text-gray-500 mt-1">
                        طول: {formData.subject.length} کاراکتر
                        {formData.subject.length > 50 && (
                          <span className="text-yellow-600"> (بهتر است کمتر از ۵۰ کاراکتر باشد)</span>
                        )}
                      </div>
                    )}
                  </AdminFormField>

                  <AdminFormField label="متن پیش‌نمایش" error={errors.preheader}>
                    <AdminInput
                      value={formData.preheader}
                      onChange={(e) => handleInputChange('preheader', e.target.value)}
                      placeholder="متن کوتاه که در پیش‌نمایش ایمیل نمایش داده می‌شود"
                      error={!!errors.preheader}
                    />
                    {formData.preheader && (
                      <div className="text-xs text-gray-500 mt-1">
                        طول: {formData.preheader.length} کاراکتر
                        {formData.preheader.length > 90 && (
                          <span className="text-yellow-600"> (بهتر است کمتر از ۹۰ کاراکتر باشد)</span>
                        )}
                      </div>
                    )}
                  </AdminFormField>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="نوع کمپین" required>
                      <AdminSelect
                        value={formData.campaignType}
                        onChange={(e) => handleInputChange('campaignType', e.target.value)}
                      >
                        {campaignTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </AdminSelect>
                    </AdminFormField>

                    <AdminFormField label="قالب">
                      <AdminSelect
                        value={formData.template}
                        onChange={(e) => handleInputChange('template', e.target.value)}
                      >
                        {templates.map(template => (
                          <option key={template.value} value={template.value}>
                            {template.label}
                          </option>
                        ))}
                      </AdminSelect>
                    </AdminFormField>
                  </div>

                  <AdminFormField label="محتوای ایمیل" required error={errors.content}>
                    <AdminTextarea
                      value={formData.content}
                      onChange={(e) => handleInputChange('content', e.target.value)}
                      placeholder="محتوای کامل ایمیل را وارد کنید"
                      rows={8}
                      error={!!errors.content}
                    />
                  </AdminFormField>
                </div>
              </AdminCard>
            )}

            {/* Recipients Tab */}
            {activeTab === 'recipients' && (
              <AdminCard title="انتخاب مخاطبان">
                <div className="space-y-4">
                  <AdminFormField label="گروه‌های مخاطب" required error={errors.recipientSegments}>
                    <div className="space-y-2">
                      {recipientSegments.map(segment => (
                        <AdminCheckbox
                          key={segment.value}
                          checked={formData.recipientSegments.includes(segment.value)}
                          onChange={(checked) => {
                            const newSegments = checked
                              ? [...formData.recipientSegments, segment.value]
                              : formData.recipientSegments.filter(s => s !== segment.value);
                            handleInputChange('recipientSegments', newSegments);
                          }}
                          label={segment.label}
                        />
                      ))}
                    </div>
                  </AdminFormField>

                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="w-5 h-5 text-blue-600" />
                      <span className="font-medium text-blue-800">تخمین مخاطبان</span>
                    </div>
                    <p className="text-blue-700">
                      تقریباً <span className="font-bold">{estimatedRecipients.toLocaleString('fa-IR')}</span> نفر این ایمیل را دریافت خواهند کرد.
                    </p>
                  </div>
                </div>
              </AdminCard>
            )}

            {/* Schedule Tab */}
            {activeTab === 'schedule' && (
              <AdminCard title="زمان‌بندی ارسال">
                <div className="space-y-4">
                  <AdminFormField label="زمان ارسال" error={errors.sendAt}>
                    <AdminInput
                      type="datetime-local"
                      value={formData.sendAt}
                      onChange={(e) => handleInputChange('sendAt', e.target.value)}
                      error={!!errors.sendAt}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      اگر خالی باشد، ایمیل بلافاصله ارسال خواهد شد
                    </div>
                  </AdminFormField>

                  <AdminFormField label="منطقه زمانی">
                    <AdminSelect
                      value={formData.timezone}
                      onChange={(e) => handleInputChange('timezone', e.target.value)}
                    >
                      <option value="Asia/Tehran">تهران (UTC+3:30)</option>
                      <option value="UTC">UTC (UTC+0)</option>
                    </AdminSelect>
                  </AdminFormField>
                </div>
              </AdminCard>
            )}

            {/* A/B Testing Tab */}
            {activeTab === 'testing' && (
              <AdminCard title="تست A/B">
                <div className="space-y-4">
                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.isABTest}
                      onChange={(checked) => handleInputChange('isABTest', checked)}
                      label="فعال‌سازی تست A/B"
                      description="دو نسخه مختلف از ایمیل ارسال شود"
                    />
                  </AdminFormField>

                  {formData.isABTest && (
                    <>
                      <AdminFormField label="نسخه فعلی">
                        <AdminSelect
                          value={formData.abTestVariant}
                          onChange={(e) => handleInputChange('abTestVariant', e.target.value)}
                        >
                          <option value="A">نسخه A</option>
                          <option value="B">نسخه B</option>
                        </AdminSelect>
                      </AdminFormField>

                      <div className="p-4 bg-yellow-50 rounded-lg">
                        <p className="text-yellow-800 text-sm">
                          <strong>نکته:</strong> برای تست A/B، ابتدا نسخه A را ایجاد کنید، سپس نسخه B را با تغییرات مورد نظر ایجاد کنید.
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </AdminCard>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status and Actions */}
            <AdminCard title="انتشار">
              <div className="space-y-4">
                <AdminFormField label="وضعیت" required>
                  <AdminSelect
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                  >
                    {statusOptions.map(status => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </AdminSelect>
                </AdminFormField>

                <div className="flex gap-2">
                  <AdminButton 
                    type="submit" 
                    variant="primary" 
                    icon={Save}
                    loading={loading}
                    className="flex-1"
                  >
                    {mode === 'create' ? 'ایجاد کمپین' : 'به‌روزرسانی'}
                  </AdminButton>
                </div>

                {formData.status === 'scheduled' && formData.sendAt && (
                  <AdminButton 
                    variant="success" 
                    icon={Send}
                    className="w-full"
                  >
                    ارسال فوری
                  </AdminButton>
                )}

                <AdminButton 
                  variant="ghost" 
                  onClick={onCancel}
                  className="w-full"
                >
                  انصراف
                </AdminButton>
              </div>
            </AdminCard>

            {/* Preview */}
            <AdminCard title="پیش‌نمایش">
              <div className="space-y-3">
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm font-medium text-gray-900 mb-1">
                    {formData.subject || 'موضوع ایمیل'}
                  </div>
                  {formData.preheader && (
                    <div className="text-xs text-gray-500 mb-2">
                      {formData.preheader}
                    </div>
                  )}
                  <div className="text-xs text-gray-600 border-t pt-2">
                    {formData.content ? formData.content.substring(0, 100) + '...' : 'محتوای ایمیل...'}
                  </div>
                </div>
              </div>
            </AdminCard>

            {/* Campaign Stats */}
            <AdminCard title="آمار کمپین">
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">مخاطبان:</span>
                  <span className="font-medium">{estimatedRecipients.toLocaleString('fa-IR')}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">نوع کمپین:</span>
                  <span className="font-medium">
                    {campaignTypes.find(t => t.value === formData.campaignType)?.label}
                  </span>
                </div>
                {formData.sendAt && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">زمان ارسال:</span>
                    <span className="font-medium">
                      {new Date(formData.sendAt).toLocaleDateString('fa-IR')}
                    </span>
                  </div>
                )}
              </div>
            </AdminCard>

            {/* Validation Messages */}
            {Object.keys(errors).length > 0 && (
              <AdminFormAlert
                type="error"
                title="خطاهای فرم"
                message={`لطفاً ${Object.keys(errors).length} خطای موجود را برطرف کنید.`}
              />
            )}

            {Object.keys(warnings).length > 0 && (
              <AdminFormAlert
                type="warning"
                title="هشدارها"
                message={`${Object.keys(warnings).length} هشدار برای بهبود کمپین وجود دارد.`}
              />
            )}
          </div>
        </div>
      </AdminForm>
    </div>
  );
};

export default NewsletterEditor;
