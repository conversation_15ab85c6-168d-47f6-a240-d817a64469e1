import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, X, Plus, Trash2, AlertCircle, Image as ImageIcon } from 'lucide-react';
import AdminForm, {
  AdminFormField,
  AdminInput,
  AdminTextarea,
  AdminSelect,
  AdminCheckbox,
  AdminFormAlert
} from '../common/AdminForm';
import AdminButton from '../common/AdminButton';
import AdminCard from '../common/AdminCard';
import AdminFileUpload from '../common/AdminFileUpload';
import {
  ProductFormData,
  ProductValidationResult,
  PERSIAN_PRODUCT_MESSAGES
} from '../../../types/adminProduct';
import { categories } from '../../../data/categories';
import { validateProductForm, generateSlug } from '../../../utils/productValidation';
import { getBrandOptions, getBrandInfo } from '../../../utils/brandUtils';
import AdminImagePreview from '../common/AdminImagePreview';

interface ProductFormProps {
  initialData?: Partial<ProductFormData>;
  onSubmit: (data: ProductFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  existingSkus?: string[];
}

const ProductForm: React.FC<ProductFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  existingSkus = []
}) => {
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    category: '',
    brand: '',
    sku: '',
    barcode: '',
    price: 0,
    discountedPrice: undefined,
    costPrice: undefined,
    compareAtPrice: undefined,
    taxable: true,
    taxClass: '',
    stock: 0,
    trackInventory: true,
    allowBackorder: false,
    lowStockThreshold: 10,
    benefits: [],
    ingredients: [],
    howToUse: [],
    size: '',
    weight: '',
    imageSrc: '',
    images: [],
    hasVariants: false,
    variants: [],
    seoTitle: '',
    seoDescription: '',
    seoKeywords: [],
    slug: '',
    status: 'draft',
    visibility: 'visible',
    featured: false,
    isNew: false,
    isBestSeller: false,
    requiresShipping: true,
    shippingWeight: undefined,
    shippingDimensions: undefined,
    tags: [],
    vendor: '',
    productType: '',
    collections: [],
    ...initialData
  });

  const [validation, setValidation] = useState<ProductValidationResult>({
    isValid: true,
    errors: []
  });

  const [newBenefit, setNewBenefit] = useState('');
  const [newIngredient, setNewIngredient] = useState('');
  const [newHowToUse, setNewHowToUse] = useState('');
  const [newTag, setNewTag] = useState('');

  // Image upload states
  const [mainImageFile, setMainImageFile] = useState<File | null>(null);
  const [additionalImageFiles, setAdditionalImageFiles] = useState<File[]>([]);

  // Auto-generate slug when name changes or when slug is empty
  useEffect(() => {
    if (formData.name && (!formData.slug || formData.slug.trim().length === 0)) {
      const newSlug = generateSlug(formData.name);
      if (newSlug && newSlug.length >= 3) {
        setFormData(prev => ({
          ...prev,
          slug: newSlug
        }));
      }
    }
  }, [formData.name, formData.slug]);

  // Auto-generate SEO title when name changes
  useEffect(() => {
    if (formData.name && !formData.seoTitle) {
      setFormData(prev => ({
        ...prev,
        seoTitle: prev.name
      }));
    }
  }, [formData.name, formData.seoTitle]);

  // Validate form on changes
  useEffect(() => {
    const result = validateProductForm(formData, existingSkus);
    setValidation(result);
  }, [formData, existingSkus]);

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayAdd = (field: 'benefits' | 'ingredients' | 'howToUse' | 'tags', value: string) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        [field]: [...prev[field], value.trim()]
      }));
      
      // Clear the input
      switch (field) {
        case 'benefits': setNewBenefit(''); break;
        case 'ingredients': setNewIngredient(''); break;
        case 'howToUse': setNewHowToUse(''); break;
        case 'tags': setNewTag(''); break;
      }
    }
  };

  const handleArrayRemove = (field: 'benefits' | 'ingredients' | 'howToUse' | 'tags', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  // Image handling functions
  const handleMainImageUpload = (file: File | null, url?: string) => {
    setMainImageFile(file);
    if (url) {
      setFormData(prev => ({
        ...prev,
        imageSrc: url
      }));
    }
  };

  const handleAdditionalImageUpload = (file: File | null, url?: string) => {
    if (file && url) {
      setAdditionalImageFiles(prev => [...prev, file]);
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, url]
      }));
    }
  };

  const handleRemoveAdditionalImage = (index: number) => {
    setAdditionalImageFiles(prev => prev.filter((_, i) => i !== index));
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validation.isValid) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const getFieldError = (field: string) => {
    return validation.errors.find(error => error.field === field)?.message;
  };

  return (
    <AdminForm onSubmit={handleSubmit} loading={loading}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <AdminCard title="اطلاعات پایه" icon={AlertCircle}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.name}
                required
                error={getFieldError('name')}
              >
                <AdminInput
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="نام محصول را وارد کنید"
                  error={!!getFieldError('name')}
                />
              </AdminFormField>

              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.sku}
                required
                error={getFieldError('sku')}
              >
                <AdminInput
                  value={formData.sku}
                  onChange={(e) => handleInputChange('sku', e.target.value)}
                  placeholder="کد محصول"
                  error={!!getFieldError('sku')}
                />
              </AdminFormField>

              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.category}
                required
                error={getFieldError('category')}
              >
                <AdminSelect
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  placeholder="دسته‌بندی را انتخاب کنید"
                  error={!!getFieldError('category')}
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </AdminSelect>
              </AdminFormField>

              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.brand}
                error={getFieldError('brand')}
              >
                <div className="space-y-2">
                  <AdminSelect
                    value={formData.brand}
                    onChange={(e) => handleInputChange('brand', e.target.value)}
                    placeholder="برند را انتخاب کنید"
                    error={!!getFieldError('brand')}
                  >
                    <option value="">برند را انتخاب کنید</option>
                    {getBrandOptions().map(brand => (
                      <option key={brand.value} value={brand.value}>
                        {brand.label}
                      </option>
                    ))}
                  </AdminSelect>

                  {/* Brand Logo Preview */}
                  {formData.brand && (
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border">
                      <div className="flex-shrink-0">
                        <AdminImagePreview
                          src={getBrandInfo(formData.brand).logo}
                          alt={`لوگو ${formData.brand}`}
                          size="lg"
                          shape="rounded"
                          className="w-16 h-16 object-contain bg-white border border-gray-200 p-2"
                          fallbackIcon={AlertCircle}
                        />
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">
                          {getBrandInfo(formData.brand).name}
                        </div>
                        {getBrandInfo(formData.brand).nameEn && (
                          <div className="text-xs text-gray-500 mt-1">
                            {getBrandInfo(formData.brand).nameEn}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          برند انتخاب شده
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </AdminFormField>
            </div>

            <AdminFormField
              label={PERSIAN_PRODUCT_MESSAGES.fields.description}
              required
              error={getFieldError('description')}
            >
              <AdminTextarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="توضیحات کامل محصول"
                rows={4}
                error={!!getFieldError('description')}
              />
            </AdminFormField>
          </AdminCard>

          {/* Pricing */}
          <AdminCard title="قیمت‌گذاری" icon={AlertCircle}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.price}
                required
                error={getFieldError('price')}
              >
                <AdminInput
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', Number(e.target.value))}
                  placeholder="0"
                  error={!!getFieldError('price')}
                />
              </AdminFormField>

              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.discountedPrice}
                error={getFieldError('discountedPrice')}
              >
                <AdminInput
                  type="number"
                  value={formData.discountedPrice || ''}
                  onChange={(e) => handleInputChange('discountedPrice', e.target.value ? Number(e.target.value) : undefined)}
                  placeholder="قیمت تخفیف‌دار (اختیاری)"
                  error={!!getFieldError('discountedPrice')}
                />
              </AdminFormField>

              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.costPrice}
                error={getFieldError('costPrice')}
              >
                <AdminInput
                  type="number"
                  value={formData.costPrice || ''}
                  onChange={(e) => handleInputChange('costPrice', e.target.value ? Number(e.target.value) : undefined)}
                  placeholder="قیمت تمام شده (اختیاری)"
                  error={!!getFieldError('costPrice')}
                />
              </AdminFormField>

              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.compareAtPrice}
                error={getFieldError('compareAtPrice')}
              >
                <AdminInput
                  type="number"
                  value={formData.compareAtPrice || ''}
                  onChange={(e) => handleInputChange('compareAtPrice', e.target.value ? Number(e.target.value) : undefined)}
                  placeholder="قیمت مقایسه (اختیاری)"
                  error={!!getFieldError('compareAtPrice')}
                />
              </AdminFormField>
            </div>

            <AdminCheckbox
              checked={formData.taxable}
              onChange={(checked) => handleInputChange('taxable', checked)}
              label={PERSIAN_PRODUCT_MESSAGES.fields.taxable}
              description="این محصول مشمول مالیات است"
            />
          </AdminCard>

          {/* Inventory */}
          <AdminCard title="موجودی" icon={AlertCircle}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.stock}
                required
                error={getFieldError('stock')}
              >
                <AdminInput
                  type="number"
                  value={formData.stock}
                  onChange={(e) => handleInputChange('stock', Number(e.target.value))}
                  placeholder="0"
                  error={!!getFieldError('stock')}
                />
              </AdminFormField>

              <AdminFormField
                label={PERSIAN_PRODUCT_MESSAGES.fields.lowStockThreshold}
                error={getFieldError('lowStockThreshold')}
              >
                <AdminInput
                  type="number"
                  value={formData.lowStockThreshold}
                  onChange={(e) => handleInputChange('lowStockThreshold', Number(e.target.value))}
                  placeholder="10"
                  error={!!getFieldError('lowStockThreshold')}
                />
              </AdminFormField>
            </div>

            <div className="space-y-3">
              <AdminCheckbox
                checked={formData.trackInventory}
                onChange={(checked) => handleInputChange('trackInventory', checked)}
                label={PERSIAN_PRODUCT_MESSAGES.fields.trackInventory}
                description="پیگیری موجودی این محصول"
              />

              <AdminCheckbox
                checked={formData.allowBackorder}
                onChange={(checked) => handleInputChange('allowBackorder', checked)}
                label={PERSIAN_PRODUCT_MESSAGES.fields.allowBackorder}
                description="اجازه سفارش در صورت عدم موجودی"
              />
            </div>
          </AdminCard>

          {/* Product Images */}
          <AdminCard title="تصاویر محصول" icon={ImageIcon}>
            {/* Main Product Image */}
            <AdminFormField
              label="تصویر اصلی محصول"
              required
              error={getFieldError('imageSrc')}
              help="تصویر اصلی محصول که در لیست محصولات نمایش داده می‌شود. ابعاد توصیه شده: 800x800 پیکسل (مربعی) - فرمت‌های مجاز: JPG, PNG, WebP - حداکثر 5 مگابایت"
            >
              {/* Current Image Preview */}
              {formData.imageSrc && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تصویر فعلی:
                  </label>
                  <div className="relative inline-block">
                    <div className="w-40 h-40 rounded-lg overflow-hidden border-2 border-gray-200 bg-gray-50">
                      <AdminImagePreview
                        src={formData.imageSrc}
                        alt={formData.name || 'تصویر محصول'}
                        size="xl"
                        shape="rounded"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, imageSrc: '' }))}
                      className="absolute -top-2 -right-2 p-1.5 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-lg"
                      title="حذف تصویر"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}

              <div className="max-w-xs">
                <AdminFileUpload
                  value={formData.imageSrc}
                  onChange={handleMainImageUpload}
                  accept="image/*"
                  maxSize={5}
                  placeholder={formData.imageSrc ? "تصویر جدید را انتخاب کنید" : "تصویر اصلی محصول را انتخاب کنید"}
                  aspectRatio="square"
                  error={!!getFieldError('imageSrc')}
                  className="w-full"
                />
              </div>
            </AdminFormField>

            {/* Additional Images */}
            <AdminFormField
              label="تصاویر اضافی"
              error={getFieldError('images')}
              help="تصاویر اضافی محصول برای گالری. ابعاد توصیه شده: 800x800 پیکسل (مربعی) - حداکثر 10 تصویر - فرمت‌های مجاز: JPG, PNG, WebP - حداکثر 5 مگابایت هر تصویر"
            >
              <div className="space-y-4">
                {/* Current Additional Images */}
                {formData.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {formData.images.map((image, index) => (
                      <div key={index} className="relative group">
                        <div className="aspect-square rounded-lg overflow-hidden border-2 border-gray-200 bg-gray-50">
                          <AdminImagePreview
                            src={image}
                            alt={`تصویر اضافی ${index + 1}`}
                            size="lg"
                            shape="rounded"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemoveAdditionalImage(index)}
                          className="absolute top-2 right-2 p-1.5 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-red-600 shadow-lg"
                        >
                          <X className="w-3 h-3" />
                        </button>
                        <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                          تصویر {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Add New Image */}
                {formData.images.length < 10 && (
                  <div className="max-w-xs">
                    <AdminFileUpload
                      onChange={handleAdditionalImageUpload}
                      accept="image/*"
                      maxSize={5}
                      placeholder="تصویر اضافی اضافه کنید"
                      aspectRatio="square"
                      className="w-full"
                    />
                  </div>
                )}

                {formData.images.length >= 10 && (
                  <div className="text-sm text-gray-500 text-center p-4 bg-gray-50 rounded-lg">
                    حداکثر 10 تصویر اضافی مجاز است
                  </div>
                )}
              </div>
            </AdminFormField>
          </AdminCard>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status */}
          <AdminCard title="وضعیت انتشار" icon={AlertCircle}>
            <div className="space-y-4">
              <AdminFormField label="وضعیت">
                <AdminSelect
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <option value="draft">پیش‌نویس</option>
                  <option value="active">فعال</option>
                  <option value="archived">بایگانی</option>
                </AdminSelect>
              </AdminFormField>

              <AdminFormField label="نمایش">
                <AdminSelect
                  value={formData.visibility}
                  onChange={(e) => handleInputChange('visibility', e.target.value)}
                >
                  <option value="visible">قابل مشاهده</option>
                  <option value="hidden">مخفی</option>
                  <option value="catalog">فقط در کاتالوگ</option>
                  <option value="search">فقط در جستجو</option>
                </AdminSelect>
              </AdminFormField>

              <div className="space-y-3">
                <AdminCheckbox
                  checked={formData.featured}
                  onChange={(checked) => handleInputChange('featured', checked)}
                  label="محصول ویژه"
                />

                <AdminCheckbox
                  checked={formData.isNew}
                  onChange={(checked) => handleInputChange('isNew', checked)}
                  label="محصول جدید"
                />

                <AdminCheckbox
                  checked={formData.isBestSeller}
                  onChange={(checked) => handleInputChange('isBestSeller', checked)}
                  label="پرفروش"
                />
              </div>
            </div>
          </AdminCard>

          {/* Form Actions */}
          <AdminCard>
            <div className="flex flex-col gap-3">
              <AdminButton
                type="submit"
                variant="primary"
                loading={loading}
                disabled={!validation.isValid}
                fullWidth
              >
                <Save className="w-4 h-4" />
                {initialData ? 'بروزرسانی محصول' : 'ایجاد محصول'}
              </AdminButton>

              <AdminButton
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
                fullWidth
              >
                <X className="w-4 h-4" />
                انصراف
              </AdminButton>
            </div>

            {/* Validation Errors */}
            {!validation.isValid && (
              <div className="mt-4 space-y-3">
                <AdminFormAlert
                  type="error"
                  title="خطاهای فرم"
                  message={`${validation.errors.length} خطا در فرم وجود دارد. لطفاً موارد زیر را بررسی کنید:`}
                />

                {/* Detailed Error List */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-red-800 mb-3">جزئیات خطاها:</h4>
                  <ul className="space-y-2">
                    {validation.errors.map((error, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-red-700">
                        <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="font-medium">
                            {PERSIAN_PRODUCT_MESSAGES.fields[error.field as keyof typeof PERSIAN_PRODUCT_MESSAGES.fields] || error.field}:
                          </span>
                          <span className="mr-1">{error.message}</span>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* Validation Warnings */}
            {validation.warnings && validation.warnings.length > 0 && (
              <div className="mt-4">
                <AdminFormAlert
                  type="warning"
                  title="هشدارها"
                  message={`${validation.warnings.length} هشدار در فرم وجود دارد`}
                />
              </div>
            )}
          </AdminCard>
        </div>
      </div>
    </AdminForm>
  );
};

export default ProductForm;
