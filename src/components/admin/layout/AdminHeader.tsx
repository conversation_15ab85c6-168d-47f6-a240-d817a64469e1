import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu,
  Bell,
  Search,
  User,
  Settings,
  LogOut,
  Shield,
  Clock,
  ChevronDown,
  ExternalLink
} from 'lucide-react';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { useAdminNotificationsContext } from '../../../contexts/AdminNotificationsContext';
import AdminButton, { AdminIconButton } from '../common/AdminButton';
import { formatPersianDate } from '../../../utils/formatters';
import { toast } from 'react-hot-toast';

interface AdminHeaderProps {
  onMenuClick: () => void;
  isMobile: boolean;
}

const AdminHeader: React.FC<AdminHeaderProps> = ({
  onMenuClick,
  isMobile
}) => {
  const navigate = useNavigate();
  const { user, logout, getSessionTimeRemaining } = useAdminAuth();
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    loading: notificationsLoading
  } = useAdminNotificationsContext();

  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const sessionTimeRemaining = getSessionTimeRemaining();
  const sessionMinutes = Math.floor(sessionTimeRemaining / 60000);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleNotificationClick = async (notification: any) => {
    try {
      // Mark as read if unread
      if (!notification.read) {
        const result = await markAsRead(notification.id);
        if (result.success) {
          toast.success('اعلان به عنوان خوانده شده علامت‌گذاری شد');
        }
      }

      // Navigate to action URL if available
      if (notification.actionUrl) {
        navigate(notification.actionUrl);
        setShowNotifications(false);
      }
    } catch (error) {
      toast.error('خطا در علامت‌گذاری اعلان');
    }
  };

  // Get recent notifications for header dropdown (max 5)
  const recentNotifications = notifications.slice(0, 5);

  const handleMarkAllAsRead = async () => {
    try {
      const result = await markAllAsRead();
      if (result.success) {
        toast.success('همه اعلان‌ها به عنوان خوانده شده علامت‌گذاری شدند');
      }
    } catch (error) {
      toast.error('خطا در علامت‌گذاری اعلان‌ها');
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 h-16 flex items-center justify-between px-6 relative z-30">
      {/* Left Section */}
      <div className="flex items-center gap-4">
        {/* Mobile Menu Button */}
        {isMobile && (
          <AdminIconButton
            icon={Menu}
            variant="ghost"
            onClick={onMenuClick}
            className="text-gray-600"
          />
        )}

        {/* Search */}
        <div className="relative hidden md:block">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="جستجو در پنل مدیریت..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-64 pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent text-sm"
          />
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-3">
        {/* Session Timer */}
        {sessionMinutes < 30 && (
          <div className="hidden md:flex items-center gap-2 px-3 py-1 bg-yellow-50 border border-yellow-200 rounded-lg">
            <Clock className="w-4 h-4 text-yellow-600" />
            <span className="text-sm text-yellow-700">
              {sessionMinutes} دقیقه تا پایان جلسه
            </span>
          </div>
        )}

        {/* Quick Actions */}
        <Link to="/" target="_blank" rel="noopener noreferrer">
          <AdminIconButton
            icon={ExternalLink}
            variant="ghost"
            tooltip="مشاهده سایت"
            className="text-gray-600"
          />
        </Link>

        {/* Notifications */}
        <div className="relative">
          <AdminIconButton
            icon={Bell}
            variant="ghost"
            onClick={() => setShowNotifications(!showNotifications)}
            className={`text-gray-600 relative transition-colors ${
              notificationsLoading ? 'animate-pulse' : ''
            }`}
          />
          {unreadCount > 0 && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-1 -left-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </motion.span>
          )}

          {/* Notifications Dropdown */}
          <AnimatePresence>
            {showNotifications && (
              <>
                <div
                  className="fixed inset-0 z-10"
                  onClick={() => setShowNotifications(false)}
                />
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-20"
                >
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">اعلان‌ها</h3>
                      {unreadCount > 0 && (
                        <button
                          onClick={handleMarkAllAsRead}
                          className="text-xs text-admin-600 hover:text-admin-700 font-medium"
                        >
                          همه را خوانده شده علامت‌گذاری کن
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    {notificationsLoading ? (
                      <div className="p-4 text-center text-gray-500">
                        در حال بارگذاری...
                      </div>
                    ) : recentNotifications.length > 0 ? (
                      recentNotifications.map((notification) => (
                        <div
                          key={notification.id}
                          className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
                            !notification.read ? 'bg-blue-50' : ''
                          }`}
                          onClick={() => handleNotificationClick(notification)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">
                                {notification.title}
                              </p>
                              <p className="text-sm text-gray-600 mt-1">
                                {notification.message}
                              </p>
                              <p className="text-xs text-gray-500 mt-2">
                                {formatPersianDate(notification.createdAt)}
                              </p>
                            </div>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-gray-500">
                        اعلانی موجود نیست
                      </div>
                    )}
                  </div>
                  <div className="p-3 border-t border-gray-200">
                    <Link
                      to="/admin/notifications"
                      className="text-sm text-admin-600 hover:text-admin-700 font-medium"
                      onClick={() => setShowNotifications(false)}
                    >
                      مشاهده همه اعلان‌ها
                    </Link>
                  </div>
                </motion.div>
              </>
            )}
          </AnimatePresence>
        </div>

        {/* User Menu */}
        <div className="relative">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div className="w-8 h-8 bg-admin-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {user?.firstName.charAt(0)}{user?.lastName.charAt(0)}
              </span>
            </div>
            <div className="hidden md:block text-right">
              <p className="text-sm font-medium text-gray-900">
                {user?.firstName} {user?.lastName}
              </p>
              <p className="text-xs text-gray-500">
                {user?.role === 'super_admin' ? 'مدیر کل' :
                 user?.role === 'admin' ? 'مدیر' :
                 user?.role === 'moderator' ? 'ناظر' : 'بیننده'}
              </p>
            </div>
            <ChevronDown className="w-4 h-4 text-gray-400" />
          </button>

          {/* User Dropdown */}
          <AnimatePresence>
            {showUserMenu && (
              <>
                <div
                  className="fixed inset-0 z-10"
                  onClick={() => setShowUserMenu(false)}
                />
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-20"
                >
                  <div className="p-3 border-b border-gray-200">
                    <p className="text-sm font-medium text-gray-900">
                      {user?.firstName} {user?.lastName}
                    </p>
                    <p className="text-xs text-gray-500">
                      {user?.email}
                    </p>
                  </div>
                  <div className="py-1">
                    <Link
                      to="/admin/profile"
                      className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <User className="w-4 h-4" />
                      پروفایل کاربری
                    </Link>
                    <Link
                      to="/admin/settings"
                      className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <Settings className="w-4 h-4" />
                      تنظیمات
                    </Link>
                    {user?.role === 'super_admin' && (
                      <Link
                        to="/admin/audit"
                        className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <Shield className="w-4 h-4" />
                        گزارش عملکرد
                      </Link>
                    )}
                  </div>
                  <div className="border-t border-gray-200 py-1">
                    <button
                      onClick={handleLogout}
                      className="flex items-center gap-3 w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                    >
                      <LogOut className="w-4 h-4" />
                      خروج از حساب
                    </button>
                  </div>
                </motion.div>
              </>
            )}
          </AnimatePresence>
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;
