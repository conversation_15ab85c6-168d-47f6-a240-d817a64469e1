import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronLeft, Home } from 'lucide-react';
import { motion } from 'framer-motion';

interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface AdminBreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
}

const AdminBreadcrumb: React.FC<AdminBreadcrumbProps> = ({
  items,
  className = ''
}) => {
  const location = useLocation();

  // Auto-generate breadcrumbs from URL if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'داشبورد', path: '/admin/dashboard', icon: Home }
    ];

    if (pathSegments.length > 2) { // Skip 'admin' segment
      const segments = pathSegments.slice(1); // Remove 'admin'
      let currentPath = '/admin';

      segments.forEach((segment, index) => {
        currentPath += `/${segment}`;
        
        // Convert segment to Persian label
        const label = getSegmentLabel(segment);
        
        // Don't add link for the last segment (current page)
        const isLast = index === segments.length - 1;
        
        breadcrumbs.push({
          label,
          path: isLast ? undefined : currentPath
        });
      });
    }

    return breadcrumbs;
  };

  const getSegmentLabel = (segment: string): string => {
    const labelMap: Record<string, string> = {
      'dashboard': 'داشبورد',
      'products': 'محصولات',
      'orders': 'سفارشات',
      'customers': 'مشتریان',
      'reviews': 'نظرات',
      'loyalty': 'باشگاه مشتریان',
      'content': 'محتوا',
      'analytics': 'آمار و گزارشات',
      'settings': 'تنظیمات',
      'users': 'کاربران',
      'audit': 'گزارش عملکرد',
      'notifications': 'اعلان‌ها',
      'profile': 'پروفایل',
      'add': 'افزودن',
      'edit': 'ویرایش',
      'view': 'مشاهده',
      'categories': 'دسته‌بندی‌ها',
      'brands': 'برندها',
      'inventory': 'موجودی',
      'pending': 'در انتظار',
      'processing': 'در حال پردازش',
      'shipped': 'ارسال شده',
      'returns': 'مرجوعی‌ها',
      'approved': 'تأیید شده',
      'rejected': 'رد شده',
      'reports': 'گزارش‌ها',
      'programs': 'برنامه‌ها',
      'points': 'امتیازات',
      'rewards': 'جوایز',
      'tiers': 'سطوح',
      'pages': 'صفحات',
      'blog': 'وبلاگ',
      'banners': 'بنرها',
      'media': 'رسانه‌ها',
      'sales': 'فروش',
      'traffic': 'ترافیک',
      'general': 'عمومی',
      'payment': 'پرداخت',
      'shipping': 'ارسال',
      'admins': 'مدیران',
      'roles': 'نقش‌ها'
    };

    return labelMap[segment] || segment;
  };

  const breadcrumbItems = items || generateBreadcrumbs();

  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <nav className={`flex items-center space-x-1 space-x-reverse text-sm ${className}`}>
      {breadcrumbItems.map((item, index) => {
        const isLast = index === breadcrumbItems.length - 1;
        const Icon = item.icon;

        return (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center"
          >
            {index > 0 && (
              <ChevronLeft className="w-4 h-4 text-gray-400 mx-2" />
            )}
            
            {isLast ? (
              <span className="flex items-center gap-2 text-gray-900 font-medium">
                {Icon && <Icon className="w-4 h-4" />}
                {item.label}
              </span>
            ) : (
              <Link
                to={item.path!}
                className="flex items-center gap-2 text-gray-500 hover:text-gray-700 transition-colors"
              >
                {Icon && <Icon className="w-4 h-4" />}
                {item.label}
              </Link>
            )}
          </motion.div>
        );
      })}
    </nav>
  );
};

// Specialized breadcrumb for specific pages
export const AdminPageBreadcrumb: React.FC<{
  title: string;
  parentPath?: string;
  parentLabel?: string;
  className?: string;
}> = ({
  title,
  parentPath,
  parentLabel,
  className = ''
}) => {
  const items: BreadcrumbItem[] = [
    { label: 'داشبورد', path: '/admin/dashboard', icon: Home }
  ];

  if (parentPath && parentLabel) {
    items.push({ label: parentLabel, path: parentPath });
  }

  items.push({ label: title });

  return <AdminBreadcrumb items={items} className={className} />;
};

// Hook for managing breadcrumbs in components
export const useBreadcrumb = () => {
  const location = useLocation();

  const setBreadcrumb = (items: BreadcrumbItem[]) => {
    // This could be enhanced to work with a context provider
    // For now, it's a placeholder for future enhancement
    return items;
  };

  const addBreadcrumbItem = (item: BreadcrumbItem) => {
    // Placeholder for adding dynamic breadcrumb items
    return item;
  };

  return {
    setBreadcrumb,
    addBreadcrumbItem,
    currentPath: location.pathname
  };
};

export default AdminBreadcrumb;
