import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  Shield, 
  Eye, 
  EyeOff, 
  Crown,
  ShieldOff,
  Info
} from 'lucide-react';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { ADMIN_ROLE_PERMISSIONS } from '../../../types/admin';

const UserDebugInfo: React.FC = () => {
  const { user, checkPermission } = useAdminAuth();
  const [isVisible, setIsVisible] = useState(false);

  if (!user) return null;

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'admin':
        return <Shield className="w-4 h-4 text-blue-500" />;
      case 'moderator':
        return <ShieldOff className="w-4 h-4 text-green-500" />;
      case 'viewer':
        return <User className="w-4 h-4 text-gray-500" />;
      default:
        return <User className="w-4 h-4 text-gray-400" />;
    }
  };

  const getRoleName = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'مدیر کل';
      case 'admin':
        return 'مدیر';
      case 'moderator':
        return 'ناظر';
      case 'viewer':
        return 'بازدیدکننده';
      default:
        return 'نامشخص';
    }
  };

  const userPermissions = ADMIN_ROLE_PERMISSIONS[user.role];
  const hasUsersPermission = checkPermission('users', 'read');

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="اطلاعات کاربر فعلی"
      >
        {isVisible ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
      </motion.button>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            className="absolute bottom-16 left-0 bg-white rounded-lg shadow-xl border border-gray-200 p-4 min-w-80 max-w-md"
          >
            <div className="flex items-center gap-2 mb-3 pb-3 border-b border-gray-200">
              <Info className="w-5 h-5 text-blue-500" />
              <h3 className="font-medium text-gray-900">اطلاعات کاربر فعلی</h3>
            </div>

            <div className="space-y-3">
              {/* User Info */}
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium text-sm">
                  {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                </div>
                <div>
                  <div className="font-medium text-gray-900">
                    {user.firstName} {user.lastName}
                  </div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                </div>
              </div>

              {/* Role */}
              <div className="flex items-center gap-2">
                {getRoleIcon(user.role)}
                <span className="text-sm font-medium text-gray-700">
                  نقش: {getRoleName(user.role)}
                </span>
              </div>

              {/* Department */}
              {user.department && (
                <div className="text-sm text-gray-600">
                  بخش: {user.department}
                </div>
              )}

              {/* Users Permission Status */}
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <User className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">
                    دسترسی مدیریت کاربران
                  </span>
                </div>
                <div className={`text-sm font-medium ${
                  hasUsersPermission ? 'text-green-600' : 'text-red-600'
                }`}>
                  {hasUsersPermission ? '✅ دارد' : '❌ ندارد'}
                </div>
                {!hasUsersPermission && (
                  <div className="text-xs text-gray-500 mt-1">
                    فقط مدیر کل دسترسی دارد
                  </div>
                )}
              </div>

              {/* Permissions Summary */}
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-sm font-medium text-blue-900 mb-2">
                  خلاصه مجوزها
                </div>
                <div className="text-xs text-blue-700">
                  {userPermissions.length} منبع قابل دسترسی
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  {userPermissions.map(p => p.resource).join(', ')}
                </div>
              </div>

              {/* Login Instructions */}
              {!hasUsersPermission && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="text-sm font-medium text-yellow-800 mb-1">
                    برای دسترسی به مدیریت کاربران:
                  </div>
                  <div className="text-xs text-yellow-700">
                    با حساب <EMAIL> وارد شوید
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UserDebugInfo;
