import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  Clock, 
  Package, 
  Truck, 
  XCircle, 
  AlertTriangle,
  MessageSquare,
  Save,
  X
} from 'lucide-react';
import { AdminOrder, ORDER_STATUS_WORKFLOW, PERSIAN_ORDER_MESSAGES } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import AdminButton from '../common/AdminButton';
import AdminModal from '../common/AdminModal';

interface OrderStatusManagerProps {
  order: AdminOrder;
  onStatusUpdate: (orderId: string, newStatus: Order['status'], note?: string) => Promise<void>;
  onClose: () => void;
  isOpen: boolean;
}

const OrderStatusManager: React.FC<OrderStatusManagerProps> = ({
  order,
  onStatusUpdate,
  onClose,
  isOpen
}) => {
  const { user, checkPermission } = useAdminAuth();
  const [selectedStatus, setSelectedStatus] = useState<Order['status'] | null>(null);
  const [note, setNote] = useState('');
  const [loading, setLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'confirmed':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      case 'processing':
        return <Package className="w-5 h-5 text-purple-500" />;
      case 'shipped':
        return <Truck className="w-5 h-5 text-indigo-500" />;
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'border-yellow-200 bg-yellow-50';
      case 'confirmed':
        return 'border-blue-200 bg-blue-50';
      case 'processing':
        return 'border-purple-200 bg-purple-50';
      case 'shipped':
        return 'border-indigo-200 bg-indigo-50';
      case 'delivered':
        return 'border-green-200 bg-green-50';
      case 'cancelled':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  // Get available status transitions
  const getAvailableTransitions = () => {
    return ORDER_STATUS_WORKFLOW.filter(transition => {
      // Check if transition is from current status
      if (transition.from !== order.status) return false;
      
      // Check user permissions
      if (!user || !transition.allowedRoles.includes(user.role)) return false;
      
      return true;
    });
  };

  const availableTransitions = getAvailableTransitions();

  const handleStatusSelect = (status: Order['status']) => {
    setSelectedStatus(status);
    
    // Check if confirmation is required
    const transition = ORDER_STATUS_WORKFLOW.find(t => 
      t.from === order.status && t.to === status
    );
    
    if (transition?.requiresConfirmation) {
      setShowConfirmation(true);
    } else {
      handleStatusUpdate(status);
    }
  };

  const handleStatusUpdate = async (status: Order['status']) => {
    if (!checkPermission('orders', 'update')) {
      return;
    }

    try {
      setLoading(true);
      await onStatusUpdate(order.id, status, note.trim() || undefined);
      setNote('');
      setSelectedStatus(null);
      setShowConfirmation(false);
      onClose();
    } catch (error) {
      console.error('Error updating status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmStatusUpdate = () => {
    if (selectedStatus) {
      handleStatusUpdate(selectedStatus);
    }
  };

  const getTransitionDescription = (status: Order['status']) => {
    const transition = ORDER_STATUS_WORKFLOW.find(t => 
      t.from === order.status && t.to === status
    );
    return transition?.description || '';
  };

  return (
    <>
      <AdminModal
        isOpen={isOpen}
        onClose={onClose}
        title="مدیریت وضعیت سفارش"
        size="lg"
      >
        <div className="space-y-6">
          {/* Current Status */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getStatusIcon(order.status)}
                <div>
                  <h3 className="font-medium text-gray-900">
                    وضعیت فعلی: {PERSIAN_ORDER_MESSAGES.status[order.status]}
                  </h3>
                  <p className="text-sm text-gray-500">
                    سفارش شماره: {order.orderNumber}
                  </p>
                </div>
              </div>
              
              {order.lastStatusUpdate && (
                <div className="text-left">
                  <p className="text-xs text-gray-500">آخرین بروزرسانی</p>
                  <p className="text-sm text-gray-700">
                    {new Date(order.lastStatusUpdate).toLocaleDateString('fa-IR')}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Available Transitions */}
          {availableTransitions.length > 0 ? (
            <div>
              <h4 className="font-medium text-gray-900 mb-4">تغییرات قابل انجام</h4>
              <div className="grid grid-cols-1 gap-3">
                {availableTransitions.map((transition) => (
                  <motion.button
                    key={transition.to}
                    onClick={() => handleStatusSelect(transition.to)}
                    className={`
                      p-4 border-2 rounded-lg text-right transition-all
                      ${getStatusColor(transition.to)}
                      hover:shadow-md hover:scale-[1.02]
                      ${selectedStatus === transition.to ? 'ring-2 ring-blue-500' : ''}
                    `}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(transition.to)}
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900">
                          {transition.label}
                        </h5>
                        <p className="text-sm text-gray-600">
                          {transition.description}
                        </p>
                        {transition.requiresConfirmation && (
                          <div className="flex items-center gap-1 mt-1">
                            <AlertTriangle className="w-3 h-3 text-orange-500" />
                            <span className="text-xs text-orange-600">
                              نیاز به تأیید
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">
                هیچ تغییر وضعیتی در حال حاضر امکان‌پذیر نیست
              </p>
            </div>
          )}

          {/* Note Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MessageSquare className="inline w-4 h-4 ml-1" />
              یادداشت (اختیاری)
            </label>
            <textarea
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="توضیحات اضافی در مورد تغییر وضعیت..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <AdminButton
              variant="outline"
              onClick={onClose}
            >
              انصراف
            </AdminButton>
          </div>
        </div>
      </AdminModal>

      {/* Confirmation Modal */}
      <AdminModal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        title="تأیید تغییر وضعیت"
        size="md"
      >
        <div className="space-y-4">
          <div className="flex items-center gap-3 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <AlertTriangle className="w-6 h-6 text-orange-500 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-orange-900">
                آیا از تغییر وضعیت اطمینان دارید؟
              </h4>
              <p className="text-sm text-orange-700 mt-1">
                {selectedStatus && getTransitionDescription(selectedStatus)}
              </p>
            </div>
          </div>

          {selectedStatus && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                {getStatusIcon(order.status)}
                <span className="text-gray-600">←</span>
                {getStatusIcon(selectedStatus)}
                <div>
                  <p className="text-sm text-gray-600">
                    از "{PERSIAN_ORDER_MESSAGES.status[order.status]}" 
                    به "{PERSIAN_ORDER_MESSAGES.status[selectedStatus]}"
                  </p>
                </div>
              </div>
            </div>
          )}

          {note && (
            <div className="bg-blue-50 rounded-lg p-3">
              <p className="text-sm text-blue-900">
                <strong>یادداشت:</strong> {note}
              </p>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4">
            <AdminButton
              variant="outline"
              onClick={() => setShowConfirmation(false)}
              disabled={loading}
            >
              انصراف
            </AdminButton>
            <AdminButton
              variant="primary"
              onClick={handleConfirmStatusUpdate}
              loading={loading}
              icon={Save}
            >
              تأیید تغییر
            </AdminButton>
          </div>
        </div>
      </AdminModal>
    </>
  );
};

export default OrderStatusManager;
