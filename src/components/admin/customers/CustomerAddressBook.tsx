import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  MapPin,
  Home,
  Building,
  Plus,
  Edit,
  Trash2,
  Star,
  Phone,
  User,
  Check,
  X
} from 'lucide-react';
import AdminCard from '../common/AdminCard';
import AdminButton from '../common/AdminButton';
import { AdminFormModal } from '../common/AdminModal';
import { AdminCustomer } from '../../../types/adminCustomer';

interface CustomerAddress {
  id: string;
  type: 'home' | 'work' | 'other';
  title: string;
  recipientName: string;
  recipientPhone: string;
  province: string;
  city: string;
  district: string;
  address: string;
  postalCode: string;
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
  lastUsed?: string;
  orderCount: number;
}

interface CustomerAddressBookProps {
  customer: AdminCustomer;
  onAddAddress: (address: Omit<CustomerAddress, 'id' | 'createdAt' | 'orderCount'>) => Promise<void>;
  onUpdateAddress: (addressId: string, address: Partial<CustomerAddress>) => Promise<void>;
  onDeleteAddress: (addressId: string) => Promise<void>;
  onSetDefaultAddress: (addressId: string) => Promise<void>;
}

const CustomerAddressBook: React.FC<CustomerAddressBookProps> = ({
  customer,
  onAddAddress,
  onUpdateAddress,
  onDeleteAddress,
  onSetDefaultAddress
}) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<CustomerAddress | null>(null);
  const [addressData, setAddressData] = useState({
    type: 'home' as CustomerAddress['type'],
    title: '',
    recipientName: '',
    recipientPhone: '',
    province: '',
    city: '',
    district: '',
    address: '',
    postalCode: '',
    isDefault: false,
    isActive: true
  });

  // Mock address data - in real app, this would come from API
  const generateMockAddresses = (): CustomerAddress[] => {
    return [
      {
        id: 'addr-1',
        type: 'home',
        title: 'منزل',
        recipientName: customer.firstName + ' ' + customer.lastName,
        recipientPhone: customer.phone,
        province: 'تهران',
        city: 'تهران',
        district: 'ولیعصر',
        address: 'خیابان ولیعصر، کوچه شهید احمدی، پلاک ۱۲۳، واحد ۴',
        postalCode: '1234567890',
        isDefault: true,
        isActive: true,
        createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
        lastUsed: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        orderCount: 15
      },
      {
        id: 'addr-2',
        type: 'work',
        title: 'محل کار',
        recipientName: customer.firstName + ' ' + customer.lastName,
        recipientPhone: customer.phone,
        province: 'تهران',
        city: 'تهران',
        district: 'سعادت‌آباد',
        address: 'میدان کاج، برج میلاد، طبقه ۱۰، واحد ۱۰۰۵',
        postalCode: '0987654321',
        isDefault: false,
        isActive: true,
        createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
        lastUsed: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        orderCount: 5
      },
      {
        id: 'addr-3',
        type: 'other',
        title: 'منزل والدین',
        recipientName: 'مریم احمدی',
        recipientPhone: '09123456789',
        province: 'اصفهان',
        city: 'اصفهان',
        district: 'چهارباغ',
        address: 'خیابان چهارباغ عباسی، کوچه گلستان، پلاک ۴۵',
        postalCode: '8765432109',
        isDefault: false,
        isActive: false,
        createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        lastUsed: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
        orderCount: 2
      }
    ];
  };

  const addresses = generateMockAddresses();

  const handleAddAddress = async () => {
    try {
      await onAddAddress(addressData);
      setAddressData({
        type: 'home',
        title: '',
        recipientName: '',
        recipientPhone: '',
        province: '',
        city: '',
        district: '',
        address: '',
        postalCode: '',
        isDefault: false,
        isActive: true
      });
      setShowAddModal(false);
    } catch (error) {
      // Error handled by parent
    }
  };

  const handleEditAddress = async () => {
    if (!selectedAddress) return;
    
    try {
      await onUpdateAddress(selectedAddress.id, addressData);
      setSelectedAddress(null);
      setShowEditModal(false);
    } catch (error) {
      // Error handled by parent
    }
  };

  const handleDeleteAddress = async (addressId: string) => {
    if (window.confirm('آیا از حذف این آدرس اطمینان دارید؟')) {
      try {
        await onDeleteAddress(addressId);
      } catch (error) {
        // Error handled by parent
      }
    }
  };

  const handleSetDefault = async (addressId: string) => {
    try {
      await onSetDefaultAddress(addressId);
    } catch (error) {
      // Error handled by parent
    }
  };

  const openEditModal = (address: CustomerAddress) => {
    setSelectedAddress(address);
    setAddressData({
      type: address.type,
      title: address.title,
      recipientName: address.recipientName,
      recipientPhone: address.recipientPhone,
      province: address.province,
      city: address.city,
      district: address.district,
      address: address.address,
      postalCode: address.postalCode,
      isDefault: address.isDefault,
      isActive: address.isActive
    });
    setShowEditModal(true);
  };

  const getAddressTypeIcon = (type: CustomerAddress['type']) => {
    switch (type) {
      case 'home':
        return <Home className="w-5 h-5 text-blue-600" />;
      case 'work':
        return <Building className="w-5 h-5 text-purple-600" />;
      case 'other':
        return <MapPin className="w-5 h-5 text-gray-600" />;
      default:
        return <MapPin className="w-5 h-5 text-gray-400" />;
    }
  };

  const getAddressTypeText = (type: CustomerAddress['type']) => {
    switch (type) {
      case 'home': return 'منزل';
      case 'work': return 'محل کار';
      case 'other': return 'سایر';
      default: return 'نامشخص';
    }
  };

  const formatPersianDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fa-IR');
  };

  return (
    <div className="space-y-6">
      {/* Address Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <MapPin className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">کل آدرس‌ها</p>
              <p className="text-2xl font-bold text-gray-900">
                {addresses.length.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Check className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">آدرس‌های فعال</p>
              <p className="text-2xl font-bold text-gray-900">
                {addresses.filter(a => a.isActive).length.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">آدرس پیش‌فرض</p>
              <p className="text-lg font-bold text-gray-900">
                {addresses.find(a => a.isDefault)?.title || 'تعریف نشده'}
              </p>
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Address List */}
      <AdminCard 
        title="دفترچه آدرس"
        headerActions={
          <AdminButton
            variant="primary"
            size="sm"
            icon={Plus}
            onClick={() => setShowAddModal(true)}
          >
            افزودن آدرس
          </AdminButton>
        }
      >
        <div className="space-y-4">
          {addresses.map((address) => (
            <motion.div
              key={address.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`border rounded-lg p-4 ${
                address.isDefault ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
              } ${!address.isActive ? 'opacity-60' : ''}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <div className="mt-1">
                    {getAddressTypeIcon(address.type)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium text-gray-900">{address.title}</h4>
                      {address.isDefault && (
                        <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <Star className="w-3 h-3" />
                          پیش‌فرض
                        </span>
                      )}
                      {!address.isActive && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          غیرفعال
                        </span>
                      )}
                    </div>
                    
                    <div className="space-y-1 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4" />
                        <span>{address.recipientName}</span>
                        <Phone className="w-4 h-4 mr-2" />
                        <span>{address.recipientPhone}</span>
                      </div>
                      
                      <div className="flex items-start gap-2">
                        <MapPin className="w-4 h-4 mt-0.5" />
                        <div>
                          <p>{address.province}، {address.city}، {address.district}</p>
                          <p>{address.address}</p>
                          <p>کد پستی: {address.postalCode}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4 mt-3 text-xs text-gray-500">
                      <span>ایجاد: {formatPersianDate(address.createdAt)}</span>
                      {address.lastUsed && (
                        <span>آخرین استفاده: {formatPersianDate(address.lastUsed)}</span>
                      )}
                      <span>{address.orderCount} سفارش</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  {!address.isDefault && address.isActive && (
                    <AdminButton
                      variant="ghost"
                      size="sm"
                      icon={Star}
                      onClick={() => handleSetDefault(address.id)}
                      title="تنظیم به عنوان پیش‌فرض"
                    />
                  )}
                  
                  <AdminButton
                    variant="ghost"
                    size="sm"
                    icon={Edit}
                    onClick={() => openEditModal(address)}
                    title="ویرایش"
                  />
                  
                  <AdminButton
                    variant="ghost"
                    size="sm"
                    icon={Trash2}
                    onClick={() => handleDeleteAddress(address.id)}
                    className="text-red-600 hover:text-red-700"
                    title="حذف"
                  />
                </div>
              </div>
            </motion.div>
          ))}
          
          {addresses.length === 0 && (
            <div className="text-center py-8">
              <MapPin className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">هیچ آدرسی ثبت نشده است</p>
            </div>
          )}
        </div>
      </AdminCard>

      {/* Add Address Modal */}
      <AdminFormModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleAddAddress}
        title="افزودن آدرس جدید"
        submitText="افزودن آدرس"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                نوع آدرس
              </label>
              <select
                value={addressData.type}
                onChange={(e) => setAddressData(prev => ({ ...prev, type: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="home">منزل</option>
                <option value="work">محل کار</option>
                <option value="other">سایر</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                عنوان آدرس
              </label>
              <input
                type="text"
                value={addressData.title}
                onChange={(e) => setAddressData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="مثال: منزل، محل کار"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                نام گیرنده
              </label>
              <input
                type="text"
                value={addressData.recipientName}
                onChange={(e) => setAddressData(prev => ({ ...prev, recipientName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="نام و نام خانوادگی گیرنده"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                شماره تماس گیرنده
              </label>
              <input
                type="tel"
                value={addressData.recipientPhone}
                onChange={(e) => setAddressData(prev => ({ ...prev, recipientPhone: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="09123456789"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                استان
              </label>
              <input
                type="text"
                value={addressData.province}
                onChange={(e) => setAddressData(prev => ({ ...prev, province: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="تهران"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                شهر
              </label>
              <input
                type="text"
                value={addressData.city}
                onChange={(e) => setAddressData(prev => ({ ...prev, city: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="تهران"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                منطقه/محله
              </label>
              <input
                type="text"
                value={addressData.district}
                onChange={(e) => setAddressData(prev => ({ ...prev, district: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="ولیعصر"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              آدرس کامل
            </label>
            <textarea
              value={addressData.address}
              onChange={(e) => setAddressData(prev => ({ ...prev, address: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="آدرس کامل شامل خیابان، کوچه، پلاک و واحد"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              کد پستی
            </label>
            <input
              type="text"
              value={addressData.postalCode}
              onChange={(e) => setAddressData(prev => ({ ...prev, postalCode: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="1234567890"
              maxLength={10}
            />
          </div>
          
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={addressData.isDefault}
                onChange={(e) => setAddressData(prev => ({ ...prev, isDefault: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">تنظیم به عنوان آدرس پیش‌فرض</span>
            </label>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={addressData.isActive}
                onChange={(e) => setAddressData(prev => ({ ...prev, isActive: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">آدرس فعال</span>
            </label>
          </div>
        </div>
      </AdminFormModal>

      {/* Edit Address Modal */}
      <AdminFormModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSubmit={handleEditAddress}
        title="ویرایش آدرس"
        submitText="ذخیره تغییرات"
      >
        {/* Same form fields as Add Modal */}
        <div className="space-y-4">
          {/* Form content identical to Add Modal */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                نوع آدرس
              </label>
              <select
                value={addressData.type}
                onChange={(e) => setAddressData(prev => ({ ...prev, type: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="home">منزل</option>
                <option value="work">محل کار</option>
                <option value="other">سایر</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                عنوان آدرس
              </label>
              <input
                type="text"
                value={addressData.title}
                onChange={(e) => setAddressData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="مثال: منزل، محل کار"
              />
            </div>
          </div>
          
          {/* Rest of form fields... */}
        </div>
      </AdminFormModal>
    </div>
  );
};

export default CustomerAddressBook;
