import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Gift,
  Star,
  TrendingUp,
  Award,
  Calendar,
  Plus,
  Minus,
  History,
  Target,
  Crown
} from 'lucide-react';
import AdminCard from '../common/AdminCard';
import AdminButton from '../common/AdminButton';
import AdminTable, { AdminTableColumn } from '../common/AdminTable';
import { AdminFormModal } from '../common/AdminModal';
import { AdminCustomer } from '../../../types/adminCustomer';
import { LOYALTY_TIERS } from '../../../types/loyalty';
import { formatPersianCurrency, formatPersianDate } from '../../../utils/customerUtils';

interface PointTransaction {
  id: string;
  type: 'earned' | 'redeemed' | 'expired' | 'bonus' | 'adjustment';
  points: number;
  description: string;
  orderId?: string;
  date: string;
  expiryDate?: string;
  adminNote?: string;
}

interface CustomerLoyaltyStatusProps {
  customer: AdminCustomer;
  onAdjustPoints: (points: number, reason: string) => Promise<void>;
}

const CustomerLoyaltyStatus: React.FC<CustomerLoyaltyStatusProps> = ({
  customer,
  onAdjustPoints
}) => {
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [adjustmentData, setAdjustmentData] = useState({
    points: 0,
    reason: '',
    type: 'bonus' as 'bonus' | 'adjustment'
  });

  // Mock loyalty data - in real app, this would come from loyalty API
  const currentTier = LOYALTY_TIERS.find(tier => 
    customer.loyaltyPoints >= tier.minPoints && 
    (!tier.maxPoints || customer.loyaltyPoints <= tier.maxPoints)
  ) || LOYALTY_TIERS[0];

  const nextTier = LOYALTY_TIERS.find(tier => tier.level === currentTier.level + 1);
  const pointsToNextTier = nextTier ? nextTier.minPoints - customer.loyaltyPoints : 0;
  const progressPercentage = nextTier ? 
    Math.min(100, (customer.loyaltyPoints / nextTier.minPoints) * 100) : 100;

  // Mock point transactions
  const generateMockTransactions = (): PointTransaction[] => {
    const types: PointTransaction['type'][] = ['earned', 'redeemed', 'bonus', 'adjustment'];
    const descriptions = [
      'خرید محصولات مراقبت از پوست',
      'استفاده از کد تخفیف',
      'هدیه عضویت در باشگاه مشتریان',
      'بونوس تولد',
      'تعدیل امتیاز توسط ادمین',
      'خرید سرم ویتامین C',
      'ثبت نظر برای محصول',
      'معرفی دوست'
    ];

    return Array.from({ length: 20 }, (_, i) => ({
      id: `tx-${i + 1}`,
      type: types[Math.floor(Math.random() * types.length)],
      points: Math.floor(Math.random() * 200) * (Math.random() > 0.7 ? -1 : 1),
      description: descriptions[Math.floor(Math.random() * descriptions.length)],
      orderId: Math.random() > 0.5 ? `GR${1000 + i}` : undefined,
      date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      expiryDate: Math.random() > 0.5 ? 
        new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() : undefined,
      adminNote: Math.random() > 0.8 ? 'تعدیل دستی توسط ادمین' : undefined
    }));
  };

  const transactions = generateMockTransactions();

  const handleAdjustPoints = async () => {
    try {
      await onAdjustPoints(adjustmentData.points, adjustmentData.reason);
      setAdjustmentData({ points: 0, reason: '', type: 'bonus' });
      setShowAdjustModal(false);
    } catch (error) {
      // Error handled by parent
    }
  };

  const getTransactionIcon = (type: PointTransaction['type']) => {
    switch (type) {
      case 'earned':
        return <Plus className="w-4 h-4 text-green-500" />;
      case 'redeemed':
        return <Minus className="w-4 h-4 text-red-500" />;
      case 'bonus':
        return <Gift className="w-4 h-4 text-purple-500" />;
      case 'adjustment':
        return <TrendingUp className="w-4 h-4 text-blue-500" />;
      case 'expired':
        return <Calendar className="w-4 h-4 text-gray-500" />;
      default:
        return <History className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTransactionTypeText = (type: PointTransaction['type']) => {
    switch (type) {
      case 'earned': return 'کسب امتیاز';
      case 'redeemed': return 'استفاده امتیاز';
      case 'bonus': return 'امتیاز هدیه';
      case 'adjustment': return 'تعدیل امتیاز';
      case 'expired': return 'انقضای امتیاز';
      default: return 'نامشخص';
    }
  };

  const columns: AdminTableColumn<PointTransaction>[] = [
    {
      key: 'type',
      title: 'نوع تراکنش',
      render: (_, transaction) => (
        <div className="flex items-center gap-2">
          {getTransactionIcon(transaction.type)}
          <span className="text-sm text-gray-600">
            {getTransactionTypeText(transaction.type)}
          </span>
        </div>
      )
    },
    {
      key: 'points',
      title: 'امتیاز',
      render: (_, transaction) => (
        <span className={`font-medium ${
          transaction.points > 0 ? 'text-green-600' : 'text-red-600'
        }`}>
          {transaction.points > 0 ? '+' : ''}{transaction.points.toLocaleString('fa-IR')}
        </span>
      )
    },
    {
      key: 'description',
      title: 'توضیحات',
      render: (_, transaction) => (
        <div>
          <p className="text-sm text-gray-900">{transaction.description}</p>
          {transaction.orderId && (
            <p className="text-xs text-gray-500">سفارش: {transaction.orderId}</p>
          )}
          {transaction.adminNote && (
            <p className="text-xs text-blue-600">{transaction.adminNote}</p>
          )}
        </div>
      )
    },
    {
      key: 'date',
      title: 'تاریخ',
      sortable: true,
      render: (_, transaction) => (
        <div className="text-sm text-gray-600">
          {formatPersianDate(transaction.date)}
        </div>
      )
    },
    {
      key: 'expiry',
      title: 'انقضا',
      render: (_, transaction) => (
        <div className="text-sm text-gray-500">
          {transaction.expiryDate ? formatPersianDate(transaction.expiryDate) : '-'}
        </div>
      )
    }
  ];

  const totalEarned = transactions
    .filter(t => t.points > 0)
    .reduce((sum, t) => sum + t.points, 0);

  const totalRedeemed = transactions
    .filter(t => t.points < 0)
    .reduce((sum, t) => sum + Math.abs(t.points), 0);

  return (
    <div className="space-y-6">
      {/* Loyalty Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">امتیاز فعلی</p>
              <p className="text-2xl font-bold text-gray-900">
                {customer.loyaltyPoints.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Plus className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">کل کسب شده</p>
              <p className="text-2xl font-bold text-gray-900">
                {totalEarned.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <Minus className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">کل استفاده شده</p>
              <p className="text-2xl font-bold text-gray-900">
                {totalRedeemed.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">سطح عضویت</p>
              <p className="text-lg font-bold text-gray-900">
                {currentTier.persianName}
              </p>
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Current Tier Status */}
      <AdminCard title="وضعیت عضویت در باشگاه مشتریان">
        <div className="space-y-6">
          {/* Tier Card */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="text-3xl">{currentTier.icon}</div>
                <div>
                  <h3 className="text-xl font-bold">{currentTier.persianName}</h3>
                  <p className="text-blue-100">{currentTier.description}</p>
                </div>
              </div>
              <Crown className="w-8 h-8 text-yellow-300" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>امتیاز فعلی</span>
                <span>{customer.loyaltyPoints.toLocaleString('fa-IR')}</span>
              </div>
              {nextTier && (
                <>
                  <div className="flex justify-between text-sm">
                    <span>تا سطح بعدی</span>
                    <span>{pointsToNextTier.toLocaleString('fa-IR')} امتیاز</span>
                  </div>
                  <div className="w-full bg-blue-400 rounded-full h-2">
                    <div 
                      className="bg-yellow-300 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progressPercentage}%` }}
                    />
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Benefits */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">مزایای سطح فعلی</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {currentTier.benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span>{benefit}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Next Tier Preview */}
          {nextTier && (
            <div className="border-t pt-4">
              <h4 className="font-medium text-gray-900 mb-3">مزایای سطح بعدی ({nextTier.persianName})</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {nextTier.benefits.slice(0, 4).map((benefit, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-gray-500">
                    <div className="w-2 h-2 bg-gray-300 rounded-full" />
                    <span>{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </AdminCard>

      {/* Point Adjustment */}
      <AdminCard 
        title="مدیریت امتیازات"
        headerActions={
          <AdminButton
            variant="primary"
            size="sm"
            icon={Plus}
            onClick={() => setShowAdjustModal(true)}
          >
            تعدیل امتیاز
          </AdminButton>
        }
      >
        <div className="text-center py-4">
          <p className="text-gray-600 mb-4">
            برای تعدیل امتیازات مشتری از دکمه "تعدیل امتیاز" استفاده کنید
          </p>
          <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <Plus className="w-4 h-4 text-green-500" />
              <span>افزایش امتیاز</span>
            </div>
            <div className="flex items-center gap-1">
              <Minus className="w-4 h-4 text-red-500" />
              <span>کاهش امتیاز</span>
            </div>
          </div>
        </div>
      </AdminCard>

      {/* Transaction History */}
      <AdminCard title="تاریخچه تراکنش‌های امتیاز">
        <AdminTable
          columns={columns}
          data={transactions}
          emptyMessage="هیچ تراکنشی یافت نشد"
          hoverable
          striped
        />
      </AdminCard>

      {/* Point Adjustment Modal */}
      <AdminFormModal
        isOpen={showAdjustModal}
        onClose={() => setShowAdjustModal(false)}
        onSubmit={handleAdjustPoints}
        title="تعدیل امتیاز مشتری"
        submitText="اعمال تغییرات"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نوع تعدیل
            </label>
            <select
              value={adjustmentData.type}
              onChange={(e) => setAdjustmentData(prev => ({ ...prev, type: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="bonus">امتیاز هدیه</option>
              <option value="adjustment">تعدیل امتیاز</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              مقدار امتیاز
            </label>
            <input
              type="number"
              value={adjustmentData.points}
              onChange={(e) => setAdjustmentData(prev => ({ ...prev, points: parseInt(e.target.value) || 0 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="مقدار امتیاز (مثبت برای افزایش، منفی برای کاهش)"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              دلیل تعدیل
            </label>
            <textarea
              value={adjustmentData.reason}
              onChange={(e) => setAdjustmentData(prev => ({ ...prev, reason: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="دلیل تعدیل امتیاز را وارد کنید..."
            />
          </div>
        </div>
      </AdminFormModal>
    </div>
  );
};

export default CustomerLoyaltyStatus;
