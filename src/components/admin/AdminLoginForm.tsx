import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Eye, EyeOff, Mail, Lock, Shield, Key } from 'lucide-react';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import { AdminLoginCredentials, PERSIAN_ADMIN_MESSAGES } from '../../types/admin';
import { EmailValidator } from '../../utils/authUtils';

// Validation schema
const adminLoginSchema = yup.object({
  email: yup
    .string()
    .required('ایمیل الزامی است')
    .test('email', 'ایمیل معتبر وارد کنید', (value) => 
      value ? EmailValidator.validate(value) : false
    ),
  password: yup
    .string()
    .required('رمز عبور الزامی است')
    .min(6, 'رمز عبور باید حداقل ۶ کاراکتر باشد'),
  twoFactorCode: yup
    .string()
    .when('twoFactorRequired', {
      is: true,
      then: (schema) => schema.required('کد دو مرحله‌ای الزامی است').length(6, 'کد باید ۶ رقم باشد'),
      otherwise: (schema) => schema.notRequired()
    }),
  rememberMe: yup.boolean()
});

interface AdminLoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

const AdminLoginForm: React.FC<AdminLoginFormProps> = ({ onSuccess, redirectTo }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [twoFactorRequired, setTwoFactorRequired] = useState(false);
  const { login, isLoading, error, clearError } = useAdminAuth();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setError,
    watch
  } = useForm<AdminLoginCredentials & { twoFactorRequired?: boolean }>({
    resolver: yupResolver(adminLoginSchema),
    defaultValues: {
      email: '',
      password: '',
      twoFactorCode: '',
      rememberMe: false,
      twoFactorRequired: false
    },
    mode: 'onChange',
    context: { twoFactorRequired }
  });

  const watchedEmail = watch('email');

  const onSubmit = async (data: AdminLoginCredentials) => {
    try {
      clearError();
      await login({
        email: EmailValidator.normalize(data.email),
        password: data.password,
        twoFactorCode: data.twoFactorCode,
        rememberMe: data.rememberMe
      });
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      // Handle 2FA requirement
      if (error.message.includes('دو مرحله‌ای')) {
        setTwoFactorRequired(true);
        return;
      }

      // Handle specific validation errors
      if (error.message.includes('ایمیل')) {
        setError('email', { message: error.message });
      } else if (error.message.includes('رمز عبور')) {
        setError('password', { message: error.message });
      } else if (error.message.includes('کد')) {
        setError('twoFactorCode', { message: error.message });
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full max-w-md mx-auto"
    >
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 p-8">
        <div className="text-center mb-8">
          <div className="bg-primary-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
            <Shield className="w-10 h-10 text-primary-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {PERSIAN_ADMIN_MESSAGES.auth.login}
          </h2>
          <p className="text-gray-600">
            پنل مدیریت گلو رویا
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Mail className="w-4 h-4 inline ml-1" />
              {PERSIAN_ADMIN_MESSAGES.auth.email} *
            </label>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="email"
                  placeholder="<EMAIL>"
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                    errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                />
              )}
            />
            {errors.email && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-1 text-sm text-red-600"
              >
                {errors.email.message}
              </motion.p>
            )}
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Lock className="w-4 h-4 inline ml-1" />
              {PERSIAN_ADMIN_MESSAGES.auth.password} *
            </label>
            <div className="relative">
              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="رمز عبور"
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors pr-12 ${
                      errors.password ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
            </div>
            {errors.password && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-1 text-sm text-red-600"
              >
                {errors.password.message}
              </motion.p>
            )}
          </div>

          {/* Two Factor Code Field */}
          {twoFactorRequired && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="overflow-hidden"
            >
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Key className="w-4 h-4 inline ml-1" />
                {PERSIAN_ADMIN_MESSAGES.auth.twoFactorCode} *
              </label>
              <Controller
                name="twoFactorCode"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="text"
                    placeholder="123456"
                    maxLength={6}
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors text-center tracking-widest ${
                      errors.twoFactorCode ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              {errors.twoFactorCode && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-1 text-sm text-red-600"
                >
                  {errors.twoFactorCode.message}
                </motion.p>
              )}
            </motion.div>
          )}

          {/* Remember Me */}
          <div className="flex items-center justify-between">
            <Controller
              name="rememberMe"
              control={control}
              render={({ field }) => (
                <label className="flex items-center">
                  <input
                    {...field}
                    type="checkbox"
                    checked={field.value}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    disabled={isLoading}
                  />
                  <span className="mr-2 text-sm text-gray-600">
                    {PERSIAN_ADMIN_MESSAGES.auth.rememberMe}
                  </span>
                </label>
              )}
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                در حال ورود...
              </>
            ) : (
              <>
                <Shield className="w-5 h-5" />
                {PERSIAN_ADMIN_MESSAGES.auth.loginButton}
              </>
            )}
          </button>

          {/* Error Message */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 border border-red-200 rounded-lg p-3"
            >
              <p className="text-sm text-red-600 text-center">{error}</p>
            </motion.div>
          )}
        </form>

        {/* Demo Credentials */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4"
        >
          <h4 className="text-sm font-medium text-blue-900 mb-2">
            🔑 اطلاعات ورود نمونه:
          </h4>
          <div className="text-xs text-blue-700 space-y-1">
            <p><strong>مدیر ارشد:</strong> <EMAIL> / Test123!</p>
            <p><strong>مدیر تست:</strong> <EMAIL> / Test123!</p>
          </div>
        </motion.div>

        {/* Security Notice */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            🔒 این صفحه با SSL محافظت می‌شود
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default AdminLoginForm;
