import React from 'react';
import { motion } from 'framer-motion';
import { Check, ShoppingCart, MapPin, CreditCard, FileCheck } from 'lucide-react';
import { CheckoutStep } from '../../types/checkout';

interface CheckoutStepsProps {
  steps: CheckoutStep[];
  currentStep: number;
  onStepClick?: (stepIndex: number) => void;
}

const CheckoutSteps: React.FC<CheckoutStepsProps> = ({
  steps,
  currentStep,
  onStepClick
}) => {
  const getStepIcon = (stepId: string, isCompleted: boolean, isActive: boolean) => {
    const iconClass = `w-5 h-5 ${
      isCompleted ? 'text-white' : isActive ? 'text-primary-600' : 'text-gray-400'
    }`;

    if (isCompleted) {
      return <Check className={iconClass} />;
    }

    switch (stepId) {
      case 'cart':
        return <ShoppingCart className={iconClass} />;
      case 'shipping':
        return <MapPin className={iconClass} />;
      case 'payment':
        return <CreditCard className={iconClass} />;
      case 'review':
        return <FileCheck className={iconClass} />;
      default:
        return <div className={`w-5 h-5 rounded-full border-2 ${iconClass}`} />;
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center flex-1">
            {/* Step Circle */}
            <div className="relative flex items-center">
              <motion.button
                onClick={() => !step.isDisabled && onStepClick?.(index)}
                disabled={step.isDisabled}
                className={`
                  relative z-10 flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200
                  ${step.isCompleted
                    ? 'bg-green-500 border-green-500'
                    : step.isActive
                    ? 'bg-white border-primary-500'
                    : 'bg-white border-gray-300'
                  }
                  ${!step.isDisabled && onStepClick ? 'cursor-pointer hover:scale-105' : ''}
                  ${step.isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
                `}
                whileHover={!step.isDisabled ? { scale: 1.05 } : {}}
                whileTap={!step.isDisabled ? { scale: 0.95 } : {}}
              >
                {getStepIcon(step.id, step.isCompleted, step.isActive)}
              </motion.button>

              {/* Step Number (for mobile) */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 md:hidden">
                <span className={`text-xs font-medium ${
                  step.isActive ? 'text-primary-600' : 'text-gray-500'
                }`}>
                  {index + 1}
                </span>
              </div>
            </div>

            {/* Step Content */}
            <div className="mr-3 flex-1 hidden md:block">
              <h3 className={`text-sm font-medium ${
                step.isActive ? 'text-primary-600' : step.isCompleted ? 'text-green-600' : 'text-gray-500'
              }`}>
                {step.title}
              </h3>
              <p className="text-xs text-gray-500 mt-1">
                {step.description}
              </p>
            </div>

            {/* Connector Line */}
            {index < steps.length - 1 && (
              <div className="flex-1 mx-4 hidden md:block">
                <div className={`h-0.5 transition-colors duration-200 ${
                  step.isCompleted ? 'bg-green-500' : 'bg-gray-200'
                }`} />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Mobile Step Info */}
      <div className="mt-6 md:hidden">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">
            {steps[currentStep]?.title}
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            {steps[currentStep]?.description}
          </p>
          <div className="mt-2">
            <span className="text-xs text-gray-500">
              مرحله {currentStep + 1} از {steps.length}
            </span>
          </div>
        </div>
      </div>

      {/* Progress Bar (Mobile) */}
      <div className="mt-4 md:hidden">
        <div className="bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-primary-500 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </div>
    </div>
  );
};

// Compact version for smaller spaces
interface CompactCheckoutStepsProps {
  currentStep: number;
  totalSteps: number;
  stepTitles: string[];
}

export const CompactCheckoutSteps: React.FC<CompactCheckoutStepsProps> = ({
  currentStep,
  totalSteps,
  stepTitles
}) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium text-gray-900">
          {stepTitles[currentStep]}
        </h3>
        <span className="text-sm text-gray-500">
          {currentStep + 1} / {totalSteps}
        </span>
      </div>
      
      <div className="flex gap-1">
        {Array.from({ length: totalSteps }, (_, index) => (
          <div
            key={index}
            className={`flex-1 h-2 rounded-full transition-colors duration-200 ${
              index <= currentStep ? 'bg-primary-500' : 'bg-gray-200'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

// Step navigation buttons
interface StepNavigationProps {
  currentStep: number;
  totalSteps: number;
  onPrevious: () => void;
  onNext: () => void;
  onSubmit?: () => void;
  isNextDisabled?: boolean;
  isLoading?: boolean;
  nextButtonText?: string;
  submitButtonText?: string;
}

export const StepNavigation: React.FC<StepNavigationProps> = ({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onSubmit,
  isNextDisabled = false,
  isLoading = false,
  nextButtonText = 'مرحله بعد',
  submitButtonText = 'ثبت سفارش'
}) => {
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <div className="flex justify-between items-center pt-6 border-t border-gray-200">
      <button
        onClick={onPrevious}
        disabled={currentStep === 0 || isLoading}
        className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        مرحله قبل
      </button>

      <div className="flex items-center gap-2">
        {/* Step indicators */}
        <div className="flex gap-1">
          {Array.from({ length: totalSteps }, (_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                index <= currentStep ? 'bg-primary-500' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>

      <motion.button
        onClick={isLastStep ? onSubmit : onNext}
        disabled={isNextDisabled || isLoading}
        className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {isLoading && (
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
        )}
        {isLastStep ? submitButtonText : nextButtonText}
      </motion.button>
    </div>
  );
};

export default CheckoutSteps;
