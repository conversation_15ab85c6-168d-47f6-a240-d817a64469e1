import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CreditCard,
  Wallet,
  Truck,
  Shield,
  Info,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import {
  PaymentMethod,
  PAYMENT_METHODS
} from '../../types/checkout';

interface PaymentFormProps {
  selectedPayment?: PaymentMethod;
  onPaymentChange: (payment: PaymentMethod) => void;
  orderTotal: number;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  selectedPayment,
  onPaymentChange,
  orderTotal
}) => {
  const getPaymentIcon = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'card':
        return <CreditCard className="w-5 h-5" />;
      case 'wallet':
        return <Wallet className="w-5 h-5" />;
      case 'cash_on_delivery':
        return <Truck className="w-5 h-5" />;
      default:
        return <CreditCard className="w-5 h-5" />;
    }
  };

  const handlePaymentSelect = (payment: PaymentMethod) => {
    onPaymentChange(payment);
  };

  return (
    <div className="space-y-6">
      {/* Payment Methods */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Shield className="w-5 h-5 text-primary-600" />
          روش پرداخت
        </h3>

        <div className="space-y-3">
          {PAYMENT_METHODS.map((method) => (
            <div key={method.id}>
              <label
                className={`
                  flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-all duration-200
                  ${!method.isAvailable ? 'opacity-50 cursor-not-allowed' : ''}
                  ${selectedPayment?.id === method.id 
                    ? 'border-primary-500 bg-primary-50' 
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}
              >
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    name="payment"
                    checked={selectedPayment?.id === method.id}
                    onChange={() => method.isAvailable && handlePaymentSelect(method)}
                    disabled={!method.isAvailable}
                    className="text-primary-600"
                  />
                  <div className="text-primary-600">
                    {getPaymentIcon(method.type)}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 flex items-center gap-2">
                      {method.name}
                      {!method.isAvailable && (
                        <span className="bg-gray-100 text-gray-500 text-xs px-2 py-0.5 rounded-full">
                          غیرفعال
                        </span>
                      )}
                    </h4>
                    <p className="text-sm text-gray-600">{method.description}</p>
                    {method.processingFee && method.processingFee > 0 && (
                      <p className="text-xs text-orange-600 mt-1">
                        کارمزد: {method.processingFee.toLocaleString()} تومان
                      </p>
                    )}
                  </div>
                </div>

              </label>
            </div>
          ))}
        </div>

        {/* Payment Security Info */}
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-start gap-3">
            <Shield className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-800 mb-1">پرداخت امن</h4>
              <p className="text-sm text-green-700">
                تمامی پرداخت‌ها از طریق درگاه‌های معتبر بانکی و با رمزگذاری SSL انجام می‌شود.
                اطلاعات کارت شما هیچ‌گاه ذخیره نمی‌شود.
              </p>
            </div>
          </div>
        </div>
      </div>



      {/* Selected Payment Summary */}
      {selectedPayment && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            خلاصه پرداخت
          </h3>

          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">روش پرداخت:</span>
              <span className="font-medium">{selectedPayment.name}</span>
            </div>

            {selectedPayment.processingFee && selectedPayment.processingFee > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">کارمزد:</span>
                <span className="font-medium">{selectedPayment.processingFee.toLocaleString()} تومان</span>
              </div>
            )}

            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between text-lg font-semibold">
                <span>مبلغ قابل پرداخت:</span>
                <span className="text-primary-600">
                  {(orderTotal + (selectedPayment.processingFee || 0)).toLocaleString()} تومان
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default PaymentForm;
