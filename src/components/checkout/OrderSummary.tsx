import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ShoppingBag, 
  Tag, 
  Truck, 
  Calculator,
  Gift,
  X,
  Check,
  AlertCircle
} from 'lucide-react';
import { CartItem } from '../../types';
import { OrderSummary as OrderSummaryType, ShippingMethod, PaymentMethod } from '../../types/checkout';

interface OrderSummaryProps {
  items: CartItem[];
  orderSummary: OrderSummaryType;
  shippingMethod?: ShippingMethod;
  paymentMethod?: PaymentMethod;
  onApplyDiscount?: (code: string) => Promise<boolean>;
  onRemoveDiscount?: () => void;
  discountCode?: string;
  isLoading?: boolean;
  compact?: boolean;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({
  items,
  orderSummary,
  shippingMethod,
  paymentMethod,
  onApplyDiscount,
  onRemoveDiscount,
  discountCode,
  isLoading = false,
  compact = false
}) => {
  const [showDiscountForm, setShowDiscountForm] = useState(false);
  const [discountInput, setDiscountInput] = useState('');
  const [discountLoading, setDiscountLoading] = useState(false);
  const [discountError, setDiscountError] = useState('');

  const handleApplyDiscount = async () => {
    if (!discountInput.trim() || !onApplyDiscount) return;
    
    setDiscountLoading(true);
    setDiscountError('');
    
    try {
      const success = await onApplyDiscount(discountInput.trim());
      if (success) {
        setShowDiscountForm(false);
        setDiscountInput('');
      } else {
        setDiscountError('کد تخفیف معتبر نیست');
      }
    } catch (error) {
      setDiscountError('خطا در اعمال کد تخفیف');
    } finally {
      setDiscountLoading(false);
    }
  };

  const handleRemoveDiscount = () => {
    onRemoveDiscount?.();
    setDiscountInput('');
    setDiscountError('');
  };

  if (compact) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="font-semibold text-gray-900 mb-3">خلاصه سفارش</h3>
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">جمع کل ({items.length} کالا):</span>
            <span>{orderSummary.subtotal.toLocaleString()} تومان</span>
          </div>
          
          {orderSummary.discount > 0 && (
            <div className="flex justify-between text-green-600">
              <span>تخفیف:</span>
              <span>-{orderSummary.discount.toLocaleString()} تومان</span>
            </div>
          )}
          
          <div className="flex justify-between">
            <span className="text-gray-600">هزینه ارسال:</span>
            <span>
              {orderSummary.shippingCost === 0 ? 'رایگان' : `${orderSummary.shippingCost.toLocaleString()} تومان`}
            </span>
          </div>
          
          <div className="border-t border-gray-200 pt-2">
            <div className="flex justify-between font-semibold text-lg">
              <span>مبلغ نهایی:</span>
              <span className="text-primary-600">{orderSummary.total.toLocaleString()} تومان</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 sticky top-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <ShoppingBag className="w-5 h-5 text-primary-600" />
        خلاصه سفارش
      </h3>

      {/* Order Items */}
      <div className="space-y-3 mb-6">
        {items.map((item, index) => {
          const itemPrice = item.product.discountedPrice || item.product.price;
          let variantPrice = 0;
          
          // Calculate variant price adjustments
          if (item.selectedVariants) {
            Object.values(item.selectedVariants).forEach(variant => {
              if (variant.price) {
                variantPrice += variant.price;
              }
            });
          }
          
          const totalItemPrice = (itemPrice + variantPrice) * item.quantity;

          return (
            <div key={`${item.product.id}-${item.variantKey || 'default'}`} className="flex gap-3">
              <img
                src={item.product.imageSrc}
                alt={item.product.name}
                className="w-12 h-12 object-cover rounded-lg border border-gray-200"
              />
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {item.product.name}
                </h4>
                {item.selectedVariants && Object.keys(item.selectedVariants).length > 0 && (
                  <div className="text-xs text-gray-500 mt-1">
                    {Object.values(item.selectedVariants).map(variant => variant.name).join(', ')}
                  </div>
                )}
                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-gray-500">تعداد: {item.quantity}</span>
                  <span className="text-sm font-medium text-gray-900">
                    {totalItemPrice.toLocaleString()} تومان
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Discount Code */}
      <div className="mb-6">
        {discountCode ? (
          <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <Gift className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-700">
                کد تخفیف: {discountCode}
              </span>
            </div>
            <button
              onClick={handleRemoveDiscount}
              className="text-green-600 hover:text-green-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div>
            {!showDiscountForm ? (
              <button
                onClick={() => setShowDiscountForm(true)}
                className="flex items-center gap-2 text-primary-600 hover:text-primary-700 text-sm font-medium"
              >
                <Tag className="w-4 h-4" />
                اعمال کد تخفیف
              </button>
            ) : (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2"
              >
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={discountInput}
                    onChange={(e) => setDiscountInput(e.target.value)}
                    placeholder="کد تخفیف را وارد کنید"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                    onKeyPress={(e) => e.key === 'Enter' && handleApplyDiscount()}
                  />
                  <button
                    onClick={handleApplyDiscount}
                    disabled={!discountInput.trim() || discountLoading}
                    className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm flex items-center gap-1"
                  >
                    {discountLoading ? (
                      <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Check className="w-3 h-3" />
                    )}
                    اعمال
                  </button>
                </div>
                
                {discountError && (
                  <div className="flex items-center gap-1 text-red-600 text-xs">
                    <AlertCircle className="w-3 h-3" />
                    {discountError}
                  </div>
                )}
                
                <button
                  onClick={() => {
                    setShowDiscountForm(false);
                    setDiscountInput('');
                    setDiscountError('');
                  }}
                  className="text-gray-500 hover:text-gray-700 text-xs"
                >
                  انصراف
                </button>
              </motion.div>
            )}
          </div>
        )}
      </div>

      {/* Order Breakdown */}
      <div className="space-y-3 mb-6">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">جمع کل ({items.length} کالا):</span>
          <span className="font-medium">{orderSummary.subtotal.toLocaleString()} تومان</span>
        </div>

        {orderSummary.discount > 0 && (
          <div className="flex justify-between text-sm text-green-600">
            <span>تخفیف:</span>
            <span>-{orderSummary.discount.toLocaleString()} تومان</span>
          </div>
        )}

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 flex items-center gap-1">
            <Truck className="w-3 h-3" />
            هزینه ارسال:
          </span>
          <span className="font-medium">
            {orderSummary.shippingCost === 0 ? (
              <span className="text-green-600">رایگان</span>
            ) : (
              `${orderSummary.shippingCost.toLocaleString()} تومان`
            )}
          </span>
        </div>

        {shippingMethod && (
          <div className="text-xs text-gray-500 mr-4">
            {shippingMethod.name} • {shippingMethod.estimatedDays}
          </div>
        )}

        {orderSummary.tax > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">مالیات:</span>
            <span className="font-medium">{orderSummary.tax.toLocaleString()} تومان</span>
          </div>
        )}

        {paymentMethod?.processingFee && paymentMethod.processingFee > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">کارمزد پرداخت:</span>
            <span className="font-medium">{paymentMethod.processingFee.toLocaleString()} تومان</span>
          </div>
        )}
      </div>

      {/* Total */}
      <div className="border-t border-gray-200 pt-4">
        <div className="flex justify-between items-center">
          <span className="text-lg font-semibold text-gray-900 flex items-center gap-1">
            <Calculator className="w-4 h-4" />
            مبلغ نهایی:
          </span>
          <span className="text-xl font-bold text-primary-600">
            {orderSummary.total.toLocaleString()} تومان
          </span>
        </div>

        {orderSummary.savings && orderSummary.savings > 0 && (
          <div className="mt-2 text-center">
            <span className="text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full">
              شما {orderSummary.savings.toLocaleString()} تومان صرفه‌جویی کردید! 🎉
            </span>
          </div>
        )}
      </div>

      {/* Payment Method Info */}
      {paymentMethod && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">
            <span className="font-medium">روش پرداخت:</span> {paymentMethod.name}
          </div>

        </div>
      )}

      {/* Security Badge */}
      <div className="mt-4 flex items-center justify-center gap-2 text-xs text-gray-500">
        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
          <Check className="w-2 h-2 text-white" />
        </div>
        پرداخت امن SSL
      </div>
    </div>
  );
};

export default OrderSummary;
