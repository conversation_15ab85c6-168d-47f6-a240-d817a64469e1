import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { 
  MapPin, 
  Plus, 
  Edit, 
  Trash2, 
  Check,
  User,
  Phone,
  Mail,
  Home,
  Navigation
} from 'lucide-react';
import { 
  ShippingAddress, 
  ShippingMethod,
  PERSIAN_PROVINCES,
  SHIPPING_METHODS,
  PERSIAN_CHECKOUT_MESSAGES 
} from '../../types/checkout';

interface ShippingFormProps {
  selectedAddress?: ShippingAddress;
  selectedShipping?: ShippingMethod;
  onAddressChange: (address: ShippingAddress) => void;
  onShippingChange: (shipping: ShippingMethod) => void;
  cartTotal: number;
}

// Validation schema
const addressSchema = yup.object({
  firstName: yup.string()
    .required('نام الزامی است')
    .min(2, 'نام باید حداقل ۲ کاراکتر باشد'),
  lastName: yup.string()
    .required('نام خانوادگی الزامی است')
    .min(2, 'نام خانوادگی باید حداقل ۲ کاراکتر باشد'),
  phone: yup.string()
    .required('شماره موبایل الزامی است')
    .matches(/^09\d{9}$/, 'شماره موبایل معتبر وارد کنید'),
  email: yup.string()
    .required('ایمیل الزامی است')
    .email('ایمیل معتبر وارد کنید'),
  province: yup.string()
    .required('استان الزامی است'),
  city: yup.string()
    .required('شهر الزامی است')
    .min(2, 'نام شهر باید حداقل ۲ کاراکتر باشد'),
  address: yup.string()
    .required('آدرس الزامی است')
    .min(10, 'آدرس باید حداقل ۱۰ کاراکتر باشد'),
  postalCode: yup.string()
    .required('کد پستی الزامی است')
    .matches(/^\d{10}$/, 'کد پستی باید ۱۰ رقم باشد')
});

const ShippingForm: React.FC<ShippingFormProps> = ({
  selectedAddress,
  selectedShipping,
  onAddressChange,
  onShippingChange,
  cartTotal
}) => {
  const [showAddressForm, setShowAddressForm] = useState(!selectedAddress);
  const [savedAddresses, setSavedAddresses] = useState<ShippingAddress[]>([]);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch
  } = useForm<ShippingAddress>({
    resolver: yupResolver(addressSchema),
    defaultValues: selectedAddress || {
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      province: '',
      city: '',
      address: '',
      postalCode: ''
    },
    mode: 'onChange'
  });

  const watchedProvince = watch('province');

  const handleAddressSubmit = (data: ShippingAddress) => {
    onAddressChange(data);
    setShowAddressForm(false);
  };

  const handleEditAddress = () => {
    setShowAddressForm(true);
  };

  const handleSaveAddress = (address: ShippingAddress) => {
    const newAddress = { ...address, id: Date.now().toString() };
    setSavedAddresses(prev => [...prev, newAddress]);
  };

  const getAvailableShippingMethods = () => {
    return SHIPPING_METHODS.filter(method => {
      if (method.id === 'free') {
        return cartTotal >= 500000; // Free shipping threshold
      }
      return true;
    });
  };

  const availableShippingMethods = getAvailableShippingMethods();

  return (
    <div className="space-y-6">
      {/* Address Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <MapPin className="w-5 h-5 text-primary-600" />
            آدرس تحویل
          </h3>
          {selectedAddress && !showAddressForm && (
            <button
              onClick={handleEditAddress}
              className="text-primary-600 hover:text-primary-700 text-sm flex items-center gap-1"
            >
              <Edit className="w-4 h-4" />
              ویرایش
            </button>
          )}
        </div>

        <AnimatePresence>
          {showAddressForm ? (
            <motion.form
              key="address-form"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              onSubmit={handleSubmit(handleAddressSubmit)}
              className="space-y-4"
            >
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <User className="w-4 h-4 inline ml-1" />
                    نام *
                  </label>
                  <Controller
                    name="firstName"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        placeholder={PERSIAN_CHECKOUT_MESSAGES.placeholders.firstName}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.firstName ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    )}
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نام خانوادگی *
                  </label>
                  <Controller
                    name="lastName"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        placeholder={PERSIAN_CHECKOUT_MESSAGES.placeholders.lastName}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.lastName ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    )}
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
                  )}
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Phone className="w-4 h-4 inline ml-1" />
                    شماره موبایل *
                  </label>
                  <Controller
                    name="phone"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="tel"
                        placeholder={PERSIAN_CHECKOUT_MESSAGES.placeholders.phone}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.phone ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    )}
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Mail className="w-4 h-4 inline ml-1" />
                    ایمیل *
                  </label>
                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="email"
                        placeholder={PERSIAN_CHECKOUT_MESSAGES.placeholders.email}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.email ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    )}
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>
              </div>

              {/* Location */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    استان *
                  </label>
                  <Controller
                    name="province"
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.province ? 'border-red-300' : 'border-gray-300'
                        }`}
                      >
                        <option value="">انتخاب استان</option>
                        {PERSIAN_PROVINCES.map(province => (
                          <option key={province} value={province}>
                            {province}
                          </option>
                        ))}
                      </select>
                    )}
                  />
                  {errors.province && (
                    <p className="mt-1 text-sm text-red-600">{errors.province.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    شهر *
                  </label>
                  <Controller
                    name="city"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        placeholder={PERSIAN_CHECKOUT_MESSAGES.placeholders.city}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.city ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    )}
                  />
                  {errors.city && (
                    <p className="mt-1 text-sm text-red-600">{errors.city.message}</p>
                  )}
                </div>
              </div>

              {/* Address */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Home className="w-4 h-4 inline ml-1" />
                  آدرس کامل *
                </label>
                <Controller
                  name="address"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      rows={3}
                      placeholder={PERSIAN_CHECKOUT_MESSAGES.placeholders.address}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none ${
                        errors.address ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  )}
                />
                {errors.address && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
                )}
              </div>

              {/* Postal Code */}
              <div className="md:w-1/2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Navigation className="w-4 h-4 inline ml-1" />
                  کد پستی *
                </label>
                <Controller
                  name="postalCode"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      placeholder={PERSIAN_CHECKOUT_MESSAGES.placeholders.postalCode}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        errors.postalCode ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  )}
                />
                {errors.postalCode && (
                  <p className="mt-1 text-sm text-red-600">{errors.postalCode.message}</p>
                )}
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                {selectedAddress && (
                  <button
                    type="button"
                    onClick={() => setShowAddressForm(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    انصراف
                  </button>
                )}
                <button
                  type="submit"
                  disabled={!isValid}
                  className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  <Check className="w-4 h-4" />
                  تأیید آدرس
                </button>
              </div>
            </motion.form>
          ) : selectedAddress ? (
            <motion.div
              key="address-display"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="bg-gray-50 rounded-lg p-4"
            >
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">
                    {selectedAddress.firstName} {selectedAddress.lastName}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {selectedAddress.phone} • {selectedAddress.email}
                  </p>
                  <p className="text-sm text-gray-600">
                    {selectedAddress.address}
                  </p>
                  <p className="text-sm text-gray-600">
                    {selectedAddress.city}، {selectedAddress.province}
                  </p>
                  <p className="text-sm text-gray-600">
                    کد پستی: {selectedAddress.postalCode}
                  </p>
                </div>
                <div className="text-green-600">
                  <Check className="w-5 h-5" />
                </div>
              </div>
            </motion.div>
          ) : null}
        </AnimatePresence>
      </div>

      {/* Shipping Methods */}
      {selectedAddress && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            روش ارسال
          </h3>
          
          <div className="space-y-3">
            {availableShippingMethods.map((method) => (
              <label
                key={method.id}
                className={`
                  flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-all duration-200
                  ${selectedShipping?.id === method.id 
                    ? 'border-primary-500 bg-primary-50' 
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}
              >
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    name="shipping"
                    checked={selectedShipping?.id === method.id}
                    onChange={() => onShippingChange(method)}
                    className="text-primary-600"
                  />
                  <div>
                    <h4 className="font-medium text-gray-900 flex items-center gap-2">
                      {method.name}
                      {method.isExpress && (
                        <span className="bg-orange-100 text-orange-700 text-xs px-2 py-0.5 rounded-full">
                          سریع
                        </span>
                      )}
                      {method.isFree && (
                        <span className="bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded-full">
                          رایگان
                        </span>
                      )}
                    </h4>
                    <p className="text-sm text-gray-600">{method.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{method.estimatedDays}</p>
                  </div>
                </div>
                <div className="text-left">
                  <span className="font-medium text-gray-900">
                    {method.price === 0 ? 'رایگان' : `${method.price.toLocaleString()} تومان`}
                  </span>
                </div>
              </label>
            ))}
          </div>

          {cartTotal < 500000 && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-700">
                💡 با خرید {(500000 - cartTotal).toLocaleString()} تومان بیشتر، ارسال رایگان دریافت کنید!
              </p>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default ShippingForm;
