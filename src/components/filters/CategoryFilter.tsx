import React from 'react';
import { motion } from 'framer-motion';
import { Check, ChevronDown } from 'lucide-react';
import { FilterOption } from '../../types';

interface CategoryFilterProps {
  categories: FilterOption[];
  selectedCategories: string[];
  onToggleCategory: (category: string) => void;
  onClearCategories: () => void;
  isCollapsible?: boolean;
  defaultExpanded?: boolean;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategories,
  onToggleCategory,
  onClearCategories,
  isCollapsible = false,
  defaultExpanded = true
}) => {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);

  const hasSelection = selectedCategories.length > 0;

  return (
    <div className="border-b border-gray-100 pb-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => isCollapsible && setIsExpanded(!isExpanded)}
          className={`flex items-center gap-2 font-medium text-text-primary ${
            isCollapsible ? 'hover:text-primary-600 transition-colors' : ''
          }`}
        >
          <span>دسته‌بندی‌ها</span>
          {hasSelection && (
            <span className="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">
              {selectedCategories.length}
            </span>
          )}
          {isCollapsible && (
            <ChevronDown 
              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
            />
          )}
        </button>
        
        {hasSelection && (
          <button
            onClick={onClearCategories}
            className="text-sm text-text-muted hover:text-red-500 transition-colors"
          >
            پاک کردن
          </button>
        )}
      </div>

      {/* Content */}
      {(!isCollapsible || isExpanded) && (
        <motion.div
          initial={isCollapsible ? { height: 0, opacity: 0 } : false}
          animate={isCollapsible ? { height: 'auto', opacity: 1 } : false}
          exit={isCollapsible ? { height: 0, opacity: 0 } : false}
          transition={{ duration: 0.2 }}
          className="space-y-3"
        >
          {/* All Categories Option */}
          <label className="flex items-center cursor-pointer group">
            <div className="relative">
              <input
                type="checkbox"
                checked={selectedCategories.length === 0}
                onChange={() => onClearCategories()}
                className="sr-only"
              />
              <div className={`w-5 h-5 rounded border-2 transition-all duration-200 ${
                selectedCategories.length === 0
                  ? 'bg-primary-500 border-primary-500'
                  : 'border-gray-300 group-hover:border-primary-400'
              }`}>
                {selectedCategories.length === 0 && (
                  <Check className="w-3 h-3 text-white absolute top-0.5 right-0.5" />
                )}
              </div>
            </div>
            <span className={`mr-3 text-sm transition-colors ${
              selectedCategories.length === 0 
                ? 'text-primary-600 font-medium' 
                : 'text-text-secondary group-hover:text-text-primary'
            }`}>
              همه دسته‌ها
            </span>
          </label>

          {/* Category Options */}
          {categories.map((category, index) => {
            const isSelected = selectedCategories.includes(category.value);
            
            return (
              <motion.label
                key={category.value}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className="flex items-center justify-between cursor-pointer group"
              >
                <div className="flex items-center">
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => onToggleCategory(category.value)}
                      className="sr-only"
                      disabled={category.disabled}
                    />
                    <div className={`w-5 h-5 rounded border-2 transition-all duration-200 ${
                      isSelected
                        ? 'bg-primary-500 border-primary-500'
                        : category.disabled
                        ? 'border-gray-200 bg-gray-50'
                        : 'border-gray-300 group-hover:border-primary-400'
                    }`}>
                      {isSelected && (
                        <Check className="w-3 h-3 text-white absolute top-0.5 right-0.5" />
                      )}
                    </div>
                  </div>
                  <span className={`mr-3 text-sm transition-colors ${
                    category.disabled
                      ? 'text-gray-400'
                      : isSelected 
                      ? 'text-primary-600 font-medium' 
                      : 'text-text-secondary group-hover:text-text-primary'
                  }`}>
                    {category.label}
                  </span>
                </div>
                
                {category.count !== undefined && (
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    isSelected
                      ? 'bg-primary-100 text-primary-700'
                      : 'bg-gray-100 text-text-muted'
                  }`}>
                    {category.count}
                  </span>
                )}
              </motion.label>
            );
          })}

          {/* No categories message */}
          {categories.length === 0 && (
            <div className="text-center py-4 text-text-muted text-sm">
              دسته‌بندی‌ای یافت نشد
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default CategoryFilter;
