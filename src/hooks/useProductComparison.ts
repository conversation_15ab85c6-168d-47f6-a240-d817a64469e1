import { useState, useEffect, useCallback, useMemo } from 'react';
import { Product } from '../types';
import {
  ComparisonItem,
  ComparisonCriteria,
  ComparisonResult,
  ComparisonContextType,
  PERSIAN_COMPARISON_MESSAGES
} from '../types/comparison';
import { formatNumber } from '../utils/formatters';
import toast from 'react-hot-toast';

// Storage utilities
const COMPARISON_STORAGE_KEY = 'glowroya_comparison';

const saveComparisonToStorage = (items: ComparisonItem[]) => {
  localStorage.setItem(COMPARISON_STORAGE_KEY, JSON.stringify(items));
};

const loadComparisonFromStorage = (): ComparisonItem[] => {
  try {
    const stored = localStorage.getItem(COMPARISON_STORAGE_KEY);
    if (stored) {
      const items = JSON.parse(stored);
      return items.map((item: any) => ({
        ...item,
        addedAt: new Date(item.addedAt)
      }));
    }
  } catch (error) {
    console.error('Error loading comparison from storage:', error);
  }
  return [];
};

// Comparison criteria definitions
const getComparisonCriteria = (): ComparisonCriteria[] => [
  {
    id: 'name',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.name,
    category: 'basic',
    getValue: (product) => product.name,
    important: true
  },
  {
    id: 'brand',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.brand,
    category: 'basic',
    getValue: (product) => product.brand,
    important: true
  },
  {
    id: 'price',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.price,
    category: 'pricing',
    getValue: (product) => product.price,
    format: (value) => `${formatNumber(value)} تومان`,
    important: true
  },
  {
    id: 'discountedPrice',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.discountedPrice,
    category: 'pricing',
    getValue: (product) => product.discountedPrice || product.price,
    format: (value) => `${formatNumber(value)} تومان`,
    important: true
  },
  {
    id: 'category',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.category,
    category: 'basic',
    getValue: (product) => product.category
  },
  {
    id: 'rating',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.rating,
    category: 'ratings',
    getValue: (product) => product.rating || 0,
    format: (value) => `${value} از ۵`,
    important: true
  },
  {
    id: 'reviewCount',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.reviewCount,
    category: 'ratings',
    getValue: (product) => product.reviewCount || 0,
    format: (value) => `${formatNumber(value)} نظر`
  },
  {
    id: 'stock',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.stock,
    category: 'specifications',
    getValue: (product) => product.stock,
    format: (value) => value > 0 ? `${formatNumber(value)} عدد` : 'ناموجود'
  },
  {
    id: 'volume',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.volume,
    category: 'specifications',
    getValue: (product) => product.volume || 'نامشخص',
    format: (value) => typeof value === 'string' ? value : `${value} میلی‌لیتر`
  },
  {
    id: 'skinType',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.skinType,
    category: 'specifications',
    getValue: (product) => product.skinType || 'همه انواع پوست'
  },
  {
    id: 'ingredients',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.ingredients,
    category: 'ingredients',
    getValue: (product) => product.ingredients?.slice(0, 3).join('، ') || 'نامشخص'
  },
  {
    id: 'benefits',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.benefits,
    category: 'benefits',
    getValue: (product) => product.benefits?.slice(0, 3).join('، ') || 'نامشخص'
  },
  {
    id: 'isNew',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.isNew,
    category: 'basic',
    getValue: (product) => product.isNew || false,
    format: (value) => value ? 'بله' : 'خیر'
  },
  {
    id: 'isBestSeller',
    name: PERSIAN_COMPARISON_MESSAGES.criteria.isBestSeller,
    category: 'basic',
    getValue: (product) => product.isBestSeller || false,
    format: (value) => value ? 'بله' : 'خیر'
  }
];

export const useProductComparison = (): ComparisonContextType => {
  const [items, setItems] = useState<ComparisonItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const maxItems = 4;
  const criteria = useMemo(() => getComparisonCriteria(), []);

  // Load comparison from storage on mount
  useEffect(() => {
    const stored = loadComparisonFromStorage();
    setItems(stored);
  }, []);

  // Save comparison to storage when items change
  useEffect(() => {
    saveComparisonToStorage(items);
  }, [items]);

  // Generate unique ID for comparison items
  const generateComparisonItemId = (productId: string): string => {
    return `comparison_${productId}_${Date.now()}`;
  };

  // Add item to comparison
  const addItem = useCallback((product: Product) => {
    // Check if already in comparison
    if (items.some(item => item.product.id === product.id)) {
      toast.error('این محصول قبلاً به مقایسه اضافه شده');
      return;
    }

    // Check max items limit
    if (items.length >= maxItems) {
      toast.error(PERSIAN_COMPARISON_MESSAGES.maxItemsReached);
      return;
    }

    const newItem: ComparisonItem = {
      id: generateComparisonItemId(product.id),
      product,
      addedAt: new Date()
    };

    setItems(prev => [...prev, newItem]);
    toast.success(PERSIAN_COMPARISON_MESSAGES.addedToComparison);
  }, [items, maxItems]);

  // Remove item from comparison
  const removeItem = useCallback((productId: string) => {
    setItems(prev => prev.filter(item => item.product.id !== productId));
    toast.success(PERSIAN_COMPARISON_MESSAGES.removedFromComparison);
  }, []);

  // Clear all comparison items
  const clearComparison = useCallback(() => {
    setItems([]);
    toast.success(PERSIAN_COMPARISON_MESSAGES.comparisonCleared);
  }, []);

  // Generate comparison results
  const generateComparison = useCallback((): ComparisonResult[] => {
    if (items.length < 2) return [];

    return criteria.map(criterion => {
      const values: { [productId: string]: any } = {};
      let winner: string | undefined;

      // Get values for each product
      items.forEach(item => {
        values[item.product.id] = criterion.getValue(item.product);
      });

      // Determine winner for numeric values
      if (criterion.category === 'pricing' || criterion.id === 'rating' || criterion.id === 'stock') {
        const numericValues = Object.entries(values).map(([id, value]) => ({
          id,
          value: typeof value === 'number' ? value : 0
        }));

        if (criterion.id === 'price' || criterion.id === 'discountedPrice') {
          // Lower price wins
          winner = numericValues.reduce((min, current) => 
            current.value < min.value ? current : min
          ).id;
        } else {
          // Higher value wins (rating, stock)
          winner = numericValues.reduce((max, current) => 
            current.value > max.value ? current : max
          ).id;
        }
      }

      return {
        criteria: criterion,
        values,
        winner
      };
    });
  }, [items, criteria]);

  // Check if product is in comparison
  const isInComparison = useCallback((productId: string): boolean => {
    return items.some(item => item.product.id === productId);
  }, [items]);

  // Check if can add more items
  const canAddMore = useMemo(() => items.length < maxItems, [items.length, maxItems]);

  // Share comparison
  const shareComparison = useCallback(() => {
    const productNames = items.map(item => item.product.name).join(' vs ');
    const shareText = `مقایسه محصولات: ${productNames}`;
    const shareUrl = `${window.location.origin}/comparison`;
    
    if (navigator.share) {
      navigator.share({
        title: shareText,
        url: shareUrl
      });
    } else {
      navigator.clipboard.writeText(shareUrl);
      toast.success('لینک مقایسه کپی شد');
    }
  }, [items]);

  // Generate comparison results
  const results = useMemo(() => generateComparison(), [generateComparison]);

  return {
    items,
    maxItems,
    isLoading,
    addItem,
    removeItem,
    clearComparison,
    criteria,
    results,
    generateComparison,
    isInComparison,
    canAddMore,
    shareComparison
  };
};
