import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { useAdminAuth } from './useAdminAuth';
import { Order } from '../types/checkout';
import {
  AdminOrder,
  OrderFilters,
  OrderAnalytics,
  OrderTimelineEvent,
  PERSIAN_ORDER_MESSAGES
} from '../types/adminOrder';
import {
  generateOrderNumber,
  generateTrackingNumber,
  calculateEstimatedDelivery,
  filterOrders,
  sortOrders,
  calculateOrderAnalytics,
  createTimelineEvent
} from '../utils/orderUtils';
import { AdminOrderService } from '../services/adminOrderService';

// Removed mock data - using only real API calls

export const useAdminOrders = () => {
  const { user, checkPermission } = useAdminAuth();
  const [orders, setOrders] = useState<AdminOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<OrderFilters>({});
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Initialize orders
  useEffect(() => {
    const loadOrders = async () => {
      if (!user || !checkPermission('orders', 'read')) {
        setError('دسترسی محدود');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await AdminOrderService.getOrders({
          page: 1,
          limit: 100,
          filters,
          sort: { field: sortBy, direction: sortOrder }
        });

        setOrders(response.orders);
        console.log('Orders loaded from API successfully');
      } catch (err) {
        console.error('Error loading orders:', err);
        setError('خطا در بارگذاری سفارشات. لطفاً اتصال به سرور را بررسی کنید.');
        setOrders([]); // Clear orders on error
      } finally {
        setLoading(false);
      }
    };

    loadOrders();
  }, [user, checkPermission, filters, sortBy, sortOrder]);

  // Filter and sort orders
  const filteredOrders = useCallback(() => {
    let result = filterOrders(orders, filters);
    result = sortOrders(result, sortBy, sortOrder);
    return result;
  }, [orders, filters, sortBy, sortOrder]);

  // Get order by ID
  const getOrder = useCallback((id: string): AdminOrder | undefined => {
    return orders.find(order => order.id === id);
  }, [orders]);

  // Update order status
  const updateOrderStatus = useCallback(async (
    orderId: string,
    newStatus: Order['status'],
    note?: string
  ): Promise<void> => {
    if (!checkPermission('orders', 'update')) {
      throw new Error(PERSIAN_ORDER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      // Update order status via API
      const updatedOrder = await AdminOrderService.updateOrderStatus(orderId, newStatus);

      // Add note if provided
      if (note) {
        await AdminOrderService.addOrderNotes(orderId, note);
      }

      // Update local state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? updatedOrder : order
        )
      );

      toast.success(PERSIAN_ORDER_MESSAGES.success.statusChanged);
    } catch (err) {
      console.error('Error updating order status:', err);
      toast.error(PERSIAN_ORDER_MESSAGES.errors.updateFailed);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Add note to order
  const addOrderNote = useCallback(async (
    orderId: string,
    note: string
  ): Promise<void> => {
    if (!checkPermission('orders', 'update')) {
      throw new Error(PERSIAN_ORDER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      // Add note via API
      const updatedOrder = await AdminOrderService.addOrderNotes(orderId, note);

      // Update local state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? updatedOrder : order
        )
      );

      toast.success(PERSIAN_ORDER_MESSAGES.success.noteAdded);
    } catch (err) {
      console.error('Error adding note:', err);
      toast.error('خطا در افزودن یادداشت');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Update order priority
  const updateOrderPriority = useCallback(async (
    orderId: string,
    priority: Order['priority']
  ): Promise<void> => {
    if (!checkPermission('orders', 'update')) {
      throw new Error(PERSIAN_ORDER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      const updatedOrder = await AdminOrderService.updateOrder(orderId, { priority });

      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? updatedOrder : order
        )
      );

      toast.success('اولویت سفارش بروزرسانی شد');
    } catch (err) {
      console.error('Error updating order priority:', err);
      toast.error('خطا در بروزرسانی اولویت');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Generate shipping label
  const generateShippingLabel = useCallback(async (orderId: string): Promise<void> => {
    if (!checkPermission('orders', 'update')) {
      throw new Error(PERSIAN_ORDER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      // Call API to generate shipping label
      const updatedOrder = await AdminOrderService.generateShippingLabel(orderId);

      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? updatedOrder : order
        )
      );

      toast.success(PERSIAN_ORDER_MESSAGES.success.labelPrinted);
    } catch (err) {
      console.error('Error generating shipping label:', err);
      toast.error('خطا در چاپ برچسب');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Calculate analytics
  const analytics = useCallback((): OrderAnalytics => {
    return calculateOrderAnalytics(orders);
  }, [orders]);

  // Bulk operations
  const bulkUpdateStatus = useCallback(async (
    orderIds: string[],
    newStatus: Order['status']
  ): Promise<void> => {
    if (!checkPermission('orders', 'update')) {
      throw new Error(PERSIAN_ORDER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      // Call API for bulk update
      const updatedOrders = await AdminOrderService.bulkUpdateStatus(orderIds, newStatus);

      setOrders(prevOrders =>
        prevOrders.map(order => {
          const updatedOrder = updatedOrders.find(updated => updated.id === order.id);
          return updatedOrder || order;
        })
      );

      toast.success(`${orderIds.length} سفارش بروزرسانی شد`);
    } catch (err) {
      console.error('Error in bulk update:', err);
      toast.error('خطا در بروزرسانی گروهی');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Update order
  const updateOrder = useCallback(async (updatedOrder: AdminOrder): Promise<void> => {
    if (!checkPermission('orders', 'update')) {
      throw new Error('شما مجوز ویرایش سفارش را ندارید');
    }

    setLoading(true);
    try {
      const result = await AdminOrderService.updateOrder(updatedOrder.id, updatedOrder);

      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === updatedOrder.id ? result : order
        )
      );

      toast.success('سفارش با موفقیت به‌روزرسانی شد');
    } catch (err) {
      console.error('Error updating order:', err);
      toast.error('خطا در به‌روزرسانی سفارش');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  return {
    // State
    orders: filteredOrders(),
    allOrders: orders,
    loading,
    error,
    filters,
    sortBy,
    sortOrder,

    // Actions
    setFilters,
    setSortBy,
    setSortOrder,
    getOrder,
    updateOrder,
    updateOrderStatus,
    addOrderNote,
    updateOrderPriority,
    generateShippingLabel,
    bulkUpdateStatus,
    analytics,

    // Utilities
    clearError: () => setError(null)
  };
};
