import { useCallback } from 'react';

declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

// Simple analytics interface
interface UseAnalyticsReturn {
  trackEvent: (eventName: string, eventData?: Record<string, any>) => void;
  trackProductView: (product: any) => void;
  trackAddToCart: (product: any, quantity?: number) => void;
  trackPageView: (pageData?: Record<string, any>) => void;
  isTrackingEnabled: () => boolean;
}

export const useAnalytics = (): UseAnalyticsReturn => {
  // Simple tracking implementation
  const trackEvent = useCallback((eventName: string, eventData?: Record<string, any>) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, eventData);
    }
  }, []);

  const trackProductView = useCallback((product: any) => {
    trackEvent('view_item', {
      item_id: product.id,
      item_name: product.name,
      item_category: product.category,
      item_brand: product.brand,
      value: product.price,
      currency: 'IRR'
    });
  }, [trackEvent]);

  const trackAddToCart = useCallback((product: any, quantity: number = 1) => {
    trackEvent('add_to_cart', {
      item_id: product.id,
      item_name: product.name,
      item_category: product.category,
      item_brand: product.brand,
      value: product.price * quantity,
      currency: 'IRR',
      quantity
    });
  }, [trackEvent]);

  const trackPageView = useCallback((pageData?: Record<string, any>) => {
    trackEvent('page_view', {
      page_title: document.title,
      page_location: window.location.href,
      ...pageData
    });
  }, [trackEvent]);

  const isTrackingEnabled = useCallback(() => {
    return typeof window !== 'undefined' && !!window.gtag;
  }, []);

  return {
    trackEvent,
    trackProductView,
    trackAddToCart,
    trackPageView,
    isTrackingEnabled
  };
};

export default useAnalytics;
