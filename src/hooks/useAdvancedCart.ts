import { useState, useEffect, useCallback, useMemo } from 'react';
import { Product, SelectedVariants } from '../types';
import {
  AdvancedCartItem,
  SavedForLaterItem,
  CartRecommendation,
  AdvancedCartSummary,
  CartAnalytics,
  CartValidationResult,
  BulkCartOperation,
  CartItemSource,
  SaveReason,
  AdvancedCartContextType
} from '../types/advancedCart';
import {
  generateCartItemId,
  calculateAdvancedCartSummary,
  generateCartRecommendations,
  saveCartToStorage,
  loadCartFromStorage,
  validateCartItems
} from '../utils/cartUtils';
import {
  createCartAnalytics,
  trackItemAdded,
  trackItemRemoved,
  trackQuantityUpdated,
  trackCartViewed,
  trackRecommendationAdded,
  loadAnalytics
} from '../utils/cartAnalytics';
import { useAuth } from '../context/AuthContext';
import toast from 'react-hot-toast';
import { PERSIAN_ADVANCED_CART_MESSAGES } from '../types/advancedCart';

export const useAdvancedCart = () => {
  const { user } = useAuth();
  const [items, setItems] = useState<AdvancedCartItem[]>([]);
  const [savedForLater, setSavedForLater] = useState<SavedForLaterItem[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [analytics, setAnalytics] = useState<CartAnalytics>(() => 
    createCartAnalytics(user?.id)
  );
  const [validation, setValidation] = useState<CartValidationResult>({
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: []
  });

  // Load cart from storage on mount
  useEffect(() => {
    const stored = loadCartFromStorage(user?.id);
    setItems(stored.items);
    setSavedForLater(stored.savedItems);

    // Load existing analytics if available
    const existingAnalytics = loadAnalytics(analytics.sessionId);
    if (existingAnalytics) {
      setAnalytics(existingAnalytics);
    }
  }, [user?.id, analytics.sessionId]);

  // Save cart to storage when items change
  useEffect(() => {
    if (items.length > 0 || savedForLater.length > 0) {
      saveCartToStorage(items, savedForLater, user?.id);
    }
  }, [items, savedForLater, user?.id]);

  // Validate cart when items change
  useEffect(() => {
    const validateCart = async () => {
      const result = await validateCartItems(items);
      setValidation(result);
    };

    if (items.length > 0) {
      validateCart();
    }
  }, [items]);

  // Calculate derived values
  const totalItems = useMemo(() => 
    items.reduce((total, item) => total + item.quantity, 0), 
    [items]
  );

  const totalPrice = useMemo(() => 
    items.reduce((total, item) => {
      let basePrice = item.product.discountedPrice || item.product.price;
      if (item.selectedVariants) {
        Object.values(item.selectedVariants).forEach(variant => {
          if (variant.price) basePrice += variant.price;
        });
      }
      return total + (basePrice * item.quantity);
    }, 0), 
    [items]
  );

  const recommendations = useMemo(() => 
    generateCartRecommendations(items), 
    [items]
  );

  const summary = useMemo(() => 
    calculateAdvancedCartSummary(items, user?.loyaltyTier || 'bronze', user?.loyaltyPoints || 0), 
    [items, user?.loyaltyTier, user?.loyaltyPoints]
  );

  // Add item to cart
  const addItem = useCallback((
    product: Product, 
    quantity: number = 1, 
    selectedVariants?: SelectedVariants,
    source: CartItemSource = 'product_page'
  ) => {
    const itemId = generateCartItemId(product.id, selectedVariants);
    const existingItemIndex = items.findIndex(item => 
      item.product.id === product.id && 
      JSON.stringify(item.selectedVariants) === JSON.stringify(selectedVariants)
    );

    const now = new Date();
    let basePrice = product.discountedPrice || product.price;
    if (selectedVariants) {
      Object.values(selectedVariants).forEach(variant => {
        if (variant.price) basePrice += variant.price;
      });
    }

    if (existingItemIndex >= 0) {
      // Update existing item
      setItems(prev => prev.map((item, index) => 
        index === existingItemIndex 
          ? { 
              ...item, 
              quantity: item.quantity + quantity,
              lastModified: now
            }
          : item
      ));
    } else {
      // Add new item
      const newItem: AdvancedCartItem = {
        id: itemId,
        product,
        quantity,
        selectedVariants,
        variantKey: itemId,
        addedAt: now,
        lastModified: now,
        source,
        loyaltyPointsEarned: Math.floor((basePrice * quantity) / 10000)
      };

      setItems(prev => [...prev, newItem]);
    }

    // Track analytics
    trackItemAdded(analytics, product.id, quantity, basePrice * quantity, source);

    toast.success(`${product.name} به سبد خرید اضافه شد`);
  }, [items, analytics]);

  // Remove item from cart
  const removeItem = useCallback((itemId: string) => {
    const item = items.find(i => i.id === itemId);
    if (!item) return;

    setItems(prev => prev.filter(i => i.id !== itemId));

    // Track analytics
    let basePrice = item.product.discountedPrice || item.product.price;
    if (item.selectedVariants) {
      Object.values(item.selectedVariants).forEach(variant => {
        if (variant.price) basePrice += variant.price;
      });
    }
    trackItemRemoved(analytics, item.product.id, item.quantity, basePrice * item.quantity);

    toast.success(`${item.product.name} از سبد خرید حذف شد`);
  }, [items, analytics]);

  // Update item quantity
  const updateQuantity = useCallback((itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }

    const item = items.find(i => i.id === itemId);
    if (!item) return;

    const oldQuantity = item.quantity;
    setItems(prev => prev.map(i => 
      i.id === itemId 
        ? { ...i, quantity, lastModified: new Date() }
        : i
    ));

    // Track analytics
    let basePrice = item.product.discountedPrice || item.product.price;
    if (item.selectedVariants) {
      Object.values(item.selectedVariants).forEach(variant => {
        if (variant.price) basePrice += variant.price;
      });
    }
    trackQuantityUpdated(analytics, item.product.id, oldQuantity, quantity, basePrice);
  }, [items, analytics, removeItem]);

  // Move item to saved for later
  const moveToSavedLater = useCallback((itemId: string, reason?: SaveReason) => {
    const item = items.find(i => i.id === itemId);
    if (!item) return;

    const savedItem: SavedForLaterItem = {
      id: `saved_${Date.now()}`,
      product: item.product,
      quantity: item.quantity,
      selectedVariants: item.selectedVariants,
      savedAt: new Date(),
      reason
    };

    setSavedForLater(prev => [...prev, savedItem]);
    setItems(prev => prev.filter(i => i.id !== itemId));

    toast.success(PERSIAN_ADVANCED_CART_MESSAGES.actions.saveForLater);
  }, [items]);

  // Move item from saved for later to cart
  const moveFromSavedLater = useCallback((savedItemId: string) => {
    const savedItem = savedForLater.find(i => i.id === savedItemId);
    if (!savedItem) return;

    addItem(savedItem.product, savedItem.quantity, savedItem.selectedVariants, 'saved_later');
    setSavedForLater(prev => prev.filter(i => i.id !== savedItemId));

    toast.success(PERSIAN_ADVANCED_CART_MESSAGES.actions.moveToCart);
  }, [savedForLater, addItem]);

  // Remove saved item
  const removeSavedItem = useCallback((savedItemId: string) => {
    setSavedForLater(prev => prev.filter(i => i.id !== savedItemId));
    toast.success(PERSIAN_ADVANCED_CART_MESSAGES.actions.removeFromSaved);
  }, []);

  // Clear cart
  const clearCart = useCallback(() => {
    setItems([]);
    toast.success('سبد خرید پاک شد');
  }, []);

  // Toggle cart
  const toggleCart = useCallback(() => {
    setIsOpen(prev => {
      const newState = !prev;
      if (newState) {
        trackCartViewed(analytics, totalPrice, totalItems);
      }
      return newState;
    });
  }, [analytics, totalPrice, totalItems]);

  // Perform bulk operation
  const performBulkOperation = useCallback((operation: BulkCartOperation) => {
    const targetItems = items.filter(item => operation.items.includes(item.id));

    switch (operation.type) {
      case 'move_to_wishlist':
        // Implementation would depend on wishlist system
        toast.success('محصولات به علاقه‌مندی‌ها منتقل شدند');
        break;

      case 'save_for_later':
        targetItems.forEach(item => moveToSavedLater(item.id));
        break;

      case 'remove_items':
        targetItems.forEach(item => removeItem(item.id));
        break;

      case 'clear_category':
        if (operation.filters?.category) {
          const categoryItems = items.filter(item => 
            item.product.category === operation.filters?.category
          );
          categoryItems.forEach(item => removeItem(item.id));
          toast.success(`همه محصولات ${operation.filters.category} حذف شدند`);
        }
        break;

      case 'clear_brand':
        if (operation.filters?.brand) {
          const brandItems = items.filter(item => 
            item.product.brand === operation.filters?.brand
          );
          brandItems.forEach(item => removeItem(item.id));
          toast.success(`همه محصولات ${operation.filters.brand} حذف شدند`);
        }
        break;
    }
  }, [items, moveToSavedLater, removeItem]);

  // Add recommendation to cart
  const addRecommendation = useCallback((recommendationId: string) => {
    const recommendation = recommendations.find(r => r.id === recommendationId);
    if (!recommendation) return;

    addItem(recommendation.product, 1, undefined, 'recommendations');
    trackRecommendationAdded(analytics, recommendationId, recommendation.product.id, recommendation.product.price);
  }, [recommendations, addItem, analytics]);

  // Dismiss recommendation
  const dismissRecommendation = useCallback((recommendationId: string) => {
    // Implementation would filter out dismissed recommendations
    toast.success('پیشنهاد نادیده گرفته شد');
  }, []);

  // Save cart manually
  const saveCart = useCallback(() => {
    saveCartToStorage(items, savedForLater, user?.id);
    toast.success(PERSIAN_ADVANCED_CART_MESSAGES.persistence.cartSaved);
  }, [items, savedForLater, user?.id]);

  // Load cart manually
  const loadCart = useCallback(() => {
    const stored = loadCartFromStorage(user?.id);
    setItems(stored.items);
    setSavedForLater(stored.savedItems);
    toast.success(PERSIAN_ADVANCED_CART_MESSAGES.persistence.cartLoaded);
  }, [user?.id]);

  // Track event manually
  const trackEvent = useCallback((event: any) => {
    // Implementation would use analytics tracking
  }, []);

  // Validate cart manually
  const validateCart = useCallback(async (): Promise<CartValidationResult> => {
    const result = await validateCartItems(items);
    setValidation(result);
    return result;
  }, [items]);

  const contextValue: AdvancedCartContextType = {
    items,
    totalItems,
    totalPrice,
    isOpen,
    savedForLater,
    recommendations,
    summary,
    analytics,
    validation,
    addItem,
    removeItem,
    updateQuantity,
    moveToSavedLater,
    moveFromSavedLater,
    removeSavedItem,
    clearCart,
    toggleCart,
    performBulkOperation,
    addRecommendation,
    dismissRecommendation,
    saveCart,
    loadCart,
    trackEvent,
    validateCart
  };

  return contextValue;
};
