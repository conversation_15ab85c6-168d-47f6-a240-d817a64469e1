import { useState, useEffect, useCallback } from 'react';
import { 
  LoyaltyMember, 
  PointTransaction, 
  LoyaltyReward,
  LOYALTY_TIERS,
  LOYALTY_REWARDS,
  POINT_EARNING_RULES
} from '../types/loyalty';

// Mock data for demonstration
const generateMockMember = (): LoyaltyMember => {
  const points = 1250;
  const tier = LOYALTY_TIERS.find(t => points >= t.minPoints && (!t.maxPoints || points <= t.maxPoints)) || LOYALTY_TIERS[0];
  const nextTier = LOYALTY_TIERS.find(t => t.level === tier.level + 1);
  
  return {
    id: 'member-1',
    userId: 'user-1',
    membershipNumber: '1234567890',
    tier,
    points,
    totalEarned: 2500,
    totalSpent: 1250000,
    joinDate: '2024-01-15T10:00:00Z',
    lastActivity: '2024-12-19T15:30:00Z',
    isActive: true,
    benefits: [],
    nextTierProgress: nextTier ? {
      currentPoints: points,
      requiredPoints: nextTier.minPoints,
      percentage: Math.round((points / nextTier.minPoints) * 100)
    } : {
      currentPoints: points,
      requiredPoints: 0,
      percentage: 100
    }
  };
};

const generateMockTransactions = (): PointTransaction[] => {
  return [
    {
      id: 'tx-1',
      memberId: 'member-1',
      type: 'earned',
      points: 150,
      description: 'خرید محصولات مراقبت از پوست',
      orderId: 'GR1234567890',
      createdAt: '2024-12-19T10:30:00Z',
      expiryDate: '2025-12-19T10:30:00Z'
    },
    {
      id: 'tx-2',
      memberId: 'member-1',
      type: 'redeemed',
      points: -200,
      description: 'استفاده از کد تخفیف ۵۰ هزار تومانی',
      createdAt: '2024-12-18T14:20:00Z'
    },
    {
      id: 'tx-3',
      memberId: 'member-1',
      type: 'bonus',
      points: 100,
      description: 'هدیه عضویت در باشگاه مشتریان',
      createdAt: '2024-01-15T10:00:00Z'
    },
    {
      id: 'tx-4',
      memberId: 'member-1',
      type: 'earned',
      points: 80,
      description: 'خرید سرم ویتامین C',
      orderId: 'GR1234567889',
      createdAt: '2024-12-15T16:45:00Z',
      expiryDate: '2025-12-15T16:45:00Z'
    },
    {
      id: 'tx-5',
      memberId: 'member-1',
      type: 'bonus',
      points: 20,
      description: 'ثبت نظر برای محصول',
      createdAt: '2024-12-14T09:15:00Z'
    }
  ];
};

export const useLoyalty = () => {
  const [member, setMember] = useState<LoyaltyMember | null>(null);
  const [pointHistory, setPointHistory] = useState<PointTransaction[]>([]);
  const [availableRewards, setAvailableRewards] = useState<LoyaltyReward[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load member data on mount
  useEffect(() => {
    loadMemberData();
  }, []);

  const loadMemberData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if user is already a member (simulate with localStorage)
      const isMember = localStorage.getItem('loyalty_member') === 'true';
      
      if (isMember) {
        const mockMember = generateMockMember();
        setMember(mockMember);
        setPointHistory(generateMockTransactions());
        setAvailableRewards(LOYALTY_REWARDS);
      }
    } catch (err) {
      setError('خطا در بارگذاری اطلاعات باشگاه مشتریان');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const joinProgram = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Create new member
      const newMember = generateMockMember();
      newMember.points = 100; // Welcome bonus
      newMember.totalEarned = 100;
      
      // Add welcome transaction
      const welcomeTransaction: PointTransaction = {
        id: 'welcome-tx',
        memberId: newMember.id,
        type: 'bonus',
        points: 100,
        description: 'هدیه عضویت در باشگاه مشتریان',
        createdAt: new Date().toISOString()
      };
      
      setMember(newMember);
      setPointHistory([welcomeTransaction]);
      setAvailableRewards(LOYALTY_REWARDS);
      
      // Save to localStorage
      localStorage.setItem('loyalty_member', 'true');
      
    } catch (err) {
      setError('خطا در عضویت در باشگاه مشتریان');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const earnPoints = useCallback(async (orderId: string, amount: number) => {
    if (!member) return;
    
    setIsLoading(true);
    
    try {
      // Calculate points based on tier multiplier
      const multiplier = POINT_EARNING_RULES.purchaseMultiplier[member.tier.id as keyof typeof POINT_EARNING_RULES.purchaseMultiplier] || 1;
      const basePoints = Math.floor(amount / POINT_EARNING_RULES.baseRate);
      const earnedPoints = Math.floor(basePoints * multiplier);
      
      // Create transaction
      const transaction: PointTransaction = {
        id: `earn-${Date.now()}`,
        memberId: member.id,
        type: 'earned',
        points: earnedPoints,
        description: `خرید به مبلغ ${amount.toLocaleString('fa-IR')} تومان`,
        orderId,
        createdAt: new Date().toISOString(),
        expiryDate: new Date(Date.now() + POINT_EARNING_RULES.pointExpiry * 24 * 60 * 60 * 1000).toISOString()
      };
      
      // Update member points
      const updatedMember = {
        ...member,
        points: member.points + earnedPoints,
        totalEarned: member.totalEarned + earnedPoints,
        totalSpent: member.totalSpent + amount,
        lastActivity: new Date().toISOString()
      };
      
      // Check for tier upgrade
      const newTier = LOYALTY_TIERS.find(t => 
        updatedMember.points >= t.minPoints && 
        (!t.maxPoints || updatedMember.points <= t.maxPoints)
      ) || LOYALTY_TIERS[0];
      
      if (newTier.level > member.tier.level) {
        updatedMember.tier = newTier;
        // Could trigger tier upgrade notification here
      }
      
      // Update next tier progress
      const nextTier = LOYALTY_TIERS.find(t => t.level === newTier.level + 1);
      updatedMember.nextTierProgress = nextTier ? {
        currentPoints: updatedMember.points,
        requiredPoints: nextTier.minPoints,
        percentage: Math.round((updatedMember.points / nextTier.minPoints) * 100)
      } : {
        currentPoints: updatedMember.points,
        requiredPoints: 0,
        percentage: 100
      };
      
      setMember(updatedMember);
      setPointHistory(prev => [transaction, ...prev]);
      
    } catch (err) {
      setError('خطا در ثبت امتیاز');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [member]);

  const redeemReward = useCallback(async (rewardId: string) => {
    if (!member) return;
    
    const reward = availableRewards.find(r => r.id === rewardId);
    if (!reward) {
      throw new Error('جایزه یافت نشد');
    }
    
    if (member.points < reward.pointsCost) {
      throw new Error('امتیاز کافی ندارید');
    }
    
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Create redemption transaction
      const transaction: PointTransaction = {
        id: `redeem-${Date.now()}`,
        memberId: member.id,
        type: 'redeemed',
        points: -reward.pointsCost,
        description: `دریافت جایزه: ${reward.title}`,
        createdAt: new Date().toISOString()
      };
      
      // Update member points
      const updatedMember = {
        ...member,
        points: member.points - reward.pointsCost,
        lastActivity: new Date().toISOString()
      };
      
      setMember(updatedMember);
      setPointHistory(prev => [transaction, ...prev]);
      
      // Update reward stock if applicable
      if (reward.stock !== undefined) {
        setAvailableRewards(prev => prev.map(r => 
          r.id === rewardId 
            ? { ...r, stock: r.stock! - 1 }
            : r
        ));
      }
      
    } catch (err) {
      setError('خطا در دریافت جایزه');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [member, availableRewards]);

  const getPointHistory = useCallback(async () => {
    // In a real app, this would load more transactions
    // For now, we just return the existing data
    return pointHistory;
  }, [pointHistory]);

  const getAvailableRewards = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real app, this would fetch fresh reward data
      setAvailableRewards(LOYALTY_REWARDS);
      
    } catch (err) {
      setError('خطا در بارگذاری جوایز');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const calculatePointsForOrder = useCallback((orderAmount: number) => {
    if (!member) return 0;
    
    const multiplier = POINT_EARNING_RULES.purchaseMultiplier[member.tier.id as keyof typeof POINT_EARNING_RULES.purchaseMultiplier] || 1;
    const basePoints = Math.floor(orderAmount / POINT_EARNING_RULES.baseRate);
    return Math.floor(basePoints * multiplier);
  }, [member]);

  const checkTierUpgrade = useCallback(async () => {
    if (!member) return;
    
    const currentTier = member.tier;
    const newTier = LOYALTY_TIERS.find(t => 
      member.points >= t.minPoints && 
      (!t.maxPoints || member.points <= t.maxPoints)
    ) || LOYALTY_TIERS[0];
    
    if (newTier.level > currentTier.level) {
      // Tier upgrade detected
      const updatedMember = { ...member, tier: newTier };
      setMember(updatedMember);
      
      // Could trigger upgrade notification here
      return true;
    }
    
    return false;
  }, [member]);

  return {
    member,
    pointHistory,
    availableRewards,
    isLoading,
    error,
    joinProgram,
    earnPoints,
    redeemReward,
    getPointHistory,
    getAvailableRewards,
    calculatePointsForOrder,
    checkTierUpgrade
  };
};
