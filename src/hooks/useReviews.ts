import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Review,
  ReviewStats,
  ReviewFilters,
  ReviewFormData,
  PERSIAN_REVIEW_TEMPLATES
} from '../types/review';
import { useAuth } from '../context/AuthContext';

// Mock data for demonstration
const generateMockReviews = (productId: number): Review[] => {
  const mockReviews: Review[] = [
    {
      id: '1',
      productId,
      userId: 'user1',
      userName: 'مریم احمدی',
      rating: 5,
      title: 'محصول فوق‌العاده‌ای',
      comment: 'واقعاً از این سرم راضی هستم. پوستم بعد از استفاده نرم و آبرسان شده و خطوط ریز صورتم کم شده. قطعاً دوباره خریداری می‌کنم.',
      pros: ['آبرسانی عالی', 'کاهش خطوط ریز', 'جذب سریع'],
      cons: [],
      isVerifiedPurchase: true,
      helpfulVotes: 12,
      totalVotes: 15,
      createdAt: '2024-12-15T10:30:00Z',
      isRecommended: true,
      skinType: 'خشک',
      ageRange: '26-35 سال',
      usageDuration: 'یک تا سه ماه',
      moderationStatus: 'approved'
    },
    {
      id: '2',
      productId,
      userId: 'user2',
      userName: 'سارا کریمی',
      rating: 4,
      title: 'خوب اما قیمت بالا',
      comment: 'محصول خوبی است و تأثیر مثبتی روی پوستم داشته. فقط قیمتش کمی بالا است. اما در کل راضی هستم.',
      pros: ['کیفیت خوب', 'تأثیر مثبت روی پوست'],
      cons: ['قیمت بالا'],
      isVerifiedPurchase: true,
      helpfulVotes: 8,
      totalVotes: 10,
      createdAt: '2024-12-10T14:20:00Z',
      isRecommended: true,
      skinType: 'مختلط',
      ageRange: '18-25 سال',
      usageDuration: 'یک هفته تا یک ماه',
      moderationStatus: 'approved'
    },
    {
      id: '3',
      productId,
      userId: 'user3',
      userName: 'فاطمه رضایی',
      rating: 3,
      title: 'متوسط',
      comment: 'نه خیلی بد نه خیلی خوب. تأثیر چندانی روی پوستم نداشت. شاید برای نوع پوست من مناسب نباشد.',
      pros: ['بو خوب', 'بسته‌بندی زیبا'],
      cons: ['تأثیر کم', 'نتیجه دیر'],
      isVerifiedPurchase: false,
      helpfulVotes: 3,
      totalVotes: 8,
      createdAt: '2024-12-05T09:15:00Z',
      isRecommended: false,
      skinType: 'چرب',
      ageRange: '36-45 سال',
      usageDuration: 'کمتر از یک هفته',
      moderationStatus: 'approved'
    }
  ];

  return mockReviews;
};

const calculateReviewStats = (reviews: Review[]): ReviewStats => {
  if (reviews.length === 0) {
    return {
      totalReviews: 0,
      averageRating: 0,
      ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
      recommendationPercentage: 0,
      verifiedPurchasePercentage: 0
    };
  }

  const totalReviews = reviews.length;
  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = totalRating / totalReviews;

  const ratingDistribution = reviews.reduce((dist, review) => {
    dist[review.rating as keyof typeof dist]++;
    return dist;
  }, { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 });

  const recommendedCount = reviews.filter(r => r.isRecommended).length;
  const recommendationPercentage = (recommendedCount / totalReviews) * 100;

  const verifiedCount = reviews.filter(r => r.isVerifiedPurchase).length;
  const verifiedPurchasePercentage = (verifiedCount / totalReviews) * 100;

  return {
    totalReviews,
    averageRating,
    ratingDistribution,
    recommendationPercentage,
    verifiedPurchasePercentage
  };
};

const sortReviews = (reviews: Review[], sortBy: ReviewFilters['sortBy']): Review[] => {
  const sorted = [...reviews];
  
  switch (sortBy) {
    case 'newest':
      return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    case 'oldest':
      return sorted.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    case 'highest':
      return sorted.sort((a, b) => b.rating - a.rating);
    case 'lowest':
      return sorted.sort((a, b) => a.rating - b.rating);
    case 'helpful':
      return sorted.sort((a, b) => {
        const aHelpfulRatio = a.totalVotes > 0 ? a.helpfulVotes / a.totalVotes : 0;
        const bHelpfulRatio = b.totalVotes > 0 ? b.helpfulVotes / b.totalVotes : 0;
        return bHelpfulRatio - aHelpfulRatio;
      });
    default:
      return sorted;
  }
};

const filterReviews = (reviews: Review[], filters: ReviewFilters): Review[] => {
  return reviews.filter(review => {
    if (filters.rating && review.rating !== filters.rating) {
      return false;
    }
    
    if (filters.verified !== undefined && review.isVerifiedPurchase !== filters.verified) {
      return false;
    }
    
    if (filters.recommended !== undefined && review.isRecommended !== filters.recommended) {
      return false;
    }
    
    if (filters.skinType && review.skinType !== filters.skinType) {
      return false;
    }
    
    return true;
  });
};

export const useReviews = (productId: number) => {
  const { user, isAuthenticated } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewStats, setReviewStats] = useState<ReviewStats>({
    totalReviews: 0,
    averageRating: 0,
    ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
    recommendationPercentage: 0,
    verifiedPurchasePercentage: 0
  });
  const [filters, setFilters] = useState<ReviewFilters>({
    sortBy: 'newest'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadReviews = useCallback(async (id: number) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockReviews = generateMockReviews(id);
      setReviews(mockReviews);
      setReviewStats(calculateReviewStats(mockReviews));
    } catch (err) {
      setError('خطا در بارگذاری نظرات');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const addReview = useCallback(async (id: number, reviewData: ReviewFormData) => {
    if (!isAuthenticated || !user) {
      throw new Error('برای ثبت نظر باید وارد حساب کاربری شوید');
    }

    setIsLoading(true);

    try {
      // Check if user has already reviewed this product
      const existingReview = reviews.find(review =>
        review.productId === id && review.userId === user.id
      );

      if (existingReview) {
        throw new Error('شما قبلاً برای این محصول نظر ثبت کرده‌اید');
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const newReview: Review = {
        id: Date.now().toString(),
        productId: id,
        userId: user.id,
        userName: `${user.firstName} ${user.lastName}`,
        rating: reviewData.rating,
        title: reviewData.title,
        comment: reviewData.comment,
        pros: reviewData.pros,
        cons: reviewData.cons,
        isVerifiedPurchase: true, // In real app, check purchase history
        helpfulVotes: 0,
        totalVotes: 0,
        createdAt: new Date().toISOString(),
        isRecommended: reviewData.isRecommended,
        skinType: reviewData.skinType,
        ageRange: reviewData.ageRange,
        usageDuration: reviewData.usageDuration,
        moderationStatus: 'pending'
      };

      setReviews(prev => [newReview, ...prev]);
      setReviewStats(prev => calculateReviewStats([newReview, ...reviews]));
    } catch (err) {
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [reviews, isAuthenticated, user]);

  const updateReview = useCallback(async (reviewId: string, reviewData: Partial<ReviewFormData>) => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setReviews(prev => prev.map(review => 
        review.id === reviewId 
          ? { ...review, ...reviewData, updatedAt: new Date().toISOString() }
          : review
      ));
    } catch (err) {
      throw new Error('خطا در به‌روزرسانی نظر');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteReview = useCallback(async (reviewId: string) => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setReviews(prev => prev.filter(review => review.id !== reviewId));
    } catch (err) {
      throw new Error('خطا در حذف نظر');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const voteReview = useCallback(async (reviewId: string, isHelpful: boolean) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setReviews(prev => prev.map(review => {
        if (review.id === reviewId) {
          return {
            ...review,
            helpfulVotes: isHelpful ? review.helpfulVotes + 1 : review.helpfulVotes,
            totalVotes: review.totalVotes + 1
          };
        }
        return review;
      }));
    } catch (err) {
      throw new Error('خطا در ثبت رأی');
    }
  }, []);

  const updateFilters = useCallback((newFilters: Partial<ReviewFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Check if current user can edit/delete a review
  const canModifyReview = useCallback((review: Review) => {
    return isAuthenticated && user && review.userId === user.id;
  }, [isAuthenticated, user]);

  // Check if current user has already reviewed this product
  const hasUserReviewed = useMemo(() => {
    if (!isAuthenticated || !user) return false;
    return reviews.some(review => review.userId === user.id);
  }, [reviews, isAuthenticated, user]);

  // Load reviews on mount
  useEffect(() => {
    loadReviews(productId);
  }, [productId, loadReviews]);

  // Apply filters and sorting
  const filteredAndSortedReviews = useMemo(() => {
    const filtered = filterReviews(reviews, filters);
    return sortReviews(filtered, filters.sortBy);
  }, [reviews, filters]);

  return {
    reviews: filteredAndSortedReviews,
    reviewStats,
    filters,
    isLoading,
    error,
    isAuthenticated,
    hasUserReviewed,
    canModifyReview,
    addReview,
    updateReview,
    deleteReview,
    voteReview,
    setFilters: updateFilters,
    loadReviews
  };
};
