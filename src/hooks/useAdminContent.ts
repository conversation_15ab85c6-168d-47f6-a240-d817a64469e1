import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { useAdminAuth } from './useAdminAuth';
import { AdminContentService } from '../services/adminContentService';
import { ApiService } from '../services/apiService';
import {
  Banner,
  Promotion,
  NewsletterCampaign,
  PageContent,
  MediaItem,
  ContentAnalytics,
  ContentFilters,
  ContentSortOptions,
  BannerFormData,
  PromotionFormData,
  NewsletterCampaignFormData,
  PageContentFormData,
  BulkContentOperation,
  ContentType,
  ContentStatus
} from '../types/adminContent';
import {
  validateBannerForm,
  validatePromotionForm,
  validateNewsletterForm,
  validatePageForm,
  generateSlug
} from '../utils/contentUtils';











export const useAdminContent = () => {
  const { user, checkPermission } = useAdminAuth();
  const [banners, setBanners] = useState<Banner[]>([]);
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [newsletters, setNewsletters] = useState<NewsletterCampaign[]>([]);
  const [pages, setPages] = useState<PageContent[]>([]);
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ContentFilters>({});
  const [sortOptions, setSortOptions] = useState<ContentSortOptions>({
    field: 'updatedAt',
    direction: 'desc'
  });

  // Initialize data from API
  useEffect(() => {
    const loadContent = async () => {
      if (!user || !checkPermission('content', 'read')) {
        setError('دسترسی کافی ندارید');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Try to load from API first
        const [bannersResult, promotionsResult, newslettersResult, pagesResult, mediaResult] = await Promise.all([
          AdminContentService.getBanners({}, 1, 100),
          AdminContentService.getPromotions({}, 1, 100),
          AdminContentService.getNewsletters({}, 1, 100),
          AdminContentService.getPages({}, 1, 100),
          AdminContentService.getMediaItems({}, 1, 100)
        ]);

        setBanners(bannersResult.banners);
        setPromotions(promotionsResult.promotions);
        setNewsletters(newslettersResult.newsletters);
        setPages(pagesResult.pages);
        setMediaItems(mediaResult.mediaItems);

        toast.success('محتوا از API بارگذاری شد');
      } catch (err) {
        console.error('Error loading content from API:', err);
        setError('خطا در بارگذاری محتوا');
        toast.error('خطا در بارگذاری محتوا');
      } finally {
        setLoading(false);
      }
    };

    loadContent();
  }, [user, checkPermission]);

  // Banner Management
  const createBanner = useCallback(async (data: BannerFormData): Promise<Banner> => {
    if (!checkPermission('content', 'create')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);

      const validation = validateBannerForm(data);
      if (!validation.isValid) {
        throw new Error(validation.errors[0].message);
      }

      const newBanner = await AdminContentService.createBanner(data);
      setBanners(prev => [newBanner, ...prev]);
      toast.success('بنر با موفقیت ایجاد شد');
      return newBanner;
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const updateBanner = useCallback(async (id: string, data: Partial<BannerFormData>): Promise<Banner> => {
    if (!checkPermission('content', 'update')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);

      const updatedBanner = await AdminContentService.updateBanner(id, data);
      setBanners(prev => prev.map(b => b.id === id ? updatedBanner : b));
      toast.success('بنر با موفقیت به‌روزرسانی شد');
      return updatedBanner;
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const deleteBanner = useCallback(async (id: string): Promise<void> => {
    if (!checkPermission('content', 'delete')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);
      await AdminContentService.deleteBanner(id);
      setBanners(prev => prev.filter(b => b.id !== id));
      toast.success('بنر با موفقیت حذف شد');
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const duplicateBanner = useCallback(async (id: string): Promise<Banner> => {
    try {
      setLoading(true);

      const originalBanner = banners.find(b => b.id === id);
      if (!originalBanner) {
        throw new Error('بنر یافت نشد');
      }

      const now = new Date().toISOString();
      const duplicatedBanner: Banner = {
        ...originalBanner,
        id: `banner-${Date.now()}`,
        title: `${originalBanner.title} (کپی)`,
        status: 'draft',
        isActive: false,
        views: 0,
        clicks: 0,
        conversions: 0,
        createdAt: now,
        updatedAt: now,
        createdBy: user?.id || 'unknown',
        updatedBy: undefined,
        publishedAt: undefined
      };

      setBanners(prev => [...prev, duplicatedBanner]);
      toast.success('بنر با موفقیت کپی شد');
      return duplicatedBanner;
    } catch (err) {
      const message = err instanceof Error ? err.message : 'خطا در کپی کردن بنر';
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [banners, user]);

  const bulkUpdateBanners = useCallback(async (ids: string[], updates: Partial<Banner>): Promise<void> => {
    try {
      setLoading(true);

      const now = new Date().toISOString();
      setBanners(prev => prev.map(banner =>
        ids.includes(banner.id)
          ? { ...banner, ...updates, updatedAt: now, updatedBy: user?.id || 'unknown' }
          : banner
      ));

      toast.success(`${ids.length} بنر با موفقیت به‌روزرسانی شد`);
    } catch (err) {
      const message = 'خطا در به‌روزرسانی گروهی بنرها';
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  const bulkDeleteBanners = useCallback(async (ids: string[]): Promise<void> => {
    try {
      setLoading(true);
      setBanners(prev => prev.filter(b => !ids.includes(b.id)));
      toast.success(`${ids.length} بنر با موفقیت حذف شد`);
    } catch (err) {
      const message = 'خطا در حذف گروهی بنرها';
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Promotion Management
  const createPromotion = useCallback(async (data: PromotionFormData): Promise<Promotion> => {
    if (!checkPermission('content', 'create')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);

      const validation = validatePromotionForm(data);
      if (!validation.isValid) {
        throw new Error(validation.errors[0].message);
      }

      const newPromotion = await AdminContentService.createPromotion(data);
      setPromotions(prev => [newPromotion, ...prev]);
      toast.success('تخفیف با موفقیت ایجاد شد');
      return newPromotion;
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const updatePromotion = useCallback(async (id: string, data: Partial<PromotionFormData>): Promise<Promotion> => {
    if (!checkPermission('content', 'update')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);

      const updatedPromotion = await AdminContentService.updatePromotion(id, data);
      setPromotions(prev => prev.map(p => p.id === id ? updatedPromotion : p));
      toast.success('تخفیف با موفقیت به‌روزرسانی شد');
      return updatedPromotion;
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const deletePromotion = useCallback(async (id: string): Promise<void> => {
    if (!checkPermission('content', 'delete')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);
      await AdminContentService.deletePromotion(id);
      setPromotions(prev => prev.filter(p => p.id !== id));
      toast.success('تخفیف با موفقیت حذف شد');
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Newsletter Campaign Management
  const createNewsletterCampaign = useCallback(async (data: NewsletterCampaignFormData): Promise<NewsletterCampaign> => {
    if (!checkPermission('content', 'create')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);

      const validation = validateNewsletterForm(data);
      if (!validation.isValid) {
        throw new Error(validation.errors[0].message);
      }

      const newCampaign = await AdminContentService.createNewsletter(data);
      setNewsletters(prev => [newCampaign, ...prev]);
      toast.success('کمپین خبرنامه با موفقیت ایجاد شد');
      return newCampaign;
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const updateNewsletterCampaign = useCallback(async (id: string, data: Partial<NewsletterCampaignFormData>): Promise<NewsletterCampaign> => {
    if (!checkPermission('content', 'update')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);

      const updatedCampaign = await AdminContentService.updateNewsletter(id, data);
      setNewsletters(prev => prev.map(n => n.id === id ? updatedCampaign : n));
      toast.success('کمپین خبرنامه با موفقیت به‌روزرسانی شد');
      return updatedCampaign;
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const deleteNewsletterCampaign = useCallback(async (id: string): Promise<void> => {
    if (!checkPermission('content', 'delete')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);
      await AdminContentService.deleteNewsletter(id);
      setNewsletters(prev => prev.filter(n => n.id !== id));
      toast.success('کمپین خبرنامه با موفقیت حذف شد');
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Additional helper functions
  const getPromotionById = useCallback(async (id: string): Promise<Promotion | null> => {
    return promotions.find(promotion => promotion.id === id) || null;
  }, [promotions]);

  const duplicatePromotion = useCallback(async (id: string): Promise<Promotion> => {
    const original = promotions.find(promotion => promotion.id === id);
    if (!original) throw new Error('Promotion not found');

    const duplicated: Promotion = {
      ...original,
      id: `promotion-${Date.now()}`,
      title: `${original.title} (کپی)`,
      code: `${original.code}-COPY`,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setPromotions(prev => [duplicated, ...prev]);
    return duplicated;
  }, [promotions]);

  const getPromotionAnalytics = useCallback(async (id: string): Promise<any> => {
    // Mock analytics data
    return {
      totalUsage: 245,
      conversions: 89,
      revenue: 12500000
    };
  }, []);

  const getNewsletterById = useCallback(async (id: string): Promise<NewsletterCampaign | null> => {
    return newsletters.find(newsletter => newsletter.id === id) || null;
  }, [newsletters]);

  const duplicateNewsletter = useCallback(async (id: string): Promise<NewsletterCampaign> => {
    const original = newsletters.find(newsletter => newsletter.id === id);
    if (!original) throw new Error('Newsletter not found');

    const duplicated: NewsletterCampaign = {
      ...original,
      id: `newsletter-${Date.now()}`,
      title: `${original.title} (کپی)`,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setNewsletters(prev => [duplicated, ...prev]);
    return duplicated;
  }, [newsletters]);

  const sendNewsletter = useCallback(async (id: string): Promise<void> => {
    setNewsletters(prev => prev.map(newsletter =>
      newsletter.id === id
        ? {
            ...newsletter,
            status: 'sent' as ContentStatus,
            sentAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        : newsletter
    ));
  }, []);

  const getNewsletterAnalytics = useCallback(async (id: string): Promise<any> => {
    // Mock analytics data
    return {
      sent: 1250,
      opened: 875,
      clicked: 156,
      unsubscribed: 12,
      bounced: 23
    };
  }, []);

  // Page Content Management
  const createPage = useCallback(async (data: PageContentFormData): Promise<PageContent> => {
    if (!checkPermission('content', 'create')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);

      const validation = validatePageForm(data);
      if (!validation.isValid) {
        throw new Error(validation.errors[0]?.message || 'داده‌های نامعتبر');
      }

      const newPage = await AdminContentService.createPage(data);
      setPages(prev => [newPage, ...prev]);
      toast.success('صفحه با موفقیت ایجاد شد');
      return newPage;
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const updatePage = useCallback(async (id: string, data: Partial<PageContentFormData>): Promise<PageContent> => {
    if (!checkPermission('content', 'update')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);

      const validation = validatePageForm(data as PageContentFormData);
      if (!validation.isValid) {
        throw new Error(validation.errors[0]?.message || 'داده‌های نامعتبر');
      }

      const updatedPage = await AdminContentService.updatePage(id, data);
      setPages(prev => prev.map(page => page.id === id ? updatedPage : page));
      toast.success('صفحه با موفقیت به‌روزرسانی شد');
      return updatedPage;
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const deletePage = useCallback(async (id: string): Promise<void> => {
    if (!checkPermission('content', 'delete')) {
      throw new Error('دسترسی کافی ندارید');
    }

    try {
      setLoading(true);
      await AdminContentService.deletePage(id);
      setPages(prev => prev.filter(page => page.id !== id));
      toast.success('صفحه با موفقیت حذف شد');
    } catch (err) {
      const message = ApiService.ErrorHandler.handleError(err as Error);
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  const getPageById = useCallback(async (id: string): Promise<PageContent | null> => {
    return pages.find(page => page.id === id) || null;
  }, [pages]);

  const duplicatePage = useCallback(async (id: string): Promise<PageContent> => {
    const original = pages.find(page => page.id === id);
    if (!original) throw new Error('Page not found');

    const duplicated: PageContent = {
      ...original,
      id: `page-${Date.now()}`,
      title: `${original.title} (کپی)`,
      slug: `${original.slug}-copy`,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setPages(prev => [duplicated, ...prev]);
    return duplicated;
  }, [pages]);

  // Get content analytics (synchronous for immediate use in components)
  const getContentAnalytics = useCallback((): ContentAnalytics => {
    const allContent = [...banners, ...promotions, ...newsletters, ...pages];

    return {
      totalContent: allContent.length,
      publishedContent: allContent.filter(c => c.status === 'published').length,
      draftContent: allContent.filter(c => c.status === 'draft').length,
      scheduledContent: allContent.filter(c => c.status === 'scheduled').length,
      totalViews: allContent.reduce((sum, c) => sum + c.views, 0),
      totalClicks: allContent.reduce((sum, c) => sum + c.clicks, 0),
      totalConversions: allContent.reduce((sum, c) => sum + c.conversions, 0),
      contentByType: {
        banner: banners.length,
        promotion: promotions.length,
        newsletter: newsletters.length,
        page: pages.length,
        media: mediaItems.length
      },
      recentActivity: [],
      topBanners: banners.slice(0, 5),
      topPromotions: promotions.slice(0, 5),
      topPages: pages.slice(0, 5)
    };
  }, [banners, promotions, newsletters, pages, mediaItems]);

  return {
    // State
    banners,
    promotions,
    newsletters,
    pages,
    mediaItems,
    loading,
    error,
    filters,
    sortOptions,

    // Actions
    setFilters,
    setSortOptions,
    createBanner,
    updateBanner,
    deleteBanner,
    createPromotion,
    updatePromotion,
    deletePromotion,
    getPromotionById,
    duplicatePromotion,
    getPromotionAnalytics,
    createNewsletterCampaign,
    updateNewsletterCampaign,
    deleteNewsletterCampaign,
    getNewsletterById,
    duplicateNewsletter,
    sendNewsletter,
    getNewsletterAnalytics,
    createPage,
    updatePage,
    deletePage,
    getPageById,
    duplicatePage,
    getContentAnalytics,

    // Utilities
    clearError: () => setError(null)
  };
};
