import { useContext } from 'react';
import AuthContext, { AuthContextType } from '../context/AuthContext';

/**
 * Custom hook to access authentication context
 * 
 * @returns AuthContextType - Authentication context with user state and actions
 * @throws Error if used outside of AuthProvider
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default useAuth;
