import { useState, useEffect, useCallback } from 'react';
import {
  AdminNotification,
  NotificationFilters,
  NotificationSettings,
  AuditLogEntry,
  AuditLogFilters,
  NotificationType,
  NotificationCategory
} from '../types/adminNotifications';






export const useAdminNotifications = () => {
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<NotificationFilters>({});
  const [auditFilters, setAuditFilters] = useState<AuditLogFilters>({});

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with actual API calls
      // const notificationsResponse = await AdminNotificationService.getNotifications();
      // const auditLogsResponse = await AdminNotificationService.getAuditLogs();
      // const settingsResponse = await AdminNotificationService.getSettings();

      // For now, set empty arrays since we removed mock data
      setNotifications([]);
      setAuditLogs([]);
      setNotificationSettings(null);

    } catch (err) {
      setError('خطا در بارگذاری اعلان‌ها');
      console.error('Notifications fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId
            ? { ...notif, read: true, readAt: new Date().toISOString() }
            : notif
        )
      );

      return { success: true };
    } catch (err) {
      console.error('Mark as read error:', err);
      return { success: false, error: 'خطا در علامت‌گذاری به عنوان خوانده شده' };
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const now = new Date().toISOString();
      setNotifications(prev =>
        prev.map(notif => ({ ...notif, read: true, readAt: now }))
      );

      return { success: true };
    } catch (err) {
      console.error('Mark all as read error:', err);
      return { success: false, error: 'خطا در علامت‌گذاری همه اعلان‌ها' };
    }
  }, []);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));

      return { success: true };
    } catch (err) {
      console.error('Delete notification error:', err);
      return { success: false, error: 'خطا در حذف اعلان' };
    }
  }, []);

  const updateNotificationSettings = useCallback(async (settings: Partial<NotificationSettings>) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setNotificationSettings(prev => prev ? { ...prev, ...settings } : null);

      return { success: true };
    } catch (err) {
      console.error('Update settings error:', err);
      return { success: false, error: 'خطا در ذخیره تنظیمات' };
    }
  }, []);

  const createNotification = useCallback(async (
    type: NotificationType,
    title: string,
    message: string,
    data?: any
  ) => {
    try {
      const newNotification: AdminNotification = {
        id: `notif-${Date.now()}`,
        type,
        title,
        message,
        data,
        read: false,
        createdAt: new Date().toISOString(),
        priority: 'medium',
        category: type.split('_')[0] as NotificationCategory
      };

      setNotifications(prev => [newNotification, ...prev]);

      return { success: true, notification: newNotification };
    } catch (err) {
      return { success: false, error: 'خطا در ایجاد اعلان' };
    }
  }, []);

  // Test function to create a sample notification
  const createTestNotification = useCallback(async () => {
    return await createNotification(
      'order_new',
      'سفارش تست جدید',
      `سفارش تست #${Math.floor(Math.random() * 10000)} ثبت شد`
    );
  }, [createNotification]);

  // Filter notifications based on current filters
  const filteredNotifications = notifications.filter(notif => {
    if (filters.category && !filters.category.includes(notif.category)) return false;
    if (filters.type && !filters.type.includes(notif.type)) return false;
    if (filters.priority && !filters.priority.includes(notif.priority)) return false;
    if (filters.read !== undefined && notif.read !== filters.read) return false;

    if (filters.dateRange) {
      const notifDate = new Date(notif.createdAt);
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      if (notifDate < startDate || notifDate > endDate) return false;
    }

    return true;
  });

  // Filter audit logs based on current filters
  const filteredAuditLogs = auditLogs.filter(log => {
    if (auditFilters.userId && log.userId !== auditFilters.userId) return false;
    if (auditFilters.action && !auditFilters.action.includes(log.action)) return false;
    if (auditFilters.resource && !auditFilters.resource.includes(log.resource)) return false;
    if (auditFilters.success !== undefined && log.success !== auditFilters.success) return false;
    if (auditFilters.ipAddress && log.ipAddress !== auditFilters.ipAddress) return false;
    
    if (auditFilters.dateRange) {
      const logDate = new Date(log.timestamp);
      const startDate = new Date(auditFilters.dateRange.start);
      const endDate = new Date(auditFilters.dateRange.end);
      if (logDate < startDate || logDate > endDate) return false;
    }
    
    return true;
  });

  const unreadCount = notifications.filter(notif => !notif.read).length;

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  return {
    notifications: filteredNotifications,
    auditLogs: filteredAuditLogs,
    notificationSettings,
    loading,
    error,
    filters,
    setFilters,
    auditFilters,
    setAuditFilters,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    updateNotificationSettings,
    createNotification,
    createTestNotification,
    refetch: fetchNotifications
  };
};
