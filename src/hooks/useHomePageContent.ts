import { useState, useEffect, useCallback } from 'react';
import { 
  HomePageContentFormData, 
  HomePageSection, 
  SectionType,
  ValidationResult,
  ValidationError,
  SectionTemplate
} from '../types/homePageContent';
import toast from 'react-hot-toast';

// Mock data for development
const generateMockHomePageContent = (): HomePageContentFormData => ({
  sections: [
    {
      id: 'hero-1',
      type: 'hero',
      title: 'بخش اصلی',
      isActive: true,
      order: 1,
      lastModified: new Date().toISOString(),
      modifiedBy: 'admin',
      content: {
        backgroundImage: 'https://images.pexels.com/photos/7290697/pexels-photo-7290697.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        title: 'پوستی درخشان با محصولات مراقبتی با کیفیت',
        subtitle: 'گلورویا',
        description: 'مراقبت از پوست با محصولات طبیعی و مؤثر، برای داشتن پوستی سالم، شاداب و درخشان.',
        primaryButton: {
          text: 'مشاهده محصولات',
          url: '/products',
          type: 'internal'
        },
        secondaryButton: {
          text: 'درباره ما',
          url: '/about',
          type: 'internal'
        },
        overlayOpacity: 0.7,
        textAlignment: 'left',
        textColor: '#ffffff',
        animation: {
          enabled: true,
          type: 'fade',
          duration: 0.6
        }
      }
    },
    {
      id: 'featured-products-1',
      type: 'featured-products',
      title: 'محصولات ویژه',
      isActive: true,
      order: 2,
      lastModified: new Date().toISOString(),
      modifiedBy: 'admin',
      content: {
        title: 'محصولات ویژه',
        subtitle: 'بهترین محصولات مراقبت از پوست',
        displayType: 'grid',
        productsPerRow: 4,
        maxProducts: 8,
        showViewAllButton: true,
        viewAllButtonText: 'مشاهده همه',
        viewAllButtonUrl: '/products',
        filterBy: 'featured',
        sortBy: 'popularity',
        sortOrder: 'desc',
        showPrices: true,
        showRatings: true,
        showAddToCart: true
      }
    },
    {
      id: 'categories-1',
      type: 'categories',
      title: 'دسته‌بندی‌ها',
      isActive: true,
      order: 3,
      lastModified: new Date().toISOString(),
      modifiedBy: 'admin',
      content: {
        title: 'دسته‌بندی محصولات',
        subtitle: 'انتخاب بر اساس نیاز پوست شما',
        displayType: 'grid',
        categoriesPerRow: 4,
        maxCategories: 8,
        showViewAllButton: true,
        viewAllButtonText: 'همه دسته‌بندی‌ها',
        viewAllButtonUrl: '/categories',
        selectedCategories: [],
        showProductCount: true,
        imageStyle: 'rounded'
      }
    },
    {
      id: 'testimonials-1',
      type: 'testimonials',
      title: 'نظرات مشتریان',
      isActive: true,
      order: 4,
      lastModified: new Date().toISOString(),
      modifiedBy: 'admin',
      content: {
        title: 'نظرات مشتریان',
        subtitle: 'تجربه واقعی کاربران',
        displayType: 'carousel',
        testimonialsPerView: 3,
        autoplay: true,
        autoplaySpeed: 5000,
        showNavigation: true,
        showPagination: true,
        testimonials: [
          {
            id: '1',
            name: 'مریم احمدی',
            rating: 5,
            content: 'محصولات گلورویا واقعاً فوق‌العاده هستند. پوست من بعد از استفاده از سرم هیالورونیک خیلی بهتر شده.',
            date: new Date().toISOString(),
            isVerified: true,
            isActive: true
          }
        ],
        cardStyle: 'default'
      }
    }
  ],
  seoSettings: {
    title: 'فروشگاه آنلاین محصولات مراقبت از پوست گلورویا',
    description: 'بهترین محصولات مراقبت از پوست، سرم، کرم و ماسک با کیفیت بالا',
    keywords: ['مراقبت از پوست', 'سرم', 'کرم', 'ماسک', 'زیبایی']
  },
  globalSettings: {
    enableAnimations: true,
    lazyLoading: true,
    mobileOptimized: true
  },
  status: 'published',
  lastModified: new Date().toISOString(),
  modifiedBy: 'admin'
});

const generateSectionTemplates = (): SectionTemplate[] => [
  {
    id: 'hero-basic',
    name: 'هیرو ساده',
    type: 'hero',
    description: 'بخش اصلی با تصویر پس‌زمینه و دکمه‌های عمل',
    thumbnail: '/templates/hero-basic.jpg',
    defaultData: {},
    isPopular: true,
    category: 'basic'
  },
  {
    id: 'products-grid',
    name: 'شبکه محصولات',
    type: 'featured-products',
    description: 'نمایش محصولات در قالب شبکه',
    thumbnail: '/templates/products-grid.jpg',
    defaultData: {},
    isPopular: true,
    category: 'basic'
  }
];

export const useHomePageContent = () => {
  const [content, setContent] = useState<HomePageContentFormData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [sectionTemplates] = useState<SectionTemplate[]>(generateSectionTemplates());

  // Load content
  useEffect(() => {
    const loadContent = async () => {
      try {
        setLoading(true);
        
        // Try to load from localStorage first
        const stored = localStorage.getItem('homepage-content');
        if (stored) {
          setContent(JSON.parse(stored));
        } else {
          // Generate mock data
          const mockContent = generateMockHomePageContent();
          setContent(mockContent);
          localStorage.setItem('homepage-content', JSON.stringify(mockContent));
        }
      } catch (err) {
        setError('خطا در بارگذاری محتوا');
        console.error('Error loading content:', err);
      } finally {
        setLoading(false);
      }
    };

    loadContent();
  }, []);

  // Save content
  const saveContent = useCallback(async (data: HomePageContentFormData) => {
    try {
      setSaving(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedContent = {
        ...data,
        lastModified: new Date().toISOString(),
        modifiedBy: 'admin'
      };
      
      localStorage.setItem('homepage-content', JSON.stringify(updatedContent));
      setContent(updatedContent);
      setIsDirty(false);
      
      toast.success('محتوا با موفقیت ذخیره شد');
      return updatedContent;
    } catch (err) {
      toast.error('خطا در ذخیره محتوا');
      throw err;
    } finally {
      setSaving(false);
    }
  }, []);

  // Update section
  const updateSection = useCallback((sectionId: string, updates: Partial<HomePageSection>) => {
    if (!content) return;

    const updatedSections = content.sections.map(section =>
      section.id === sectionId
        ? { ...section, ...updates, lastModified: new Date().toISOString() }
        : section
    );

    const updatedContent = {
      ...content,
      sections: updatedSections
    };

    setContent(updatedContent);
    setIsDirty(true);
  }, [content]);

  // Add section
  const addSection = useCallback((sectionData: Partial<HomePageSection>, position?: number) => {
    if (!content) return;

    const newSection: HomePageSection = {
      id: `section-${Date.now()}`,
      type: 'hero',
      title: 'بخش جدید',
      isActive: true,
      order: position ?? content.sections.length + 1,
      lastModified: new Date().toISOString(),
      modifiedBy: 'admin',
      ...sectionData
    } as HomePageSection;

    const updatedSections = [...content.sections];
    if (position !== undefined) {
      updatedSections.splice(position, 0, newSection);
      // Update order for subsequent sections
      updatedSections.forEach((section, index) => {
        section.order = index + 1;
      });
    } else {
      updatedSections.push(newSection);
    }

    const updatedContent = {
      ...content,
      sections: updatedSections
    };

    setContent(updatedContent);
    setIsDirty(true);
  }, [content]);

  // Remove section
  const removeSection = useCallback((sectionId: string) => {
    if (!content) return;

    const updatedSections = content.sections
      .filter(section => section.id !== sectionId)
      .map((section, index) => ({
        ...section,
        order: index + 1
      }));

    const updatedContent = {
      ...content,
      sections: updatedSections
    };

    setContent(updatedContent);
    setIsDirty(true);
  }, [content]);

  // Reorder sections
  const reorderSections = useCallback((fromIndex: number, toIndex: number) => {
    if (!content) return;

    const updatedSections = [...content.sections];
    const [movedSection] = updatedSections.splice(fromIndex, 1);
    updatedSections.splice(toIndex, 0, movedSection);

    // Update order
    updatedSections.forEach((section, index) => {
      section.order = index + 1;
    });

    const updatedContent = {
      ...content,
      sections: updatedSections
    };

    setContent(updatedContent);
    setIsDirty(true);
  }, [content]);

  // Validate content
  const validateContent = useCallback((data: HomePageContentFormData): ValidationResult => {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Validate each section
    data.sections.forEach(section => {
      if (!section.title.trim()) {
        errors.push({
          sectionId: section.id,
          field: 'title',
          message: 'عنوان بخش الزامی است',
          type: 'error'
        });
      }

      // Section-specific validation
      if (section.type === 'hero') {
        const heroSection = section as any;
        if (!heroSection.content?.backgroundImage) {
          errors.push({
            sectionId: section.id,
            field: 'backgroundImage',
            message: 'تصویر پس‌زمینه الزامی است',
            type: 'error'
          });
        }
      }
    });

    // SEO validation
    if (!data.seoSettings.title.trim()) {
      errors.push({
        sectionId: 'seo',
        field: 'title',
        message: 'عنوان SEO الزامی است',
        type: 'error'
      });
    }

    if (data.seoSettings.title.length > 60) {
      warnings.push({
        sectionId: 'seo',
        field: 'title',
        message: 'عنوان SEO بهتر است کمتر از 60 کاراکتر باشد',
        type: 'warning'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }, []);

  // Publish content
  const publishContent = useCallback(async () => {
    if (!content) return;

    const validation = validateContent(content);
    if (!validation.isValid) {
      toast.error('لطفاً ابتدا خطاهای موجود را برطرف کنید');
      return;
    }

    const publishedContent = {
      ...content,
      status: 'published' as const,
      lastModified: new Date().toISOString()
    };

    return await saveContent(publishedContent);
  }, [content, validateContent, saveContent]);

  return {
    // State
    content,
    loading,
    saving,
    error,
    isDirty,
    previewMode,
    sectionTemplates,

    // Actions
    saveContent,
    updateSection,
    addSection,
    removeSection,
    reorderSections,
    validateContent,
    publishContent,
    setPreviewMode,

    // Utilities
    clearError: () => setError(null)
  };
};
