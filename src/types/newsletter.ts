export interface NewsletterSubscription {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  isActive: boolean;
  subscribedAt: string;
  unsubscribedAt?: string;
  preferences: NewsletterPreferences;
  source: SubscriptionSource;
  tags: string[];
}

export interface NewsletterPreferences {
  productUpdates: boolean;
  promotions: boolean;
  skincareTips: boolean;
  newArrivals: boolean;
  exclusiveOffers: boolean;
  weeklyDigest: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  language: 'fa' | 'en';
}

export type SubscriptionSource = 
  | 'footer'
  | 'popup'
  | 'checkout'
  | 'registration'
  | 'product_page'
  | 'home_page'
  | 'manual';

export interface NewsletterFormData {
  email: string;
  firstName?: string;
  lastName?: string;
  preferences?: Partial<NewsletterPreferences>;
  source: SubscriptionSource;
  acceptTerms: boolean;
}

export interface NewsletterContextType {
  // State
  isSubscribed: boolean;
  isLoading: boolean;
  error: string | null;
  subscription: NewsletterSubscription | null;
  
  // Actions
  subscribe: (data: NewsletterFormData) => Promise<void>;
  unsubscribe: (email: string, reason?: string) => Promise<void>;
  updatePreferences: (preferences: Partial<NewsletterPreferences>) => Promise<void>;
  checkSubscription: (email: string) => Promise<boolean>;
  resendConfirmation: (email: string) => Promise<void>;
  clearError: () => void;
}

export interface NewsletterCampaign {
  id: string;
  title: string;
  subject: string;
  content: string;
  type: CampaignType;
  status: CampaignStatus;
  scheduledAt?: string;
  sentAt?: string;
  recipients: number;
  openRate?: number;
  clickRate?: number;
  createdAt: string;
  updatedAt: string;
}

export type CampaignType = 
  | 'promotional'
  | 'product_update'
  | 'skincare_tips'
  | 'new_arrivals'
  | 'exclusive_offer'
  | 'weekly_digest';

export type CampaignStatus = 
  | 'draft'
  | 'scheduled'
  | 'sending'
  | 'sent'
  | 'cancelled';

export interface NewsletterTemplate {
  id: string;
  name: string;
  type: CampaignType;
  subject: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
}

// Persian messages and content
export const PERSIAN_NEWSLETTER_MESSAGES = {
  success: {
    subscribed: 'با موفقیت در خبرنامه عضو شدید',
    unsubscribed: 'با موفقیت از خبرنامه خارج شدید',
    preferencesUpdated: 'تنظیمات خبرنامه به‌روزرسانی شد',
    confirmationSent: 'ایمیل تأیید ارسال شد',
    alreadySubscribed: 'شما قبلاً در خبرنامه عضو هستید'
  },
  errors: {
    invalidEmail: 'ایمیل معتبر وارد کنید',
    emailRequired: 'ایمیل الزامی است',
    subscriptionFailed: 'خطا در عضویت در خبرنامه',
    unsubscribeFailed: 'خطا در لغو عضویت',
    networkError: 'خطا در اتصال به سرور',
    emailExists: 'این ایمیل قبلاً ثبت شده است',
    emailNotFound: 'ایمیل در خبرنامه یافت نشد',
    termsRequired: 'پذیرش قوانین الزامی است'
  },
  placeholders: {
    email: 'ایمیل خود را وارد کنید',
    firstName: 'نام (اختیاری)',
    lastName: 'نام خانوادگی (اختیاری)'
  },
  labels: {
    email: 'ایمیل',
    firstName: 'نام',
    lastName: 'نام خانوادگی',
    acceptTerms: 'قوانین و مقررات را می‌پذیرم',
    productUpdates: 'اطلاع از محصولات جدید',
    promotions: 'تخفیف‌ها و پیشنهادات ویژه',
    skincareTips: 'نکات مراقبت از پوست',
    newArrivals: 'محصولات تازه وارد',
    exclusiveOffers: 'پیشنهادات انحصاری',
    weeklyDigest: 'خلاصه هفتگی',
    frequency: 'تعداد دفعات ارسال',
    language: 'زبان'
  },
  buttons: {
    subscribe: 'عضویت در خبرنامه',
    unsubscribe: 'لغو عضویت',
    updatePreferences: 'به‌روزرسانی تنظیمات',
    resendConfirmation: 'ارسال مجدد تأیید',
    close: 'بستن',
    cancel: 'انصراف',
    save: 'ذخیره'
  },
  content: {
    title: 'عضویت در خبرنامه گلو رویا',
    subtitle: 'از آخرین محصولات و تخفیف‌های ویژه باخبر شوید',
    description: 'با عضویت در خبرنامه گلو رویا، اولین نفری باشید که از محصولات جدید، نکات مراقبت از پوست و تخفیف‌های انحصاری مطلع می‌شوید.',
    benefits: [
      'اطلاع از محصولات جدید',
      'تخفیف‌های انحصاری',
      'نکات تخصصی مراقبت از پوست',
      'پیشنهادات شخصی‌سازی شده',
      'دسترسی زودهنگام به فروش‌های ویژه'
    ],
    privacy: 'ما به حریم خصوصی شما احترام می‌گذاریم و هرگز اطلاعات شما را با اشخاص ثالث به اشتراک نمی‌گذاریم.',
    unsubscribeNote: 'می‌توانید در هر زمان از خبرنامه خارج شوید.',
    confirmationTitle: 'تأیید عضویت',
    confirmationMessage: 'لطفاً ایمیل خود را بررسی کنید و روی لینک تأیید کلیک کنید.',
    welcomeTitle: 'خوش آمدید!',
    welcomeMessage: 'با موفقیت در خبرنامه گلو رویا عضو شدید.',
    unsubscribeTitle: 'لغو عضویت',
    unsubscribeMessage: 'متأسفیم که تصمیم به ترک خبرنامه گرفته‌اید.',
    unsubscribeReasons: [
      'ایمیل‌های زیادی دریافت می‌کنم',
      'محتوا مرتبط نیست',
      'دیگر علاقه‌ای ندارم',
      'مشکل فنی',
      'سایر دلایل'
    ]
  },
  frequencies: [
    { value: 'daily', label: 'روزانه' },
    { value: 'weekly', label: 'هفتگی' },
    { value: 'monthly', label: 'ماهانه' }
  ]
};

// Newsletter templates
export const NEWSLETTER_TEMPLATES = {
  welcome: {
    subject: 'خوش آمدید به خانواده گلو رویا! 🌟',
    content: `
      سلام {{firstName}},
      
      از عضویت شما در خبرنامه گلو رویا بسیار خوشحالیم! 
      
      به عنوان عضو جدید، کد تخفیف ۱۰٪ برای اولین خرید شما: WELCOME10
      
      با آرزوی پوستی سالم و درخشان،
      تیم گلو رویا
    `
  },
  promotion: {
    subject: 'تخفیف ویژه {{discount}}٪ فقط برای شما! 🎉',
    content: `
      سلام {{firstName}},
      
      فرصت طلایی! تخفیف {{discount}}٪ روی تمام محصولات مراقبت از پوست.
      
      کد تخفیف: {{promoCode}}
      اعتبار تا: {{expiryDate}}
      
      همین حالا خرید کنید!
    `
  },
  newProduct: {
    subject: 'محصول جدید: {{productName}} 🆕',
    content: `
      سلام {{firstName}},
      
      محصول جدید {{productName}} اکنون در دسترس است!
      
      {{productDescription}}
      
      قیمت ویژه: {{price}} تومان
      
      سفارش دهید!
    `
  },
  skincareTips: {
    subject: 'نکات هفتگی مراقبت از پوست 💡',
    content: `
      سلام {{firstName}},
      
      نکته این هفته: {{tip}}
      
      برای پوستی سالم‌تر، این نکات را دنبال کنید:
      {{tips}}
      
      موفق باشید!
    `
  }
};

// Storage keys
export const NEWSLETTER_STORAGE_KEYS = {
  SUBSCRIPTION: 'newsletter_subscription',
  PREFERENCES: 'newsletter_preferences',
  POPUP_SHOWN: 'newsletter_popup_shown',
  LAST_POPUP: 'newsletter_last_popup'
} as const;
