import { Product } from './index';

// Wishlist types
export interface WishlistItem {
  id: string;
  product: Product;
  addedAt: Date;
  notes?: string;
  priority: WishlistPriority;
  category?: string;
  tags: string[];
}

export type WishlistPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface WishlistFilters {
  category?: string;
  priority?: WishlistPriority;
  priceRange?: [number, number];
  availability?: 'all' | 'in_stock' | 'out_of_stock';
  sortBy: WishlistSortOption;
}

export type WishlistSortOption = 
  | 'date_added' 
  | 'date_added_desc' 
  | 'name' 
  | 'name_desc' 
  | 'price' 
  | 'price_desc' 
  | 'priority';

// Product comparison types
export interface ComparisonItem {
  id: string;
  product: Product;
  addedAt: Date;
}

export interface ComparisonCriteria {
  id: string;
  name: string;
  category: ComparisonCategory;
  getValue: (product: Product) => string | number | boolean;
  format?: (value: any) => string;
  important?: boolean;
}

export type ComparisonCategory = 
  | 'basic' 
  | 'pricing' 
  | 'specifications' 
  | 'ingredients' 
  | 'benefits' 
  | 'ratings';

export interface ComparisonResult {
  criteria: ComparisonCriteria;
  values: { [productId: string]: any };
  winner?: string; // Product ID with best value
  notes?: string;
}

// Recently viewed types
export interface RecentlyViewedItem {
  id: string;
  product: Product;
  viewedAt: Date;
  viewCount: number;
  timeSpent?: number; // seconds
  source?: ViewSource;
}

export type ViewSource = 
  | 'search' 
  | 'category' 
  | 'recommendation' 
  | 'direct_link' 
  | 'related_products';

// Context types
export interface WishlistContextType {
  items: WishlistItem[];
  totalItems: number;
  isLoading: boolean;
  
  // Actions
  addItem: (product: Product, priority?: WishlistPriority, notes?: string) => void;
  removeItem: (itemId: string) => void;
  updateItem: (itemId: string, updates: Partial<WishlistItem>) => void;
  clearWishlist: () => void;
  
  // Filters and sorting
  filters: WishlistFilters;
  setFilters: (filters: Partial<WishlistFilters>) => void;
  filteredItems: WishlistItem[];
  
  // Utilities
  isInWishlist: (productId: string) => boolean;
  getWishlistItem: (productId: string) => WishlistItem | undefined;
  moveToCart: (itemId: string) => Product | null;
  shareWishlist: () => void;
}

export interface ComparisonContextType {
  items: ComparisonItem[];
  maxItems: number;
  isLoading: boolean;
  
  // Actions
  addItem: (product: Product) => void;
  removeItem: (productId: string) => void;
  clearComparison: () => void;
  
  // Comparison logic
  criteria: ComparisonCriteria[];
  results: ComparisonResult[];
  generateComparison: () => ComparisonResult[];
  
  // Utilities
  isInComparison: (productId: string) => boolean;
  canAddMore: boolean;
  shareComparison: () => void;
}

export interface RecentlyViewedContextType {
  items: RecentlyViewedItem[];
  maxItems: number;
  
  // Actions
  addItem: (product: Product, source?: ViewSource, timeSpent?: number) => void;
  removeItem: (productId: string) => void;
  clearHistory: () => void;
  
  // Analytics
  getViewCount: (productId: string) => number;
  getTotalTimeSpent: (productId: string) => number;
  getMostViewedProducts: (limit?: number) => RecentlyViewedItem[];
  getRecommendationsBasedOnHistory: () => Product[];
}

// Persian messages
export const PERSIAN_WISHLIST_MESSAGES = {
  title: 'علاقه‌مندی‌ها',
  empty: 'هیچ محصولی در علاقه‌مندی‌های شما نیست',
  addToWishlist: 'افزودن به علاقه‌مندی‌ها',
  removeFromWishlist: 'حذف از علاقه‌مندی‌ها',
  addedToWishlist: 'محصول به علاقه‌مندی‌ها اضافه شد',
  removedFromWishlist: 'محصول از علاقه‌مندی‌ها حذف شد',
  moveToCart: 'انتقال به سبد خرید',
  movedToCart: 'محصول به سبد خرید منتقل شد',
  shareWishlist: 'اشتراک‌گذاری لیست',
  clearWishlist: 'پاک کردن همه',
  wishlistCleared: 'علاقه‌مندی‌ها پاک شدند',
  priority: {
    low: 'کم',
    medium: 'متوسط',
    high: 'زیاد',
    urgent: 'فوری'
  },
  filters: {
    all: 'همه',
    category: 'دسته‌بندی',
    priority: 'اولویت',
    priceRange: 'محدوده قیمت',
    availability: 'موجودی',
    sortBy: 'مرتب‌سازی بر اساس'
  },
  sort: {
    date_added: 'تاریخ اضافه شدن (جدید)',
    date_added_desc: 'تاریخ اضافه شدن (قدیم)',
    name: 'نام (الف-ی)',
    name_desc: 'نام (ی-الف)',
    price: 'قیمت (کم به زیاد)',
    price_desc: 'قیمت (زیاد به کم)',
    priority: 'اولویت'
  }
};

export const PERSIAN_COMPARISON_MESSAGES = {
  title: 'مقایسه محصولات',
  empty: 'هیچ محصولی برای مقایسه انتخاب نشده',
  addToComparison: 'افزودن به مقایسه',
  removeFromComparison: 'حذف از مقایسه',
  addedToComparison: 'محصول به مقایسه اضافه شد',
  removedFromComparison: 'محصول از مقایسه حذف شد',
  compareNow: 'مقایسه کنید',
  clearComparison: 'پاک کردن همه',
  comparisonCleared: 'مقایسه پاک شد',
  maxItemsReached: 'حداکثر ۴ محصول قابل مقایسه است',
  shareComparison: 'اشتراک‌گذاری مقایسه',
  categories: {
    basic: 'اطلاعات پایه',
    pricing: 'قیمت‌گذاری',
    specifications: 'مشخصات',
    ingredients: 'ترکیبات',
    benefits: 'فواید',
    ratings: 'امتیازات'
  },
  criteria: {
    name: 'نام محصول',
    brand: 'برند',
    price: 'قیمت',
    discountedPrice: 'قیمت با تخفیف',
    category: 'دسته‌بندی',
    rating: 'امتیاز',
    reviewCount: 'تعداد نظرات',
    stock: 'موجودی',
    volume: 'حجم',
    skinType: 'نوع پوست',
    ingredients: 'ترکیبات اصلی',
    benefits: 'فواید',
    isNew: 'محصول جدید',
    isBestSeller: 'پرفروش'
  }
};

export const PERSIAN_RECENTLY_VIEWED_MESSAGES = {
  title: 'بازدیدهای اخیر',
  empty: 'هیچ محصولی اخیراً مشاهده نکرده‌اید',
  clearHistory: 'پاک کردن تاریخچه',
  historyCleared: 'تاریخچه پاک شد',
  viewedTimes: 'بار مشاهده',
  timeSpent: 'زمان صرف شده',
  source: {
    search: 'جستجو',
    category: 'دسته‌بندی',
    recommendation: 'پیشنهاد',
    direct_link: 'لینک مستقیم',
    related_products: 'محصولات مرتبط'
  }
};
