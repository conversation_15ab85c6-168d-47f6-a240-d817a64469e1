export interface ShippingAddress {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  province: string;
  city: string;
  address: string;
  postalCode: string;
  isDefault?: boolean;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'wallet' | 'cash_on_delivery';
  name: string;
  description: string;
  icon: string;
  isAvailable: boolean;
  processingFee?: number;
}

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
  isExpress?: boolean;
  isFree?: boolean;
}

export interface OrderSummary {
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount: number;
  total: number;
  savings?: number;
}

export interface CheckoutStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
  isActive: boolean;
  isDisabled: boolean;
}

export interface CheckoutFormData {
  shippingAddress: ShippingAddress;
  paymentMethod: PaymentMethod;
  shippingMethod: ShippingMethod;
  specialInstructions?: string;
  agreeToTerms: boolean;
  subscribeToNewsletter: boolean;
}

export interface Order {
  id: string;
  orderNumber: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  items: OrderItem[];
  shippingAddress: ShippingAddress;
  paymentMethod: PaymentMethod;
  shippingMethod: ShippingMethod;
  orderSummary: OrderSummary;
  createdAt: string;
  estimatedDelivery?: string;
  trackingNumber?: string;
  specialInstructions?: string;
  // Admin-specific fields
  customerId?: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  tags?: string[];
  internalNotes?: string;
  lastStatusUpdate?: string;
  updatedBy?: string;
  refundAmount?: number;
  refundReason?: string;
  refundStatus?: 'none' | 'requested' | 'approved' | 'processed' | 'rejected';
}

export interface OrderItem {
  productId: number;
  productName: string;
  productImage: string;
  quantity: number;
  price: number;
  discountedPrice?: number;
  selectedVariants?: any;
  variantKey?: string;
}

export interface CheckoutContextType {
  currentStep: number;
  steps: CheckoutStep[];
  formData: Partial<CheckoutFormData>;
  orderSummary: OrderSummary;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCurrentStep: (step: number) => void;
  updateFormData: (data: Partial<CheckoutFormData>) => void;
  validateStep: (step: number) => boolean;
  submitOrder: () => Promise<Order>;
  calculateOrderSummary: () => void;
  applyDiscount: (code: string) => Promise<boolean>;
  removeDiscount: () => void;
}

// Persian constants and templates
export const PERSIAN_PROVINCES = [
  'تهران', 'اصفهان', 'فارس', 'خراسان رضوی', 'آذربایجان شرقی', 'خوزستان',
  'مازندران', 'کرمان', 'آذربایجان غربی', 'گیلان', 'مرکزی', 'قم',
  'قزوین', 'گلستان', 'کردستان', 'همدان', 'یزد', 'لرستان',
  'کرمانشاه', 'بوشهر', 'زنجان', 'سمنان', 'ایلام', 'کهگیلویه و بویراحمد',
  'چهارمحال و بختیاری', 'آذربایجان غربی', 'هرمزگان', 'سیستان و بلوچستان',
  'اردبیل', 'خراسان شمالی', 'خراسان جنوبی', 'البرز'
];

export const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'card',
    type: 'card',
    name: 'پرداخت آنلاین',
    description: 'پرداخت با کارت بانکی از طریق درگاه امن',
    icon: '💳',
    isAvailable: true,
    processingFee: 0
  },
  {
    id: 'wallet',
    type: 'wallet',
    name: 'کیف پول',
    description: 'پرداخت از موجودی کیف پول',
    icon: '👛',
    isAvailable: false,
    processingFee: 0
  },
  {
    id: 'cod',
    type: 'cash_on_delivery',
    name: 'پرداخت در محل',
    description: 'پرداخت هنگام تحویل کالا',
    icon: '🚚',
    isAvailable: true,
    processingFee: 15000
  }
];

export const SHIPPING_METHODS: ShippingMethod[] = [
  {
    id: 'standard',
    name: 'ارسال عادی',
    description: 'ارسال با پست پیشتاز',
    price: 25000,
    estimatedDays: '۳-۵ روز کاری',
    isExpress: false,
    isFree: false
  },
  {
    id: 'express',
    name: 'ارسال سریع',
    description: 'ارسال با پیک موتوری',
    price: 45000,
    estimatedDays: '۱-۲ روز کاری',
    isExpress: true,
    isFree: false
  },
  {
    id: 'free',
    name: 'ارسال رایگان',
    description: 'برای خریدهای بالای ۵۰۰ هزار تومان',
    price: 0,
    estimatedDays: '۴-۷ روز کاری',
    isExpress: false,
    isFree: true
  }
];

export const CHECKOUT_STEPS: CheckoutStep[] = [
  {
    id: 'cart',
    title: 'سبد خرید',
    description: 'بررسی محصولات انتخابی',
    isCompleted: false,
    isActive: true,
    isDisabled: false
  },
  {
    id: 'shipping',
    title: 'آدرس تحویل',
    description: 'انتخاب آدرس و روش ارسال',
    isCompleted: false,
    isActive: false,
    isDisabled: false
  },
  {
    id: 'payment',
    title: 'پرداخت',
    description: 'انتخاب روش پرداخت',
    isCompleted: false,
    isActive: false,
    isDisabled: false
  },
  {
    id: 'review',
    title: 'بررسی نهایی',
    description: 'تأیید و ثبت سفارش',
    isCompleted: false,
    isActive: false,
    isDisabled: false
  }
];

export const CHECKOUT_VALIDATION_RULES = {
  shippingAddress: {
    firstName: { required: true, minLength: 2, maxLength: 50 },
    lastName: { required: true, minLength: 2, maxLength: 50 },
    phone: { required: true, pattern: /^09\d{9}$/ },
    email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    province: { required: true },
    city: { required: true, minLength: 2, maxLength: 50 },
    address: { required: true, minLength: 10, maxLength: 200 },
    postalCode: { required: true, pattern: /^\d{10}$/ }
  }
};

export const PERSIAN_CHECKOUT_MESSAGES = {
  success: {
    orderPlaced: 'سفارش شما با موفقیت ثبت شد',
    addressSaved: 'آدرس با موفقیت ذخیره شد',
    discountApplied: 'کد تخفیف اعمال شد'
  },
  errors: {
    required: 'این فیلد الزامی است',
    invalidPhone: 'شماره موبایل معتبر وارد کنید',
    invalidEmail: 'ایمیل معتبر وارد کنید',
    invalidPostalCode: 'کد پستی باید ۱۰ رقم باشد',
    paymentFailed: 'پرداخت ناموفق بود',
    invalidDiscount: 'کد تخفیف معتبر نیست',
    networkError: 'خطا در ارتباط با سرور'
  },
  placeholders: {
    firstName: 'نام',
    lastName: 'نام خانوادگی',
    phone: '۰۹۱۲۳۴۵۶۷۸۹',
    email: '<EMAIL>',
    city: 'نام شهر',
    address: 'آدرس کامل',
    postalCode: '۱۲۳۴۵۶۷۸۹۰',
    specialInstructions: 'توضیحات ویژه برای ارسال...'
  }
};
