export interface Review {
  id: string;
  productId: number;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number; // 1-5 stars
  title: string;
  comment: string;
  pros: string[];
  cons: string[];
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  totalVotes: number;
  images?: string[];
  createdAt: string;
  updatedAt?: string;
  isRecommended: boolean;
  skinType?: string;
  ageRange?: string;
  usageDuration?: string;
  moderationStatus: 'pending' | 'approved' | 'rejected';
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
  recommendationPercentage: number;
  verifiedPurchasePercentage: number;
}

export interface ReviewFilters {
  rating?: number;
  verified?: boolean;
  recommended?: boolean;
  skinType?: string;
  sortBy: 'newest' | 'oldest' | 'highest' | 'lowest' | 'helpful';
}

export interface ReviewFormData {
  rating: number;
  title: string;
  comment: string;
  pros: string[];
  cons: string[];
  isRecommended: boolean;
  skinType?: string;
  ageRange?: string;
  usageDuration?: string;
  images?: File[];
}

export interface ReviewVote {
  reviewId: string;
  userId: string;
  isHelpful: boolean;
  createdAt: string;
}

export interface ReviewContextType {
  reviews: Review[];
  reviewStats: ReviewStats;
  filters: ReviewFilters;
  isLoading: boolean;
  error: string | null;
  addReview: (productId: number, reviewData: ReviewFormData) => Promise<void>;
  updateReview: (reviewId: string, reviewData: Partial<ReviewFormData>) => Promise<void>;
  deleteReview: (reviewId: string) => Promise<void>;
  voteReview: (reviewId: string, isHelpful: boolean) => Promise<void>;
  setFilters: (filters: Partial<ReviewFilters>) => void;
  loadReviews: (productId: number) => Promise<void>;
}

// Persian review templates and constants
export const PERSIAN_REVIEW_TEMPLATES = {
  skinTypes: [
    { value: 'dry', label: 'خشک' },
    { value: 'oily', label: 'چرب' },
    { value: 'combination', label: 'مختلط' },
    { value: 'sensitive', label: 'حساس' },
    { value: 'normal', label: 'معمولی' }
  ],
  ageRanges: [
    { value: '18-25', label: '۱۸-۲۵ سال' },
    { value: '26-35', label: '۲۶-۳۵ سال' },
    { value: '36-45', label: '۳۶-۴۵ سال' },
    { value: '46-55', label: '۴۶-۵۵ سال' },
    { value: '55+', label: 'بالای ۵۵ سال' }
  ],
  usageDurations: [
    { value: 'less-than-week', label: 'کمتر از یک هفته' },
    { value: 'week-to-month', label: 'یک هفته تا یک ماه' },
    { value: 'month-to-three', label: 'یک تا سه ماه' },
    { value: 'three-to-six', label: 'سه تا شش ماه' },
    { value: 'more-than-six', label: 'بیش از شش ماه' }
  ],
  sortOptions: [
    { value: 'newest', label: 'جدیدترین' },
    { value: 'oldest', label: 'قدیمی‌ترین' },
    { value: 'highest', label: 'بالاترین امتیاز' },
    { value: 'lowest', label: 'پایین‌ترین امتیاز' },
    { value: 'helpful', label: 'مفیدترین' }
  ]
};

export const REVIEW_VALIDATION_RULES = {
  title: {
    minLength: 5,
    maxLength: 100,
    required: true
  },
  comment: {
    minLength: 20,
    maxLength: 1000,
    required: true
  },
  pros: {
    maxItems: 5,
    maxLength: 50
  },
  cons: {
    maxItems: 5,
    maxLength: 50
  },
  images: {
    maxFiles: 5,
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
  }
};

export const PERSIAN_REVIEW_MESSAGES = {
  success: {
    added: 'نظر شما با موفقیت ثبت شد و پس از تأیید نمایش داده خواهد شد',
    updated: 'نظر شما با موفقیت به‌روزرسانی شد',
    deleted: 'نظر شما حذف شد',
    voted: 'رأی شما ثبت شد'
  },
  errors: {
    required: 'این فیلد الزامی است',
    minLength: 'حداقل {min} کاراکتر وارد کنید',
    maxLength: 'حداکثر {max} کاراکتر مجاز است',
    invalidRating: 'لطفاً امتیاز خود را انتخاب کنید',
    networkError: 'خطا در ارتباط با سرور. لطفاً دوباره تلاش کنید',
    unauthorized: 'برای ثبت نظر باید وارد حساب کاربری خود شوید',
    alreadyReviewed: 'شما قبلاً برای این محصول نظر ثبت کرده‌اید',
    notPurchased: 'فقط خریداران این محصول می‌توانند نظر ثبت کنند'
  },
  placeholders: {
    title: 'عنوان نظر خود را بنویسید...',
    comment: 'تجربه خود از استفاده از این محصول را بنویسید...',
    pros: 'نکات مثبت محصول',
    cons: 'نکات منفی محصول'
  }
};
