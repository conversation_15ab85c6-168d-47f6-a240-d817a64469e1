import { User, UserAddress } from './auth';
import { LoyaltyMember, LoyaltyTier } from './loyalty';
import { AdminOrder } from './adminOrder';

// Extended customer interface for admin management
export interface AdminCustomer extends User {
  // Customer analytics
  analytics: {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: string;
    firstOrderDate?: string;
    lifetimeValue: number;
    orderFrequency: number; // orders per month
    returnRate: number; // percentage
    cancelationRate: number; // percentage
    favoriteCategories: string[];
    preferredPaymentMethod?: string;
    averageDeliveryTime: number; // days
    customerSatisfactionScore?: number; // 1-5
  };

  // Customer status and segmentation
  status: CustomerStatus;
  segment: CustomerSegment;
  tags: string[];
  notes: CustomerNote[];
  
  // VIP and loyalty information
  isVip: boolean;
  vipSince?: string;
  loyaltyMember?: LoyaltyMember;
  
  // Communication preferences and history
  communicationHistory: CommunicationRecord[];
  marketingConsent: {
    email: boolean;
    sms: boolean;
    push: boolean;
    phone: boolean;
    updatedAt: string;
  };
  
  // Support and tickets
  supportTickets: SupportTicket[];
  
  // Admin tracking
  createdBy?: string;
  lastModifiedBy?: string;
  lastModifiedAt?: string;
  
  // Risk and fraud
  riskScore: number; // 0-100
  fraudFlags: FraudFlag[];
  
  // Geographic and demographic
  location?: {
    country: string;
    province: string;
    city: string;
    timezone: string;
  };
  
  // Behavioral data
  behavior: {
    loginFrequency: number; // logins per month
    sessionDuration: number; // average minutes
    pageViews: number;
    cartAbandonmentRate: number; // percentage
    wishlistItems: number;
    reviewsCount: number;
    referralsCount: number;
  };
}

export type CustomerStatus = 
  | 'active'
  | 'inactive' 
  | 'suspended'
  | 'blocked'
  | 'pending_verification'
  | 'churned';

export type CustomerSegment = 
  | 'new'
  | 'regular'
  | 'vip'
  | 'champion'
  | 'at_risk'
  | 'lost'
  | 'potential_loyalist'
  | 'need_attention';

export interface CustomerNote {
  id: string;
  customerId: string;
  content: string;
  type: 'general' | 'support' | 'sales' | 'billing' | 'complaint';
  isPrivate: boolean;
  createdBy: string;
  createdByName: string;
  createdAt: string;
  updatedAt?: string;
}

export interface CommunicationRecord {
  id: string;
  customerId: string;
  type: 'email' | 'sms' | 'phone' | 'chat' | 'in_app';
  direction: 'inbound' | 'outbound';
  subject?: string;
  content: string;
  status: 'sent' | 'delivered' | 'read' | 'replied' | 'failed';
  sentBy?: string;
  sentByName?: string;
  sentAt: string;
  readAt?: string;
  repliedAt?: string;
  metadata?: Record<string, any>;
}

export interface SupportTicket {
  id: string;
  customerId: string;
  subject: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  category: TicketCategory;
  assignedTo?: string;
  assignedToName?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  firstResponseAt?: string;
  messages: TicketMessage[];
  tags: string[];
  satisfaction?: {
    rating: number; // 1-5
    feedback?: string;
    ratedAt: string;
  };
}

export type TicketStatus = 
  | 'open'
  | 'in_progress'
  | 'waiting_customer'
  | 'waiting_internal'
  | 'resolved'
  | 'closed';

export type TicketPriority = 'low' | 'normal' | 'high' | 'urgent';

export type TicketCategory = 
  | 'general'
  | 'order'
  | 'payment'
  | 'shipping'
  | 'product'
  | 'account'
  | 'technical'
  | 'complaint'
  | 'refund'
  | 'exchange';

export interface TicketMessage {
  id: string;
  ticketId: string;
  content: string;
  isInternal: boolean;
  sentBy: string;
  sentByName: string;
  sentByType: 'customer' | 'admin';
  sentAt: string;
  attachments?: TicketAttachment[];
}

export interface TicketAttachment {
  id: string;
  filename: string;
  url: string;
  size: number;
  mimeType: string;
  uploadedAt: string;
}

export interface FraudFlag {
  id: string;
  type: 'suspicious_activity' | 'multiple_accounts' | 'payment_fraud' | 'address_mismatch' | 'velocity_check';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  createdAt: string;
  resolvedAt?: string;
  resolvedBy?: string;
  notes?: string;
}

// Customer filters and search
export interface CustomerFilters {
  search?: string;
  status?: CustomerStatus[];
  segment?: CustomerSegment[];
  isVip?: boolean;
  loyaltyTier?: string[];
  registrationDateFrom?: string;
  registrationDateTo?: string;
  lastOrderDateFrom?: string;
  lastOrderDateTo?: string;
  totalSpentMin?: number;
  totalSpentMax?: number;
  orderCountMin?: number;
  orderCountMax?: number;
  location?: {
    country?: string;
    province?: string;
    city?: string;
  };
  tags?: string[];
  riskScoreMin?: number;
  riskScoreMax?: number;
  hasActiveTickets?: boolean;
  marketingConsent?: {
    email?: boolean;
    sms?: boolean;
  };
}

export interface CustomerSortOptions {
  field: 'name' | 'email' | 'createdAt' | 'lastOrderDate' | 'totalSpent' | 'totalOrders' | 'lifetimeValue' | 'riskScore';
  direction: 'asc' | 'desc';
}

// Customer analytics and reporting
export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomersThisMonth: number;
  activeCustomers: number;
  churnedCustomers: number;
  vipCustomers: number;
  
  segmentDistribution: Record<CustomerSegment, number>;
  statusDistribution: Record<CustomerStatus, number>;
  loyaltyTierDistribution: Record<string, number>;
  
  averageLifetimeValue: number;
  averageOrderValue: number;
  averageOrderFrequency: number;
  customerRetentionRate: number;
  churnRate: number;
  
  topSpenders: AdminCustomer[];
  mostActiveCustomers: AdminCustomer[];
  atRiskCustomers: AdminCustomer[];
  
  geographicDistribution: Record<string, number>;
  acquisitionChannels: Record<string, number>;
  
  supportMetrics: {
    totalTickets: number;
    openTickets: number;
    averageResolutionTime: number;
    customerSatisfactionScore: number;
  };
}

// Customer export options
export interface CustomerExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  fields: string[];
  filters?: CustomerFilters;
  includeOrders?: boolean;
  includeLoyalty?: boolean;
  includeTickets?: boolean;
  dateRange?: {
    from: string;
    to: string;
  };
}

// Customer import options
export interface CustomerImportOptions {
  file: File;
  mapping: Record<string, string>;
  skipDuplicates: boolean;
  updateExisting: boolean;
  sendWelcomeEmail: boolean;
}

// Persian messages for customer management
export const PERSIAN_CUSTOMER_MESSAGES = {
  status: {
    active: 'فعال',
    inactive: 'غیرفعال',
    suspended: 'معلق',
    blocked: 'مسدود',
    pending_verification: 'در انتظار تأیید',
    churned: 'از دست رفته'
  },
  segment: {
    new: 'جدید',
    regular: 'عادی',
    vip: 'ویژه',
    champion: 'قهرمان',
    at_risk: 'در معرض خطر',
    lost: 'از دست رفته',
    potential_loyalist: 'وفادار بالقوه',
    need_attention: 'نیاز به توجه'
  },
  ticketStatus: {
    open: 'باز',
    in_progress: 'در حال بررسی',
    waiting_customer: 'در انتظار مشتری',
    waiting_internal: 'در انتظار داخلی',
    resolved: 'حل شده',
    closed: 'بسته'
  },
  ticketPriority: {
    low: 'کم',
    normal: 'عادی',
    high: 'بالا',
    urgent: 'فوری'
  },
  ticketCategory: {
    general: 'عمومی',
    order: 'سفارش',
    payment: 'پرداخت',
    shipping: 'ارسال',
    product: 'محصول',
    account: 'حساب کاربری',
    technical: 'فنی',
    complaint: 'شکایت',
    refund: 'بازگشت وجه',
    exchange: 'تعویض'
  },
  actions: {
    viewProfile: 'مشاهده پروفایل',
    editCustomer: 'ویرایش مشتری',
    viewOrders: 'مشاهده سفارشات',
    viewLoyalty: 'مشاهده باشگاه مشتریان',
    createTicket: 'ایجاد تیکت',
    sendMessage: 'ارسال پیام',
    addNote: 'افزودن یادداشت',
    blockCustomer: 'مسدود کردن',
    unblockCustomer: 'رفع مسدودی',
    makeVip: 'تبدیل به ویژه',
    removeVip: 'حذف از ویژه',
    exportData: 'خروجی اطلاعات',
    mergeCustomers: 'ادغام مشتریان'
  },
  success: {
    customerUpdated: 'اطلاعات مشتری بروزرسانی شد',
    customerBlocked: 'مشتری مسدود شد',
    customerUnblocked: 'مسدودی مشتری رفع شد',
    vipStatusUpdated: 'وضعیت ویژه بروزرسانی شد',
    noteAdded: 'یادداشت اضافه شد',
    messageSent: 'پیام ارسال شد',
    ticketCreated: 'تیکت ایجاد شد',
    dataExported: 'اطلاعات صادر شد'
  },
  errors: {
    customerNotFound: 'مشتری یافت نشد',
    updateFailed: 'خطا در بروزرسانی',
    insufficientPermissions: 'دسترسی کافی ندارید',
    invalidData: 'اطلاعات نامعتبر',
    exportFailed: 'خطا در صدور اطلاعات',
    messageFailed: 'خطا در ارسال پیام'
  }
} as const;
