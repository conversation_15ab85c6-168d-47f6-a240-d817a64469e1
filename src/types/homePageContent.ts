// Home Page Content Management Types

export interface HomePageSection {
  id: string;
  type: SectionType;
  title: string;
  isActive: boolean;
  order: number;
  lastModified: string;
  modifiedBy: string;
}

export type SectionType = 
  | 'hero'
  | 'featured-products'
  | 'categories'
  | 'testimonials'
  | 'newsletter'
  | 'about-brand'
  | 'special-offers'
  | 'new-products'
  | 'discounted-products';

// Hero Section
export interface HeroSectionData extends HomePageSection {
  type: 'hero';
  content: {
    backgroundImage: string;
    mobileBackgroundImage?: string;
    title: string;
    subtitle: string;
    description: string;
    primaryButton: {
      text: string;
      url: string;
      type: 'internal' | 'external';
    };
    secondaryButton?: {
      text: string;
      url: string;
      type: 'internal' | 'external';
    };
    overlayOpacity: number;
    textAlignment: 'left' | 'center' | 'right';
    backgroundColor?: string;
    textColor?: string;
    animation: {
      enabled: boolean;
      type: 'fade' | 'slide' | 'zoom';
      duration: number;
    };
  };
}

// Featured Products Section
export interface FeaturedProductsSectionData extends HomePageSection {
  type: 'featured-products';
  content: {
    title: string;
    subtitle?: string;
    description?: string;
    displayType: 'grid' | 'carousel' | 'list';
    productsPerRow: 2 | 3 | 4 | 5;
    maxProducts: number;
    showViewAllButton: boolean;
    viewAllButtonText: string;
    viewAllButtonUrl: string;
    filterBy: 'featured' | 'bestseller' | 'new' | 'discounted' | 'category' | 'manual';
    categoryFilter?: number[];
    manualProductIds?: number[];
    sortBy: 'name' | 'price' | 'rating' | 'date' | 'popularity';
    sortOrder: 'asc' | 'desc';
    showPrices: boolean;
    showRatings: boolean;
    showAddToCart: boolean;
    backgroundColor?: string;
  };
}

// Categories Section
export interface CategoriesSectionData extends HomePageSection {
  type: 'categories';
  content: {
    title: string;
    subtitle?: string;
    description?: string;
    displayType: 'grid' | 'carousel' | 'masonry';
    categoriesPerRow: 2 | 3 | 4 | 6;
    maxCategories: number;
    showViewAllButton: boolean;
    viewAllButtonText: string;
    viewAllButtonUrl: string;
    selectedCategories: Array<{
      id: number;
      customImage?: string;
      customTitle?: string;
      customDescription?: string;
      promotionalText?: string;
      isPromoted: boolean;
    }>;
    showProductCount: boolean;
    imageStyle: 'square' | 'circle' | 'rounded';
    backgroundColor?: string;
  };
}

// Testimonials Section
export interface TestimonialsSectionData extends HomePageSection {
  type: 'testimonials';
  content: {
    title: string;
    subtitle?: string;
    description?: string;
    displayType: 'carousel' | 'grid' | 'masonry';
    testimonialsPerView: 1 | 2 | 3;
    autoplay: boolean;
    autoplaySpeed: number;
    showNavigation: boolean;
    showPagination: boolean;
    testimonials: Array<{
      id: string;
      name: string;
      avatar?: string;
      rating: number;
      title?: string;
      content: string;
      productId?: number;
      location?: string;
      date: string;
      isVerified: boolean;
      isActive: boolean;
    }>;
    backgroundColor?: string;
    cardStyle: 'default' | 'minimal' | 'bordered' | 'shadow';
  };
}

// Newsletter Section
export interface NewsletterSectionData extends HomePageSection {
  type: 'newsletter';
  content: {
    title: string;
    subtitle?: string;
    description: string;
    backgroundImage?: string;
    backgroundColor?: string;
    textColor?: string;
    inputPlaceholder: string;
    buttonText: string;
    successMessage: string;
    privacyText: string;
    incentiveText?: string;
    discountOffer?: {
      enabled: boolean;
      percentage: number;
      text: string;
    };
    style: 'default' | 'minimal' | 'card' | 'fullwidth';
    position: 'inline' | 'floating' | 'modal';
  };
}

// About/Brand Story Section
export interface AboutBrandSectionData extends HomePageSection {
  type: 'about-brand';
  content: {
    title: string;
    subtitle?: string;
    description: string;
    content: string;
    image: string;
    imagePosition: 'left' | 'right' | 'top' | 'bottom';
    imageStyle: 'default' | 'rounded' | 'circle';
    ctaButton?: {
      enabled: boolean;
      text: string;
      url: string;
      type: 'internal' | 'external';
    };
    features?: Array<{
      icon: string;
      title: string;
      description: string;
    }>;
    backgroundColor?: string;
    textAlignment: 'left' | 'center' | 'right';
  };
}

// Special Offers Section
export interface SpecialOffersSectionData extends HomePageSection {
  type: 'special-offers';
  content: {
    title: string;
    subtitle?: string;
    offers: Array<{
      id: string;
      title: string;
      description: string;
      image: string;
      discountPercentage?: number;
      discountText: string;
      ctaText: string;
      ctaUrl: string;
      ctaType: 'internal' | 'external';
      validUntil?: string;
      isActive: boolean;
      backgroundColor?: string;
      textColor?: string;
    }>;
    displayType: 'carousel' | 'grid' | 'banner';
    autoplay: boolean;
    autoplaySpeed: number;
    showTimer: boolean;
    backgroundColor?: string;
  };
}

// Form Data Types
export interface HomePageContentFormData {
  sections: Array<
    | HeroSectionData
    | FeaturedProductsSectionData
    | CategoriesSectionData
    | TestimonialsSectionData
    | NewsletterSectionData
    | AboutBrandSectionData
    | SpecialOffersSectionData
  >;
  seoSettings: {
    title: string;
    description: string;
    keywords: string[];
    ogImage?: string;
  };
  globalSettings: {
    enableAnimations: boolean;
    lazyLoading: boolean;
    mobileOptimized: boolean;
  };
  status: 'draft' | 'published';
  lastModified: string;
  modifiedBy: string;
}

// Section Templates
export interface SectionTemplate {
  id: string;
  name: string;
  type: SectionType;
  description: string;
  thumbnail: string;
  defaultData: Partial<HomePageSection>;
  isPopular: boolean;
  category: 'basic' | 'advanced' | 'promotional';
}

// Validation Types
export interface ValidationError {
  sectionId: string;
  field: string;
  message: string;
  type: 'error' | 'warning';
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}
