import { Review, ReviewStats } from './review';

// Enhanced review interface for admin management (Task 1.7 Integration)
export interface AdminReview {
  id: string;
  productId: string;
  userId: string;
  rating: number;
  title?: string;
  content: string;
  pros: string[];
  cons: string[];
  isVerified: boolean;
  isApproved: boolean;
  isRecommended: boolean;
  helpfulCount: number;
  unhelpfulCount: number;
  skinType?: string;
  ageRange?: string;
  usageDuration?: string;
  moderationStatus: ReviewModerationStatus;
  moderationNotes?: string;
  createdAt: string;
  updatedAt: string;

  // Relations from Task 1.7 API
  user: {
    id: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
  };

  product: {
    id: string;
    name: string;
    slug: string;
  };

  images: ReviewImage[];
  votes: ReviewVote[];
  responses: ReviewResponse[];
  _count: {
    votes: number;
  };

  // Legacy admin fields for backward compatibility
  customer?: {
    id: string;
    name: string;
    email: string;
    totalReviews: number;
    averageRating: number;
    isVerifiedCustomer: boolean;
    registrationDate: string;
    lastOrderDate?: string;
    loyaltyTier?: string;
  };

  // Review analytics
  analytics: {
    viewCount: number;
    helpfulnessRatio: number; // helpfulVotes / totalVotes
    reportCount: number;
    responseTime?: number; // minutes from submission to moderation
    engagementScore: number; // based on votes, views, responses
  };

  // Admin tracking
  adminNotes: AdminNote[];
  lastModifiedBy?: string;
  lastModifiedAt?: string;
  
  // Reporting and flags
  reports: ReviewReport[];
  isHighlighted: boolean; // Featured review
  isPinned: boolean; // Pinned to top
  isHidden: boolean; // Hidden from public view
  
  // Response from business
  businessResponse?: {
    id: string;
    content: string;
    respondedBy: string;
    respondedByName: string;
    respondedAt: string;
    isPublic: boolean;
  };

  // Legacy moderation structure for backward compatibility
  moderation?: {
    status: string;
    moderatedBy?: string;
    moderatedByName?: string;
    moderatedAt?: string;
    moderationNotes?: string;
    autoModerated: boolean;
    contentFlags: ContentFlag[];
    qualityScore: number;
    spamScore: number;
    sentimentScore: number;
  };
}

// New interfaces from Task 1.7 API
export interface ReviewImage {
  id: string;
  reviewId: string;
  url: string;
  alt?: string;
  sortOrder: number;
  createdAt: string;
}

export interface ReviewVote {
  id: string;
  reviewId: string;
  userId: string;
  isHelpful: boolean;
  createdAt: string;
  user?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface ReviewResponse {
  id: string;
  reviewId: string;
  userId: string;
  content: string;
  isOfficial: boolean;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
  };
}

// Task 1.7 API Review Statistics
export interface ReviewAnalyticsAPI {
  totalReviews: number;
  pendingReviews: number;
  approvedReviews: number;
  rejectedReviews: number;
  averageRating: number;
  reviewsThisMonth: number;
  reviewsLastMonth: number;
  topRatedProducts: Array<{
    productId: string;
    productName: string;
    averageRating: number;
    reviewCount: number;
  }>;
  recentReviews: AdminReview[];
}

// Task 1.7 API Product Review Statistics
export interface ProductReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  verifiedPurchasePercentage: number;
  recommendationPercentage: number;
}

export type ReviewModerationStatus =
  | 'PENDING'
  | 'APPROVED'
  | 'REJECTED'
  | 'FLAGGED';

export interface ContentFlag {
  id: string;
  type: ContentFlagType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  confidence: number; // 0-1
  detectedAt: string;
  source: 'auto' | 'manual' | 'user_report';
}

export type ContentFlagType = 
  | 'spam'
  | 'inappropriate_language'
  | 'fake_review'
  | 'off_topic'
  | 'personal_information'
  | 'promotional_content'
  | 'duplicate_content'
  | 'harassment'
  | 'misleading_information'
  | 'copyright_violation';

export interface AdminNote {
  id: string;
  reviewId: string;
  content: string;
  createdBy: string;
  createdByName: string;
  createdAt: string;
  isPrivate: boolean;
  type: 'general' | 'moderation' | 'escalation' | 'follow_up';
}

export interface ReviewReport {
  id: string;
  reviewId: string;
  reportedBy: string;
  reportedByName?: string;
  reason: ReviewReportReason;
  description?: string;
  reportedAt: string;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  reviewedBy?: string;
  reviewedAt?: string;
  resolution?: string;
}

export type ReviewReportReason = 
  | 'spam'
  | 'fake'
  | 'inappropriate'
  | 'off_topic'
  | 'personal_info'
  | 'harassment'
  | 'misleading'
  | 'other';

// Review filters for admin (Task 1.7 API Integration)
export interface AdminReviewFilters {
  search?: string;
  productId?: string;
  userId?: string;
  rating?: number;
  isVerified?: boolean;
  isApproved?: boolean;
  moderationStatus?: ReviewModerationStatus;
  isRecommended?: boolean;
  skinType?: string;
  ageRange?: string;
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful';
  page?: number;
  limit?: number;

  // Legacy filters for backward compatibility
  status?: ReviewModerationStatus[];
  dateFrom?: string;
  dateTo?: string;
  customerId?: string;
  brand?: string;
  category?: string;
  isVerifiedPurchase?: boolean;
  hasImages?: boolean;
  hasReports?: boolean;
  qualityScoreMin?: number;
  qualityScoreMax?: number;
  spamScoreMin?: number;
  spamScoreMax?: number;
  contentFlags?: ContentFlagType[];
  moderatedBy?: string;
  isHighlighted?: boolean;
  isPinned?: boolean;
  hasBusinessResponse?: boolean;
}

export interface ReviewSortOptions {
  field: 'createdAt' | 'rating' | 'helpfulVotes' | 'qualityScore' | 'spamScore' | 'reportCount' | 'moderatedAt';
  direction: 'asc' | 'desc';
}

// Review analytics and reporting
export interface ReviewAnalytics {
  totalReviews: number;
  pendingReviews: number;
  approvedReviews: number;
  rejectedReviews: number;
  flaggedReviews: number;
  
  moderationStats: {
    averageResponseTime: number; // minutes
    autoApprovalRate: number; // percentage
    manualReviewRate: number; // percentage
    rejectionRate: number; // percentage
  };
  
  qualityMetrics: {
    averageQualityScore: number;
    averageSpamScore: number;
    averageHelpfulnessRatio: number;
    verifiedPurchasePercentage: number;
  };
  
  contentAnalysis: {
    flagDistribution: Record<ContentFlagType, number>;
    sentimentDistribution: {
      positive: number;
      neutral: number;
      negative: number;
    };
    languageQuality: {
      averageLength: number;
      readabilityScore: number;
    };
  };
  
  customerInsights: {
    topReviewers: Array<{
      customerId: string;
      customerName: string;
      reviewCount: number;
      averageRating: number;
      averageQuality: number;
    }>;
    newReviewers: number;
    repeatReviewers: number;
  };
  
  productInsights: {
    mostReviewedProducts: Array<{
      productId: number;
      productName: string;
      reviewCount: number;
      averageRating: number;
      averageQuality: number;
    }>;
    categoryDistribution: Record<string, number>;
    brandDistribution: Record<string, number>;
  };
  
  timeAnalysis: {
    reviewsThisMonth: number;
    reviewsLastMonth: number;
    growthRate: number; // percentage
    peakHours: number[];
    peakDays: string[];
  };
}

// Bulk operations
export interface BulkReviewAction {
  type: 'approve' | 'reject' | 'flag' | 'highlight' | 'pin' | 'hide' | 'delete' | 'assign_moderator';
  reviewIds: string[];
  reason?: string;
  notes?: string;
  assignedTo?: string;
}

export interface ReviewModerationDecision {
  reviewId: string;
  action: 'approve' | 'reject' | 'flag' | 'require_review';
  reason?: string;
  notes?: string;
  contentFlags?: ContentFlag[];
}

// Review export options
export interface ReviewExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  fields: string[];
  filters?: AdminReviewFilters;
  includeCustomerInfo?: boolean;
  includeProductInfo?: boolean;
  includeModerationData?: boolean;
  includeAnalytics?: boolean;
  dateRange?: {
    from: string;
    to: string;
  };
}

// Review templates and automation
export interface ReviewModerationRule {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  priority: number;
  conditions: ReviewRuleCondition[];
  actions: ReviewRuleAction[];
  createdBy: string;
  createdAt: string;
  lastModified: string;
}

export interface ReviewRuleCondition {
  field: 'rating' | 'length' | 'spamScore' | 'qualityScore' | 'customerReviewCount' | 'hasImages' | 'isVerifiedPurchase';
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'not_contains';
  value: any;
}

export interface ReviewRuleAction {
  type: 'auto_approve' | 'auto_reject' | 'flag' | 'require_manual_review' | 'assign_moderator';
  parameters?: Record<string, any>;
}

// Persian messages for review management (Task 1.7 Integration)
export const PERSIAN_REVIEW_ADMIN_MESSAGES = {
  moderationStatus: {
    PENDING: 'در انتظار بررسی',
    APPROVED: 'تأیید شده',
    REJECTED: 'رد شده',
    FLAGGED: 'علامت‌گذاری شده',
    // Legacy support
    pending: 'در انتظار بررسی',
    approved: 'تأیید شده',
    rejected: 'رد شده',
    flagged: 'علامت‌گذاری شده',
    auto_approved: 'تأیید خودکار',
    auto_rejected: 'رد خودکار',
    requires_review: 'نیاز به بررسی'
  },
  contentFlags: {
    spam: 'اسپم',
    inappropriate_language: 'زبان نامناسب',
    fake_review: 'نظر جعلی',
    off_topic: 'خارج از موضوع',
    personal_information: 'اطلاعات شخصی',
    promotional_content: 'محتوای تبلیغاتی',
    duplicate_content: 'محتوای تکراری',
    harassment: 'آزار و اذیت',
    misleading_information: 'اطلاعات گمراه‌کننده',
    copyright_violation: 'نقض حق نشر'
  },
  reportReasons: {
    spam: 'اسپم',
    fake: 'جعلی',
    inappropriate: 'نامناسب',
    off_topic: 'خارج از موضوع',
    personal_info: 'اطلاعات شخصی',
    harassment: 'آزار و اذیت',
    misleading: 'گمراه‌کننده',
    other: 'سایر'
  },
  actions: {
    approve: 'تأیید',
    reject: 'رد',
    flag: 'علامت‌گذاری',
    moderate: 'تعدیل',
    addResponse: 'افزودن پاسخ',
    viewVotes: 'مشاهده آرا',
    viewImages: 'مشاهده تصاویر',
    viewAnalytics: 'مشاهده آمار',
    highlight: 'برجسته کردن',
    pin: 'سنجاق کردن',
    hide: 'مخفی کردن',
    delete: 'حذف',
    respond: 'پاسخ دادن',
    addNote: 'افزودن یادداشت',
    viewDetails: 'مشاهده جزئیات',
    viewCustomer: 'مشاهده مشتری',
    viewProduct: 'مشاهده محصول',
    exportData: 'خروجی اطلاعات',
    bulkApprove: 'تأیید گروهی',
    bulkReject: 'رد گروهی'
  },
  success: {
    reviewApproved: 'نظر تأیید شد',
    reviewRejected: 'نظر رد شد',
    reviewFlagged: 'نظر علامت‌گذاری شد',
    reviewModerated: 'نظر تعدیل شد',
    responseAdded: 'پاسخ اضافه شد',
    voteRecorded: 'رأی ثبت شد',
    analyticsLoaded: 'آمار بارگذاری شد',
    bulkActionCompleted: 'عملیات گروهی انجام شد',
    noteAdded: 'یادداشت اضافه شد',
    dataExported: 'اطلاعات صادر شد',
    settingsUpdated: 'تنظیمات بروزرسانی شد'
  },
  errors: {
    reviewNotFound: 'نظر یافت نشد',
    moderationFailed: 'خطا در تعدیل نظر',
    responseAddFailed: 'خطا در افزودن پاسخ',
    voteRecordFailed: 'خطا در ثبت رأی',
    analyticsLoadFailed: 'خطا در بارگذاری آمار',
    insufficientPermissions: 'دسترسی کافی ندارید',
    invalidData: 'اطلاعات نامعتبر',
    exportFailed: 'خطا در صدور اطلاعات',
    bulkActionFailed: 'خطا در عملیات گروهی'
  }
} as const;
