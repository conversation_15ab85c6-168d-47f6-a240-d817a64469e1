export type AdminRole = 'super_admin' | 'admin' | 'moderator' | 'viewer';

export interface AdminUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: AdminRole;
  permissions: AdminPermission[];
  avatar?: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  department?: string;
  phone?: string;
  twoFactorEnabled: boolean;
}

export interface AdminPermission {
  resource: AdminResource;
  actions: AdminAction[];
}

export type AdminResource = 
  | 'products'
  | 'orders'
  | 'customers'
  | 'reviews'
  | 'loyalty'
  | 'content'
  | 'analytics'
  | 'settings'
  | 'users'
  | 'audit'
  | 'notifications';

export type AdminAction = 
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'approve'
  | 'reject'
  | 'export'
  | 'import'
  | 'moderate'
  | 'configure';

export interface AdminAuthState {
  user: AdminUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
  sessionExpiry: number | null;
}

export interface AdminLoginCredentials {
  email: string;
  password: string;
  twoFactorCode?: string;
  rememberMe?: boolean;
}

export interface AdminAuthContextType {
  // State
  user: AdminUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  sessionExpiry: number | null;
  
  // Actions
  login: (credentials: AdminLoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  checkPermission: (resource: AdminResource, action: AdminAction) => boolean;
  hasRole: (role: AdminRole) => boolean;
  hasAnyRole: (roles: AdminRole[]) => boolean;
  clearError: () => void;
  
  // Session management
  extendSession: () => Promise<void>;
  getSessionTimeRemaining: () => number;
}

export interface AdminAuthResponse {
  user: AdminUser;
  token: string;
  refreshToken: string;
  expiresIn: number;
  permissions: AdminPermission[];
}

// Role-based permission definitions
export const ADMIN_ROLE_PERMISSIONS: Record<AdminRole, AdminPermission[]> = {
  super_admin: [
    {
      resource: 'products',
      actions: ['create', 'read', 'update', 'delete', 'export', 'import']
    },
    {
      resource: 'orders',
      actions: ['create', 'read', 'update', 'delete', 'export']
    },
    {
      resource: 'customers',
      actions: ['create', 'read', 'update', 'delete', 'export']
    },
    {
      resource: 'reviews',
      actions: ['read', 'update', 'delete', 'approve', 'reject', 'moderate', 'export']
    },
    {
      resource: 'loyalty',
      actions: ['create', 'read', 'update', 'delete', 'configure']
    },
    {
      resource: 'content',
      actions: ['create', 'read', 'update', 'delete']
    },
    {
      resource: 'analytics',
      actions: ['read', 'export']
    },
    {
      resource: 'settings',
      actions: ['read', 'update', 'configure']
    },
    {
      resource: 'users',
      actions: ['create', 'read', 'update', 'delete']
    },
    {
      resource: 'audit',
      actions: ['read', 'export']
    },
    {
      resource: 'notifications',
      actions: ['read', 'create', 'update', 'delete']
    }
  ],
  admin: [
    {
      resource: 'products',
      actions: ['create', 'read', 'update', 'delete', 'export', 'import']
    },
    {
      resource: 'orders',
      actions: ['read', 'update', 'export']
    },
    {
      resource: 'customers',
      actions: ['read', 'update', 'export']
    },
    {
      resource: 'reviews',
      actions: ['read', 'update', 'approve', 'reject', 'moderate', 'export']
    },
    {
      resource: 'loyalty',
      actions: ['read', 'update']
    },
    {
      resource: 'content',
      actions: ['create', 'read', 'update', 'delete']
    },
    {
      resource: 'analytics',
      actions: ['read', 'export']
    },
    {
      resource: 'settings',
      actions: ['read']
    },
    {
      resource: 'notifications',
      actions: ['read', 'create', 'update']
    }
  ],
  moderator: [
    {
      resource: 'products',
      actions: ['read', 'update']
    },
    {
      resource: 'orders',
      actions: ['read', 'update']
    },
    {
      resource: 'customers',
      actions: ['read']
    },
    {
      resource: 'reviews',
      actions: ['read', 'approve', 'reject', 'moderate']
    },
    {
      resource: 'content',
      actions: ['read', 'update']
    },
    {
      resource: 'analytics',
      actions: ['read']
    },
    {
      resource: 'notifications',
      actions: ['read']
    }
  ],
  viewer: [
    {
      resource: 'products',
      actions: ['read']
    },
    {
      resource: 'orders',
      actions: ['read']
    },
    {
      resource: 'customers',
      actions: ['read']
    },
    {
      resource: 'reviews',
      actions: ['read']
    },
    {
      resource: 'analytics',
      actions: ['read']
    },
    {
      resource: 'notifications',
      actions: ['read']
    }
  ]
};

// Persian messages for admin interface
export const PERSIAN_ADMIN_MESSAGES = {
  auth: {
    login: 'ورود به پنل مدیریت',
    logout: 'خروج از پنل',
    email: 'ایمیل',
    password: 'رمز عبور',
    twoFactorCode: 'کد دو مرحله‌ای',
    rememberMe: 'مرا به خاطر بسپار',
    loginButton: 'ورود',
    loginSuccess: 'با موفقیت وارد پنل مدیریت شدید',
    logoutSuccess: 'با موفقیت از پنل خارج شدید',
    invalidCredentials: 'ایمیل یا رمز عبور اشتباه است',
    accessDenied: 'دسترسی غیرمجاز',
    sessionExpired: 'جلسه کاری شما منقضی شده است',
    sessionExtended: 'جلسه کاری تمدید شد',
    twoFactorRequired: 'کد دو مرحله‌ای الزامی است',
    invalidTwoFactor: 'کد دو مرحله‌ای نامعتبر است'
  },
  roles: {
    super_admin: 'مدیر ارشد',
    admin: 'مدیر',
    moderator: 'ناظر',
    viewer: 'بازدیدکننده'
  },
  permissions: {
    products: 'محصولات',
    orders: 'سفارشات',
    customers: 'مشتریان',
    reviews: 'نظرات',
    loyalty: 'باشگاه مشتریان',
    content: 'محتوا',
    analytics: 'آمار و گزارشات',
    settings: 'تنظیمات',
    users: 'کاربران',
    audit: 'گزارش عملکرد',
    notifications: 'اعلان‌ها'
  },
  actions: {
    create: 'ایجاد',
    read: 'مشاهده',
    update: 'ویرایش',
    delete: 'حذف',
    approve: 'تأیید',
    reject: 'رد',
    export: 'خروجی',
    import: 'ورودی',
    moderate: 'نظارت',
    configure: 'پیکربندی'
  },
  errors: {
    networkError: 'خطا در اتصال به سرور',
    serverError: 'خطای سرور',
    unauthorized: 'عدم دسترسی',
    forbidden: 'دسترسی ممنوع',
    notFound: 'یافت نشد',
    validationError: 'خطای اعتبارسنجی',
    sessionTimeout: 'زمان جلسه تمام شد'
  },
  navigation: {
    dashboard: 'داشبورد',
    products: 'مدیریت محصولات',
    orders: 'مدیریت سفارشات',
    customers: 'مدیریت مشتریان',
    reviews: 'مدیریت نظرات',
    loyalty: 'باشگاه مشتریان',
    content: 'مدیریت محتوا',
    analytics: 'آمار و گزارشات',
    settings: 'تنظیمات سیستم',
    users: 'مدیریت کاربران',
    audit: 'گزارش عملکرد',
    profile: 'پروفایل کاربری'
  }
};

// Storage keys for admin authentication
export const ADMIN_STORAGE_KEYS = {
  TOKEN: 'admin_auth_token',
  REFRESH_TOKEN: 'admin_refresh_token',
  USER: 'admin_user',
  REMEMBER_ME: 'admin_remember_me',
  LAST_LOGIN: 'admin_last_login',
  SESSION_EXPIRY: 'admin_session_expiry'
} as const;

// Session configuration
export const ADMIN_SESSION_CONFIG = {
  DEFAULT_EXPIRY: 8 * 60 * 60 * 1000, // 8 hours
  REMEMBER_ME_EXPIRY: 30 * 24 * 60 * 60 * 1000, // 30 days
  WARNING_THRESHOLD: 15 * 60 * 1000, // 15 minutes
  AUTO_LOGOUT_THRESHOLD: 5 * 60 * 1000, // 5 minutes
  REFRESH_THRESHOLD: 30 * 60 * 1000 // 30 minutes
} as const;
