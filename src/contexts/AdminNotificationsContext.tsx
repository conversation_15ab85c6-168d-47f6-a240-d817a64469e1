import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { AdminNotification, NotificationType, NotificationCategory } from '../types/adminNotifications';

interface AdminNotificationsContextType {
  notifications: AdminNotification[];
  unreadCount: number;
  markAsRead: (id: string) => Promise<any>;
  markAllAsRead: () => Promise<any>;
  deleteNotification: (id: string) => Promise<any>;
  refetch: () => Promise<void>;
  loading: boolean;
  error: string | null;
}

const AdminNotificationsContext = createContext<AdminNotificationsContextType | undefined>(undefined);

export const useAdminNotificationsContext = () => {
  const context = useContext(AdminNotificationsContext);
  if (context === undefined) {
    throw new Error('useAdminNotificationsContext must be used within an AdminNotificationsProvider');
  }
  return context;
};



interface AdminNotificationsProviderProps {
  children: React.ReactNode;
}

export const AdminNotificationsProvider: React.FC<AdminNotificationsProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with actual API call
      // const response = await AdminNotificationService.getNotifications();
      // setNotifications(response.data);

      // For now, set empty array since we removed mock data
      setNotifications([]);
    } catch (err) {
      setError('خطا در بارگذاری اعلان‌ها');
      console.error('Notifications fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId
            ? { ...notif, read: true, readAt: new Date().toISOString() }
            : notif
        )
      );

      return { success: true };
    } catch (err) {
      console.error('Mark as read error:', err);
      return { success: false, error: 'خطا در علامت‌گذاری به عنوان خوانده شده' };
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setNotifications(prev =>
        prev.map(notif => ({ ...notif, read: true, readAt: new Date().toISOString() }))
      );

      return { success: true };
    } catch (err) {
      console.error('Mark all as read error:', err);
      return { success: false, error: 'خطا در علامت‌گذاری همه اعلان‌ها' };
    }
  }, []);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));

      return { success: true };
    } catch (err) {
      console.error('Delete notification error:', err);
      return { success: false, error: 'خطا در حذف اعلان' };
    }
  }, []);

  const unreadCount = notifications.filter(notif => !notif.read).length;

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Auto-refresh notifications every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchNotifications();
    }, 30000);

    return () => clearInterval(interval);
  }, [fetchNotifications]);

  const value: AdminNotificationsContextType = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refetch: fetchNotifications,
    loading,
    error
  };

  return (
    <AdminNotificationsContext.Provider value={value}>
      {children}
    </AdminNotificationsContext.Provider>
  );
};
