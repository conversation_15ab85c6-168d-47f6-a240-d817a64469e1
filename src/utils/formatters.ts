// Utility functions for formatting data in Persian/Farsi locale

/**
 * Format price in Iranian Rial with Persian numerals
 */
export const formatPrice = (price: number): string => {
  if (typeof price !== 'number' || isNaN(price)) {
    return '۰ تومان';
  }

  // Convert to Persian numerals
  const persianNumerals = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  
  // Format with thousand separators
  const formatted = price.toLocaleString('en-US');
  
  // Convert to Persian numerals
  const persianFormatted = formatted.replace(/\d/g, (digit) => persianNumerals[parseInt(digit)]);
  
  return `${persianFormatted} تومان`;
};

/**
 * Format number with Persian numerals
 */
export const formatNumber = (num: number): string => {
  if (typeof num !== 'number' || isNaN(num)) {
    return '۰';
  }

  const persianNumerals = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  const formatted = num.toLocaleString('en-US');
  
  return formatted.replace(/\d/g, (digit) => persianNumerals[parseInt(digit)]);
};

/**
 * Format percentage with Persian numerals
 */
export const formatPercentage = (percentage: number, decimals = 1): string => {
  if (typeof percentage !== 'number' || isNaN(percentage)) {
    return '۰٪';
  }

  const persianNumerals = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  const formatted = percentage.toFixed(decimals);
  
  const persianFormatted = formatted.replace(/\d/g, (digit) => persianNumerals[parseInt(digit)]);
  
  return `${persianFormatted}٪`;
};

/**
 * Format date in Persian calendar
 */
export const formatPersianDate = (date: string | Date): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Use Persian calendar formatting
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(dateObj);
  } catch (error) {
    return 'تاریخ نامعتبر';
  }
};

/**
 * Format date and time in Persian
 */
export const formatPersianDateTime = (date: string | Date): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(dateObj);
  } catch (error) {
    return 'تاریخ نامعتبر';
  }
};

/**
 * Format relative time in Persian (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date: string | Date): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'همین الان';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${formatNumber(diffInMinutes)} دقیقه پیش`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${formatNumber(diffInHours)} ساعت پیش`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${formatNumber(diffInDays)} روز پیش`;
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return `${formatNumber(diffInMonths)} ماه پیش`;
    }

    const diffInYears = Math.floor(diffInMonths / 12);
    return `${formatNumber(diffInYears)} سال پیش`;
  } catch (error) {
    return 'نامعلوم';
  }
};

/**
 * Format file size in Persian
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '۰ بایت';

  const k = 1024;
  const sizes = ['بایت', 'کیلوبایت', 'مگابایت', 'گیگابایت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${formatNumber(size)} ${sizes[i]}`;
};

/**
 * Format currency with different currencies
 */
export const formatCurrency = (amount: number, currency = 'IRR'): string => {
  const currencyMap = {
    IRR: 'ریال',
    IRT: 'تومان',
    USD: 'دلار',
    EUR: 'یورو'
  };

  const currencyName = currencyMap[currency as keyof typeof currencyMap] || currency;
  return `${formatNumber(amount)} ${currencyName}`;
};

/**
 * Format weight in Persian
 */
export const formatWeight = (grams: number): string => {
  if (grams < 1000) {
    return `${formatNumber(grams)} گرم`;
  }
  
  const kg = grams / 1000;
  return `${formatNumber(parseFloat(kg.toFixed(2)))} کیلوگرم`;
};

/**
 * Format dimensions in Persian
 */
export const formatDimensions = (length: number, width: number, height: number, unit = 'cm'): string => {
  const unitName = unit === 'cm' ? 'سانتی‌متر' : 'اینچ';
  return `${formatNumber(length)} × ${formatNumber(width)} × ${formatNumber(height)} ${unitName}`;
};

/**
 * Truncate text with Persian ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + '...';
};

/**
 * Format phone number in Persian format
 */
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Check if it's a valid Iranian mobile number
  if (cleaned.length === 11 && cleaned.startsWith('09')) {
    const persianNumerals = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    const formatted = `${cleaned.substring(0, 4)}-${cleaned.substring(4, 7)}-${cleaned.substring(7)}`;
    return formatted.replace(/\d/g, (digit) => persianNumerals[parseInt(digit)]);
  }
  
  return phone;
};

/**
 * Convert English numbers to Persian
 */
export const toPersianNumbers = (str: string): string => {
  const persianNumerals = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  return str.replace(/\d/g, (digit) => persianNumerals[parseInt(digit)]);
};

/**
 * Convert Persian numbers to English
 */
export const toEnglishNumbers = (str: string): string => {
  const persianNumerals = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  
  let result = str;
  
  // Convert Persian numerals
  persianNumerals.forEach((persian, index) => {
    result = result.replace(new RegExp(persian, 'g'), index.toString());
  });
  
  // Convert Arabic numerals
  arabicNumerals.forEach((arabic, index) => {
    result = result.replace(new RegExp(arabic, 'g'), index.toString());
  });
  
  return result;
};
