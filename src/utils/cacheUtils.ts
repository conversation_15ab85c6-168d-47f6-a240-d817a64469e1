// Caching utilities for performance optimization

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of entries
  strategy?: 'lru' | 'lfu' | 'fifo'; // Cache eviction strategy
}

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
}

// Default cache options
export const DEFAULT_CACHE_OPTIONS: Required<CacheOptions> = {
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 100,
  strategy: 'lru'
};

/**
 * In-memory cache implementation
 */
export class MemoryCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private options: Required<CacheOptions>;
  private stats: CacheStats = { hits: 0, misses: 0, size: 0, hitRate: 0 };

  constructor(options: CacheOptions = {}) {
    this.options = { ...DEFAULT_CACHE_OPTIONS, ...options };
  }

  /**
   * Get item from cache
   */
  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Update access info
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.stats.hits++;
    this.updateHitRate();
    
    return entry.data;
  }

  /**
   * Set item in cache
   */
  set(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const expirationTime = ttl || this.options.ttl;
    
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + expirationTime,
      accessCount: 1,
      lastAccessed: now
    };

    // Check if we need to evict entries
    if (this.cache.size >= this.options.maxSize && !this.cache.has(key)) {
      this.evict();
    }

    this.cache.set(key, entry);
    this.stats.size = this.cache.size;
  }

  /**
   * Delete item from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    this.stats.size = this.cache.size;
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, size: 0, hitRate: 0 };
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Evict entries based on strategy
   */
  private evict(): void {
    switch (this.options.strategy) {
      case 'lru':
        this.evictLRU();
        break;
      case 'lfu':
        this.evictLFU();
        break;
      case 'fifo':
        this.evictFIFO();
        break;
    }
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Evict least frequently used entry
   */
  private evictLFU(): void {
    let leastUsedKey = '';
    let leastUsedCount = Infinity;

    for (const [key, entry] of this.cache) {
      if (entry.accessCount < leastUsedCount) {
        leastUsedCount = entry.accessCount;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
    }
  }

  /**
   * Evict first in, first out entry
   */
  private evictFIFO(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Update hit rate statistics
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }
}

/**
 * LocalStorage cache implementation
 */
export class LocalStorageCache<T> {
  private prefix: string;
  private options: Required<CacheOptions>;

  constructor(prefix: string = 'cache', options: CacheOptions = {}) {
    this.prefix = prefix;
    this.options = { ...DEFAULT_CACHE_OPTIONS, ...options };
  }

  /**
   * Get item from localStorage cache
   */
  get(key: string): T | null {
    try {
      const item = localStorage.getItem(`${this.prefix}:${key}`);
      if (!item) return null;

      const entry: CacheEntry<T> = JSON.parse(item);
      
      // Check if expired
      if (Date.now() > entry.expiresAt) {
        this.delete(key);
        return null;
      }

      // Update access info
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      localStorage.setItem(`${this.prefix}:${key}`, JSON.stringify(entry));

      return entry.data;
    } catch (error) {
      console.warn('LocalStorage cache get error:', error);
      return null;
    }
  }

  /**
   * Set item in localStorage cache
   */
  set(key: string, data: T, ttl?: number): void {
    try {
      const now = Date.now();
      const expirationTime = ttl || this.options.ttl;
      
      const entry: CacheEntry<T> = {
        data,
        timestamp: now,
        expiresAt: now + expirationTime,
        accessCount: 1,
        lastAccessed: now
      };

      localStorage.setItem(`${this.prefix}:${key}`, JSON.stringify(entry));
    } catch (error) {
      // Silently handle localStorage errors
    }
  }

  /**
   * Delete item from localStorage cache
   */
  delete(key: string): boolean {
    try {
      localStorage.removeItem(`${this.prefix}:${key}`);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(`${this.prefix}:`)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('LocalStorage cache clear error:', error);
    }
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    return this.get(key) !== null;
  }
}

/**
 * Create a cached version of an async function
 */
export const createCachedFunction = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  cache: MemoryCache<Awaited<ReturnType<T>>>,
  keyGenerator?: (...args: Parameters<T>) => string
): T => {
  return (async (...args: Parameters<T>) => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    
    // Try to get from cache first
    const cached = cache.get(key);
    if (cached !== null) {
      return cached;
    }

    // Execute function and cache result
    const result = await fn(...args);
    cache.set(key, result);
    
    return result;
  }) as T;
};

/**
 * Global cache instances
 */
export const apiCache = new MemoryCache<any>({
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 50,
  strategy: 'lru'
});

export const imageCache = new MemoryCache<string>({
  ttl: 30 * 60 * 1000, // 30 minutes
  maxSize: 200,
  strategy: 'lru'
});

export const componentCache = new MemoryCache<any>({
  ttl: 10 * 60 * 1000, // 10 minutes
  maxSize: 100,
  strategy: 'lfu'
});

export const persistentCache = new LocalStorageCache('app-cache', {
  ttl: 24 * 60 * 60 * 1000, // 24 hours
  maxSize: 1000,
  strategy: 'lru'
});

/**
 * Cache cleanup utility
 */
export const cleanupExpiredCache = (): void => {
  // Clean up memory caches
  [apiCache, imageCache, componentCache].forEach(cache => {
    const keys = cache.keys();
    keys.forEach(key => {
      cache.get(key); // This will automatically remove expired entries
    });
  });

  // Clean up localStorage cache
  try {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('app-cache:')) {
        const item = localStorage.getItem(key);
        if (item) {
          try {
            const entry = JSON.parse(item);
            if (Date.now() > entry.expiresAt) {
              localStorage.removeItem(key);
            }
          } catch (error) {
            // Remove invalid entries
            localStorage.removeItem(key);
          }
        }
      }
    });
  } catch (error) {
    // Silently handle cleanup errors
  }
};

// Auto cleanup every 10 minutes
setInterval(cleanupExpiredCache, 10 * 60 * 1000);
