import { 
  MobileDetection, 
  DeviceType, 
  ResponsiveBreakpoints, 
  TouchGesture, 
  MobileViewport,
  DEFAULT_BREAKPOINTS,
  TOUCH_THRESHOLDS 
} from '../types/mobile';

/**
 * Detect mobile device and capabilities
 */
export const detectMobileDevice = (): MobileDetection => {
  const userAgent = navigator.userAgent || '';
  const screenWidth = window.innerWidth;
  const screenHeight = window.innerHeight;
  
  // Check for touch support
  const touchSupported = 'ontouchstart' in window || 
                        navigator.maxTouchPoints > 0 || 
                        (navigator as any).msMaxTouchPoints > 0;
  
  // Determine device type based on screen width
  const deviceType: DeviceType = getDeviceType(screenWidth);
  
  // Determine orientation
  const orientation = screenWidth > screenHeight ? 'landscape' : 'portrait';
  
  return {
    isMobile: deviceType === 'mobile',
    isTablet: deviceType === 'tablet',
    isDesktop: deviceType === 'desktop',
    screenWidth,
    screenHeight,
    orientation,
    touchSupported,
    userAgent,
    deviceType
  };
};

/**
 * Get device type based on screen width
 */
export const getDeviceType = (width: number, breakpoints: ResponsiveBreakpoints = DEFAULT_BREAKPOINTS): DeviceType => {
  if (width < breakpoints.mobile) {
    return 'mobile';
  } else if (width < breakpoints.desktop) {
    return 'tablet';
  } else {
    return 'desktop';
  }
};

/**
 * Check if current device is mobile
 */
export const isMobileDevice = (): boolean => {
  return getDeviceType(window.innerWidth) === 'mobile';
};

/**
 * Check if current device is tablet
 */
export const isTabletDevice = (): boolean => {
  return getDeviceType(window.innerWidth) === 'tablet';
};

/**
 * Check if current device is desktop
 */
export const isDesktopDevice = (): boolean => {
  return getDeviceType(window.innerWidth) === 'desktop';
};

/**
 * Get responsive grid columns based on screen width
 */
export const getResponsiveColumns = (
  width: number,
  config: { mobile: number; tablet: number; desktop: number; wide: number },
  breakpoints: ResponsiveBreakpoints = DEFAULT_BREAKPOINTS
): number => {
  if (width < breakpoints.mobile) {
    return config.mobile;
  } else if (width < breakpoints.tablet) {
    return config.mobile;
  } else if (width < breakpoints.desktop) {
    return config.tablet;
  } else if (width < breakpoints.wide) {
    return config.desktop;
  } else {
    return config.wide;
  }
};

/**
 * Calculate optimal grid columns based on container width and item width
 */
export const calculateOptimalColumns = (
  containerWidth: number,
  minItemWidth: number,
  gap: number = 16
): number => {
  const availableWidth = containerWidth - gap;
  const itemWidthWithGap = minItemWidth + gap;
  const columns = Math.floor(availableWidth / itemWidthWithGap);
  return Math.max(1, columns);
};

/**
 * Get viewport information
 */
export const getViewportInfo = (): MobileViewport => {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    scale: window.devicePixelRatio || 1,
    orientation: window.orientation || 0,
    isFullscreen: document.fullscreenElement !== null
  };
};

/**
 * Detect touch gesture from touch events
 */
export const detectTouchGesture = (
  startEvent: TouchEvent | MouseEvent,
  endEvent: TouchEvent | MouseEvent
): TouchGesture | null => {
  const startTime = Date.now();
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  // Get coordinates
  const startX = 'touches' in startEvent ? startEvent.touches[0].clientX : startEvent.clientX;
  const startY = 'touches' in startEvent ? startEvent.touches[0].clientY : startEvent.clientY;
  const endX = 'changedTouches' in endEvent ? endEvent.changedTouches[0].clientX : endEvent.clientX;
  const endY = 'changedTouches' in endEvent ? endEvent.changedTouches[0].clientY : endEvent.clientY;
  
  const deltaX = endX - startX;
  const deltaY = endY - startY;
  const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  
  // Detect tap
  if (distance <= TOUCH_THRESHOLDS.tap.maxDistance && duration <= TOUCH_THRESHOLDS.tap.maxDuration) {
    return {
      type: 'tap',
      startX,
      startY,
      endX,
      endY,
      duration
    };
  }
  
  // Detect swipe
  if (distance >= TOUCH_THRESHOLDS.swipe.minDistance && duration <= TOUCH_THRESHOLDS.swipe.maxDuration) {
    const direction = getSwipeDirection(deltaX, deltaY);
    return {
      type: 'swipe',
      startX,
      startY,
      endX,
      endY,
      duration,
      direction
    };
  }
  
  // Detect long press
  if (distance <= TOUCH_THRESHOLDS.longPress.maxDistance && duration >= TOUCH_THRESHOLDS.longPress.minDuration) {
    return {
      type: 'long-press',
      startX,
      startY,
      endX,
      endY,
      duration
    };
  }
  
  return null;
};

/**
 * Get swipe direction from delta values
 */
export const getSwipeDirection = (deltaX: number, deltaY: number): 'left' | 'right' | 'up' | 'down' => {
  const absDeltaX = Math.abs(deltaX);
  const absDeltaY = Math.abs(deltaY);
  
  if (absDeltaX > absDeltaY) {
    return deltaX > 0 ? 'right' : 'left';
  } else {
    return deltaY > 0 ? 'down' : 'up';
  }
};

/**
 * Optimize touch target size
 */
export const optimizeTouchTarget = (element: HTMLElement, minSize: number = 44): void => {
  const rect = element.getBoundingClientRect();
  const currentSize = Math.min(rect.width, rect.height);
  
  if (currentSize < minSize) {
    const padding = (minSize - currentSize) / 2;
    element.style.padding = `${padding}px`;
  }
};

/**
 * Debounce function for resize events
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function for scroll events
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Check if element is in viewport
 */
export const isElementInViewport = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};

/**
 * Get safe area insets for devices with notches
 */
export const getSafeAreaInsets = () => {
  const style = getComputedStyle(document.documentElement);
  return {
    top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
    right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
    bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
    left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0')
  };
};

/**
 * Format number for mobile display (shorter format)
 */
export const formatMobileNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

/**
 * Get optimal image size for mobile
 */
export const getOptimalImageSize = (
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } => {
  const aspectRatio = originalWidth / originalHeight;
  
  let width = originalWidth;
  let height = originalHeight;
  
  if (width > maxWidth) {
    width = maxWidth;
    height = width / aspectRatio;
  }
  
  if (height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }
  
  return { width: Math.round(width), height: Math.round(height) };
};
