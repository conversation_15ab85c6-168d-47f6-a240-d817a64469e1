import { 
  AdminCustomer, 
  CustomerFilters, 
  CustomerSortOptions, 
  CustomerAnalytics,
  CustomerSegment,
  CustomerStatus,
  SupportTicket,
  PERSIAN_CUSTOMER_MESSAGES 
} from '../types/adminCustomer';
import { LoyaltyTier } from '../types/loyalty';

/**
 * Filter customers based on provided criteria
 */
export const filterCustomers = (
  customers: AdminCustomer[], 
  filters: CustomerFilters
): AdminCustomer[] => {
  return customers.filter(customer => {
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = `${customer.firstName} ${customer.lastName} ${customer.email} ${customer.phone || ''}`.toLowerCase();
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(customer.status)) {
        return false;
      }
    }

    // Segment filter
    if (filters.segment && filters.segment.length > 0) {
      if (!filters.segment.includes(customer.segment)) {
        return false;
      }
    }

    // VIP filter
    if (filters.isVip !== undefined) {
      if (customer.isVip !== filters.isVip) {
        return false;
      }
    }

    // Loyalty tier filter
    if (filters.loyaltyTier && filters.loyaltyTier.length > 0) {
      if (!customer.loyaltyMember || !filters.loyaltyTier.includes(customer.loyaltyMember.tier.id)) {
        return false;
      }
    }

    // Registration date filter
    if (filters.registrationDateFrom) {
      if (new Date(customer.createdAt) < new Date(filters.registrationDateFrom)) {
        return false;
      }
    }
    if (filters.registrationDateTo) {
      if (new Date(customer.createdAt) > new Date(filters.registrationDateTo)) {
        return false;
      }
    }

    // Last order date filter
    if (filters.lastOrderDateFrom && customer.analytics.lastOrderDate) {
      if (new Date(customer.analytics.lastOrderDate) < new Date(filters.lastOrderDateFrom)) {
        return false;
      }
    }
    if (filters.lastOrderDateTo && customer.analytics.lastOrderDate) {
      if (new Date(customer.analytics.lastOrderDate) > new Date(filters.lastOrderDateTo)) {
        return false;
      }
    }

    // Total spent filter
    if (filters.totalSpentMin !== undefined) {
      if (customer.analytics.totalSpent < filters.totalSpentMin) {
        return false;
      }
    }
    if (filters.totalSpentMax !== undefined) {
      if (customer.analytics.totalSpent > filters.totalSpentMax) {
        return false;
      }
    }

    // Order count filter
    if (filters.orderCountMin !== undefined) {
      if (customer.analytics.totalOrders < filters.orderCountMin) {
        return false;
      }
    }
    if (filters.orderCountMax !== undefined) {
      if (customer.analytics.totalOrders > filters.orderCountMax) {
        return false;
      }
    }

    // Location filter
    if (filters.location) {
      if (filters.location.country && customer.location?.country !== filters.location.country) {
        return false;
      }
      if (filters.location.province && customer.location?.province !== filters.location.province) {
        return false;
      }
      if (filters.location.city && customer.location?.city !== filters.location.city) {
        return false;
      }
    }

    // Tags filter
    if (filters.tags && filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag => customer.tags.includes(tag));
      if (!hasMatchingTag) {
        return false;
      }
    }

    // Risk score filter
    if (filters.riskScoreMin !== undefined) {
      if (customer.riskScore < filters.riskScoreMin) {
        return false;
      }
    }
    if (filters.riskScoreMax !== undefined) {
      if (customer.riskScore > filters.riskScoreMax) {
        return false;
      }
    }

    // Active tickets filter
    if (filters.hasActiveTickets !== undefined) {
      const hasActiveTickets = customer.supportTickets.some(ticket => 
        ['open', 'in_progress', 'waiting_customer', 'waiting_internal'].includes(ticket.status)
      );
      if (hasActiveTickets !== filters.hasActiveTickets) {
        return false;
      }
    }

    // Marketing consent filter
    if (filters.marketingConsent) {
      if (filters.marketingConsent.email !== undefined) {
        if (customer.marketingConsent.email !== filters.marketingConsent.email) {
          return false;
        }
      }
      if (filters.marketingConsent.sms !== undefined) {
        if (customer.marketingConsent.sms !== filters.marketingConsent.sms) {
          return false;
        }
      }
    }

    return true;
  });
};

/**
 * Sort customers based on provided options
 */
export const sortCustomers = (
  customers: AdminCustomer[], 
  sortOptions: CustomerSortOptions
): AdminCustomer[] => {
  return [...customers].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortOptions.field) {
      case 'name':
        aValue = `${a.firstName} ${a.lastName}`.toLowerCase();
        bValue = `${b.firstName} ${b.lastName}`.toLowerCase();
        break;
      case 'email':
        aValue = a.email.toLowerCase();
        bValue = b.email.toLowerCase();
        break;
      case 'createdAt':
        aValue = new Date(a.createdAt);
        bValue = new Date(b.createdAt);
        break;
      case 'lastOrderDate':
        aValue = a.analytics.lastOrderDate ? new Date(a.analytics.lastOrderDate) : new Date(0);
        bValue = b.analytics.lastOrderDate ? new Date(b.analytics.lastOrderDate) : new Date(0);
        break;
      case 'totalSpent':
        aValue = a.analytics.totalSpent;
        bValue = b.analytics.totalSpent;
        break;
      case 'totalOrders':
        aValue = a.analytics.totalOrders;
        bValue = b.analytics.totalOrders;
        break;
      case 'lifetimeValue':
        aValue = a.analytics.lifetimeValue;
        bValue = b.analytics.lifetimeValue;
        break;
      case 'riskScore':
        aValue = a.riskScore;
        bValue = b.riskScore;
        break;
      default:
        return 0;
    }

    if (aValue < bValue) {
      return sortOptions.direction === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortOptions.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });
};

/**
 * Calculate customer analytics
 */
export const calculateCustomerAnalytics = (customers: AdminCustomer[]): CustomerAnalytics => {
  const now = new Date();
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  
  // Basic counts
  const totalCustomers = customers.length;
  const newCustomersThisMonth = customers.filter(c => new Date(c.createdAt) >= thisMonth).length;
  const activeCustomers = customers.filter(c => c.status === 'active').length;
  const churnedCustomers = customers.filter(c => c.segment === 'lost' || c.segment === 'churned').length;
  const vipCustomers = customers.filter(c => c.isVip).length;

  // Distribution calculations
  const segmentDistribution = customers.reduce((acc, customer) => {
    acc[customer.segment] = (acc[customer.segment] || 0) + 1;
    return acc;
  }, {} as Record<CustomerSegment, number>);

  const statusDistribution = customers.reduce((acc, customer) => {
    acc[customer.status] = (acc[customer.status] || 0) + 1;
    return acc;
  }, {} as Record<CustomerStatus, number>);

  const loyaltyTierDistribution = customers.reduce((acc, customer) => {
    if (customer.loyaltyMember) {
      const tierName = customer.loyaltyMember.tier.persianName;
      acc[tierName] = (acc[tierName] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  // Financial metrics
  const totalSpent = customers.reduce((sum, c) => sum + c.analytics.totalSpent, 0);
  const totalOrders = customers.reduce((sum, c) => sum + c.analytics.totalOrders, 0);
  const averageLifetimeValue = totalCustomers > 0 ? totalSpent / totalCustomers : 0;
  const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
  const averageOrderFrequency = customers.reduce((sum, c) => sum + c.analytics.orderFrequency, 0) / totalCustomers;

  // Retention metrics
  const customersWithOrders = customers.filter(c => c.analytics.totalOrders > 0);
  const returningCustomers = customers.filter(c => c.analytics.totalOrders > 1);
  const customerRetentionRate = customersWithOrders.length > 0 ? (returningCustomers.length / customersWithOrders.length) * 100 : 0;
  const churnRate = totalCustomers > 0 ? (churnedCustomers / totalCustomers) * 100 : 0;

  // Top customers
  const topSpenders = [...customers]
    .sort((a, b) => b.analytics.totalSpent - a.analytics.totalSpent)
    .slice(0, 10);

  const mostActiveCustomers = [...customers]
    .sort((a, b) => b.analytics.totalOrders - a.analytics.totalOrders)
    .slice(0, 10);

  const atRiskCustomers = customers
    .filter(c => c.segment === 'at_risk' || c.riskScore > 70)
    .slice(0, 10);

  // Geographic distribution
  const geographicDistribution = customers.reduce((acc, customer) => {
    if (customer.location?.province) {
      acc[customer.location.province] = (acc[customer.location.province] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  // Support metrics
  const allTickets = customers.flatMap(c => c.supportTickets);
  const openTickets = allTickets.filter(t => ['open', 'in_progress', 'waiting_customer', 'waiting_internal'].includes(t.status));
  const resolvedTickets = allTickets.filter(t => t.status === 'resolved' && t.resolvedAt);
  
  const averageResolutionTime = resolvedTickets.length > 0 
    ? resolvedTickets.reduce((sum, ticket) => {
        const created = new Date(ticket.createdAt);
        const resolved = new Date(ticket.resolvedAt!);
        return sum + (resolved.getTime() - created.getTime()) / (1000 * 60 * 60); // hours
      }, 0) / resolvedTickets.length
    : 0;

  const ticketsWithSatisfaction = allTickets.filter(t => t.satisfaction);
  const customerSatisfactionScore = ticketsWithSatisfaction.length > 0
    ? ticketsWithSatisfaction.reduce((sum, t) => sum + t.satisfaction!.rating, 0) / ticketsWithSatisfaction.length
    : 0;

  return {
    totalCustomers,
    newCustomersThisMonth,
    activeCustomers,
    churnedCustomers,
    vipCustomers,
    segmentDistribution,
    statusDistribution,
    loyaltyTierDistribution,
    averageLifetimeValue,
    averageOrderValue,
    averageOrderFrequency,
    customerRetentionRate,
    churnRate,
    topSpenders,
    mostActiveCustomers,
    atRiskCustomers,
    geographicDistribution,
    acquisitionChannels: {}, // Would be populated from actual data
    supportMetrics: {
      totalTickets: allTickets.length,
      openTickets: openTickets.length,
      averageResolutionTime,
      customerSatisfactionScore
    }
  };
};

/**
 * Determine customer segment based on behavior and analytics
 */
export const determineCustomerSegment = (customer: AdminCustomer): CustomerSegment => {
  const { analytics, createdAt } = customer;
  const daysSinceRegistration = Math.floor((Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24));
  const daysSinceLastOrder = analytics.lastOrderDate
    ? Math.floor((Date.now() - new Date(analytics.lastOrderDate).getTime()) / (1000 * 60 * 60 * 24))
    : Infinity;

  // New customers (registered within 30 days)
  if (daysSinceRegistration <= 30) {
    return 'new';
  }

  // Lost customers (no orders in 180+ days)
  if (daysSinceLastOrder > 180) {
    return 'lost';
  }

  // At risk customers (no orders in 60-180 days)
  if (daysSinceLastOrder > 60) {
    return 'at_risk';
  }

  // VIP customers
  if (customer.isVip || analytics.lifetimeValue > 5000000) {
    return 'vip';
  }

  // Champions (high value, frequent orders)
  if (analytics.lifetimeValue > 2000000 && analytics.orderFrequency > 2) {
    return 'champion';
  }

  // Potential loyalists (good value, moderate frequency)
  if (analytics.lifetimeValue > 1000000 && analytics.orderFrequency > 1) {
    return 'potential_loyalist';
  }

  // Need attention (low engagement)
  if (analytics.orderFrequency < 0.5 || analytics.averageOrderValue < 200000) {
    return 'need_attention';
  }

  // Regular customers
  return 'regular';
};

/**
 * Calculate customer risk score
 */
export const calculateRiskScore = (customer: AdminCustomer): number => {
  let riskScore = 0;

  // High return rate
  if (customer.analytics.returnRate > 20) {
    riskScore += 25;
  } else if (customer.analytics.returnRate > 10) {
    riskScore += 15;
  }

  // High cancellation rate
  if (customer.analytics.cancelationRate > 15) {
    riskScore += 20;
  } else if (customer.analytics.cancelationRate > 8) {
    riskScore += 10;
  }

  // Multiple fraud flags
  if (customer.fraudFlags.length > 2) {
    riskScore += 30;
  } else if (customer.fraudFlags.length > 0) {
    riskScore += 15;
  }

  // Suspicious behavior patterns
  if (customer.behavior.cartAbandonmentRate > 80) {
    riskScore += 10;
  }

  // Low customer satisfaction
  if (customer.analytics.customerSatisfactionScore && customer.analytics.customerSatisfactionScore < 2) {
    riskScore += 20;
  }

  // Multiple support tickets
  const activeTickets = customer.supportTickets.filter(t =>
    ['open', 'in_progress'].includes(t.status)
  ).length;
  if (activeTickets > 3) {
    riskScore += 15;
  }

  return Math.min(riskScore, 100);
};

/**
 * Format customer display name
 */
export const formatCustomerName = (customer: AdminCustomer): string => {
  return `${customer.firstName} ${customer.lastName}`.trim();
};

/**
 * Format customer phone number for display
 */
export const formatCustomerPhone = (phone?: string): string => {
  if (!phone) return '';

  // Remove non-digits
  const digits = phone.replace(/\D/g, '');

  // Format Iranian mobile numbers
  if (digits.startsWith('98') && digits.length === 12) {
    return `+${digits.slice(0, 2)} ${digits.slice(2, 5)} ${digits.slice(5, 8)} ${digits.slice(8)}`;
  }

  if (digits.startsWith('0') && digits.length === 11) {
    return `${digits.slice(0, 4)} ${digits.slice(4, 7)} ${digits.slice(7)}`;
  }

  return phone;
};

/**
 * Get customer status badge color
 */
export const getCustomerStatusColor = (status: CustomerStatus): string => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'inactive':
      return 'bg-gray-100 text-gray-800';
    case 'suspended':
      return 'bg-yellow-100 text-yellow-800';
    case 'blocked':
      return 'bg-red-100 text-red-800';
    case 'pending_verification':
      return 'bg-blue-100 text-blue-800';
    case 'churned':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Get customer segment badge color
 */
export const getCustomerSegmentColor = (segment: CustomerSegment): string => {
  switch (segment) {
    case 'new':
      return 'bg-blue-100 text-blue-800';
    case 'regular':
      return 'bg-gray-100 text-gray-800';
    case 'vip':
      return 'bg-purple-100 text-purple-800';
    case 'champion':
      return 'bg-yellow-100 text-yellow-800';
    case 'at_risk':
      return 'bg-orange-100 text-orange-800';
    case 'lost':
      return 'bg-red-100 text-red-800';
    case 'potential_loyalist':
      return 'bg-green-100 text-green-800';
    case 'need_attention':
      return 'bg-pink-100 text-pink-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Get risk score color
 */
export const getRiskScoreColor = (riskScore: number): string => {
  if (riskScore >= 80) return 'text-red-600';
  if (riskScore >= 60) return 'text-orange-600';
  if (riskScore >= 40) return 'text-yellow-600';
  if (riskScore >= 20) return 'text-blue-600';
  return 'text-green-600';
};

/**
 * Format Persian currency
 */
export const formatPersianCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fa-IR', {
    style: 'currency',
    currency: 'IRR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

/**
 * Format Persian date
 */
export const formatPersianDate = (date: string | Date): string => {
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date));
};

/**
 * Format number to Persian digits
 */
export const formatPersianNumber = (number: number): string => {
  return new Intl.NumberFormat('fa-IR').format(number);
};

/**
 * Format Persian date and time
 */
export const formatPersianDateTime = (date: string | Date): string => {
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
};



/**
 * Generate customer export data
 */
export const generateCustomerExportData = (
  customers: AdminCustomer[],
  fields: string[]
): Record<string, any>[] => {
  return customers.map(customer => {
    const exportData: Record<string, any> = {};

    fields.forEach(field => {
      switch (field) {
        case 'name':
          exportData['نام'] = formatCustomerName(customer);
          break;
        case 'email':
          exportData['ایمیل'] = customer.email;
          break;
        case 'phone':
          exportData['تلفن'] = formatCustomerPhone(customer.phone);
          break;
        case 'status':
          exportData['وضعیت'] = PERSIAN_CUSTOMER_MESSAGES.status[customer.status];
          break;
        case 'segment':
          exportData['بخش'] = PERSIAN_CUSTOMER_MESSAGES.segment[customer.segment];
          break;
        case 'totalOrders':
          exportData['تعداد سفارشات'] = customer.analytics.totalOrders;
          break;
        case 'totalSpent':
          exportData['مجموع خرید'] = formatPersianCurrency(customer.analytics.totalSpent);
          break;
        case 'lifetimeValue':
          exportData['ارزش مشتری'] = formatPersianCurrency(customer.analytics.lifetimeValue);
          break;
        case 'registrationDate':
          exportData['تاریخ عضویت'] = formatPersianDate(customer.createdAt);
          break;
        case 'lastOrderDate':
          exportData['آخرین سفارش'] = customer.analytics.lastOrderDate
            ? formatPersianDate(customer.analytics.lastOrderDate)
            : 'ندارد';
          break;
        case 'loyaltyTier':
          exportData['سطح وفاداری'] = customer.loyaltyMember?.tier.persianName || 'عضو نیست';
          break;
        case 'riskScore':
          exportData['امتیاز ریسک'] = customer.riskScore;
          break;
        case 'location':
          exportData['موقعیت'] = customer.location
            ? `${customer.location.city}, ${customer.location.province}`
            : '';
          break;
      }
    });

    return exportData;
  });
};
