import { products } from '../data/products';
import { categories } from '../data/categories';

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

/**
 * Generate sitemap URLs for the website
 */
export const generateSitemapUrls = (): SitemapUrl[] => {
  const baseUrl = window.location.origin;
  const currentDate = new Date().toISOString().split('T')[0];
  
  const urls: SitemapUrl[] = [];

  // Homepage
  urls.push({
    loc: `${baseUrl}/`,
    lastmod: currentDate,
    changefreq: 'daily',
    priority: 1.0
  });

  // Products page
  urls.push({
    loc: `${baseUrl}/products`,
    lastmod: currentDate,
    changefreq: 'daily',
    priority: 0.9
  });

  // Individual product pages
  products.forEach(product => {
    urls.push({
      loc: `${baseUrl}/product/${product.id}`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.8
    });
  });

  // Category pages
  categories.forEach(category => {
    urls.push({
      loc: `${baseUrl}/products?category=${encodeURIComponent(category.name)}`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.7
    });
  });

  // Brand pages (extract unique brands from products)
  const brands = [...new Set(products.map(p => p.brand).filter(Boolean))];
  brands.forEach(brand => {
    urls.push({
      loc: `${baseUrl}/products?brand=${encodeURIComponent(brand!)}`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.6
    });
  });

  return urls;
};

/**
 * Generate XML sitemap content
 */
export const generateSitemapXML = (): string => {
  const urls = generateSitemapUrls();
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
  
  urls.forEach(url => {
    xml += '  <url>\n';
    xml += `    <loc>${url.loc}</loc>\n`;
    if (url.lastmod) {
      xml += `    <lastmod>${url.lastmod}</lastmod>\n`;
    }
    if (url.changefreq) {
      xml += `    <changefreq>${url.changefreq}</changefreq>\n`;
    }
    if (url.priority) {
      xml += `    <priority>${url.priority}</priority>\n`;
    }
    xml += '  </url>\n';
  });
  
  xml += '</urlset>';
  
  return xml;
};

/**
 * Generate robots.txt content
 */
export const generateRobotsTxt = (): string => {
  const baseUrl = window.location.origin;
  
  return `User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Disallow admin or private areas (if any)
# Disallow: /admin/
# Disallow: /private/

# Allow all search engines to crawl the site
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /`;
};

/**
 * Download sitemap as XML file
 */
export const downloadSitemap = (): void => {
  const xml = generateSitemapXML();
  const blob = new Blob([xml], { type: 'application/xml' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = 'sitemap.xml';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * Download robots.txt file
 */
export const downloadRobotsTxt = (): void => {
  const content = generateRobotsTxt();
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = 'robots.txt';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * Generate meta tags for social media sharing
 */
export const generateSocialMetaTags = (
  title: string,
  description: string,
  image?: string,
  url?: string
) => {
  const defaultImage = '/images/og-default.jpg';
  const currentUrl = url || window.location.href;
  
  return {
    // Open Graph
    'og:title': title,
    'og:description': description,
    'og:image': image || defaultImage,
    'og:url': currentUrl,
    'og:type': 'website',
    'og:site_name': 'آرامش پوست',
    'og:locale': 'fa_IR',
    
    // Twitter Card
    'twitter:card': 'summary_large_image',
    'twitter:title': title,
    'twitter:description': description,
    'twitter:image': image || defaultImage,
    
    // Additional meta tags
    'description': description,
    'keywords': 'محصولات مراقبت از پوست, زیبایی, آرایشی بهداشتی',
    'author': 'آرامش پوست',
    'robots': 'index, follow',
    'language': 'Persian',
    'revisit-after': '7 days'
  };
};

/**
 * Validate and optimize meta description
 */
export const optimizeMetaDescription = (description: string): string => {
  // Remove HTML tags
  let cleaned = description.replace(/<[^>]*>/g, '');
  
  // Trim whitespace
  cleaned = cleaned.trim();
  
  // Ensure it's not too long (optimal length is 150-160 characters)
  if (cleaned.length > 160) {
    cleaned = cleaned.substring(0, 157) + '...';
  }
  
  // Ensure it's not too short (minimum 120 characters for better SEO)
  if (cleaned.length < 120) {
    cleaned += ' فروشگاه آنلاین محصولات مراقبت از پوست و زیبایی آرامش پوست.';
  }
  
  return cleaned;
};

/**
 * Generate JSON-LD structured data for the website
 */
export const generateWebsiteStructuredData = () => {
  const baseUrl = window.location.origin;
  
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "آرامش پوست",
    "alternateName": "GlowRoya Skincare",
    "url": baseUrl,
    "description": "فروشگاه آنلاین محصولات مراقبت از پوست و زیبایی",
    "inLanguage": "fa-IR",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/products?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "آرامش پوست",
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`
      }
    }
  };
};
