import { 
  AdminLoyaltyMember, 
  AdminLoyaltyTier, 
  AdminLoyaltyReward, 
  AdminPointTransaction,
  LoyaltyMemberFilters,
  LoyaltyRewardFilters,
  PointTransactionFilters,
  LoyaltyProgramAnalytics,
  LoyaltyTierFormData,
  LoyaltyRewardFormData
} from '../types/adminLoyalty';
import { LoyaltyMember, LoyaltyTier, LoyaltyReward, PointTransaction } from '../types/loyalty';

// Filter functions
export const filterLoyaltyMembers = (
  members: AdminLoyaltyMember[], 
  filters: LoyaltyMemberFilters
): AdminLoyaltyMember[] => {
  return members.filter(member => {
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = `
        ${member.customerInfo.name} 
        ${member.customerInfo.email} 
        ${member.membershipNumber}
      `.toLowerCase();
      
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }

    // Tier filter
    if (filters.tier && filters.tier.length > 0) {
      if (!filters.tier.includes(member.tier.id)) {
        return false;
      }
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      const memberStatus = member.isActive ? 'active' : 'inactive';
      if (!filters.status.includes(memberStatus)) {
        return false;
      }
    }

    // Points range filter
    if (filters.pointsMin !== undefined && member.points < filters.pointsMin) {
      return false;
    }
    if (filters.pointsMax !== undefined && member.points > filters.pointsMax) {
      return false;
    }

    // Total spent range filter
    if (filters.totalSpentMin !== undefined && member.customerInfo.totalSpent < filters.totalSpentMin) {
      return false;
    }
    if (filters.totalSpentMax !== undefined && member.customerInfo.totalSpent > filters.totalSpentMax) {
      return false;
    }

    // Engagement score filter
    if (filters.engagementScore) {
      const score = member.analytics.engagementScore;
      if (score < filters.engagementScore.min || score > filters.engagementScore.max) {
        return false;
      }
    }

    // Flags filter
    if (filters.flags && filters.flags.length > 0) {
      const memberFlags = member.flags.filter(flag => flag.isActive).map(flag => flag.type);
      const hasMatchingFlag = filters.flags.some(flag => memberFlags.includes(flag));
      if (!hasMatchingFlag) {
        return false;
      }
    }

    // Date filters
    if (filters.joinDateFrom) {
      if (new Date(member.joinDate) < new Date(filters.joinDateFrom)) {
        return false;
      }
    }
    if (filters.joinDateTo) {
      if (new Date(member.joinDate) > new Date(filters.joinDateTo)) {
        return false;
      }
    }

    if (filters.lastActivityFrom) {
      if (new Date(member.lastActivity) < new Date(filters.lastActivityFrom)) {
        return false;
      }
    }
    if (filters.lastActivityTo) {
      if (new Date(member.lastActivity) > new Date(filters.lastActivityTo)) {
        return false;
      }
    }

    return true;
  });
};

export const filterLoyaltyRewards = (
  rewards: AdminLoyaltyReward[], 
  filters: LoyaltyRewardFilters
): AdminLoyaltyReward[] => {
  return rewards.filter(reward => {
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = `${reward.title} ${reward.description}`.toLowerCase();
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }

    // Type filter
    if (filters.type && filters.type.length > 0) {
      if (!filters.type.includes(reward.type)) {
        return false;
      }
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      let rewardStatus: 'active' | 'inactive' | 'out_of_stock' = 'active';
      if (!reward.isActive) {
        rewardStatus = 'inactive';
      } else if (reward.inventory && reward.inventory.availableStock <= 0) {
        rewardStatus = 'out_of_stock';
      }
      
      if (!filters.status.includes(rewardStatus)) {
        return false;
      }
    }

    // Points cost range filter
    if (filters.pointsCostMin !== undefined && reward.pointsCost < filters.pointsCostMin) {
      return false;
    }
    if (filters.pointsCostMax !== undefined && reward.pointsCost > filters.pointsCostMax) {
      return false;
    }

    // Redemption count filter
    if (filters.redemptionCountMin !== undefined && reward.redemptionCount < filters.redemptionCountMin) {
      return false;
    }
    if (filters.redemptionCountMax !== undefined && reward.redemptionCount > filters.redemptionCountMax) {
      return false;
    }

    // Date filters
    if (filters.createdFrom) {
      if (new Date(reward.createdAt) < new Date(filters.createdFrom)) {
        return false;
      }
    }
    if (filters.createdTo) {
      if (new Date(reward.createdAt) > new Date(filters.createdTo)) {
        return false;
      }
    }

    return true;
  });
};

export const filterPointTransactions = (
  transactions: AdminPointTransaction[], 
  filters: PointTransactionFilters
): AdminPointTransaction[] => {
  return transactions.filter(transaction => {
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = `
        ${transaction.customerInfo.name} 
        ${transaction.customerInfo.email} 
        ${transaction.description}
      `.toLowerCase();
      
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }

    // Type filter
    if (filters.type && filters.type.length > 0) {
      if (!filters.type.includes(transaction.type)) {
        return false;
      }
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(transaction.status)) {
        return false;
      }
    }

    // Source filter
    if (filters.source && filters.source.length > 0) {
      if (!filters.source.includes(transaction.metadata.source)) {
        return false;
      }
    }

    // Points range filter
    if (filters.pointsMin !== undefined && Math.abs(transaction.points) < filters.pointsMin) {
      return false;
    }
    if (filters.pointsMax !== undefined && Math.abs(transaction.points) > filters.pointsMax) {
      return false;
    }

    // Member filter
    if (filters.memberId && transaction.memberId !== filters.memberId) {
      return false;
    }

    // Date filters
    if (filters.dateFrom) {
      if (new Date(transaction.createdAt) < new Date(filters.dateFrom)) {
        return false;
      }
    }
    if (filters.dateTo) {
      if (new Date(transaction.createdAt) > new Date(filters.dateTo)) {
        return false;
      }
    }

    return true;
  });
};

// Sort functions
export const sortLoyaltyMembers = (
  members: AdminLoyaltyMember[], 
  sortBy: string, 
  sortOrder: 'asc' | 'desc'
): AdminLoyaltyMember[] => {
  return [...members].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortBy) {
      case 'name':
        aValue = a.customerInfo.name;
        bValue = b.customerInfo.name;
        break;
      case 'points':
        aValue = a.points;
        bValue = b.points;
        break;
      case 'tier':
        aValue = a.tier.level;
        bValue = b.tier.level;
        break;
      case 'totalSpent':
        aValue = a.customerInfo.totalSpent;
        bValue = b.customerInfo.totalSpent;
        break;
      case 'joinDate':
        aValue = new Date(a.joinDate);
        bValue = new Date(b.joinDate);
        break;
      case 'lastActivity':
        aValue = new Date(a.lastActivity);
        bValue = new Date(b.lastActivity);
        break;
      case 'engagementScore':
        aValue = a.analytics.engagementScore;
        bValue = b.analytics.engagementScore;
        break;
      default:
        aValue = a.customerInfo.name;
        bValue = b.customerInfo.name;
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
};

// Analytics calculation functions
export const calculateLoyaltyAnalytics = (
  members: AdminLoyaltyMember[],
  tiers: AdminLoyaltyTier[],
  rewards: AdminLoyaltyReward[],
  transactions: AdminPointTransaction[]
): LoyaltyProgramAnalytics => {
  const now = new Date();
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

  // Overview calculations
  const totalMembers = members.length;
  const activeMembers = members.filter(m => m.isActive).length;
  const newMembersThisMonth = members.filter(m => 
    new Date(m.joinDate) >= thisMonth
  ).length;
  
  const memberGrowthRate = totalMembers > 0 ? 
    (newMembersThisMonth / totalMembers) * 100 : 0;

  const totalPointsIssued = transactions
    .filter(t => t.type === 'earned' || t.type === 'bonus')
    .reduce((sum, t) => sum + t.points, 0);

  const totalPointsRedeemed = transactions
    .filter(t => t.type === 'redeemed')
    .reduce((sum, t) => sum + Math.abs(t.points), 0);

  const redemptionRate = totalPointsIssued > 0 ? 
    (totalPointsRedeemed / totalPointsIssued) * 100 : 0;

  const averagePointsPerMember = totalMembers > 0 ? 
    members.reduce((sum, m) => sum + m.points, 0) / totalMembers : 0;

  // Tier distribution
  const tierStats: { [tierId: string]: any } = {};
  tiers.forEach(tier => {
    const tierMembers = members.filter(m => m.tier.id === tier.id);
    tierStats[tier.id] = {
      memberCount: tierMembers.length,
      percentage: totalMembers > 0 ? (tierMembers.length / totalMembers) * 100 : 0,
      averageSpend: tierMembers.length > 0 ? 
        tierMembers.reduce((sum, m) => sum + m.customerInfo.totalSpent, 0) / tierMembers.length : 0,
      retentionRate: 85 // Mock value - would be calculated from actual data
    };
  });

  // Engagement metrics
  const averageEngagementScore = members.length > 0 ? 
    members.reduce((sum, m) => sum + m.analytics.engagementScore, 0) / members.length : 0;

  const activeMembers30Days = members.filter(m => {
    const lastActivity = new Date(m.lastActivity);
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    return lastActivity >= thirtyDaysAgo;
  }).length;

  const pointsEarnedThisMonth = transactions
    .filter(t => 
      (t.type === 'earned' || t.type === 'bonus') && 
      new Date(t.createdAt) >= thisMonth
    )
    .reduce((sum, t) => sum + t.points, 0);

  const pointsRedeemedThisMonth = transactions
    .filter(t => 
      t.type === 'redeemed' && 
      new Date(t.createdAt) >= thisMonth
    )
    .reduce((sum, t) => sum + Math.abs(t.points), 0);

  // Top rewards
  const rewardRedemptions: { [rewardId: string]: number } = {};
  rewards.forEach(reward => {
    rewardRedemptions[reward.id] = reward.redemptionCount;
  });

  const topRewards = Object.entries(rewardRedemptions)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([rewardId, redemptions]) => {
      const reward = rewards.find(r => r.id === rewardId);
      return {
        rewardId,
        title: reward?.title || 'نامشخص',
        redemptions
      };
    });

  // Financial metrics
  const totalLiability = members.reduce((sum, m) => sum + m.points, 0) * 100; // 100 tomans per point
  const monthlyLiabilityCost = pointsEarnedThisMonth * 100;
  const averageRedemptionValue = totalPointsRedeemed > 0 ? 
    rewards.reduce((sum, r) => sum + (r.redemptionCount * r.value), 0) / totalPointsRedeemed : 0;

  return {
    overview: {
      totalMembers,
      activeMembers,
      newMembersThisMonth,
      memberGrowthRate,
      totalPointsIssued,
      totalPointsRedeemed,
      redemptionRate,
      averagePointsPerMember
    },
    tiers: tierStats,
    engagement: {
      averageEngagementScore,
      activeMembers30Days,
      pointsEarnedThisMonth,
      pointsRedeemedThisMonth,
      topRewards
    },
    financial: {
      totalLiability,
      monthlyLiabilityCost,
      averageRedemptionValue,
      programROI: 15.5, // Mock value
      costPerMember: totalMembers > 0 ? totalLiability / totalMembers : 0
    },
    trends: {
      membershipGrowth: [], // Would be populated with historical data
      pointsActivity: [], // Would be populated with historical data
      tierDistribution: tiers.map(tier => ({
        tier: tier.persianName,
        count: tierStats[tier.id]?.memberCount || 0,
        percentage: tierStats[tier.id]?.percentage || 0
      }))
    }
  };
};

// Validation functions
export const validateTierForm = (data: LoyaltyTierFormData, existingTiers: LoyaltyTier[] = []): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!data.name.trim()) {
    errors.push('نام انگلیسی الزامی است');
  }

  if (!data.persianName.trim()) {
    errors.push('نام فارسی الزامی است');
  }

  if (data.level < 1) {
    errors.push('سطح باید بیشتر از صفر باشد');
  }

  if (data.minPoints < 0) {
    errors.push('حداقل امتیاز نمی‌تواند منفی باشد');
  }

  if (data.maxPoints && data.maxPoints <= data.minPoints) {
    errors.push('حداکثر امتیاز باید بیشتر از حداقل امتیاز باشد');
  }

  if (data.discountPercentage < 0 || data.discountPercentage > 100) {
    errors.push('درصد تخفیف باید بین ۰ تا ۱۰۰ باشد');
  }

  // Check for duplicate levels
  const duplicateLevel = existingTiers.find(tier => tier.level === data.level);
  if (duplicateLevel) {
    errors.push('سطح تکراری است');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateRewardForm = (data: LoyaltyRewardFormData): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!data.title.trim()) {
    errors.push('عنوان الزامی است');
  }

  if (!data.description.trim()) {
    errors.push('توضیحات الزامی است');
  }

  if (data.pointsCost <= 0) {
    errors.push('هزینه امتیاز باید بیشتر از صفر باشد');
  }

  if (data.value <= 0) {
    errors.push('ارزش جایزه باید بیشتر از صفر باشد');
  }

  if (data.stock && data.stock < 0) {
    errors.push('موجودی نمی‌تواند منفی باشد');
  }

  if (data.validUntil && new Date(data.validUntil) <= new Date()) {
    errors.push('تاریخ انقضا باید در آینده باشد');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Utility functions
export const formatLoyaltyMembershipNumber = (number: string): string => {
  return number.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
};

export const calculateMemberEngagementScore = (member: AdminLoyaltyMember): number => {
  // Mock calculation - in real app would use complex algorithm
  const factors = {
    recentActivity: member.lastActivity ? 30 : 0,
    pointsEarned: Math.min(member.totalEarned / 1000, 30),
    redemptionRate: member.analytics.redemptionRate,
    orderFrequency: Math.min(member.customerInfo.totalOrders * 2, 20)
  };

  return Math.min(
    factors.recentActivity + factors.pointsEarned + factors.redemptionRate + factors.orderFrequency,
    100
  );
};

export const generateMembershipNumber = (): string => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `1${timestamp}${random}`;
};
