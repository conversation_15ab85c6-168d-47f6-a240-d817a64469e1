import { Product } from '../types';

/**
 * Generate SEO-friendly URL slug from Persian text
 */
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .trim()
    .replace(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g, '') // Remove Persian characters
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Generate meta description for products
 */
export const generateProductMetaDescription = (product: Product): string => {
  const baseDescription = `${product.name} - ${product.description.substring(0, 100)}...`;
  const priceText = product.discountedPrice 
    ? `قیمت: ${product.discountedPrice.toLocaleString()} تومان (تخفیف دار)`
    : `قیمت: ${product.price.toLocaleString()} تومان`;
  
  return `${baseDescription} ${priceText} | فروشگاه آرامش پوست`;
};

/**
 * Generate meta keywords for products
 */
export const generateProductKeywords = (product: Product): string[] => {
  const keywords = [
    product.name,
    product.category,
    product.brand || '',
    ...product.benefits.slice(0, 3),
    'مراقبت از پوست',
    'محصولات زیبایی',
    'فروشگاه آنلاین'
  ];

  // Add category-specific keywords
  const categoryKeywords: { [key: string]: string[] } = {
    'سرم': ['سرم صورت', 'سرم پوست', 'سرم آبرسان', 'سرم ضد پیری'],
    'کرم': ['کرم صورت', 'کرم مرطوب کننده', 'کرم ضد پیری', 'کرم روز'],
    'ماسک': ['ماسک صورت', 'ماسک پوست', 'ماسک طبیعی', 'ماسک احیاکننده'],
    'پاک کننده': ['پاک کننده صورت', 'شوینده پوست', 'کلنزر', 'پاک کننده آرایش'],
    'تونر': ['تونر صورت', 'تونر پوست', 'لوسیون', 'آبرسان']
  };

  if (categoryKeywords[product.category]) {
    keywords.push(...categoryKeywords[product.category]);
  }

  return keywords.filter(Boolean);
};

/**
 * Generate page title for different pages
 */
export const generatePageTitle = (
  pageType: 'home' | 'products' | 'product' | 'category' | 'search',
  data?: any
): string => {
  const siteName = 'آرامش پوست';
  
  switch (pageType) {
    case 'home':
      return `${siteName} | محصولات مراقبت از پوست و زیبایی`;
    
    case 'products':
      return `فروشگاه محصولات | ${siteName}`;
    
    case 'product':
      return `${data.name} | ${siteName}`;
    
    case 'category':
      return `${data.categoryName} | ${siteName}`;
    
    case 'search':
      return `نتایج جستجو برای "${data.query}" | ${siteName}`;
    
    default:
      return siteName;
  }
};

/**
 * Generate breadcrumb data for structured data
 */
export const generateBreadcrumbs = (path: string, productName?: string): any[] => {
  const breadcrumbs = [
    { name: 'خانه', url: '/' }
  ];

  if (path.includes('/products')) {
    breadcrumbs.push({ name: 'محصولات', url: '/products' });
    
    if (productName) {
      breadcrumbs.push({ 
        name: productName, 
        url: window.location.href 
      });
    }
  }

  return breadcrumbs;
};

/**
 * Generate Open Graph image URL for products
 */
export const generateOGImage = (product?: Product): string => {
  if (product && product.imageSrc) {
    return product.imageSrc;
  }
  
  // Default OG image
  return '/images/og-default.jpg';
};

/**
 * Validate and clean meta description
 */
export const cleanMetaDescription = (description: string): string => {
  // Remove HTML tags
  const cleaned = description.replace(/<[^>]*>/g, '');
  
  // Limit to 160 characters for optimal SEO
  if (cleaned.length > 160) {
    return cleaned.substring(0, 157) + '...';
  }
  
  return cleaned;
};

/**
 * Generate canonical URL
 */
export const generateCanonicalUrl = (path: string): string => {
  const baseUrl = window.location.origin;
  return `${baseUrl}${path}`;
};

/**
 * Persian-specific SEO optimizations
 */
export const optimizeForPersianSEO = (text: string): string => {
  return text
    // Normalize Persian numbers
    .replace(/[۰-۹]/g, (match) => {
      const persianNumbers = '۰۱۲۳۴۵۶۷۸۹';
      const englishNumbers = '0123456789';
      return englishNumbers[persianNumbers.indexOf(match)];
    })
    // Normalize Persian characters
    .replace(/ي/g, 'ی')
    .replace(/ك/g, 'ک')
    .replace(/ء/g, 'ئ');
};

/**
 * Generate FAQ structured data for common questions
 */
export const generateProductFAQs = (product: Product): any[] => {
  return [
    {
      question: `${product.name} چگونه استفاده می‌شود؟`,
      answer: product.howToUse.join(' ')
    },
    {
      question: `ترکیبات ${product.name} چیست؟`,
      answer: `این محصول حاوی ${product.ingredients.join('، ')} می‌باشد.`
    },
    {
      question: `فواید ${product.name} چیست؟`,
      answer: product.benefits.join('، ')
    },
    {
      question: `قیمت ${product.name} چقدر است؟`,
      answer: product.discountedPrice 
        ? `قیمت این محصول ${product.discountedPrice.toLocaleString()} تومان (با تخفیف) می‌باشد.`
        : `قیمت این محصول ${product.price.toLocaleString()} تومان می‌باشد.`
    }
  ];
};

/**
 * Generate rich snippets data for products
 */
export const generateProductRichSnippets = (product: Product) => {
  return {
    name: product.name,
    description: cleanMetaDescription(product.description),
    image: product.imageSrc,
    brand: product.brand || 'آرامش پوست',
    category: product.category,
    price: product.discountedPrice || product.price,
    currency: 'IRR',
    availability: product.stock > 0 ? 'in_stock' : 'out_of_stock',
    rating: product.rating,
    reviewCount: product.reviewCount,
    keywords: generateProductKeywords(product)
  };
};
