// Analytics utilities for event tracking and data processing

import { formatNumber } from './formatters';
import {
  AnalyticsEventData,
  EcommerceEvent,
  EcommerceItem,
  UserProperties,
  PageViewEvent,
  SearchEvent,
  ConversionEvent,
  CustomEvent,
  PrivacySettings,
  DEFAULT_ANALYTICS_CONFIG,
  AnalyticsEventName,
  TrafficSource,
  DeviceCategory
} from '../types/analytics';

/**
 * Generate unique session ID
 */
export const generateSessionId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Generate unique user ID
 */
export const generateUserId = (): string => {
  return `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Get or create session ID from sessionStorage
 */
export const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('analytics_session_id');
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  return sessionId;
};

/**
 * Get or create user ID from localStorage
 */
export const getUserId = (): string | null => {
  return localStorage.getItem('analytics_user_id');
};

/**
 * Set user ID in localStorage
 */
export const setUserId = (userId: string): void => {
  localStorage.setItem('analytics_user_id', userId);
};

/**
 * Clear user ID from localStorage
 */
export const clearUserId = (): void => {
  localStorage.removeItem('analytics_user_id');
};

/**
 * Detect device category
 */
export const getDeviceCategory = (): DeviceCategory => {
  const width = window.innerWidth;
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  return 'desktop';
};

/**
 * Detect traffic source from URL parameters and referrer
 */
export const getTrafficSource = (): TrafficSource => {
  const urlParams = new URLSearchParams(window.location.search);
  const referrer = document.referrer;

  // Check UTM parameters
  const utmSource = urlParams.get('utm_source');
  const utmMedium = urlParams.get('utm_medium');

  if (utmMedium) {
    switch (utmMedium.toLowerCase()) {
      case 'cpc':
      case 'ppc':
      case 'paid':
        return 'paid_search';
      case 'social':
        return 'social';
      case 'email':
        return 'email';
      case 'display':
        return 'display';
      case 'affiliate':
        return 'affiliate';
      default:
        return 'other';
    }
  }

  if (utmSource) {
    const source = utmSource.toLowerCase();
    if (source.includes('google') || source.includes('bing') || source.includes('yahoo')) {
      return 'organic_search';
    }
    if (source.includes('facebook') || source.includes('twitter') || source.includes('instagram')) {
      return 'social';
    }
  }

  // Check referrer
  if (referrer) {
    const referrerDomain = new URL(referrer).hostname.toLowerCase();

    if (referrerDomain.includes('google') || referrerDomain.includes('bing') || referrerDomain.includes('yahoo')) {
      return 'organic_search';
    }
    if (referrerDomain.includes('facebook') || referrerDomain.includes('twitter') || referrerDomain.includes('instagram')) {
      return 'social';
    }

    return 'referral';
  }

  return 'direct';
};

/**
 * Extract UTM parameters from URL
 */
export const getUTMParameters = (): Record<string, string> => {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    utm_source: urlParams.get('utm_source') || '',
    utm_medium: urlParams.get('utm_medium') || '',
    utm_campaign: urlParams.get('utm_campaign') || '',
    utm_content: urlParams.get('utm_content') || '',
    utm_term: urlParams.get('utm_term') || ''
  };
};

/**
 * Check if user has given analytics consent
 */
export const hasAnalyticsConsent = (): boolean => {
  const consent = localStorage.getItem('analytics_consent');
  return consent === 'true';
};

/**
 * Set analytics consent
 */
export const setAnalyticsConsent = (consent: boolean): void => {
  localStorage.setItem('analytics_consent', consent.toString());
  localStorage.setItem('analytics_consent_timestamp', Date.now().toString());
};

// Format currency for analytics display
export const formatCurrency = (amount: number): string => {
  if (amount >= 1000000000) {
    return `${(amount / 1000000000).toFixed(1)} میلیارد تومان`;
  } else if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)} میلیون تومان`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(0)} هزار تومان`;
  }
  return `${formatNumber(amount)} تومان`;
};

// Format percentage with Persian numbers
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${formatNumber(value.toFixed(decimals))}%`;
};

// Format growth rate with appropriate color and icon
export const formatGrowthRate = (rate: number): { 
  formatted: string; 
  color: string; 
  trend: 'up' | 'down' | 'neutral' 
} => {
  const formatted = formatPercentage(Math.abs(rate));
  
  if (rate > 0) {
    return {
      formatted: `+${formatted}`,
      color: 'text-green-600',
      trend: 'up'
    };
  } else if (rate < 0) {
    return {
      formatted: `-${formatted}`,
      color: 'text-red-600',
      trend: 'down'
    };
  } else {
    return {
      formatted: formatted,
      color: 'text-gray-600',
      trend: 'neutral'
    };
  }
};

// Calculate period-over-period growth
export const calculateGrowth = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

// Format large numbers with appropriate units
export const formatLargeNumber = (num: number): string => {
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(1)}B`;
  } else if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return formatNumber(num);
};

// Generate color palette for charts
export const getChartColors = (count: number): string[] => {
  const baseColors = [
    '#3B82F6', // Blue
    '#10B981', // Green
    '#F59E0B', // Yellow
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#06B6D4', // Cyan
    '#F97316', // Orange
    '#84CC16', // Lime
    '#EC4899', // Pink
    '#6B7280'  // Gray
  ];
  
  const colors = [];
  for (let i = 0; i < count; i++) {
    colors.push(baseColors[i % baseColors.length]);
  }
  return colors;
};

// Calculate conversion rate
export const calculateConversionRate = (conversions: number, visitors: number): number => {
  if (visitors === 0) return 0;
  return (conversions / visitors) * 100;
};

// Format time duration (seconds to readable format)
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)} ثانیه`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${formatNumber(minutes)} دقیقه${remainingSeconds > 0 ? ` و ${formatNumber(remainingSeconds)} ثانیه` : ''}`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${formatNumber(hours)} ساعت${minutes > 0 ? ` و ${formatNumber(minutes)} دقیقه` : ''}`;
  }
};

// Generate date range options
export const getDateRangeOptions = () => [
  {
    label: 'امروز',
    value: 'today',
    start: new Date().toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  },
  {
    label: 'دیروز',
    value: 'yesterday',
    start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  },
  {
    label: '۷ روز گذشته',
    value: 'last7days',
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  },
  {
    label: '۳۰ روز گذشته',
    value: 'last30days',
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  },
  {
    label: '۹۰ روز گذشته',
    value: 'last90days',
    start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  },
  {
    label: 'این ماه',
    value: 'thismonth',
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  },
  {
    label: 'ماه گذشته',
    value: 'lastmonth',
    start: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1).toISOString().split('T')[0],
    end: new Date(new Date().getFullYear(), new Date().getMonth(), 0).toISOString().split('T')[0]
  }
];

// Calculate average from array of numbers
export const calculateAverage = (numbers: number[]): number => {
  if (numbers.length === 0) return 0;
  return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
};

// Calculate median from array of numbers
export const calculateMedian = (numbers: number[]): number => {
  if (numbers.length === 0) return 0;
  
  const sorted = [...numbers].sort((a, b) => a - b);
  const middle = Math.floor(sorted.length / 2);
  
  if (sorted.length % 2 === 0) {
    return (sorted[middle - 1] + sorted[middle]) / 2;
  } else {
    return sorted[middle];
  }
};

// Generate trend analysis
export const analyzeTrend = (data: number[]): {
  trend: 'increasing' | 'decreasing' | 'stable';
  strength: 'strong' | 'moderate' | 'weak';
  description: string;
} => {
  if (data.length < 2) {
    return {
      trend: 'stable',
      strength: 'weak',
      description: 'داده‌های کافی برای تحلیل روند موجود نیست'
    };
  }

  const firstHalf = data.slice(0, Math.floor(data.length / 2));
  const secondHalf = data.slice(Math.floor(data.length / 2));
  
  const firstAvg = calculateAverage(firstHalf);
  const secondAvg = calculateAverage(secondHalf);
  
  const change = ((secondAvg - firstAvg) / firstAvg) * 100;
  
  let trend: 'increasing' | 'decreasing' | 'stable';
  let strength: 'strong' | 'moderate' | 'weak';
  let description: string;
  
  if (Math.abs(change) < 5) {
    trend = 'stable';
    strength = 'weak';
    description = 'روند ثابت و بدون تغییر قابل توجه';
  } else if (change > 0) {
    trend = 'increasing';
    if (change > 20) {
      strength = 'strong';
      description = 'روند صعودی قوی';
    } else if (change > 10) {
      strength = 'moderate';
      description = 'روند صعودی متوسط';
    } else {
      strength = 'weak';
      description = 'روند صعودی ضعیف';
    }
  } else {
    trend = 'decreasing';
    if (change < -20) {
      strength = 'strong';
      description = 'روند نزولی قوی';
    } else if (change < -10) {
      strength = 'moderate';
      description = 'روند نزولی متوسط';
    } else {
      strength = 'weak';
      description = 'روند نزولی ضعیف';
    }
  }
  
  return { trend, strength, description };
};

/**
 * Create base analytics event data
 */
export const createBaseEventData = (eventName: AnalyticsEventName): AnalyticsEventData => {
  const utmParams = getUTMParameters();

  return {
    event_name: eventName,
    timestamp: Date.now(),
    session_id: getSessionId(),
    user_id: getUserId() || undefined,
    page_url: window.location.href,
    page_title: document.title,
    user_agent: navigator.userAgent,
    referrer: document.referrer || undefined,
    utm_source: utmParams.utm_source || undefined,
    utm_medium: utmParams.utm_medium || undefined,
    utm_campaign: utmParams.utm_campaign || undefined,
    utm_content: utmParams.utm_content || undefined,
    utm_term: utmParams.utm_term || undefined,
    custom_dimensions: {
      device_category: getDeviceCategory(),
      traffic_source: getTrafficSource(),
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screen_resolution: `${screen.width}x${screen.height}`,
      viewport_size: `${window.innerWidth}x${window.innerHeight}`
    }
  };
};

/**
 * Create ecommerce item from product data
 */
export const createEcommerceItem = (
  product: any,
  quantity: number = 1,
  variant?: string
): EcommerceItem => {
  return {
    item_id: product.id,
    item_name: product.name,
    item_category: product.category,
    item_category2: product.subcategory,
    item_brand: product.brand,
    item_variant: variant,
    price: product.price,
    quantity,
    currency: 'IRR',
    discount: product.discount || 0
  };
};

/**
 * Create ecommerce event
 */
export const createEcommerceEvent = (
  eventName: string,
  items: EcommerceItem[],
  transactionId?: string,
  value?: number,
  coupon?: string,
  shipping?: number,
  tax?: number,
  paymentMethod?: string
): EcommerceEvent => {
  return {
    event_name: eventName,
    currency: 'IRR',
    value: value || items.reduce((total, item) => total + (item.price * item.quantity), 0),
    transaction_id: transactionId,
    items,
    coupon,
    shipping,
    tax,
    payment_method: paymentMethod
  };
};

/**
 * Debounce function for analytics events
 */
export const debounceAnalyticsEvent = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Export data to CSV format
export const exportToCSV = (data: any[], filename: string): void => {
  if (data.length === 0) return;

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};
