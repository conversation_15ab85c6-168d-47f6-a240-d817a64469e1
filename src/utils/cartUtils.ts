import { Product, SelectedVariants } from '../types';
import { 
  AdvancedCartItem, 
  CartDiscount, 
  AdvancedCartSummary, 
  CartRecommendation,
  RecommendationReason,
  CartItemSource,
  SavedForLaterItem,
  LoyaltyPointsInfo,
  ShippingInfo,
  PaymentOption
} from '../types/advancedCart';
import { formatPrice, formatNumber } from './formatters';
import { products } from '../data/products';

// Generate unique cart item ID
export const generateCartItemId = (productId: number, selectedVariants?: SelectedVariants): string => {
  const timestamp = Date.now();
  const variantKey = selectedVariants 
    ? Object.keys(selectedVariants)
        .sort()
        .map(key => `${key}:${selectedVariants[key].id}`)
        .join('|')
    : '';
  
  return `cart_${productId}_${variantKey}_${timestamp}`;
};

// Calculate item price with variants and discounts
export const calculateItemPrice = (
  product: Product, 
  selectedVariants?: SelectedVariants,
  discounts: CartDiscount[] = []
): number => {
  let basePrice = product.discountedPrice || product.price;

  // Add variant price adjustments
  if (selectedVariants) {
    Object.values(selectedVariants).forEach(variant => {
      if (variant.price) {
        basePrice += variant.price;
      }
    });
  }

  // Apply item-specific discounts
  const itemDiscounts = discounts.filter(d => d.appliedTo === 'item');
  let finalPrice = basePrice;

  itemDiscounts.forEach(discount => {
    if (discount.type === 'percentage') {
      finalPrice -= (finalPrice * discount.value / 100);
    } else if (discount.type === 'fixed') {
      finalPrice -= discount.value;
    }
  });

  return Math.max(0, finalPrice);
};

// Calculate loyalty points for item
export const calculateLoyaltyPoints = (
  price: number, 
  quantity: number,
  loyaltyTier: string = 'bronze'
): number => {
  const baseRate = 10000; // 1 point per 10,000 tomans
  const multipliers = {
    bronze: 1,
    silver: 1.5,
    gold: 2,
    platinum: 3
  };

  const multiplier = multipliers[loyaltyTier as keyof typeof multipliers] || 1;
  const totalAmount = price * quantity;
  
  return Math.floor((totalAmount / baseRate) * multiplier);
};

// Generate cart recommendations
export const generateCartRecommendations = (
  cartItems: AdvancedCartItem[],
  maxRecommendations: number = 4
): CartRecommendation[] => {
  if (cartItems.length === 0) return [];

  const recommendations: CartRecommendation[] = [];
  const cartProductIds = cartItems.map(item => item.product.id);
  const cartCategories = [...new Set(cartItems.map(item => item.product.category))];
  const cartBrands = [...new Set(cartItems.map(item => item.product.brand).filter(Boolean))];

  // Frequently bought together (same category, different products)
  cartCategories.forEach(category => {
    const categoryProducts = products.filter(p => 
      p.category === category && 
      !cartProductIds.includes(p.id) &&
      p.stock > 0
    );

    categoryProducts.slice(0, 2).forEach(product => {
      recommendations.push({
        id: `fbt_${product.id}`,
        product,
        reason: 'frequently_bought_together',
        confidence: 0.8,
        discount: {
          id: `bundle_${product.id}`,
          type: 'percentage',
          value: 10,
          description: 'تخفیف ۱۰٪ برای خرید همراه',
          appliedTo: 'item'
        }
      });
    });
  });

  // Brand collection recommendations
  cartBrands.forEach(brand => {
    const brandProducts = products.filter(p => 
      p.brand === brand && 
      !cartProductIds.includes(p.id) &&
      p.stock > 0
    );

    brandProducts.slice(0, 1).forEach(product => {
      recommendations.push({
        id: `brand_${product.id}`,
        product,
        reason: 'brand_collection',
        confidence: 0.7
      });
    });
  });

  // Popular products in price range
  const avgPrice = cartItems.reduce((sum, item) => sum + item.product.price, 0) / cartItems.length;
  const priceRangeProducts = products.filter(p => 
    Math.abs(p.price - avgPrice) <= avgPrice * 0.5 &&
    !cartProductIds.includes(p.id) &&
    p.stock > 0 &&
    p.isBestSeller
  );

  priceRangeProducts.slice(0, 2).forEach(product => {
    recommendations.push({
      id: `price_${product.id}`,
      product,
      reason: 'price_range_match',
      confidence: 0.6
    });
  });

  // Sort by confidence and return top recommendations
  return recommendations
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, maxRecommendations);
};

// Calculate advanced cart summary
export const calculateAdvancedCartSummary = (
  items: AdvancedCartItem[],
  loyaltyTier: string = 'bronze',
  currentLoyaltyPoints: number = 0
): AdvancedCartSummary => {
  const subtotal = items.reduce((sum, item) => {
    const itemPrice = calculateItemPrice(item.product, item.selectedVariants, item.appliedDiscounts);
    return sum + (itemPrice * item.quantity);
  }, 0);

  // Calculate discounts
  const allDiscounts: CartDiscount[] = [];
  items.forEach(item => {
    if (item.appliedDiscounts) {
      allDiscounts.push(...item.appliedDiscounts);
    }
  });

  const totalDiscount = allDiscounts.reduce((sum, discount) => {
    if (discount.type === 'percentage') {
      return sum + (subtotal * discount.value / 100);
    } else if (discount.type === 'fixed') {
      return sum + discount.value;
    }
    return sum;
  }, 0);

  // Calculate shipping
  const shipping: ShippingInfo = {
    cost: subtotal >= 500000 ? 0 : 25000, // Free shipping over 500k tomans
    method: 'استاندارد',
    estimatedDays: 3,
    isFree: subtotal >= 500000,
    freeShippingThreshold: 500000,
    remainingForFreeShipping: Math.max(0, 500000 - subtotal)
  };

  // Calculate loyalty points
  const pointsToEarn = items.reduce((sum, item) => {
    const itemPrice = calculateItemPrice(item.product, item.selectedVariants, item.appliedDiscounts);
    return sum + calculateLoyaltyPoints(itemPrice, item.quantity, loyaltyTier);
  }, 0);

  const loyaltyPoints: LoyaltyPointsInfo = {
    currentPoints: currentLoyaltyPoints,
    pointsToEarn,
    pointsToSpend: 0,
    availableRewards: [],
    nextTierProgress: calculateNextTierProgress(loyaltyTier, currentLoyaltyPoints + pointsToEarn)
  };

  // Payment options
  const paymentOptions: PaymentOption[] = [
    {
      id: 'card',
      name: 'پرداخت آنلاین',
      type: 'card',
      available: true
    }
  ];

  const total = subtotal - totalDiscount + shipping.cost;
  const savings = totalDiscount + (shipping.isFree ? 25000 : 0);

  return {
    subtotal,
    discounts: allDiscounts,
    totalDiscount,
    shipping,
    tax: { rate: 0, amount: 0, included: true },
    loyaltyPoints,
    total,
    savings,
    estimatedDelivery: new Date(Date.now() + shipping.estimatedDays * 24 * 60 * 60 * 1000),
    paymentOptions
  };
};

// Calculate next tier progress
const calculateNextTierProgress = (currentTier: string, totalPoints: number) => {
  const tiers = {
    bronze: { min: 0, next: 'silver', nextMin: 1000 },
    silver: { min: 1000, next: 'gold', nextMin: 5000 },
    gold: { min: 5000, next: 'platinum', nextMin: 15000 },
    platinum: { min: 15000, next: null, nextMin: null }
  };

  const tier = tiers[currentTier as keyof typeof tiers];
  if (!tier || !tier.next) return undefined;

  return {
    current: currentTier,
    next: tier.next,
    pointsNeeded: tier.nextMin! - totalPoints
  };
};

// Cart persistence utilities
export const saveCartToStorage = (
  items: AdvancedCartItem[],
  savedItems: SavedForLaterItem[],
  userId?: string
): void => {
  const cartData = {
    items,
    savedItems,
    timestamp: Date.now(),
    userId
  };

  const key = userId ? `cart_${userId}` : 'guest_cart';
  
  try {
    localStorage.setItem(key, JSON.stringify(cartData));
  } catch (error) {
    // Silently handle localStorage errors
  }
};

export const loadCartFromStorage = (userId?: string): {
  items: AdvancedCartItem[];
  savedItems: SavedForLaterItem[];
} => {
  const key = userId ? `cart_${userId}` : 'guest_cart';
  
  try {
    const stored = localStorage.getItem(key);
    if (!stored) return { items: [], savedItems: [] };

    const cartData = JSON.parse(stored);
    
    // Check if cart is not too old (7 days)
    const maxAge = 7 * 24 * 60 * 60 * 1000;
    if (Date.now() - cartData.timestamp > maxAge) {
      localStorage.removeItem(key);
      return { items: [], savedItems: [] };
    }

    return {
      items: cartData.items || [],
      savedItems: cartData.savedItems || []
    };
  } catch (error) {
    console.warn('Failed to load cart from localStorage:', error);
    return { items: [], savedItems: [] };
  }
};

// Validate cart items against current product data
export const validateCartItems = async (items: AdvancedCartItem[]) => {
  const errors: any[] = [];
  const warnings: any[] = [];
  const suggestions: any[] = [];

  items.forEach(item => {
    const currentProduct = products.find(p => p.id === item.product.id);
    
    if (!currentProduct) {
      errors.push({
        itemId: item.id,
        type: 'discontinued',
        message: 'این محصول دیگر موجود نیست',
        suggestedAction: 'حذف از سبد خرید'
      });
      return;
    }

    if (currentProduct.stock === 0) {
      errors.push({
        itemId: item.id,
        type: 'out_of_stock',
        message: 'این محصول موجود نیست',
        suggestedAction: 'ذخیره برای بعد'
      });
    } else if (currentProduct.stock < item.quantity) {
      errors.push({
        itemId: item.id,
        type: 'insufficient_stock',
        message: `تنها ${formatNumber(currentProduct.stock)} عدد موجود است`,
        suggestedAction: 'کاهش تعداد'
      });
    } else if (currentProduct.stock <= 5) {
      warnings.push({
        itemId: item.id,
        type: 'low_stock',
        message: 'موجودی محدود'
      });
    }

    if (currentProduct.price !== item.product.price) {
      warnings.push({
        itemId: item.id,
        type: 'price_increase',
        message: `قیمت از ${formatPrice(item.product.price)} به ${formatPrice(currentProduct.price)} تغییر کرده`
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions
  };
};

// Format cart item display name with variants
export const formatCartItemName = (item: AdvancedCartItem): string => {
  let name = item.product.name;
  
  if (item.selectedVariants && Object.keys(item.selectedVariants).length > 0) {
    const variantNames = Object.values(item.selectedVariants)
      .map(variant => variant.name)
      .join(' - ');
    name += ` (${variantNames})`;
  }
  
  return name;
};

// Calculate estimated delivery date
export const calculateEstimatedDelivery = (
  items: AdvancedCartItem[],
  shippingMethod: string = 'standard'
): Date => {
  const baseDays = shippingMethod === 'express' ? 1 : 3;
  const maxProcessingDays = Math.max(...items.map(item => 
    item.product.category === 'سرم و اسانس' ? 2 : 1
  ));
  
  const totalDays = baseDays + maxProcessingDays;
  return new Date(Date.now() + totalDays * 24 * 60 * 60 * 1000);
};
