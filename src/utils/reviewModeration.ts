import { 
  AdminReview, 
  AdminReviewFilters, 
  ReviewSortOptions, 
  ReviewAnalytics,
  ContentFlag,
  ContentFlagType,
  ReviewModerationStatus,
  PERSIAN_REVIEW_ADMIN_MESSAGES 
} from '../types/adminReview';

/**
 * Filter reviews based on admin criteria
 */
export const filterAdminReviews = (
  reviews: AdminReview[], 
  filters: AdminReviewFilters
): AdminReview[] => {
  return reviews.filter(review => {
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = `${review.title || ''} ${review.content} ${review.customer?.name || ''} ${review.product.name}`.toLowerCase();
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      const reviewStatus = review.moderation?.status || review.moderationStatus?.toLowerCase() || 'pending';
      if (!filters.status.includes(reviewStatus as any)) {
        return false;
      }
    }

    // Rating filter
    if (filters.rating && filters.rating.length > 0) {
      if (!filters.rating.includes(review.rating)) {
        return false;
      }
    }

    // Date range filter
    if (filters.dateFrom) {
      if (new Date(review.createdAt) < new Date(filters.dateFrom)) {
        return false;
      }
    }
    if (filters.dateTo) {
      if (new Date(review.createdAt) > new Date(filters.dateTo)) {
        return false;
      }
    }

    // Product filter
    if (filters.productId !== undefined) {
      if (review.productId !== filters.productId) {
        return false;
      }
    }

    // Customer filter
    if (filters.customerId) {
      if (review.customer?.id !== filters.customerId) {
        return false;
      }
    }

    // Brand filter
    if (filters.brand) {
      if (review.product.brand !== filters.brand) {
        return false;
      }
    }

    // Category filter
    if (filters.category) {
      if (review.product.category !== filters.category) {
        return false;
      }
    }

    // Verified purchase filter
    if (filters.isVerifiedPurchase !== undefined) {
      if (review.isVerified !== filters.isVerifiedPurchase) {
        return false;
      }
    }

    // Has images filter
    if (filters.hasImages !== undefined) {
      const hasImages = review.images && review.images.length > 0;
      if (hasImages !== filters.hasImages) {
        return false;
      }
    }

    // Has reports filter
    if (filters.hasReports !== undefined) {
      const hasReports = review.reports.length > 0;
      if (hasReports !== filters.hasReports) {
        return false;
      }
    }

    // Quality score filter
    if (filters.qualityScoreMin !== undefined) {
      if ((review.moderation?.qualityScore || 85) < filters.qualityScoreMin) {
        return false;
      }
    }
    if (filters.qualityScoreMax !== undefined) {
      if ((review.moderation?.qualityScore || 85) > filters.qualityScoreMax) {
        return false;
      }
    }

    // Spam score filter
    if (filters.spamScoreMin !== undefined) {
      if ((review.moderation?.spamScore || 10) < filters.spamScoreMin) {
        return false;
      }
    }
    if (filters.spamScoreMax !== undefined) {
      if ((review.moderation?.spamScore || 10) > filters.spamScoreMax) {
        return false;
      }
    }

    // Content flags filter
    if (filters.contentFlags && filters.contentFlags.length > 0) {
      const reviewFlags = review.moderation?.contentFlags?.map(flag => flag.type) || [];
      const hasMatchingFlag = filters.contentFlags.some(flag => reviewFlags.includes(flag));
      if (!hasMatchingFlag) {
        return false;
      }
    }

    // Moderated by filter
    if (filters.moderatedBy) {
      if (review.moderation?.moderatedBy !== filters.moderatedBy) {
        return false;
      }
    }

    // Highlighted filter
    if (filters.isHighlighted !== undefined) {
      if (review.isHighlighted !== filters.isHighlighted) {
        return false;
      }
    }

    // Pinned filter
    if (filters.isPinned !== undefined) {
      if (review.isPinned !== filters.isPinned) {
        return false;
      }
    }

    // Business response filter
    if (filters.hasBusinessResponse !== undefined) {
      const hasResponse = !!review.businessResponse;
      if (hasResponse !== filters.hasBusinessResponse) {
        return false;
      }
    }

    return true;
  });
};

/**
 * Sort reviews based on admin criteria
 */
export const sortAdminReviews = (
  reviews: AdminReview[], 
  sortOptions: ReviewSortOptions
): AdminReview[] => {
  return [...reviews].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortOptions.field) {
      case 'createdAt':
        aValue = new Date(a.createdAt);
        bValue = new Date(b.createdAt);
        break;
      case 'rating':
        aValue = a.rating;
        bValue = b.rating;
        break;
      case 'helpfulVotes':
        aValue = a.helpfulCount;
        bValue = b.helpfulCount;
        break;
      case 'qualityScore':
        aValue = a.moderation?.qualityScore || 85;
        bValue = b.moderation?.qualityScore || 85;
        break;
      case 'spamScore':
        aValue = a.moderation?.spamScore || 10;
        bValue = b.moderation?.spamScore || 10;
        break;
      case 'reportCount':
        aValue = a.reports?.length || 0;
        bValue = b.reports?.length || 0;
        break;
      case 'moderatedAt':
        aValue = a.moderation?.moderatedAt ? new Date(a.moderation.moderatedAt) : new Date(0);
        bValue = b.moderation?.moderatedAt ? new Date(b.moderation.moderatedAt) : new Date(0);
        break;
      default:
        return 0;
    }

    if (aValue < bValue) {
      return sortOptions.direction === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortOptions.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });
};

/**
 * Calculate review quality score
 */
export const calculateQualityScore = (review: AdminReview): number => {
  let score = 50; // Base score

  // Length factor (optimal length is 100-500 characters)
  const commentLength = review.content.length;
  if (commentLength >= 100 && commentLength <= 500) {
    score += 15;
  } else if (commentLength >= 50 && commentLength < 100) {
    score += 10;
  } else if (commentLength > 500) {
    score += 5;
  } else {
    score -= 10;
  }

  // Title quality
  if (review.title && review.title.length >= 10 && review.title.length <= 100) {
    score += 10;
  }

  // Verified purchase
  if (review.isVerified) {
    score += 20;
  }

  // Has pros/cons
  if (review.pros.length > 0 || review.cons.length > 0) {
    score += 10;
  }

  // Has images
  if (review.images && review.images.length > 0) {
    score += 10;
  }

  // Customer credibility
  if (review.customer?.totalReviews && review.customer.totalReviews > 5) {
    score += 5;
  }
  if (review.customer?.averageRating && review.customer.averageRating >= 3 && review.customer.averageRating <= 4.5) {
    score += 5; // Balanced reviewer
  }

  // Helpfulness ratio
  const totalVotes = review.helpfulCount + review.unhelpfulCount;
  if (totalVotes > 0) {
    const helpfulnessRatio = review.helpfulCount / totalVotes;
    score += Math.round(helpfulnessRatio * 10);
  }

  // Content flags penalty
  if (review.moderation?.contentFlags) {
    review.moderation.contentFlags.forEach(flag => {
      switch (flag.severity) {
        case 'low':
          score -= 5;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'high':
          score -= 20;
          break;
        case 'critical':
          score -= 30;
          break;
      }
    });
  }

  return Math.max(0, Math.min(100, score));
};

/**
 * Calculate spam score
 */
export const calculateSpamScore = (review: AdminReview): number => {
  let score = 0;

  // Duplicate content detection (simplified)
  const commonSpamPhrases = ['خرید کنید', 'بهترین قیمت', 'تخفیف ویژه', 'فوری', 'محدود'];
  const spamPhraseCount = commonSpamPhrases.filter(phrase =>
    review.content.includes(phrase) || (review.title && review.title.includes(phrase))
  ).length;
  score += spamPhraseCount * 15;

  // Excessive capitalization
  const capsRatio = (review.content.match(/[A-Z]/g) || []).length / review.content.length;
  if (capsRatio > 0.3) {
    score += 20;
  }

  // Excessive punctuation
  const punctuationRatio = (review.content.match(/[!?]{2,}/g) || []).length;
  score += punctuationRatio * 10;

  // Very short or very long content
  if (review.content.length < 20) {
    score += 25;
  } else if (review.content.length > 1000) {
    score += 15;
  }

  // Extreme ratings from new customers
  if (review.customer?.totalReviews && review.customer.totalReviews <= 1 && (review.rating === 1 || review.rating === 5)) {
    score += 10;
  }

  // Multiple reviews from same customer in short time
  if (review.customer?.totalReviews && review.customer.totalReviews > 10) {
    score += 5;
  }

  // No verified purchase
  if (!review.isVerified) {
    score += 15;
  }

  return Math.max(0, Math.min(100, score));
};

/**
 * Detect content flags automatically
 */
export const detectContentFlags = (review: AdminReview): ContentFlag[] => {
  const flags: ContentFlag[] = [];
  const content = `${review.title || ''} ${review.content}`.toLowerCase();

  // Spam detection
  const spamScore = calculateSpamScore(review);
  if (spamScore > 60) {
    flags.push({
      id: `flag_${Date.now()}_spam`,
      type: 'spam',
      severity: spamScore > 80 ? 'high' : 'medium',
      description: 'محتوای احتمالاً اسپم شناسایی شد',
      confidence: spamScore / 100,
      detectedAt: new Date().toISOString(),
      source: 'auto'
    });
  }

  // Inappropriate language detection (simplified)
  const inappropriateWords = ['بد', 'افتضاح', 'کلاهبردار', 'دزد'];
  const inappropriateCount = inappropriateWords.filter(word => content.includes(word)).length;
  if (inappropriateCount > 0) {
    flags.push({
      id: `flag_${Date.now()}_inappropriate`,
      type: 'inappropriate_language',
      severity: inappropriateCount > 2 ? 'high' : 'medium',
      description: 'زبان نامناسب شناسایی شد',
      confidence: Math.min(inappropriateCount / 3, 1),
      detectedAt: new Date().toISOString(),
      source: 'auto'
    });
  }

  // Personal information detection
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
  const phoneRegex = /\b\d{11}\b|\b\d{4}-\d{3}-\d{4}\b/;
  if (emailRegex.test(content) || phoneRegex.test(content)) {
    flags.push({
      id: `flag_${Date.now()}_personal_info`,
      type: 'personal_information',
      severity: 'medium',
      description: 'اطلاعات شخصی شناسایی شد',
      confidence: 0.8,
      detectedAt: new Date().toISOString(),
      source: 'auto'
    });
  }

  // Promotional content detection
  const promoWords = ['تخفیف', 'فروش', 'خرید', 'قیمت', 'ارزان'];
  const promoCount = promoWords.filter(word => content.includes(word)).length;
  if (promoCount > 2) {
    flags.push({
      id: `flag_${Date.now()}_promotional`,
      type: 'promotional_content',
      severity: 'low',
      description: 'محتوای تبلیغاتی احتمالی',
      confidence: Math.min(promoCount / 5, 1),
      detectedAt: new Date().toISOString(),
      source: 'auto'
    });
  }

  return flags;
};

/**
 * Calculate sentiment score
 */
export const calculateSentimentScore = (review: AdminReview): number => {
  const content = `${review.title || ''} ${review.content}`.toLowerCase();
  
  const positiveWords = ['عالی', 'خوب', 'بهترین', 'راضی', 'توصیه', 'کیفیت', 'مناسب'];
  const negativeWords = ['بد', 'افتضاح', 'ضعیف', 'نامناسب', 'پشیمان', 'اشتباه'];
  
  const positiveCount = positiveWords.filter(word => content.includes(word)).length;
  const negativeCount = negativeWords.filter(word => content.includes(word)).length;
  
  // Combine with rating
  const ratingScore = (review.rating - 3) / 2; // Convert 1-5 to -1 to 1
  const wordScore = (positiveCount - negativeCount) / 10;
  
  return Math.max(-1, Math.min(1, (ratingScore + wordScore) / 2));
};

/**
 * Determine moderation status automatically
 */
export const determineAutoModerationStatus = (review: AdminReview): ReviewModerationStatus => {
  const qualityScore = calculateQualityScore(review);
  const spamScore = calculateSpamScore(review);
  const contentFlags = detectContentFlags(review);
  
  // Auto reject if high spam score or critical flags
  if (spamScore > 80 || contentFlags.some(flag => flag.severity === 'critical')) {
    return 'auto_rejected';
  }
  
  // Flag if medium-high spam or multiple flags
  if (spamScore > 60 || contentFlags.length > 2) {
    return 'flagged';
  }
  
  // Auto approve if high quality and low spam
  if (qualityScore > 70 && spamScore < 30 && contentFlags.length === 0) {
    return 'auto_approved';
  }
  
  // Require manual review for everything else
  return 'requires_review';
};

/**
 * Get moderation status color
 */
export const getModerationStatusColor = (status: ReviewModerationStatus): string => {
  switch (status) {
    case 'approved':
    case 'auto_approved':
      return 'bg-green-100 text-green-800';
    case 'rejected':
    case 'auto_rejected':
      return 'bg-red-100 text-red-800';
    case 'flagged':
      return 'bg-orange-100 text-orange-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'requires_review':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Get content flag color
 */
export const getContentFlagColor = (severity: ContentFlag['severity']): string => {
  switch (severity) {
    case 'critical':
      return 'bg-red-100 text-red-800';
    case 'high':
      return 'bg-orange-100 text-orange-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Format Persian date and time
 */
export const formatPersianDateTime = (date: string | Date): string => {
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
};

/**
 * Format Persian date
 */
export const formatPersianDate = (date: string | Date): string => {
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date));
};

/**
 * Format number to Persian digits
 */
export const formatPersianNumber = (number: number): string => {
  return new Intl.NumberFormat('fa-IR').format(number);
};

/**
 * Generate review export data
 */
export const generateReviewExportData = (
  reviews: AdminReview[],
  fields: string[]
): Record<string, any>[] => {
  return reviews.map(review => {
    const exportData: Record<string, any> = {};
    
    fields.forEach(field => {
      switch (field) {
        case 'title':
          exportData['عنوان'] = review.title;
          break;
        case 'rating':
          exportData['امتیاز'] = review.rating;
          break;
        case 'customer':
          exportData['مشتری'] = review.customer?.name || `${review.user.firstName} ${review.user.lastName}`;
          break;
        case 'product':
          exportData['محصول'] = review.product.name;
          break;
        case 'status':
          exportData['وضعیت'] = PERSIAN_REVIEW_ADMIN_MESSAGES.moderationStatus[review.moderation?.status || review.moderationStatus || 'PENDING'];
          break;
        case 'qualityScore':
          exportData['امتیاز کیفیت'] = review.moderation?.qualityScore || 85;
          break;
        case 'spamScore':
          exportData['امتیاز اسپم'] = review.moderation?.spamScore || 10;
          break;
        case 'createdAt':
          exportData['تاریخ ایجاد'] = formatPersianDateTime(review.createdAt);
          break;
        case 'moderatedAt':
          exportData['تاریخ بررسی'] = review.moderation?.moderatedAt
            ? formatPersianDateTime(review.moderation.moderatedAt)
            : 'بررسی نشده';
          break;
        case 'isVerified':
          exportData['خرید تأیید شده'] = review.isVerified ? 'بله' : 'خیر';
          break;
      }
    });
    
    return exportData;
  });
};
