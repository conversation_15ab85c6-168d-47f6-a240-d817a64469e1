import { CartEvent, CartEventType, CartAnalytics } from '../types/advancedCart';

// Generate unique session ID
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Generate unique event ID
export const generateEventId = (): string => {
  return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Create cart analytics instance
export const createCartAnalytics = (userId?: string): CartAnalytics => {
  return {
    sessionId: generateSessionId(),
    userId,
    events: [],
    startTime: new Date(),
    lastActivity: new Date(),
    totalValue: 0,
    itemCount: 0,
    abandonmentRisk: 0
  };
};

// Track cart event
export const trackCartEvent = (
  analytics: CartAnalytics,
  eventType: CartEventType,
  metadata?: Record<string, any>
): CartEvent => {
  const event: CartEvent = {
    id: generateEventId(),
    type: eventType,
    timestamp: new Date(),
    ...metadata
  };

  analytics.events.push(event);
  analytics.lastActivity = new Date();

  // Update analytics metrics
  updateAnalyticsMetrics(analytics);

  // Persist to localStorage for analytics
  persistAnalytics(analytics);

  return event;
};

// Update analytics metrics
const updateAnalyticsMetrics = (analytics: CartAnalytics): void => {
  const events = analytics.events;
  
  // Calculate total value from add events
  analytics.totalValue = events
    .filter(e => e.type === 'item_added')
    .reduce((sum, e) => sum + (e.value || 0), 0);

  // Calculate item count
  const addEvents = events.filter(e => e.type === 'item_added');
  const removeEvents = events.filter(e => e.type === 'item_removed');
  analytics.itemCount = addEvents.length - removeEvents.length;

  // Calculate abandonment risk
  analytics.abandonmentRisk = calculateAbandonmentRisk(analytics);
};

// Calculate abandonment risk score (0-1)
const calculateAbandonmentRisk = (analytics: CartAnalytics): number => {
  const now = Date.now();
  const lastActivity = analytics.lastActivity.getTime();
  const timeSinceLastActivity = now - lastActivity;

  // Base risk factors
  let risk = 0;

  // Time since last activity (increases risk)
  const minutesSinceActivity = timeSinceLastActivity / (1000 * 60);
  if (minutesSinceActivity > 30) risk += 0.3;
  if (minutesSinceActivity > 60) risk += 0.3;
  if (minutesSinceActivity > 120) risk += 0.4;

  // Cart value (higher value = lower risk)
  if (analytics.totalValue > 500000) risk -= 0.2;
  if (analytics.totalValue > 1000000) risk -= 0.2;

  // Number of items (more items = lower risk)
  if (analytics.itemCount > 3) risk -= 0.1;
  if (analytics.itemCount > 5) risk -= 0.1;

  // Engagement level (more events = lower risk)
  const eventCount = analytics.events.length;
  if (eventCount > 10) risk -= 0.1;
  if (eventCount > 20) risk -= 0.1;

  // Check for checkout attempts
  const checkoutAttempts = analytics.events.filter(e => e.type === 'checkout_started').length;
  if (checkoutAttempts > 0) risk -= 0.2;

  return Math.max(0, Math.min(1, risk));
};

// Persist analytics to localStorage
const persistAnalytics = (analytics: CartAnalytics): void => {
  try {
    const key = `cart_analytics_${analytics.sessionId}`;
    localStorage.setItem(key, JSON.stringify({
      ...analytics,
      events: analytics.events.slice(-50) // Keep only last 50 events
    }));
  } catch (error) {
    console.warn('Failed to persist cart analytics:', error);
  }
};

// Load analytics from localStorage
export const loadAnalytics = (sessionId: string): CartAnalytics | null => {
  try {
    const key = `cart_analytics_${sessionId}`;
    const stored = localStorage.getItem(key);
    if (!stored) return null;

    const data = JSON.parse(stored);
    return {
      ...data,
      startTime: new Date(data.startTime),
      lastActivity: new Date(data.lastActivity),
      events: data.events.map((e: any) => ({
        ...e,
        timestamp: new Date(e.timestamp)
      }))
    };
  } catch (error) {
    console.warn('Failed to load cart analytics:', error);
    return null;
  }
};

// Get analytics summary
export const getAnalyticsSummary = (analytics: CartAnalytics) => {
  const events = analytics.events;
  const sessionDuration = Date.now() - analytics.startTime.getTime();

  return {
    sessionDuration: Math.round(sessionDuration / 1000 / 60), // minutes
    totalEvents: events.length,
    itemsAdded: events.filter(e => e.type === 'item_added').length,
    itemsRemoved: events.filter(e => e.type === 'item_removed').length,
    cartViews: events.filter(e => e.type === 'cart_viewed').length,
    checkoutAttempts: events.filter(e => e.type === 'checkout_started').length,
    recommendationsViewed: events.filter(e => e.type === 'recommendation_viewed').length,
    recommendationsAdded: events.filter(e => e.type === 'recommendation_added').length,
    abandonmentRisk: analytics.abandonmentRisk,
    averageTimePerEvent: events.length > 0 ? sessionDuration / events.length / 1000 : 0
  };
};

// Track specific cart actions
export const trackItemAdded = (
  analytics: CartAnalytics,
  productId: number,
  quantity: number,
  value: number,
  source?: string
): CartEvent => {
  return trackCartEvent(analytics, 'item_added', {
    productId,
    quantity,
    value,
    source
  });
};

export const trackItemRemoved = (
  analytics: CartAnalytics,
  productId: number,
  quantity: number,
  value: number
): CartEvent => {
  return trackCartEvent(analytics, 'item_removed', {
    productId,
    quantity,
    value
  });
};

export const trackQuantityUpdated = (
  analytics: CartAnalytics,
  productId: number,
  oldQuantity: number,
  newQuantity: number,
  value: number
): CartEvent => {
  return trackCartEvent(analytics, 'quantity_updated', {
    productId,
    oldQuantity,
    newQuantity,
    quantity: newQuantity - oldQuantity,
    value
  });
};

export const trackCartViewed = (
  analytics: CartAnalytics,
  totalValue: number,
  itemCount: number
): CartEvent => {
  return trackCartEvent(analytics, 'cart_viewed', {
    value: totalValue,
    itemCount
  });
};

export const trackCheckoutStarted = (
  analytics: CartAnalytics,
  totalValue: number,
  itemCount: number
): CartEvent => {
  return trackCartEvent(analytics, 'checkout_started', {
    value: totalValue,
    itemCount
  });
};

export const trackCartAbandoned = (
  analytics: CartAnalytics,
  reason?: string
): CartEvent => {
  return trackCartEvent(analytics, 'cart_abandoned', {
    reason,
    value: analytics.totalValue,
    itemCount: analytics.itemCount
  });
};

export const trackRecommendationViewed = (
  analytics: CartAnalytics,
  recommendationId: string,
  productId: number,
  reason: string
): CartEvent => {
  return trackCartEvent(analytics, 'recommendation_viewed', {
    recommendationId,
    productId,
    reason
  });
};

export const trackRecommendationAdded = (
  analytics: CartAnalytics,
  recommendationId: string,
  productId: number,
  value: number
): CartEvent => {
  return trackCartEvent(analytics, 'recommendation_added', {
    recommendationId,
    productId,
    value
  });
};

// Clean up old analytics data
export const cleanupOldAnalytics = (maxAge: number = 7): void => {
  try {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('cart_analytics_'));
    const cutoff = Date.now() - (maxAge * 24 * 60 * 60 * 1000);

    keys.forEach(key => {
      try {
        const data = JSON.parse(localStorage.getItem(key) || '{}');
        const startTime = new Date(data.startTime).getTime();
        
        if (startTime < cutoff) {
          localStorage.removeItem(key);
        }
      } catch (error) {
        // Remove corrupted data
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    // Silently handle cleanup errors
  }
};

// Export analytics data for reporting
export const exportAnalyticsData = (analytics: CartAnalytics) => {
  return {
    sessionId: analytics.sessionId,
    userId: analytics.userId,
    startTime: analytics.startTime.toISOString(),
    lastActivity: analytics.lastActivity.toISOString(),
    summary: getAnalyticsSummary(analytics),
    events: analytics.events.map(event => ({
      id: event.id,
      type: event.type,
      timestamp: event.timestamp.toISOString(),
      productId: event.productId,
      quantity: event.quantity,
      value: event.value,
      metadata: event.metadata
    }))
  };
};

// Initialize analytics cleanup on page load
if (typeof window !== 'undefined') {
  // Clean up old analytics data on initialization
  cleanupOldAnalytics();
}
