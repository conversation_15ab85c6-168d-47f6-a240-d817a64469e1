import React from 'react';
import { motion } from 'framer-motion';
import { Crown, ArrowRight, Star, Gift, TrendingUp, Award, Tag } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import LoyaltyProgram from '../components/loyalty/LoyaltyProgram';
import { LOYALTY_TIERS } from '../types/loyalty';

const LoyaltyPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-4"
          >
            <ArrowRight className="w-4 h-4" />
            بازگشت به خانه
          </button>
          
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="inline-flex items-center gap-3 mb-4"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">باشگاه مشتریان گلورویا</h1>
            </motion.div>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-gray-600 text-lg max-w-2xl mx-auto"
            >
              با هر خرید امتیاز کسب کنید، سطح عضویت خود را ارتقا دهید و از مزایای ویژه بهره‌مند شوید
            </motion.p>
          </div>
        </div>

        {/* Loyalty Program Component */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-12"
        >
          <LoyaltyProgram />
        </motion.div>

        {/* Tier Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg border border-gray-200 p-8 mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            سطوح عضویت باشگاه مشتریان
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {LOYALTY_TIERS.map((tier, index) => {
              const getTierGradient = () => {
                switch (tier.id) {
                  case 'bronze': return 'from-amber-400 to-amber-600';
                  case 'silver': return 'from-gray-400 to-gray-600';
                  case 'gold': return 'from-yellow-400 to-yellow-600';
                  case 'platinum': return 'from-purple-400 to-purple-600';
                  default: return 'from-gray-400 to-gray-600';
                }
              };

              return (
                <motion.div
                  key={tier.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  className="relative h-full"
                >
                  <div className={`bg-gradient-to-br ${getTierGradient()} rounded-lg p-6 text-white relative overflow-hidden h-full flex flex-col min-h-[320px]`}>
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-10">
                      <div className="absolute top-2 right-2 w-16 h-16 rounded-full border border-white"></div>
                      <div className="absolute bottom-2 left-2 w-12 h-12 rounded-full border border-white"></div>
                    </div>

                    <div className="relative z-10 flex flex-col h-full">
                      <div className="text-center mb-4">
                        <div className="text-3xl mb-2">{tier.icon}</div>
                        <h3 className="text-xl font-bold">{tier.persianName}</h3>
                        <p className="text-sm opacity-90">{tier.name}</p>
                      </div>

                      <div className="text-center mb-4">
                        <p className="text-sm opacity-75">حداقل امتیاز</p>
                        <p className="text-lg font-bold">
                          {tier.minPoints.toLocaleString('fa-IR')}
                          {tier.maxPoints && ` - ${tier.maxPoints.toLocaleString('fa-IR')}`}
                        </p>
                      </div>

                      <div className="space-y-2 text-sm flex-grow">
                        {tier.discountPercentage > 0 && (
                          <div className="flex items-center gap-2">
                            <Tag className="w-3 h-3 flex-shrink-0" />
                            <span>{tier.discountPercentage}% تخفیف همیشگی</span>
                          </div>
                        )}

                        <div className="flex items-center gap-2">
                          <Star className="w-3 h-3 flex-shrink-0" />
                          <span>
                            ضریب امتیاز: {
                              tier.level === 1 ? '۱' :
                              tier.level === 2 ? '۱.۵' :
                              tier.level === 3 ? '۲' : '۳'
                            }×
                          </span>
                        </div>

                        <div className="flex items-center gap-2">
                          <Gift className="w-3 h-3 flex-shrink-0" />
                          <span>هدیه تولد: {tier.birthdayBonus} امتیاز</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* How It Works */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-lg border border-gray-200 p-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            چگونه کار می‌کند؟
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">۱. خرید کنید</h3>
              <p className="text-gray-600">
                با هر ۱۰ هزار تومان خرید، ۱ امتیاز کسب کنید. اعضای سطوح بالاتر امتیاز بیشتری دریافت می‌کنند.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">۲. سطح خود را ارتقا دهید</h3>
              <p className="text-gray-600">
                با کسب امتیاز بیشتر، سطح عضویت خود را ارتقا دهید و از مزایای بیشتری بهره‌مند شوید.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">۳. جوایز دریافت کنید</h3>
              <p className="text-gray-600">
                امتیازات خود را برای دریافت تخفیف‌ها، محصولات رایگان و تجربه‌های ویژه استفاده کنید.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default LoyaltyPage;
