import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Crown,
  Plus,
  Edit,
  Trash2,
  Users,
  TrendingUp,
  DollarSign,
  Star,
  Settings,
  Eye,
  BarChart3
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminTable, { AdminTableColumn } from '../../../components/admin/common/AdminTable';
import AdminModal, { AdminFormModal } from '../../../components/admin/common/AdminModal';
import { useAdminLoyalty } from '../../../hooks/useAdminLoyalty';
import { AdminLoyaltyTier, LoyaltyTierFormData } from '../../../types/adminLoyalty';
import { formatNumber as formatPersianNumber } from '../../../utils/formatters';

const TierManagementPage: React.FC = () => {
  const { tiers, loading, error, analytics } = useAdminLoyalty();
  const [selectedTier, setSelectedTier] = useState<AdminLoyaltyTier | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const [formData, setFormData] = useState<LoyaltyTierFormData>({
    name: '',
    persianName: '',
    level: 1,
    minPoints: 0,
    maxPoints: undefined,
    color: '#3B82F6',
    icon: '🥉',
    benefits: [],
    discountPercentage: 0,
    freeShippingThreshold: 0,
    birthdayBonus: 0,
    description: '',
    isActive: true
  });

  const handleCreateTier = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowCreateModal(false);
      setFormData({
        name: '',
        persianName: '',
        level: 1,
        minPoints: 0,
        maxPoints: undefined,
        color: '#3B82F6',
        icon: '🥉',
        benefits: [],
        discountPercentage: 0,
        freeShippingThreshold: 0,
        birthdayBonus: 0,
        description: '',
        isActive: true
      });
      // toast.success('سطح جدید با موفقیت ایجاد شد');
    } catch (error) {
      // toast.error('خطا در ایجاد سطح جدید');
    }
  };

  const handleEditTier = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowEditModal(false);
      setSelectedTier(null);
      // toast.success('سطح با موفقیت به‌روزرسانی شد');
    } catch (error) {
      // toast.error('خطا در به‌روزرسانی سطح');
    }
  };

  const handleDeleteTier = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowDeleteModal(false);
      setSelectedTier(null);
      // toast.success('سطح با موفقیت حذف شد');
    } catch (error) {
      // toast.error('خطا در حذف سطح');
    }
  };

  const openEditModal = (tier: AdminLoyaltyTier) => {
    setSelectedTier(tier);
    setFormData({
      name: tier.name,
      persianName: tier.persianName,
      level: tier.level,
      minPoints: tier.minPoints,
      maxPoints: tier.maxPoints,
      color: tier.color,
      icon: tier.icon,
      benefits: tier.benefits,
      discountPercentage: tier.discountPercentage,
      freeShippingThreshold: tier.freeShippingThreshold,
      birthdayBonus: tier.birthdayBonus,
      description: tier.description,
      isActive: tier.isActive
    });
    setShowEditModal(true);
  };

  const openDeleteModal = (tier: AdminLoyaltyTier) => {
    setSelectedTier(tier);
    setShowDeleteModal(true);
  };

  const openDetailsModal = (tier: AdminLoyaltyTier) => {
    setSelectedTier(tier);
    setShowDetailsModal(true);
  };

  const formatCurrency = (amount: number) => {
    return `${formatPersianNumber(amount.toLocaleString())} تومان`;
  };

  const formatPercentage = (value: number) => {
    return `${formatPersianNumber(value)}%`;
  };

  const columns: AdminTableColumn<AdminLoyaltyTier>[] = [
    {
      key: 'level',
      title: 'سطح',
      sortable: true,
      render: (tier) => (
        <div className="flex items-center gap-2">
          <span className="text-2xl">{tier.icon}</span>
          <span className="font-bold text-lg">{formatPersianNumber(tier.level)}</span>
        </div>
      )
    },
    {
      key: 'persianName',
      title: 'نام سطح',
      sortable: true,
      render: (tier) => (
        <div>
          <p className="font-medium text-gray-900">{tier.persianName}</p>
          <p className="text-sm text-gray-500">{tier.name}</p>
        </div>
      )
    },
    {
      key: 'pointRange',
      title: 'محدوده امتیاز',
      render: (tier) => (
        <div className="text-sm">
          <p className="font-medium">
            {formatPersianNumber(tier.minPoints)} 
            {tier.maxPoints ? ` - ${formatPersianNumber(tier.maxPoints)}` : '+'}
          </p>
          <p className="text-gray-500">امتیاز</p>
        </div>
      )
    },
    {
      key: 'memberCount',
      title: 'تعداد اعضا',
      sortable: true,
      render: (tier) => (
        <div className="flex items-center gap-2">
          <Users className="w-4 h-4 text-gray-400" />
          <span className="font-medium">{formatPersianNumber(tier.memberCount)}</span>
        </div>
      )
    },
    {
      key: 'benefits',
      title: 'مزایا',
      render: (tier) => (
        <div className="space-y-1">
          <div className="flex items-center gap-1 text-sm">
            <DollarSign className="w-3 h-3 text-green-500" />
            <span>{formatPercentage(tier.discountPercentage)} تخفیف</span>
          </div>
          <div className="flex items-center gap-1 text-sm">
            <Star className="w-3 h-3 text-yellow-500" />
            <span>{formatPersianNumber(tier.birthdayBonus)} امتیاز تولد</span>
          </div>
        </div>
      )
    },
    {
      key: 'performance',
      title: 'عملکرد',
      render: (tier) => (
        <div className="space-y-1">
          <div className="flex items-center gap-1 text-sm">
            <TrendingUp className="w-3 h-3 text-blue-500" />
            <span>{formatPercentage(tier.retentionRate)} نگهداری</span>
          </div>
          <div className="flex items-center gap-1 text-sm">
            <BarChart3 className="w-3 h-3 text-purple-500" />
            <span>{formatCurrency(tier.averageSpend)}</span>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      render: (tier) => (
        <span className={`
          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
          ${tier.isActive 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
          }
        `}>
          {tier.isActive ? 'فعال' : 'غیرفعال'}
        </span>
      )
    }
  ];

  const rowActions = (tier: AdminLoyaltyTier) => (
    <div className="flex items-center gap-2">
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Eye}
        onClick={() => openDetailsModal(tier)}
      >
        مشاهده
      </AdminButton>
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Edit}
        onClick={() => openEditModal(tier)}
      >
        ویرایش
      </AdminButton>
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Trash2}
        onClick={() => openDeleteModal(tier)}
        className="text-red-600 hover:text-red-700"
      >
        حذف
      </AdminButton>
    </div>
  );

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <AdminButton onClick={() => window.location.reload()}>
              تلاش مجدد
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <Crown className="w-8 h-8 text-admin-600" />
              مدیریت سطوح عضویت
            </h1>
            <p className="text-gray-600 mt-1">
              تنظیم و مدیریت سطوح مختلف باشگاه مشتریان
            </p>
          </div>
          
          <AdminButton
            variant="primary"
            icon={Plus}
            onClick={() => setShowCreateModal(true)}
          >
            افزودن سطح جدید
          </AdminButton>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">کل سطوح</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(tiers.length)}
                </p>
              </div>
              <Crown className="w-8 h-8 text-yellow-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">سطوح فعال</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(tiers.filter(t => t.isActive).length)}
                </p>
              </div>
              <Settings className="w-8 h-8 text-green-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">میانگین نگهداری</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPercentage(
                    tiers.reduce((sum, t) => sum + t.retentionRate, 0) / tiers.length
                  )}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">کل اعضا</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(
                    tiers.reduce((sum, t) => sum + t.memberCount, 0)
                  )}
                </p>
              </div>
              <Users className="w-8 h-8 text-purple-500" />
            </div>
          </AdminCard>
        </div>

        {/* Tiers Table */}
        <AdminCard title="لیست سطوح عضویت" icon={Crown}>
          <AdminTable
            columns={columns}
            data={tiers}
            loading={loading}
            rowActions={rowActions}
            emptyMessage="هیچ سطحی یافت نشد"
          />
        </AdminCard>

        {/* Create Tier Modal */}
        <AdminFormModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateTier}
          title="افزودن سطح جدید"
          subtitle="اطلاعات سطح عضویت جدید را وارد کنید"
          submitText="ایجاد سطح"
          size="lg"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نام فارسی *
              </label>
              <input
                type="text"
                value={formData.persianName}
                onChange={(e) => setFormData(prev => ({ ...prev, persianName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="مثال: برنزی"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نام انگلیسی *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="مثال: Bronze"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                سطح *
              </label>
              <input
                type="number"
                value={formData.level}
                onChange={(e) => setFormData(prev => ({ ...prev, level: parseInt(e.target.value) || 1 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                min="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                آیکون
              </label>
              <input
                type="text"
                value={formData.icon}
                onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="🥉"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                حداقل امتیاز *
              </label>
              <input
                type="number"
                value={formData.minPoints}
                onChange={(e) => setFormData(prev => ({ ...prev, minPoints: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                حداکثر امتیاز
              </label>
              <input
                type="number"
                value={formData.maxPoints || ''}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  maxPoints: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                درصد تخفیف
              </label>
              <input
                type="number"
                value={formData.discountPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, discountPercentage: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                min="0"
                max="100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                امتیاز جایزه تولد
              </label>
              <input
                type="number"
                value={formData.birthdayBonus}
                onChange={(e) => setFormData(prev => ({ ...prev, birthdayBonus: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                min="0"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              توضیحات
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              rows={3}
              placeholder="توضیحات سطح عضویت..."
            />
          </div>

          <div className="mt-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={formData.isActive}
                onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                className="rounded border-gray-300 text-admin-600 focus:ring-admin-500"
              />
              <span className="text-sm font-medium text-gray-700">سطح فعال باشد</span>
            </label>
          </div>
        </AdminFormModal>

        {/* Edit Modal - Similar structure to Create Modal */}
        {/* Delete Confirmation Modal */}
        <AdminModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          title="حذف سطح عضویت"
          size="sm"
        >
          <div className="text-center py-4">
            <Trash2 className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-2">
              آیا مطمئن هستید؟
            </p>
            <p className="text-gray-600 mb-6">
              سطح "{selectedTier?.persianName}" و تمام اطلاعات مرتبط با آن حذف خواهد شد.
            </p>
            <div className="flex gap-3 justify-center">
              <AdminButton
                variant="outline"
                onClick={() => setShowDeleteModal(false)}
              >
                انصراف
              </AdminButton>
              <AdminButton
                variant="danger"
                onClick={handleDeleteTier}
              >
                حذف سطح
              </AdminButton>
            </div>
          </div>
        </AdminModal>
      </div>
    </AdminLayout>
  );
};

export default TierManagementPage;
