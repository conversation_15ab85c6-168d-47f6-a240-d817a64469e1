import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Gift,
  Users,
  TrendingUp,
  Award,
  Star,
  DollarSign,
  Activity,
  Target,
  Crown,
  Sparkles,
  BarChart3,
  PieChart,
  Calendar,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard, { AdminStatsCard, AdminActionCard } from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminLoyalty } from '../../../hooks/useAdminLoyalty';
import { formatNumber as formatPersianNumber } from '../../../utils/formatters';

const LoyaltyDashboardPage: React.FC = () => {
  const { analytics, loading, error, tiers, members, rewards } = useAdminLoyalty();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  // Safety check for analytics data
  if (!analytics && !loading && !error) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">در حال بارگذاری اطلاعات...</p>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <AdminButton onClick={() => window.location.reload()}>
              تلاش مجدد
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  const formatCurrency = (amount: number) => {
    return `${formatPersianNumber(amount.toLocaleString())} تومان`;
  };

  const formatPercentage = (value: number) => {
    return `${formatPersianNumber(value.toFixed(1))}%`;
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <Gift className="w-8 h-8 text-admin-600" />
              داشبورد باشگاه مشتریان
            </h1>
            <p className="text-gray-600 mt-1">
              مدیریت و نظارت بر برنامه وفاداری مشتریان
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
            >
              <option value="week">هفته گذشته</option>
              <option value="month">ماه گذشته</option>
              <option value="quarter">سه ماه گذشته</option>
              <option value="year">سال گذشته</option>
            </select>
            
            <AdminButton variant="primary" icon={BarChart3}>
              گزارش تفصیلی
            </AdminButton>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdminStatsCard
            title="کل اعضا"
            value={formatPersianNumber(analytics.overview.totalMembers)}
            unit="نفر"
            change={`+${formatPersianNumber(analytics.overview.newMembersThisMonth)} این ماه`}
            changeType="increase"
            icon={Users}
            color="bg-blue-500"
            loading={loading}
          />
          
          <AdminStatsCard
            title="اعضای فعال"
            value={formatPersianNumber(analytics.overview.activeMembers)}
            unit="نفر"
            change={formatPercentage(analytics.overview.totalMembers > 0 ? (analytics.overview.activeMembers / analytics.overview.totalMembers) * 100 : 0)}
            changeType="increase"
            icon={Activity}
            color="bg-green-500"
            loading={loading}
          />
          
          <AdminStatsCard
            title="کل امتیازات صادر شده"
            value={formatPersianNumber(analytics.overview.totalPointsIssued)}
            unit="امتیاز"
            change={formatPercentage(analytics.overview.memberGrowthRate)}
            changeType="increase"
            icon={Star}
            color="bg-yellow-500"
            loading={loading}
          />
          
          <AdminStatsCard
            title="نرخ استفاده"
            value={formatPercentage(analytics.overview.redemptionRate)}
            change={`${formatPersianNumber(analytics.overview.totalPointsRedeemed)} استفاده شده`}
            changeType="increase"
            icon={Target}
            color="bg-purple-500"
            loading={loading}
          />
        </div>

        {/* Financial Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <AdminCard title="وضعیت مالی برنامه" icon={DollarSign} className="lg:col-span-2">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                  <div>
                    <p className="text-sm text-gray-600">کل بدهی امتیازات</p>
                    <p className="text-xl font-bold text-red-600">
                      {formatCurrency(analytics.financial.totalLiability)}
                    </p>
                  </div>
                  <ArrowUpRight className="w-8 h-8 text-red-500" />
                </div>
                
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <div>
                    <p className="text-sm text-gray-600">هزینه ماهانه</p>
                    <p className="text-xl font-bold text-blue-600">
                      {formatCurrency(analytics.financial.monthlyLiabilityCost)}
                    </p>
                  </div>
                  <Calendar className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                  <div>
                    <p className="text-sm text-gray-600">بازگشت سرمایه</p>
                    <p className="text-xl font-bold text-green-600">
                      {formatPercentage(analytics.financial.programROI)}
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-green-500" />
                </div>
                
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm text-gray-600">هزینه هر عضو</p>
                    <p className="text-xl font-bold text-gray-600">
                      {formatCurrency(analytics.financial.costPerMember)}
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-gray-500" />
                </div>
              </div>
            </div>
          </AdminCard>

          <AdminCard title="عملکرد امتیازات" icon={Sparkles}>
            <div className="space-y-4">
              <div className="text-center p-4 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg">
                <Star className="w-12 h-12 text-yellow-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(analytics.engagement.pointsEarnedThisMonth)}
                </p>
                <p className="text-sm text-gray-600">امتیاز کسب شده این ماه</p>
              </div>
              
              <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg">
                <Gift className="w-12 h-12 text-purple-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(analytics.engagement.pointsRedeemedThisMonth)}
                </p>
                <p className="text-sm text-gray-600">امتیاز استفاده شده این ماه</p>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Tier Distribution & Top Rewards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AdminCard title="توزیع سطوح عضویت" icon={Crown}>
            <div className="space-y-4">
              {Object.entries(analytics.tiers).map(([tierId, tierData]) => {
                const tier = tiers.find(t => t.id === tierId);
                if (!tier) return null;
                
                return (
                  <div key={tierId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full`} style={{ backgroundColor: tier.color }}></div>
                      <div>
                        <p className="font-medium text-gray-900">{tier.persianName}</p>
                        <p className="text-sm text-gray-600">
                          میانگین خرید: {formatCurrency(tierData.averageSpend)}
                        </p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-gray-900">
                        {formatPersianNumber(tierData.memberCount)} نفر
                      </p>
                      <p className="text-sm text-gray-600">
                        {formatPercentage(tierData.percentage)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </AdminCard>

          <AdminCard title="محبوب‌ترین جوایز" icon={Award}>
            <div className="space-y-3">
              {analytics.engagement.topRewards.map((reward, index) => (
                <div key={reward.rewardId} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm
                      ${index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : index === 2 ? 'bg-orange-500' : 'bg-gray-300'}
                    `}>
                      {formatPersianNumber(index + 1)}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{reward.title}</p>
                      <p className="text-sm text-gray-600">
                        {formatPersianNumber(reward.redemptions)} بار استفاده
                      </p>
                    </div>
                  </div>
                  <TrendingUp className="w-5 h-5 text-green-500" />
                </div>
              ))}
            </div>
          </AdminCard>
        </div>

        {/* Quick Actions */}
        <AdminCard title="عملیات سریع" icon={Activity}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <AdminActionCard
              title="مدیریت اعضا"
              description="مشاهده و مدیریت اعضای باشگاه"
              icon={Users}
              onClick={() => window.location.href = '/admin/loyalty/members'}
            />
            
            <AdminActionCard
              title="مدیریت سطوح"
              description="تنظیم سطوح عضویت و مزایا"
              icon={Crown}
              onClick={() => window.location.href = '/admin/loyalty/tiers'}
            />
            
            <AdminActionCard
              title="مدیریت جوایز"
              description="ایجاد و ویرایش جوایز"
              icon={Gift}
              onClick={() => window.location.href = '/admin/loyalty/rewards'}
            />
            
            <AdminActionCard
              title="تراکنش‌های امتیاز"
              description="مشاهده تاریخچه امتیازات"
              icon={Activity}
              onClick={() => window.location.href = '/admin/loyalty/transactions'}
            />
          </div>
        </AdminCard>

        {/* Recent Activity */}
        <AdminCard title="فعالیت‌های اخیر" icon={Activity}>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 border-r-4 border-green-500 bg-green-50 rounded">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900">عضو جدید به سطح طلایی ارتقا یافت</p>
                  <p className="text-sm text-gray-600">۵ دقیقه پیش</p>
                </div>
              </div>
              <ArrowUpRight className="w-5 h-5 text-green-500" />
            </div>
            
            <div className="flex items-center justify-between p-3 border-r-4 border-blue-500 bg-blue-50 rounded">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900">جایزه جدید "کیت مراقبت پوست" اضافه شد</p>
                  <p className="text-sm text-gray-600">۱۵ دقیقه پیش</p>
                </div>
              </div>
              <Gift className="w-5 h-5 text-blue-500" />
            </div>
            
            <div className="flex items-center justify-between p-3 border-r-4 border-purple-500 bg-purple-50 rounded">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900">۱۰۰ امتیاز جایزه تولد به اعضا اعطا شد</p>
                  <p className="text-sm text-gray-600">۳۰ دقیقه پیش</p>
                </div>
              </div>
              <Sparkles className="w-5 h-5 text-purple-500" />
            </div>
          </div>
        </AdminCard>
      </div>
    </AdminLayout>
  );
};

export default LoyaltyDashboardPage;
