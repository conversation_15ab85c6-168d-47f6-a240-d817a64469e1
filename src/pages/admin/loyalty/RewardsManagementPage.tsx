import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Gift,
  Plus,
  Edit,
  Trash2,
  Eye,
  Star,
  Package,
  Truck,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminTable, { AdminTableColumn } from '../../../components/admin/common/AdminTable';
import AdminModal, { AdminFormModal } from '../../../components/admin/common/AdminModal';
import { useAdminLoyalty } from '../../../hooks/useAdminLoyalty';
import { AdminLoyaltyReward, LoyaltyRewardFormData, LoyaltyRewardFilters } from '../../../types/adminLoyalty';
import { formatNumber as formatPersianNumber } from '../../../utils/formatters';

const RewardsManagementPage: React.FC = () => {
  const { rewards, loading, error } = useAdminLoyalty();
  const [selectedReward, setSelectedReward] = useState<AdminLoyaltyReward | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [filters, setFilters] = useState<LoyaltyRewardFilters>({});

  const [formData, setFormData] = useState<LoyaltyRewardFormData>({
    title: '',
    description: '',
    pointsCost: 0,
    type: 'discount',
    value: 0,
    isAvailable: true,
    stock: undefined,
    image: '',
    validUntil: '',
    terms: [],
    targetTiers: [],
    maxRedemptionsPerMember: undefined
  });

  const handleCreateReward = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowCreateModal(false);
      resetForm();
    } catch (error) {
      // Handle error
    }
  };

  const handleEditReward = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowEditModal(false);
      setSelectedReward(null);
    } catch (error) {
      // Handle error
    }
  };

  const handleDeleteReward = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowDeleteModal(false);
      setSelectedReward(null);
    } catch (error) {
      // Handle error
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      pointsCost: 0,
      type: 'discount',
      value: 0,
      isAvailable: true,
      stock: undefined,
      image: '',
      validUntil: '',
      terms: [],
      targetTiers: [],
      maxRedemptionsPerMember: undefined
    });
  };

  const openEditModal = (reward: AdminLoyaltyReward) => {
    setSelectedReward(reward);
    setFormData({
      title: reward.title,
      description: reward.description,
      pointsCost: reward.pointsCost,
      type: reward.type,
      value: reward.value,
      isAvailable: reward.isAvailable,
      stock: reward.stock,
      image: reward.image,
      validUntil: reward.validUntil || '',
      terms: reward.terms,
      targetTiers: [],
      maxRedemptionsPerMember: undefined
    });
    setShowEditModal(true);
  };

  const openDeleteModal = (reward: AdminLoyaltyReward) => {
    setSelectedReward(reward);
    setShowDeleteModal(true);
  };

  const openDetailsModal = (reward: AdminLoyaltyReward) => {
    setSelectedReward(reward);
    setShowDetailsModal(true);
  };

  const formatCurrency = (amount: number) => {
    return `${formatPersianNumber(amount.toLocaleString())} تومان`;
  };

  const getRewardTypeIcon = (type: string) => {
    switch (type) {
      case 'discount': return <DollarSign className="w-4 h-4" />;
      case 'product': return <Package className="w-4 h-4" />;
      case 'shipping': return <Truck className="w-4 h-4" />;
      case 'experience': return <Star className="w-4 h-4" />;
      default: return <Gift className="w-4 h-4" />;
    }
  };

  const getRewardTypeName = (type: string) => {
    switch (type) {
      case 'discount': return 'تخفیف';
      case 'product': return 'محصول';
      case 'shipping': return 'ارسال';
      case 'experience': return 'تجربه';
      default: return 'نامشخص';
    }
  };

  const getStatusIcon = (reward: AdminLoyaltyReward) => {
    if (!reward.isActive) {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }
    if (reward.inventory && reward.inventory.availableStock <= reward.inventory.lowStockThreshold) {
      return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getStatusText = (reward: AdminLoyaltyReward) => {
    if (!reward.isActive) return 'غیرفعال';
    if (reward.inventory && reward.inventory.availableStock <= 0) return 'تمام شده';
    if (reward.inventory && reward.inventory.availableStock <= reward.inventory.lowStockThreshold) return 'موجودی کم';
    return 'فعال';
  };

  const columns: AdminTableColumn<AdminLoyaltyReward>[] = [
    {
      key: 'title',
      title: 'جایزه',
      sortable: true,
      render: (reward) => (
        <div className="flex items-center gap-3">
          {reward.image && (
            <img 
              src={reward.image} 
              alt={reward.title}
              className="w-12 h-12 rounded-lg object-cover"
            />
          )}
          <div>
            <p className="font-medium text-gray-900">{reward.title}</p>
            <p className="text-sm text-gray-500 line-clamp-1">{reward.description}</p>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      title: 'نوع',
      render: (reward) => (
        <div className="flex items-center gap-2">
          {getRewardTypeIcon(reward.type)}
          <span className="text-sm">{getRewardTypeName(reward.type)}</span>
        </div>
      )
    },
    {
      key: 'pointsCost',
      title: 'هزینه امتیاز',
      sortable: true,
      render: (reward) => (
        <div className="text-center">
          <p className="font-bold text-purple-600">
            {formatPersianNumber(reward.pointsCost)}
          </p>
          <p className="text-xs text-gray-500">امتیاز</p>
        </div>
      )
    },
    {
      key: 'value',
      title: 'ارزش',
      sortable: true,
      render: (reward) => (
        <div className="text-center">
          <p className="font-medium text-green-600">
            {formatCurrency(reward.value)}
          </p>
        </div>
      )
    },
    {
      key: 'redemptions',
      title: 'استفاده',
      sortable: true,
      render: (reward) => (
        <div className="text-center">
          <p className="font-bold text-blue-600">
            {formatPersianNumber(reward.redemptionCount)}
          </p>
          <p className="text-xs text-gray-500">بار</p>
        </div>
      )
    },
    {
      key: 'inventory',
      title: 'موجودی',
      render: (reward) => (
        <div className="text-center">
          {reward.inventory ? (
            <>
              <p className="font-medium">
                {formatPersianNumber(reward.inventory.availableStock)}
              </p>
              <p className="text-xs text-gray-500">
                از {formatPersianNumber(reward.inventory.totalStock)}
              </p>
            </>
          ) : (
            <span className="text-gray-400">نامحدود</span>
          )}
        </div>
      )
    },
    {
      key: 'performance',
      title: 'عملکرد',
      render: (reward) => (
        <div className="space-y-1">
          <div className="flex items-center gap-1 text-sm">
            <TrendingUp className="w-3 h-3 text-blue-500" />
            <span>{formatPersianNumber(reward.analytics.redemptionsThisMonth)} این ماه</span>
          </div>
          <div className="flex items-center gap-1 text-sm">
            <Star className="w-3 h-3 text-yellow-500" />
            <span>{formatPersianNumber(reward.analytics.averageRating.toFixed(1))}</span>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      render: (reward) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(reward)}
          <span className="text-sm">{getStatusText(reward)}</span>
        </div>
      )
    }
  ];

  const rowActions = (reward: AdminLoyaltyReward) => (
    <div className="flex items-center gap-2">
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Eye}
        onClick={() => openDetailsModal(reward)}
      >
        مشاهده
      </AdminButton>
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Edit}
        onClick={() => openEditModal(reward)}
      >
        ویرایش
      </AdminButton>
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Trash2}
        onClick={() => openDeleteModal(reward)}
        className="text-red-600 hover:text-red-700"
      >
        حذف
      </AdminButton>
    </div>
  );

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <AdminButton onClick={() => window.location.reload()}>
              تلاش مجدد
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  // Safety check for rewards data
  if (!rewards || rewards.length === 0) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">هیچ جایزه‌ای یافت نشد</p>
            <AdminButton onClick={() => setShowCreateModal(true)}>
              ایجاد اولین جایزه
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  const activeRewards = rewards.filter(r => r.isActive).length;
  const totalRedemptions = rewards.reduce((sum, r) => sum + r.redemptionCount, 0);
  const totalValue = rewards.reduce((sum, r) => sum + (r.redemptionCount * r.value), 0);
  const lowStockRewards = rewards.filter(r => 
    r.inventory && r.inventory.availableStock <= r.inventory.lowStockThreshold
  ).length;

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <Gift className="w-8 h-8 text-admin-600" />
              مدیریت جوایز و تخفیف‌ها
            </h1>
            <p className="text-gray-600 mt-1">
              ایجاد و مدیریت جوایز قابل استفاده با امتیاز
            </p>
          </div>
          
          <AdminButton
            variant="primary"
            icon={Plus}
            onClick={() => setShowCreateModal(true)}
          >
            افزودن جایزه جدید
          </AdminButton>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">کل جوایز</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(rewards.length)}
                </p>
              </div>
              <Gift className="w-8 h-8 text-purple-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">جوایز فعال</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(activeRewards)}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">کل استفاده</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(totalRedemptions)}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">موجودی کم</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(lowStockRewards)}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-yellow-500" />
            </div>
          </AdminCard>
        </div>

        {/* Filters */}
        <AdminCard>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                جستجو
              </label>
              <input
                type="text"
                value={filters.search || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="نام جایزه..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع جایزه
              </label>
              <select
                value={filters.type?.[0] || ''}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  type: e.target.value ? [e.target.value as any] : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              >
                <option value="">همه انواع</option>
                <option value="discount">تخفیف</option>
                <option value="product">محصول</option>
                <option value="shipping">ارسال</option>
                <option value="experience">تجربه</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وضعیت
              </label>
              <select
                value={filters.status?.[0] || ''}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  status: e.target.value ? [e.target.value as any] : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              >
                <option value="">همه وضعیت‌ها</option>
                <option value="active">فعال</option>
                <option value="inactive">غیرفعال</option>
                <option value="out_of_stock">تمام شده</option>
              </select>
            </div>

            <div className="flex items-end">
              <AdminButton
                variant="outline"
                onClick={() => setFilters({})}
                className="w-full"
              >
                پاک کردن فیلترها
              </AdminButton>
            </div>
          </div>
        </AdminCard>

        {/* Rewards Table */}
        <AdminCard title="لیست جوایز" icon={Gift}>
          <AdminTable
            columns={columns}
            data={rewards}
            loading={loading}
            rowActions={rowActions}
            emptyMessage="هیچ جایزه‌ای یافت نشد"
          />
        </AdminCard>

        {/* Create Reward Modal */}
        <AdminFormModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateReward}
          title="افزودن جایزه جدید"
          subtitle="اطلاعات جایزه جدید را وارد کنید"
          submitText="ایجاد جایزه"
          size="lg"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                عنوان جایزه *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="مثال: تخفیف ۲۰ درصدی"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع جایزه *
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              >
                <option value="discount">تخفیف</option>
                <option value="product">محصول</option>
                <option value="shipping">ارسال رایگان</option>
                <option value="experience">تجربه</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                هزینه امتیاز *
              </label>
              <input
                type="number"
                value={formData.pointsCost}
                onChange={(e) => setFormData(prev => ({ ...prev, pointsCost: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                min="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ارزش (تومان) *
              </label>
              <input
                type="number"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                min="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                موجودی (اختیاری)
              </label>
              <input
                type="number"
                value={formData.stock || ''}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  stock: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                min="0"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              توضیحات *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              rows={3}
              placeholder="توضیحات جایزه..."
            />
          </div>

          <div className="mt-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={formData.isAvailable}
                onChange={(e) => setFormData(prev => ({ ...prev, isAvailable: e.target.checked }))}
                className="rounded border-gray-300 text-admin-600 focus:ring-admin-500"
              />
              <span className="text-sm font-medium text-gray-700">جایزه فعال باشد</span>
            </label>
          </div>
        </AdminFormModal>

        {/* Delete Confirmation Modal */}
        <AdminModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          title="حذف جایزه"
          size="sm"
        >
          <div className="text-center py-4">
            <Trash2 className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-2">
              آیا مطمئن هستید؟
            </p>
            <p className="text-gray-600 mb-6">
              جایزه "{selectedReward?.title}" و تمام اطلاعات مرتبط با آن حذف خواهد شد.
            </p>
            <div className="flex gap-3 justify-center">
              <AdminButton
                variant="outline"
                onClick={() => setShowDeleteModal(false)}
              >
                انصراف
              </AdminButton>
              <AdminButton
                variant="danger"
                onClick={handleDeleteReward}
              >
                حذف جایزه
              </AdminButton>
            </div>
          </div>
        </AdminModal>
      </div>
    </AdminLayout>
  );
};

export default RewardsManagementPage;
