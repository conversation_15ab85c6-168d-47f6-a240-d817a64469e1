import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Star,
  User,
  Calendar,
  MessageSquare,
  Flag,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ArrowLeft,
  Edit,
  Trash2,
  Pin,
  Eye,
  EyeOff,
  ThumbsUp,
  ThumbsDown,
  Reply,
  Shield,
  Package,
  Image as ImageIcon
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminFormModal } from '../../../components/admin/common/AdminModal';
import { useAdminReviews } from '../../../hooks/useAdminReviews';
import { AdminReview, ReviewModerationDecision, PERSIAN_REVIEW_ADMIN_MESSAGES } from '../../../types/adminReview';
import { formatPersianDateTime, formatPersianDate } from '../../../utils/reviewModeration';
import toast from 'react-hot-toast';

const ReviewDetailsPage: React.FC = () => {
  const { reviewId } = useParams<{ reviewId: string }>();
  const navigate = useNavigate();
  const { getReviewById, moderateReview, addAdminNote, respondToReview } = useAdminReviews();
  
  const [review, setReview] = useState<AdminReview | null>(null);
  const [showModerationModal, setShowModerationModal] = useState(false);
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [moderationDecision, setModerationDecision] = useState<ReviewModerationDecision>({
    action: 'approve',
    notes: '',
    notifyCustomer: true
  });
  const [adminNote, setAdminNote] = useState('');
  const [businessResponse, setBusinessResponse] = useState({
    content: '',
    isPublic: true
  });

  useEffect(() => {
    if (reviewId) {
      // In a real app, this would fetch from API
      // For now, we'll simulate getting review data
      const mockReview: AdminReview = {
        id: reviewId,
        productId: 1,
        userId: 'user-1',
        userName: 'سارا احمدی',
        userAvatar: '',
        rating: 4,
        title: 'محصول عالی و با کیفیت',
        comment: 'این محصول واقعاً فوق‌العاده است. کیفیت بسیار بالایی دارد و نتیجه‌اش عالی بوده. به همه توصیه می‌کنم.',
        pros: ['کیفیت عالی', 'نتیجه سریع', 'بسته‌بندی مناسب'],
        cons: ['قیمت کمی بالا'],
        isVerifiedPurchase: true,
        helpfulVotes: 12,
        totalVotes: 15,
        images: ['/api/placeholder/300/300', '/api/placeholder/300/300'],
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z',
        isRecommended: true,
        skinType: 'مختلط',
        ageRange: '25-35',
        usageDuration: '2 ماه',
        moderationStatus: 'pending',
        
        // Admin-specific fields
        moderation: {
          status: 'pending',
          contentFlags: [],
          qualityScore: 85,
          spamScore: 15,
          sentimentScore: 0.8
        },
        customer: {
          id: 'user-1',
          name: 'سارا احمدی',
          email: '<EMAIL>',
          totalReviews: 8,
          averageRating: 4.2,
          isVerifiedCustomer: true,
          registrationDate: '2023-06-15',
          lastOrderDate: '2024-01-10',
          loyaltyTier: 'gold'
        },
        product: {
          id: 1,
          name: 'کرم آبرسان روزانه',
          brand: 'گلو رویا',
          category: 'مراقبت از پوست',
          image: '/api/placeholder/100/100',
          currentPrice: 250000,
          averageRating: 4.3,
          totalReviews: 156,
          isActive: true
        },
        analytics: {
          viewCount: 45,
          helpfulnessRatio: 0.8,
          reportCount: 0,
          responseTime: 120,
          engagementScore: 85
        },
        adminNotes: [],
        reports: [],
        isHighlighted: false,
        isPinned: false,
        isHidden: false,
        lastModifiedAt: '2024-01-15T10:30:00Z'
      };
      
      setReview(mockReview);
    }
  }, [reviewId]);

  const handleModeration = async () => {
    if (!review) return;

    try {
      await moderateReview(review.id, moderationDecision);
      toast.success('نظر با موفقیت بررسی شد');
      setShowModerationModal(false);
      // Refresh review data
    } catch (error) {
      toast.error('خطا در بررسی نظر');
    }
  };

  const handleAddNote = async () => {
    if (!review || !adminNote.trim()) return;

    try {
      await addAdminNote(review.id, {
        content: adminNote,
        isPrivate: true
      });
      toast.success('یادداشت اضافه شد');
      setShowNoteModal(false);
      setAdminNote('');
    } catch (error) {
      toast.error('خطا در افزودن یادداشت');
    }
  };

  const handleBusinessResponse = async () => {
    if (!review || !businessResponse.content.trim()) return;

    try {
      await respondToReview(review.id, businessResponse);
      toast.success('پاسخ ارسال شد');
      setShowResponseModal(false);
      setBusinessResponse({ content: '', isPublic: true });
    } catch (error) {
      toast.error('خطا در ارسال پاسخ');
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'flagged':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getQualityScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!review) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title={`جزئیات نظر #${review.id}`}
      subtitle="مدیریت و بررسی نظر مشتری"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <AdminButton
            variant="outline"
            onClick={() => navigate('/admin/reviews')}
            icon={ArrowLeft}
          >
            بازگشت به لیست نظرات
          </AdminButton>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              onClick={() => setShowNoteModal(true)}
              icon={Edit}
            >
              افزودن یادداشت
            </AdminButton>
            <AdminButton
              variant="outline"
              onClick={() => setShowResponseModal(true)}
              icon={Reply}
            >
              پاسخ کسب‌وکار
            </AdminButton>
            <AdminButton
              onClick={() => setShowModerationModal(true)}
              icon={Shield}
            >
              بررسی نظر
            </AdminButton>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Review Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Review Header */}
            <AdminCard>
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold">
                    {review.customer.name.charAt(0)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {review.customer.name}
                    </h3>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="w-4 h-4" />
                      <span>{formatPersianDate(review.createdAt)}</span>
                      {review.isVerifiedPurchase && (
                        <>
                          <span>•</span>
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="w-4 h-4" />
                            <span>خرید تایید شده</span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(review.moderation.status)}`}>
                    {review.moderation.status === 'pending' ? 'در انتظار بررسی' :
                     review.moderation.status === 'approved' ? 'تایید شده' :
                     review.moderation.status === 'rejected' ? 'رد شده' : 'علامت‌گذاری شده'}
                  </span>
                  {review.isPinned && <Pin className="w-4 h-4 text-blue-500" />}
                  {review.isHighlighted && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  {renderStars(review.rating)}
                  <span className="text-lg font-semibold text-gray-900">{review.rating}/5</span>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    {review.title}
                  </h4>
                  <p className="text-gray-700 leading-relaxed">
                    {review.comment}
                  </p>
                </div>

                {(review.pros.length > 0 || review.cons.length > 0) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {review.pros.length > 0 && (
                      <div>
                        <h5 className="font-medium text-green-700 mb-2 flex items-center gap-2">
                          <ThumbsUp className="w-4 h-4" />
                          نکات مثبت
                        </h5>
                        <ul className="space-y-1">
                          {review.pros.map((pro, index) => (
                            <li key={index} className="text-sm text-gray-700 flex items-center gap-2">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                              {pro}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {review.cons.length > 0 && (
                      <div>
                        <h5 className="font-medium text-red-700 mb-2 flex items-center gap-2">
                          <ThumbsDown className="w-4 h-4" />
                          نکات منفی
                        </h5>
                        <ul className="space-y-1">
                          {review.cons.map((con, index) => (
                            <li key={index} className="text-sm text-gray-700 flex items-center gap-2">
                              <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                              {con}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {review.images && review.images.length > 0 && (
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                      <ImageIcon className="w-4 h-4" />
                      تصاویر ({review.images.length})
                    </h5>
                    <div className="flex gap-2">
                      {review.images.map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`تصویر نظر ${index + 1}`}
                          className="w-20 h-20 object-cover rounded-lg border border-gray-200"
                        />
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <ThumbsUp className="w-4 h-4" />
                      <span>{review.helpfulVotes} مفید</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageSquare className="w-4 h-4" />
                      <span>{review.totalVotes} نظر</span>
                    </div>
                  </div>
                  
                  {review.isRecommended && (
                    <div className="flex items-center gap-1 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">توصیه می‌کند</span>
                    </div>
                  )}
                </div>
              </div>
            </AdminCard>

            {/* Business Response */}
            {review.businessResponse && (
              <AdminCard title="پاسخ کسب‌وکار">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Package className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-medium text-blue-900">
                        {review.businessResponse.respondedByName}
                      </span>
                    </div>
                    <span className="text-sm text-blue-600">
                      {formatPersianDateTime(review.businessResponse.respondedAt)}
                    </span>
                  </div>
                  <p className="text-blue-800">{review.businessResponse.content}</p>
                  {!review.businessResponse.isPublic && (
                    <div className="mt-2 flex items-center gap-1 text-sm text-blue-600">
                      <EyeOff className="w-4 h-4" />
                      <span>پاسخ خصوصی</span>
                    </div>
                  )}
                </div>
              </AdminCard>
            )}

            {/* Admin Notes */}
            {review.adminNotes.length > 0 && (
              <AdminCard title="یادداشت‌های مدیریت">
                <div className="space-y-3">
                  {review.adminNotes.map((note) => (
                    <div key={note.id} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-900">{note.createdByName}</span>
                        <span className="text-sm text-gray-600">
                          {formatPersianDateTime(note.createdAt)}
                        </span>
                      </div>
                      <p className="text-gray-700">{note.content}</p>
                    </div>
                  ))}
                </div>
              </AdminCard>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Product Info */}
            <AdminCard title="اطلاعات محصول">
              <div className="flex items-center gap-3 mb-4">
                <img
                  src={review.product.image}
                  alt={review.product.name}
                  className="w-16 h-16 object-cover rounded-lg border border-gray-200"
                />
                <div>
                  <h4 className="font-medium text-gray-900">{review.product.name}</h4>
                  <p className="text-sm text-gray-600">{review.product.brand}</p>
                  <p className="text-sm text-gray-600">{review.product.category}</p>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">میانگین امتیاز:</span>
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="font-medium">{review.product.averageRating}</span>
                  <span className="text-gray-500">({review.product.totalReviews})</span>
                </div>
              </div>
            </AdminCard>

            {/* Customer Info */}
            <AdminCard title="اطلاعات مشتری">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">نام:</span>
                  <span className="font-medium">{review.customer.name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">ایمیل:</span>
                  <span className="text-sm">{review.customer.email}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">کل نظرات:</span>
                  <span className="font-medium">{review.customer.totalReviews}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">میانگین امتیاز:</span>
                  <span className="font-medium">{review.customer.averageRating}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">عضویت:</span>
                  <span className="text-sm">{formatPersianDate(review.customer.registrationDate)}</span>
                </div>
                {review.customer.loyaltyTier && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">سطح وفاداری:</span>
                    <span className="font-medium capitalize">{review.customer.loyaltyTier}</span>
                  </div>
                )}
              </div>
            </AdminCard>

            {/* Moderation Info */}
            <AdminCard title="اطلاعات بررسی">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">امتیاز کیفیت:</span>
                  <span className={`font-medium ${getQualityScoreColor(review.moderation.qualityScore)}`}>
                    {review.moderation.qualityScore}/100
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">امتیاز اسپم:</span>
                  <span className={`font-medium ${review.moderation.spamScore > 50 ? 'text-red-600' : 'text-green-600'}`}>
                    {review.moderation.spamScore}/100
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">احساسات:</span>
                  <span className={`font-medium ${
                    review.moderation.sentimentScore > 0.5 ? 'text-green-600' :
                    review.moderation.sentimentScore < -0.5 ? 'text-red-600' : 'text-yellow-600'
                  }`}>
                    {review.moderation.sentimentScore > 0.5 ? 'مثبت' :
                     review.moderation.sentimentScore < -0.5 ? 'منفی' : 'خنثی'}
                  </span>
                </div>
                {review.moderation.moderatedBy && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">بررسی شده توسط:</span>
                      <span className="text-sm">{review.moderation.moderatedByName}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">تاریخ بررسی:</span>
                      <span className="text-sm">{formatPersianDate(review.moderation.moderatedAt!)}</span>
                    </div>
                  </>
                )}
              </div>
            </AdminCard>

            {/* Quick Actions */}
            <AdminCard title="عملیات سریع">
              <div className="space-y-2">
                <AdminButton
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  icon={review.isPinned ? Pin : Pin}
                >
                  {review.isPinned ? 'حذف پین' : 'پین کردن'}
                </AdminButton>
                <AdminButton
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  icon={review.isHighlighted ? Star : Star}
                >
                  {review.isHighlighted ? 'حذف برجسته‌سازی' : 'برجسته کردن'}
                </AdminButton>
                <AdminButton
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  icon={review.isHidden ? Eye : EyeOff}
                >
                  {review.isHidden ? 'نمایش' : 'مخفی کردن'}
                </AdminButton>
                <AdminButton
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-red-600 hover:text-red-700"
                  icon={Trash2}
                >
                  حذف نظر
                </AdminButton>
              </div>
            </AdminCard>
          </div>
        </div>
      </div>

      {/* Moderation Modal */}
      <AdminFormModal
        isOpen={showModerationModal}
        onClose={() => setShowModerationModal(false)}
        onSubmit={handleModeration}
        title="بررسی نظر"
        submitText="اعمال تصمیم"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تصمیم
            </label>
            <select
              value={moderationDecision.action}
              onChange={(e) => setModerationDecision(prev => ({ 
                ...prev, 
                action: e.target.value as any 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="approve">تایید</option>
              <option value="reject">رد</option>
              <option value="flag">علامت‌گذاری</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              یادداشت (اختیاری)
            </label>
            <textarea
              rows={3}
              value={moderationDecision.notes}
              onChange={(e) => setModerationDecision(prev => ({ 
                ...prev, 
                notes: e.target.value 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="دلیل تصمیم خود را بنویسید"
            />
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="notifyCustomer"
              checked={moderationDecision.notifyCustomer}
              onChange={(e) => setModerationDecision(prev => ({ 
                ...prev, 
                notifyCustomer: e.target.checked 
              }))}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <label htmlFor="notifyCustomer" className="mr-2 text-sm text-gray-700">
              اطلاع‌رسانی به مشتری
            </label>
          </div>
        </div>
      </AdminFormModal>

      {/* Add Note Modal */}
      <AdminFormModal
        isOpen={showNoteModal}
        onClose={() => setShowNoteModal(false)}
        onSubmit={handleAddNote}
        title="افزودن یادداشت"
        submitText="افزودن یادداشت"
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            یادداشت
          </label>
          <textarea
            rows={4}
            value={adminNote}
            onChange={(e) => setAdminNote(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="یادداشت خود را وارد کنید"
          />
        </div>
      </AdminFormModal>

      {/* Business Response Modal */}
      <AdminFormModal
        isOpen={showResponseModal}
        onClose={() => setShowResponseModal(false)}
        onSubmit={handleBusinessResponse}
        title="پاسخ کسب‌وکار"
        submitText="ارسال پاسخ"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              پاسخ
            </label>
            <textarea
              rows={4}
              value={businessResponse.content}
              onChange={(e) => setBusinessResponse(prev => ({ 
                ...prev, 
                content: e.target.value 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="پاسخ خود را وارد کنید"
            />
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isPublic"
              checked={businessResponse.isPublic}
              onChange={(e) => setBusinessResponse(prev => ({ 
                ...prev, 
                isPublic: e.target.checked 
              }))}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <label htmlFor="isPublic" className="mr-2 text-sm text-gray-700">
              نمایش عمومی پاسخ
            </label>
          </div>
        </div>
      </AdminFormModal>
    </AdminLayout>
  );
};

export default ReviewDetailsPage;
