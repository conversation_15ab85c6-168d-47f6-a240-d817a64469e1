import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Star,
  MessageSquare,
  Clock,
  CheckCircle,
  XCircle,
  Flag,
  AlertTriangle,
  Download,
  RefreshCw,
  Calendar,
  Filter,
  Users,
  Package,
  ThumbsUp,
  Eye
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminReviews } from '../../../hooks/useAdminReviews';
import { formatPersianNumber, formatPersianDate } from '../../../utils/reviewModeration';

const ReviewAnalyticsPage: React.FC = () => {
  const { reviews, getAnalytics } = useAdminReviews();
  const [dateRange, setDateRange] = useState('30'); // days
  const [refreshing, setRefreshing] = useState(false);

  const analytics = getAnalytics();

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleExport = () => {
    // Export analytics data
    console.log('Exporting review analytics data');
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    change?: number;
    icon: React.ComponentType<{ className?: string }>;
    color: string;
    subtitle?: string;
  }> = ({ title, value, change, icon: Icon, color, subtitle }) => (
    <AdminCard>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500">{subtitle}</p>
          )}
          {change !== undefined && (
            <div className={`flex items-center mt-1 ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change >= 0 ? (
                <TrendingUp className="w-4 h-4 ml-1" />
              ) : (
                <TrendingDown className="w-4 h-4 ml-1" />
              )}
              <span className="text-sm font-medium">
                {Math.abs(change)}% نسبت به ماه قبل
              </span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </AdminCard>
  );

  const ratingDistribution = [
    { rating: 5, count: reviews.filter(r => r.rating === 5).length, color: 'bg-green-500' },
    { rating: 4, count: reviews.filter(r => r.rating === 4).length, color: 'bg-green-400' },
    { rating: 3, count: reviews.filter(r => r.rating === 3).length, color: 'bg-yellow-500' },
    { rating: 2, count: reviews.filter(r => r.rating === 2).length, color: 'bg-orange-500' },
    { rating: 1, count: reviews.filter(r => r.rating === 1).length, color: 'bg-red-500' }
  ];

  const maxCount = Math.max(...ratingDistribution.map(r => r.count));

  return (
    <AdminLayout
      title="تحلیل‌های نظرات"
      subtitle="گزارش‌های تفصیلی و تحلیل نظرات مشتریان"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="7">7 روز گذشته</option>
              <option value="30">30 روز گذشته</option>
              <option value="90">90 روز گذشته</option>
              <option value="365">یک سال گذشته</option>
            </select>
          </div>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              loading={refreshing}
              onClick={handleRefresh}
            >
              به‌روزرسانی
            </AdminButton>
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
            >
              خروجی گزارش
            </AdminButton>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="کل نظرات"
            value={formatPersianNumber(analytics.totalReviews)}
            change={15}
            icon={MessageSquare}
            color="bg-blue-500"
          />
          
          <StatCard
            title="نظرات در انتظار"
            value={formatPersianNumber(analytics.pendingReviews)}
            change={-8}
            icon={Clock}
            color="bg-yellow-500"
            subtitle={`${((analytics.pendingReviews / analytics.totalReviews) * 100).toFixed(1)}% از کل`}
          />
          
          <StatCard
            title="نظرات تایید شده"
            value={formatPersianNumber(analytics.approvedReviews)}
            change={12}
            icon={CheckCircle}
            color="bg-green-500"
            subtitle={`${((analytics.approvedReviews / analytics.totalReviews) * 100).toFixed(1)}% از کل`}
          />
          
          <StatCard
            title="میانگین امتیاز کیفیت"
            value={analytics.qualityMetrics.averageQualityScore.toFixed(1)}
            change={3}
            icon={Star}
            color="bg-purple-500"
          />
        </div>

        {/* Moderation Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AdminCard title="آمار بررسی">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">میانگین زمان پاسخ:</span>
                <span className="font-medium">
                  {analytics.moderationStats.averageResponseTime.toFixed(0)} دقیقه
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">نرخ تایید خودکار:</span>
                <span className="font-medium text-green-600">
                  {analytics.moderationStats.autoApprovalRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">نرخ بررسی دستی:</span>
                <span className="font-medium text-blue-600">
                  {analytics.moderationStats.manualReviewRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">نرخ رد:</span>
                <span className="font-medium text-red-600">
                  {analytics.moderationStats.rejectionRate.toFixed(1)}%
                </span>
              </div>
            </div>
          </AdminCard>

          <AdminCard title="کیفیت محتوا">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">میانگین امتیاز کیفیت:</span>
                <span className="font-medium">
                  {analytics.qualityMetrics.averageQualityScore.toFixed(1)}/100
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">میانگین امتیاز اسپم:</span>
                <span className="font-medium">
                  {analytics.qualityMetrics.averageSpamScore.toFixed(1)}/100
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">نرخ مفید بودن:</span>
                <span className="font-medium">
                  {(analytics.qualityMetrics.averageHelpfulnessRatio * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">خرید تایید شده:</span>
                <span className="font-medium text-green-600">
                  {analytics.qualityMetrics.verifiedPurchasePercentage.toFixed(1)}%
                </span>
              </div>
            </div>
          </AdminCard>

          <AdminCard title="تحلیل احساسات">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">مثبت:</span>
                <span className="font-medium text-green-600">
                  {analytics.contentAnalysis.sentimentDistribution.positive}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">خنثی:</span>
                <span className="font-medium text-yellow-600">
                  {analytics.contentAnalysis.sentimentDistribution.neutral}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">منفی:</span>
                <span className="font-medium text-red-600">
                  {analytics.contentAnalysis.sentimentDistribution.negative}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">میانگین طول متن:</span>
                <span className="font-medium">
                  {analytics.contentAnalysis.languageQuality.averageLength} کلمه
                </span>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Rating Distribution */}
        <AdminCard title="توزیع امتیازات">
          <div className="space-y-4">
            {ratingDistribution.map(({ rating, count, color }) => {
              const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0;
              const totalPercentage = analytics.totalReviews > 0 ? (count / analytics.totalReviews) * 100 : 0;
              
              return (
                <div key={rating} className="flex items-center gap-4">
                  <div className="flex items-center gap-2 min-w-0 w-20">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: rating }, (_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 ${color} rounded-full transition-all duration-300`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-sm text-gray-600 min-w-0 w-24 text-left">
                    {formatPersianNumber(count)} ({totalPercentage.toFixed(1)}%)
                  </div>
                </div>
              );
            })}
          </div>
        </AdminCard>

        {/* Content Flags Distribution */}
        <AdminCard title="توزیع علامت‌های محتوا">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(analytics.contentAnalysis.flagDistribution).map(([flag, count]) => {
              const flagNames: Record<string, string> = {
                spam: 'اسپم',
                inappropriate: 'نامناسب',
                fake: 'جعلی',
                offensive: 'توهین‌آمیز',
                promotional: 'تبلیغاتی',
                irrelevant: 'غیرمرتبط'
              };
              
              return (
                <div key={flag} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">
                      {flagNames[flag] || flag}
                    </span>
                    <span className="text-lg font-bold text-gray-900">
                      {formatPersianNumber(count)}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </AdminCard>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AdminCard title="نظرات اخیر با امتیاز بالا">
            <div className="space-y-3">
              {reviews
                .filter(r => r.rating >= 4)
                .slice(0, 5)
                .map((review) => (
                  <div key={review.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        {Array.from({ length: review.rating }, (_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 text-sm">
                          {review.customer.name}
                        </div>
                        <div className="text-sm text-gray-600 truncate max-w-xs">
                          {review.title}
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatPersianDate(review.createdAt)}
                    </div>
                  </div>
                ))}
            </div>
          </AdminCard>

          <AdminCard title="نظرات نیازمند توجه">
            <div className="space-y-3">
              {reviews
                .filter(r => r.moderation.status === 'flagged' || r.moderation.spamScore > 70)
                .slice(0, 5)
                .map((review) => (
                  <div key={review.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <AlertTriangle className="w-5 h-5 text-red-500" />
                      <div>
                        <div className="font-medium text-gray-900 text-sm">
                          {review.customer.name}
                        </div>
                        <div className="text-sm text-gray-600 truncate max-w-xs">
                          {review.title}
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium text-red-600">
                        اسپم: {review.moderation.spamScore}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatPersianDate(review.createdAt)}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </AdminCard>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <AdminCard title="نرخ پاسخ">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {((reviews.filter(r => r.businessResponse).length / analytics.totalReviews) * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-gray-600">نظرات دارای پاسخ</p>
            </div>
          </AdminCard>

          <AdminCard title="نرخ تعامل">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {(reviews.reduce((sum, r) => sum + r.helpfulVotes, 0) / analytics.totalReviews).toFixed(1)}
              </div>
              <p className="text-sm text-gray-600">میانگین رای مفید</p>
            </div>
          </AdminCard>

          <AdminCard title="نرخ تصاویر">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {((reviews.filter(r => r.images && r.images.length > 0).length / analytics.totalReviews) * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-gray-600">نظرات دارای تصویر</p>
            </div>
          </AdminCard>

          <AdminCard title="نرخ توصیه">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {((reviews.filter(r => r.isRecommended).length / analytics.totalReviews) * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-gray-600">نظرات توصیه‌کننده</p>
            </div>
          </AdminCard>
        </div>
      </div>
    </AdminLayout>
  );
};

export default ReviewAnalyticsPage;
