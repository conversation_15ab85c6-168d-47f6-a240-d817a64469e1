import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Edit,
  MessageSquare,
  Plus,
  ShoppingBag,
  Star,
  AlertTriangle,
  Clock,
  User,
  CreditCard,
  Home,
  Activity,
  FileText,
  Gift
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminFormModal } from '../../../components/admin/common/AdminModal';
import CustomerProfile from '../../../components/admin/customers/CustomerProfile';
import CustomerOrderHistory from '../../../components/admin/customers/CustomerOrderHistory';
import CustomerLoyaltyStatus from '../../../components/admin/customers/CustomerLoyaltyStatus';
import CustomerReviewHistory from '../../../components/admin/customers/CustomerReviewHistory';
import CustomerAddressBook from '../../../components/admin/customers/CustomerAddressBook';
import CustomerActivityLog from '../../../components/admin/customers/CustomerActivityLog';
import { useAdminCustomers } from '../../../hooks/useAdminCustomers';
import { AdminCustomer, CustomerNote, SupportTicket } from '../../../types/adminCustomer';
import { formatCustomerName, formatPersianDate, formatPersianCurrency } from '../../../utils/customerUtils';

type TabType = 'profile' | 'orders' | 'loyalty' | 'reviews' | 'addresses' | 'activity';

interface Tab {
  id: TabType;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const CustomerDetailsPage: React.FC = () => {
  const { id: customerId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const {
    getCustomerById,
    updateCustomer,
    addCustomerNote,
    toggleVipStatus,
    toggleCustomerBlock,
    createSupportTicket,
    sendMessage,
    loading,
    allCustomers
  } = useAdminCustomers();

  const [customer, setCustomer] = useState<AdminCustomer | null>(null);
  const [customerNotFound, setCustomerNotFound] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('profile');
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showTicketModal, setShowTicketModal] = useState(false);

  const tabs: Tab[] = [
    { id: 'profile', label: 'اطلاعات شخصی', icon: User },
    { id: 'orders', label: 'تاریخچه سفارشات', icon: ShoppingBag },
    { id: 'loyalty', label: 'باشگاه مشتریان', icon: Gift },
    { id: 'reviews', label: 'نظرات و امتیازها', icon: Star },
    { id: 'addresses', label: 'دفترچه آدرس', icon: Home },
    { id: 'activity', label: 'گزارش فعالیت', icon: Activity }
  ];
  const [messageData, setMessageData] = useState({
    type: 'email' as 'email' | 'sms' | 'in_app',
    subject: '',
    content: ''
  });
  const [ticketData, setTicketData] = useState({
    subject: '',
    description: '',
    category: 'general' as SupportTicket['category'],
    priority: 'normal' as SupportTicket['priority']
  });

  useEffect(() => {
    if (customerId) {
      const foundCustomer = getCustomerById(customerId);

      if (foundCustomer) {
        setCustomer(foundCustomer);
        setCustomerNotFound(false);
      } else {
        // Only set customer not found if we're not loading
        // This prevents showing "not found" while data is still loading
        if (!loading) {
          setCustomerNotFound(true);
          setCustomer(null);
        }
      }
    }
  }, [customerId, getCustomerById, loading, allCustomers]);

  // Show loading state while customers are being loaded
  if (loading && !customer) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              در حال بارگذاری...
            </h3>
            <p className="text-gray-600">
              لطفاً منتظر بمانید
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Show not found state only after loading is complete and customer is still not found
  if (customerNotFound || (!loading && !customer)) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              مشتری یافت نشد
            </h3>
            <p className="text-gray-600 mb-4">
              مشتری با شناسه مورد نظر در سیستم موجود نیست
            </p>
            <AdminButton
              variant="primary"
              icon={ArrowLeft}
              onClick={() => navigate('/admin/customers')}
            >
              بازگشت به لیست مشتریان
            </AdminButton>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // If we still don't have a customer but we're not loading and not explicitly not found,
  // show loading (edge case)
  if (!customer) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              در حال بارگذاری...
            </h3>
            <p className="text-gray-600">
              لطفاً منتظر بمانید
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const handleUpdateCustomer = async (updates: Partial<AdminCustomer>) => {
    try {
      await updateCustomer(customer.id, updates);
      // Update local state
      setCustomer(prev => prev ? { ...prev, ...updates } : null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleAddNote = async (note: Omit<CustomerNote, 'id' | 'customerId' | 'createdAt' | 'createdBy' | 'createdByName'>) => {
    try {
      await addCustomerNote(customer.id, note);
      // Refresh customer data
      const updatedCustomer = getCustomerById(customer.id);
      setCustomer(updatedCustomer || null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleToggleVip = async () => {
    try {
      await toggleVipStatus(customer.id, !customer.isVip);
      setCustomer(prev => prev ? { ...prev, isVip: !prev.isVip } : null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleToggleBlock = async () => {
    try {
      await toggleCustomerBlock(customer.id, customer.status !== 'blocked');
      setCustomer(prev => prev ? { 
        ...prev, 
        status: prev.status === 'blocked' ? 'active' : 'blocked' 
      } : null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleSendMessage = () => {
    setShowMessageModal(true);
  };

  const handleCreateTicket = () => {
    setShowTicketModal(true);
  };

  const handleSubmitMessage = async () => {
    try {
      await sendMessage(customer.id, messageData);
      setMessageData({ type: 'email', subject: '', content: '' });
      setShowMessageModal(false);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleSubmitTicket = async () => {
    try {
      await createSupportTicket(customer.id, ticketData);
      setTicketData({ subject: '', description: '', category: 'general', priority: 'normal' });
      setShowTicketModal(false);
      // Refresh customer data
      const updatedCustomer = getCustomerById(customer.id);
      setCustomer(updatedCustomer || null);
    } catch (error) {
      // Error handled by hook
    }
  };

  // Enhanced handlers for new functionality
  const handleViewOrder = (orderId: string) => {
    navigate(`/admin/orders/${orderId}`);
  };

  const handleAdjustPoints = async (points: number, reason: string) => {
    try {
      // In real app, this would call API to adjust loyalty points
      console.log('Adjusting points:', { customerId: customer.id, points, reason });
      // Update customer loyalty points locally
      setCustomer(prev => prev ? {
        ...prev,
        loyaltyPoints: prev.loyaltyPoints + points
      } : null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleViewReview = (reviewId: string) => {
    navigate(`/admin/reviews/${reviewId}`);
  };

  const handleViewProduct = (productId: string) => {
    navigate(`/admin/products/${productId}`);
  };

  const handleModerateReview = async (reviewId: string, action: 'approve' | 'reject' | 'flag') => {
    try {
      // In real app, this would call API to moderate review
      console.log('Moderating review:', { reviewId, action });
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleAddAddress = async (address: any) => {
    try {
      // In real app, this would call API to add address
      console.log('Adding address:', { customerId: customer.id, address });
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleUpdateAddress = async (addressId: string, address: any) => {
    try {
      // In real app, this would call API to update address
      console.log('Updating address:', { addressId, address });
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleDeleteAddress = async (addressId: string) => {
    try {
      // In real app, this would call API to delete address
      console.log('Deleting address:', { addressId });
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleSetDefaultAddress = async (addressId: string) => {
    try {
      // In real app, this would call API to set default address
      console.log('Setting default address:', { addressId });
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleViewActivityDetails = (activityId: string) => {
    // In real app, this would show activity details modal
    console.log('Viewing activity details:', { activityId });
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <AdminButton
              variant="ghost"
              icon={ArrowLeft}
              onClick={() => navigate('/admin/customers')}
            >
              بازگشت
            </AdminButton>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {formatCustomerName(customer)}
              </h1>
              <p className="text-gray-600 mt-1">
                جزئیات و مدیریت اطلاعات مشتری
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={MessageSquare}
              onClick={handleSendMessage}
            >
              ارسال پیام
            </AdminButton>
            <AdminButton
              variant="outline"
              icon={Plus}
              onClick={handleCreateTicket}
            >
              ایجاد تیکت
            </AdminButton>
            <AdminButton
              variant="primary"
              icon={Edit}
              onClick={() => navigate(`/admin/customers/${customer.id}/edit`)}
            >
              ویرایش
            </AdminButton>
          </div>
        </div>

        {/* Tab Navigation */}
        <AdminCard>
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 space-x-reverse">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="mt-6">
            {activeTab === 'profile' && (
              <CustomerProfile
                customer={customer}
                onUpdateCustomer={handleUpdateCustomer}
                onAddNote={handleAddNote}
                onToggleVip={handleToggleVip}
                onToggleBlock={handleToggleBlock}
                onSendMessage={handleSendMessage}
                onCreateTicket={handleCreateTicket}
                loading={loading}
              />
            )}

            {activeTab === 'orders' && (
              <CustomerOrderHistory
                customer={customer}
                onViewOrder={handleViewOrder}
              />
            )}

            {activeTab === 'loyalty' && (
              <CustomerLoyaltyStatus
                customer={customer}
                onAdjustPoints={handleAdjustPoints}
              />
            )}

            {activeTab === 'reviews' && (
              <CustomerReviewHistory
                customer={customer}
                onViewReview={handleViewReview}
                onViewProduct={handleViewProduct}
                onModerateReview={handleModerateReview}
              />
            )}

            {activeTab === 'addresses' && (
              <CustomerAddressBook
                customer={customer}
                onAddAddress={handleAddAddress}
                onUpdateAddress={handleUpdateAddress}
                onDeleteAddress={handleDeleteAddress}
                onSetDefaultAddress={handleSetDefaultAddress}
              />
            )}

            {activeTab === 'activity' && (
              <CustomerActivityLog
                customer={customer}
                onViewDetails={handleViewActivityDetails}
              />
            )}
          </div>
        </AdminCard>
      </div>

      {/* Send Message Modal */}
      <AdminFormModal
        isOpen={showMessageModal}
        onClose={() => setShowMessageModal(false)}
        onSubmit={handleSubmitMessage}
        title={`ارسال پیام به ${formatCustomerName(customer)}`}
        submitText="ارسال پیام"
        loading={loading}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نوع پیام
            </label>
            <select
              value={messageData.type}
              onChange={(e) => setMessageData(prev => ({ ...prev, type: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="email">ایمیل</option>
              <option value="sms">پیامک</option>
              <option value="in_app">اعلان درون برنامه</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              موضوع
            </label>
            <input
              type="text"
              value={messageData.subject}
              onChange={(e) => setMessageData(prev => ({ ...prev, subject: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="موضوع پیام..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              متن پیام
            </label>
            <textarea
              value={messageData.content}
              onChange={(e) => setMessageData(prev => ({ ...prev, content: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="متن پیام خود را وارد کنید..."
            />
          </div>
        </div>
      </AdminFormModal>

      {/* Create Ticket Modal */}
      <AdminFormModal
        isOpen={showTicketModal}
        onClose={() => setShowTicketModal(false)}
        onSubmit={handleSubmitTicket}
        title={`ایجاد تیکت برای ${formatCustomerName(customer)}`}
        submitText="ایجاد تیکت"
        loading={loading}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              موضوع
            </label>
            <input
              type="text"
              value={ticketData.subject}
              onChange={(e) => setTicketData(prev => ({ ...prev, subject: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="موضوع تیکت..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              دسته‌بندی
            </label>
            <select
              value={ticketData.category}
              onChange={(e) => setTicketData(prev => ({ ...prev, category: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="general">عمومی</option>
              <option value="order">سفارش</option>
              <option value="payment">پرداخت</option>
              <option value="shipping">ارسال</option>
              <option value="product">محصول</option>
              <option value="account">حساب کاربری</option>
              <option value="technical">فنی</option>
              <option value="complaint">شکایت</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اولویت
            </label>
            <select
              value={ticketData.priority}
              onChange={(e) => setTicketData(prev => ({ ...prev, priority: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="low">کم</option>
              <option value="normal">عادی</option>
              <option value="high">بالا</option>
              <option value="urgent">فوری</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              توضیحات
            </label>
            <textarea
              value={ticketData.description}
              onChange={(e) => setTicketData(prev => ({ ...prev, description: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="توضیحات تیکت..."
            />
          </div>
        </div>
      </AdminFormModal>
    </AdminLayout>
  );
};

export default CustomerDetailsPage;
