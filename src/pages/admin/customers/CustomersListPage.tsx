import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Users,
  Filter,
  Download,
  MessageSquare,
  Crown,
  Shield,
  Plus,
  Search,
  X
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminFormModal } from '../../../components/admin/common/AdminModal';
import CustomerTable from '../../../components/admin/customers/CustomerTable';
import { useAdminCustomers } from '../../../hooks/useAdminCustomers';
import { AdminCustomer, CustomerFilters, PERSIAN_CUSTOMER_MESSAGES } from '../../../types/adminCustomer';
import { formatCustomerName } from '../../../utils/customerUtils';
import { LOYALTY_TIERS } from '../../../types/loyalty';

const CustomersListPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    customers,
    loading,
    filters,
    setFilters,
    toggleCustomerBlock,
    toggleVipStatus,
    exportCustomers,
    sendMessage,
    createSupportTicket,
    getAnalytics
  } = useAdminCustomers();

  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showTicketModal, setShowTicketModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<AdminCustomer | null>(null);
  const [searchValue, setSearchValue] = useState('');

  const analytics = getAnalytics();

  const handleSelectCustomer = (customerId: string) => {
    setSelectedCustomers(prev => 
      prev.includes(customerId)
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedCustomers(selected ? customers.map(c => c.id) : []);
  };

  const handleViewCustomer = (customer: AdminCustomer) => {
    navigate(`/admin/customers/${customer.id}`);
  };

  const handleEditCustomer = (customer: AdminCustomer) => {
    navigate(`/admin/customers/${customer.id}/edit`);
  };

  const handleToggleBlock = async (customer: AdminCustomer) => {
    try {
      await toggleCustomerBlock(customer.id, customer.status !== 'blocked');
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleToggleVip = async (customer: AdminCustomer) => {
    try {
      await toggleVipStatus(customer.id, !customer.isVip);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleSendMessage = (customer: AdminCustomer) => {
    setSelectedCustomer(customer);
    setShowMessageModal(true);
  };

  const handleCreateTicket = (customer: AdminCustomer) => {
    setSelectedCustomer(customer);
    setShowTicketModal(true);
  };

  const handleExport = async () => {
    try {
      await exportCustomers(filters);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    setFilters({ ...filters, search: value });
  };

  const clearFilters = () => {
    setFilters({});
    setSearchValue('');
  };

  const hasActiveFilters = Object.keys(filters).some(key => 
    key !== 'search' && filters[key as keyof CustomerFilters] !== undefined
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">مدیریت مشتریان</h1>
            <p className="text-gray-600 mt-1">
              مشاهده و مدیریت اطلاعات مشتریان
            </p>
          </div>
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
              loading={loading}
            >
              خروجی
            </AdminButton>
            <AdminButton
              variant="outline"
              icon={Filter}
              onClick={() => setShowFilters(!showFilters)}
            >
              فیلتر
              {hasActiveFilters && (
                <span className="mr-1 bg-blue-500 text-white text-xs rounded-full w-2 h-2"></span>
              )}
            </AdminButton>
          </div>
        </div>

        {/* Analytics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdminCard>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">کل مشتریان</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.totalCustomers.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Plus className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">مشتریان جدید این ماه</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.newCustomersThisMonth.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Crown className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">مشتریان ویژه</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.vipCustomers.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">تیکت‌های باز</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.supportMetrics.openTickets.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <AdminCard title="فیلترها">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    وضعیت
                  </label>
                  <select
                    value={filters.status?.[0] || ''}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      status: e.target.value ? [e.target.value as any] : undefined 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">همه وضعیت‌ها</option>
                    <option value="active">فعال</option>
                    <option value="inactive">غیرفعال</option>
                    <option value="blocked">مسدود</option>
                    <option value="suspended">معلق</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    بخش
                  </label>
                  <select
                    value={filters.segment?.[0] || ''}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      segment: e.target.value ? [e.target.value as any] : undefined 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">همه بخش‌ها</option>
                    <option value="new">جدید</option>
                    <option value="regular">عادی</option>
                    <option value="vip">ویژه</option>
                    <option value="champion">قهرمان</option>
                    <option value="at_risk">در معرض خطر</option>
                    <option value="lost">از دست رفته</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    سطح وفاداری
                  </label>
                  <select
                    value={filters.loyaltyTier?.[0] || ''}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      loyaltyTier: e.target.value ? [e.target.value] : undefined 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">همه سطوح</option>
                    {LOYALTY_TIERS.map(tier => (
                      <option key={tier.id} value={tier.id}>
                        {tier.persianName}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    مشتری ویژه
                  </label>
                  <select
                    value={filters.isVip === undefined ? '' : filters.isVip.toString()}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      isVip: e.target.value === '' ? undefined : e.target.value === 'true'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">همه</option>
                    <option value="true">ویژه</option>
                    <option value="false">عادی</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <span className="text-sm text-gray-600">
                  {customers.length} مشتری یافت شد
                </span>
                <div className="flex items-center gap-2">
                  <AdminButton
                    variant="outline"
                    size="sm"
                    icon={X}
                    onClick={clearFilters}
                  >
                    پاک کردن فیلترها
                  </AdminButton>
                  <AdminButton
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilters(false)}
                  >
                    بستن
                  </AdminButton>
                </div>
              </div>
            </AdminCard>
          </motion.div>
        )}

        {/* Customers Table */}
        <AdminCard>
          <CustomerTable
            customers={customers}
            loading={loading}
            selectedCustomers={selectedCustomers}
            onSelectCustomer={handleSelectCustomer}
            onSelectAll={handleSelectAll}
            onViewCustomer={handleViewCustomer}
            onEditCustomer={handleEditCustomer}
            onToggleBlock={handleToggleBlock}
            onToggleVip={handleToggleVip}
            onSendMessage={handleSendMessage}
            onCreateTicket={handleCreateTicket}
            searchValue={searchValue}
            onSearchChange={handleSearchChange}
          />
        </AdminCard>
      </div>

      {/* Message Modal */}
      <AdminFormModal
        isOpen={showMessageModal}
        onClose={() => setShowMessageModal(false)}
        onSubmit={() => {
          // Handle send message
          setShowMessageModal(false);
        }}
        title={`ارسال پیام به ${selectedCustomer ? formatCustomerName(selectedCustomer) : ''}`}
        submitText="ارسال پیام"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نوع پیام
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="email">ایمیل</option>
              <option value="sms">پیامک</option>
              <option value="in_app">اعلان درون برنامه</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              موضوع
            </label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="موضوع پیام..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              متن پیام
            </label>
            <textarea
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="متن پیام خود را وارد کنید..."
            />
          </div>
        </div>
      </AdminFormModal>

      {/* Ticket Modal */}
      <AdminFormModal
        isOpen={showTicketModal}
        onClose={() => setShowTicketModal(false)}
        onSubmit={() => {
          // Handle create ticket
          setShowTicketModal(false);
        }}
        title={`ایجاد تیکت برای ${selectedCustomer ? formatCustomerName(selectedCustomer) : ''}`}
        submitText="ایجاد تیکت"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              موضوع
            </label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="موضوع تیکت..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              دسته‌بندی
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="general">عمومی</option>
              <option value="order">سفارش</option>
              <option value="payment">پرداخت</option>
              <option value="shipping">ارسال</option>
              <option value="product">محصول</option>
              <option value="account">حساب کاربری</option>
              <option value="technical">فنی</option>
              <option value="complaint">شکایت</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اولویت
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="low">کم</option>
              <option value="normal">عادی</option>
              <option value="high">بالا</option>
              <option value="urgent">فوری</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              توضیحات
            </label>
            <textarea
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="توضیحات تیکت..."
            />
          </div>
        </div>
      </AdminFormModal>
    </AdminLayout>
  );
};

export default CustomersListPage;
