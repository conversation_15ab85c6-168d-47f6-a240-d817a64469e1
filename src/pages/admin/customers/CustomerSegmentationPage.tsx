import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Users,
  Filter,
  Download,
  Plus,
  Search,
  Tag,
  TrendingUp,
  Crown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  ShoppingBag,
  Calendar,
  Target,
  BarChart3
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminFormModal } from '../../../components/admin/common/AdminModal';
import { useAdminCustomers } from '../../../hooks/useAdminCustomers';
import { AdminCustomer, CustomerSegment, PERSIAN_CUSTOMER_MESSAGES } from '../../../types/adminCustomer';
import { formatPersianNumber, formatPersianCurrency } from '../../../utils/customerUtils';

interface SegmentData {
  id: string;
  name: string;
  description: string;
  criteria: {
    totalSpentMin?: number;
    totalSpentMax?: number;
    totalOrdersMin?: number;
    totalOrdersMax?: number;
    lastOrderDaysAgo?: number;
    registrationDaysAgo?: number;
    loyaltyTier?: string[];
    tags?: string[];
    isVip?: boolean;
    status?: string[];
  };
  customerCount: number;
  color: string;
  icon: React.ComponentType<{ className?: string }>;
}

const CustomerSegmentationPage: React.FC = () => {
  const navigate = useNavigate();
  const { customers, getAnalytics } = useAdminCustomers();
  const [searchValue, setSearchValue] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<SegmentData | null>(null);

  // Predefined segments
  const predefinedSegments: SegmentData[] = [
    {
      id: 'vip',
      name: 'مشتریان ویژه',
      description: 'مشتریان با وضعیت VIP',
      criteria: { isVip: true },
      customerCount: customers.filter(c => c.isVip).length,
      color: 'bg-yellow-500',
      icon: Crown
    },
    {
      id: 'high_value',
      name: 'مشتریان پرارزش',
      description: 'مشتریان با خرید بالای 10 میلیون تومان',
      criteria: { totalSpentMin: 10000000 },
      customerCount: customers.filter(c => c.analytics.totalSpent >= 10000000).length,
      color: 'bg-green-500',
      icon: TrendingUp
    },
    {
      id: 'frequent_buyers',
      name: 'خریداران مداوم',
      description: 'مشتریان با بیش از 10 سفارش',
      criteria: { totalOrdersMin: 10 },
      customerCount: customers.filter(c => c.analytics.totalOrders >= 10).length,
      color: 'bg-blue-500',
      icon: ShoppingBag
    },
    {
      id: 'new_customers',
      name: 'مشتریان جدید',
      description: 'مشتریان ثبت‌نام شده در 30 روز گذشته',
      criteria: { registrationDaysAgo: 30 },
      customerCount: customers.filter(c => c.segment === 'new').length,
      color: 'bg-purple-500',
      icon: Star
    },
    {
      id: 'at_risk',
      name: 'مشتریان در معرض خطر',
      description: 'مشتریان بدون خرید در 60 روز گذشته',
      criteria: { lastOrderDaysAgo: 60 },
      customerCount: customers.filter(c => c.segment === 'at_risk').length,
      color: 'bg-orange-500',
      icon: AlertTriangle
    },
    {
      id: 'lost',
      name: 'مشتریان از دست رفته',
      description: 'مشتریان بدون خرید در 180 روز گذشته',
      criteria: { lastOrderDaysAgo: 180 },
      customerCount: customers.filter(c => c.segment === 'lost').length,
      color: 'bg-red-500',
      icon: Clock
    }
  ];

  const analytics = getAnalytics();

  const handleViewSegment = (segment: SegmentData) => {
    // Navigate to customers list with segment filter
    const searchParams = new URLSearchParams();
    searchParams.set('segment', segment.id);
    navigate(`/admin/customers?${searchParams.toString()}`);
  };

  const handleExportSegment = (segment: SegmentData) => {
    // Export segment data
    console.log('Exporting segment:', segment.name);
  };

  const SegmentCard: React.FC<{ segment: SegmentData }> = ({ segment }) => {
    const Icon = segment.icon;
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow"
      >
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={`w-12 h-12 ${segment.color} rounded-lg flex items-center justify-center`}>
              <Icon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{segment.name}</h3>
              <p className="text-sm text-gray-600">{segment.description}</p>
            </div>
          </div>
          <div className="text-left">
            <div className="text-2xl font-bold text-gray-900">
              {formatPersianNumber(segment.customerCount)}
            </div>
            <div className="text-sm text-gray-500">مشتری</div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {((segment.customerCount / customers.length) * 100).toFixed(1)}% از کل مشتریان
          </div>
          <div className="flex items-center gap-2">
            <AdminButton
              size="sm"
              variant="outline"
              onClick={() => handleViewSegment(segment)}
            >
              مشاهده
            </AdminButton>
            <AdminButton
              size="sm"
              variant="outline"
              onClick={() => handleExportSegment(segment)}
              icon={Download}
            >
              خروجی
            </AdminButton>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <AdminLayout
      title="بخش‌بندی مشتریان"
      subtitle="تحلیل و دسته‌بندی مشتریان بر اساس رفتار خرید"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="جستجو در بخش‌ها..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={BarChart3}
              onClick={() => navigate('/admin/customers/analytics')}
            >
              تحلیل‌های تفصیلی
            </AdminButton>
            <AdminButton
              icon={Plus}
              onClick={() => setShowCreateModal(true)}
            >
              ایجاد بخش جدید
            </AdminButton>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(analytics.totalCustomers)}
                </div>
                <div className="text-sm text-gray-600">کل مشتریان</div>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(analytics.activeCustomers)}
                </div>
                <div className="text-sm text-gray-600">مشتریان فعال</div>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Crown className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(analytics.vipCustomers)}
                </div>
                <div className="text-sm text-gray-600">مشتریان ویژه</div>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(analytics.newCustomersThisMonth)}
                </div>
                <div className="text-sm text-gray-600">مشتریان جدید این ماه</div>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Segments Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {predefinedSegments
            .filter(segment => 
              segment.name.toLowerCase().includes(searchValue.toLowerCase()) ||
              segment.description.toLowerCase().includes(searchValue.toLowerCase())
            )
            .map(segment => (
              <SegmentCard key={segment.id} segment={segment} />
            ))}
        </div>

        {/* Segment Distribution Chart */}
        <AdminCard title="توزیع بخش‌های مشتریان">
          <div className="space-y-4">
            {predefinedSegments.map(segment => {
              const percentage = (segment.customerCount / customers.length) * 100;
              return (
                <div key={segment.id} className="flex items-center gap-4">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <div className={`w-4 h-4 ${segment.color} rounded`}></div>
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {segment.name}
                    </span>
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 ${segment.color} rounded-full transition-all duration-300`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-sm text-gray-600 min-w-0">
                    {formatPersianNumber(segment.customerCount)} ({percentage.toFixed(1)}%)
                  </div>
                </div>
              );
            })}
          </div>
        </AdminCard>
      </div>

      {/* Create Segment Modal */}
      <AdminFormModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={() => {
          // Handle create segment
          setShowCreateModal(false);
        }}
        title="ایجاد بخش جدید"
        submitText="ایجاد بخش"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              نام بخش
            </label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="نام بخش را وارد کنید"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              توضیحات
            </label>
            <textarea
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="توضیحات بخش را وارد کنید"
            />
          </div>
        </div>
      </AdminFormModal>
    </AdminLayout>
  );
};

export default CustomerSegmentationPage;
