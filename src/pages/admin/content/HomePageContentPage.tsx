import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Save, 
  Eye, 
  Smartphone, 
  Tablet, 
  Monitor, 
  Plus, 
  Settings,
  Globe,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useHomePageContent } from '../../../hooks/useHomePageContent';
import SectionEditor from '../../../components/admin/content/SectionEditor';
import SectionList from '../../../components/admin/content/SectionList';
import PreviewPanel from '../../../components/admin/content/PreviewPanel';
import SEOSettings from '../../../components/admin/content/SEOSettings';
import GlobalSettings from '../../../components/admin/content/GlobalSettings';
import toast from 'react-hot-toast';

const HomePageContentPage: React.FC = () => {
  const {
    content,
    loading,
    saving,
    error,
    isDirty,
    previewMode,
    setPreviewMode,
    saveContent,
    publishContent,
    validateContent
  } = useHomePageContent();

  const [activeTab, setActiveTab] = useState<'sections' | 'seo' | 'settings'>('sections');
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const handleSave = async () => {
    if (!content) return;
    
    try {
      await saveContent(content);
    } catch (error) {
      console.error('Save error:', error);
    }
  };

  const handlePublish = async () => {
    if (!content) return;

    const validation = validateContent(content);
    if (!validation.isValid) {
      toast.error(`${validation.errors.length} خطا در محتوا وجود دارد`);
      return;
    }

    try {
      await publishContent();
      toast.success('صفحه اصلی با موفقیت منتشر شد');
    } catch (error) {
      console.error('Publish error:', error);
    }
  };

  const getStatusColor = () => {
    if (!content) return 'gray';
    return content.status === 'published' ? 'green' : 'yellow';
  };

  const getStatusText = () => {
    if (!content) return 'نامشخص';
    return content.status === 'published' ? 'منتشر شده' : 'پیش‌نویس';
  };

  if (loading) {
    return (
      <AdminLayout title="مدیریت صفحه اصلی">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout title="مدیریت صفحه اصلی">
        <AdminCard>
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">خطا در بارگذاری</h3>
            <p className="text-gray-600">{error}</p>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="مدیریت صفحه اصلی"
      subtitle="ویرایش و مدیریت محتوای صفحه اصلی سایت"
      actions={
        <div className="flex items-center gap-3">
          {/* Status Indicator */}
          <div className="flex items-center gap-2 px-3 py-1.5 rounded-full bg-gray-100">
            <div className={`w-2 h-2 rounded-full ${
              getStatusColor() === 'green' ? 'bg-green-500' : 
              getStatusColor() === 'yellow' ? 'bg-yellow-500' : 'bg-gray-500'
            }`} />
            <span className="text-sm text-gray-700">{getStatusText()}</span>
          </div>

          {/* Preview Mode Selector */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setPreviewMode('desktop')}
              className={`p-2 rounded ${previewMode === 'desktop' ? 'bg-white shadow-sm' : ''}`}
              title="نمایش دسکتاپ"
            >
              <Monitor className="w-4 h-4" />
            </button>
            <button
              onClick={() => setPreviewMode('tablet')}
              className={`p-2 rounded ${previewMode === 'tablet' ? 'bg-white shadow-sm' : ''}`}
              title="نمایش تبلت"
            >
              <Tablet className="w-4 h-4" />
            </button>
            <button
              onClick={() => setPreviewMode('mobile')}
              className={`p-2 rounded ${previewMode === 'mobile' ? 'bg-white shadow-sm' : ''}`}
              title="نمایش موبایل"
            >
              <Smartphone className="w-4 h-4" />
            </button>
          </div>

          {/* Action Buttons */}
          <AdminButton
            variant="outline"
            icon={Eye}
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? 'مخفی کردن پیش‌نمایش' : 'پیش‌نمایش'}
          </AdminButton>

          <AdminButton
            variant="outline"
            icon={Save}
            onClick={handleSave}
            loading={saving}
            disabled={!isDirty}
          >
            ذخیره
          </AdminButton>

          <AdminButton
            variant="primary"
            icon={Globe}
            onClick={handlePublish}
            loading={saving}
          >
            انتشار
          </AdminButton>
        </div>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Main Content */}
        <div className={`${showPreview ? 'lg:col-span-6' : 'lg:col-span-12'} space-y-6`}>
          {/* Tabs */}
          <AdminCard>
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 space-x-reverse">
                <button
                  onClick={() => setActiveTab('sections')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'sections'
                      ? 'border-admin-500 text-admin-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  بخش‌های صفحه
                </button>
                <button
                  onClick={() => setActiveTab('seo')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'seo'
                      ? 'border-admin-500 text-admin-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  تنظیمات SEO
                </button>
                <button
                  onClick={() => setActiveTab('settings')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'settings'
                      ? 'border-admin-500 text-admin-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  تنظیمات کلی
                </button>
              </nav>
            </div>

            <div className="p-6">
              <AnimatePresence mode="wait">
                {activeTab === 'sections' && (
                  <motion.div
                    key="sections"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <SectionList
                      sections={content?.sections || []}
                      selectedSectionId={selectedSectionId}
                      onSelectSection={setSelectedSectionId}
                    />
                  </motion.div>
                )}

                {activeTab === 'seo' && (
                  <motion.div
                    key="seo"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <SEOSettings
                      settings={content?.seoSettings}
                      onChange={(settings) => {
                        if (content) {
                          // Update SEO settings logic here
                        }
                      }}
                    />
                  </motion.div>
                )}

                {activeTab === 'settings' && (
                  <motion.div
                    key="settings"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <GlobalSettings
                      settings={content?.globalSettings}
                      onChange={(settings) => {
                        if (content) {
                          // Update global settings logic here
                        }
                      }}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </AdminCard>

          {/* Section Editor */}
          {selectedSectionId && (
            <SectionEditor
              section={content?.sections.find(s => s.id === selectedSectionId)}
              onUpdate={(updates) => {
                // Update section logic here
              }}
              onClose={() => setSelectedSectionId(null)}
            />
          )}

          {/* Dirty State Warning */}
          {isDirty && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="fixed bottom-6 left-6 right-6 lg:left-auto lg:right-6 lg:w-96 z-50"
            >
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg">
                <div className="flex items-center gap-3">
                  <Clock className="w-5 h-5 text-yellow-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-yellow-800">
                      تغییرات ذخیره نشده
                    </p>
                    <p className="text-xs text-yellow-700">
                      تغییرات شما هنوز ذخیره نشده‌اند
                    </p>
                  </div>
                  <AdminButton
                    size="sm"
                    variant="outline"
                    onClick={handleSave}
                    loading={saving}
                  >
                    ذخیره
                  </AdminButton>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* Preview Panel */}
        {showPreview && (
          <div className="lg:col-span-6">
            <div className="sticky top-6">
              <PreviewPanel
                content={content}
                previewMode={previewMode}
                onClose={() => setShowPreview(false)}
              />
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default HomePageContentPage;
