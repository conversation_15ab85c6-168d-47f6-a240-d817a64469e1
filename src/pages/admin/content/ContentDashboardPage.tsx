import React from 'react';
import { motion } from 'framer-motion';
import { 
  Image, 
  Tag, 
  Mail, 
  FileText, 
  Eye, 
  MousePointer, 
  TrendingUp,
  Plus,
  BarChart3,
  Calendar,
  Users
} from 'lucide-react';
import { Link } from 'react-router-dom';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { formatContentStatus, formatPersianDate } from '../../../utils/contentUtils';

const ContentDashboardPage: React.FC = () => {
  const { 
    banners, 
    promotions, 
    newsletters, 
    pages, 
    mediaItems, 
    loading, 
    getContentAnalytics 
  } = useAdminContent();

  const analytics = getContentAnalytics();

  const quickActions = [
    {
      title: 'ایجاد بنر جدید',
      description: 'افزودن بنر تبلیغاتی جدید',
      icon: Image,
      href: '/admin/content/banners/create',
      color: 'bg-blue-500'
    },
    {
      title: 'ایجاد تخفیف',
      description: 'تنظیم کمپین تخفیف جدید',
      icon: Tag,
      href: '/admin/content/promotions/create',
      color: 'bg-green-500'
    },
    {
      title: 'کمپین ایمیل',
      description: 'ارسال خبرنامه جدید',
      icon: Mail,
      href: '/admin/content/newsletter/create',
      color: 'bg-purple-500'
    },
    {
      title: 'صفحه جدید',
      description: 'ایجاد صفحه محتوایی',
      icon: FileText,
      href: '/admin/content/pages/create',
      color: 'bg-orange-500'
    }
  ];

  const statsCards = [
    {
      title: 'کل محتوا',
      value: analytics.totalContent.toLocaleString('fa-IR'),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'بازدید کل',
      value: analytics.totalViews.toLocaleString('fa-IR'),
      icon: Eye,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'کلیک کل',
      value: analytics.totalClicks.toLocaleString('fa-IR'),
      icon: MousePointer,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'تبدیل کل',
      value: analytics.totalConversions.toLocaleString('fa-IR'),
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  const contentTypeStats = [
    {
      type: 'بنرها',
      count: analytics.contentByType.banner,
      published: banners.filter(b => b.status === 'published').length,
      icon: Image,
      color: 'text-blue-600'
    },
    {
      type: 'تخفیف‌ها',
      count: analytics.contentByType.promotion,
      published: promotions.filter(p => p.status === 'published').length,
      icon: Tag,
      color: 'text-green-600'
    },
    {
      type: 'خبرنامه‌ها',
      count: analytics.contentByType.newsletter,
      published: newsletters.filter(n => n.status === 'published').length,
      icon: Mail,
      color: 'text-purple-600'
    },
    {
      type: 'صفحات',
      count: analytics.contentByType.page,
      published: pages.filter(p => p.status === 'published').length,
      icon: FileText,
      color: 'text-orange-600'
    }
  ];

  const recentContent = [
    ...banners.slice(0, 3).map(b => ({ ...b, type: 'banner' as const })),
    ...promotions.slice(0, 2).map(p => ({ ...p, type: 'promotion' as const })),
    ...newsletters.slice(0, 2).map(n => ({ ...n, type: 'newsletter' as const })),
    ...pages.slice(0, 2).map(p => ({ ...p, type: 'page' as const }))
  ].sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()).slice(0, 8);

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'banner': return Image;
      case 'promotion': return Tag;
      case 'newsletter': return Mail;
      case 'page': return FileText;
      default: return FileText;
    }
  };

  const getContentTypeColor = (type: string) => {
    switch (type) {
      case 'banner': return 'text-blue-600';
      case 'promotion': return 'text-green-600';
      case 'newsletter': return 'text-purple-600';
      case 'page': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <AdminLayout title="مدیریت محتوا">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="مدیریت محتوا"
      subtitle="مدیریت بنرها، تخفیف‌ها، خبرنامه و صفحات"
    >
      <div className="space-y-6">
        {/* Quick Actions */}
        <AdminCard title="عملیات سریع" className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <motion.div
                  key={action.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    to={action.href}
                    className="block p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow group"
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform`}>
                        <Icon className="w-5 h-5" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{action.title}</h3>
                        <p className="text-sm text-gray-500">{action.description}</p>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              );
            })}
          </div>
        </AdminCard>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Content Type Statistics */}
          <AdminCard title="آمار انواع محتوا" icon={BarChart3}>
            <div className="space-y-4">
              {contentTypeStats.map((item, index) => {
                const Icon = item.icon;
                return (
                  <motion.div
                    key={item.type}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <Icon className={`w-5 h-5 ${item.color}`} />
                      <span className="font-medium text-gray-900">{item.type}</span>
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium text-gray-900">
                        {item.count.toLocaleString('fa-IR')} کل
                      </div>
                      <div className="text-xs text-gray-500">
                        {item.published.toLocaleString('fa-IR')} منتشر شده
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </AdminCard>

          {/* Recent Content */}
          <AdminCard title="محتوای اخیر" icon={Calendar}>
            <div className="space-y-3">
              {recentContent.map((content, index) => {
                const Icon = getContentTypeIcon(content.type);
                const colorClass = getContentTypeColor(content.type);
                
                return (
                  <motion.div
                    key={content.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <Icon className={`w-4 h-4 ${colorClass}`} />
                      <div>
                        <h4 className="font-medium text-gray-900 text-sm">{content.title}</h4>
                        <p className="text-xs text-gray-500">
                          {formatPersianDate(content.updatedAt)}
                        </p>
                      </div>
                    </div>
                    <div className="text-left">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        content.status === 'published' ? 'bg-green-100 text-green-800' :
                        content.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                        content.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {formatContentStatus(content.status)}
                      </span>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </AdminCard>
        </div>

        {/* Content Management Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdminCard>
            <div className="text-center">
              <Image className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900 mb-1">مدیریت بنرها</h3>
              <p className="text-sm text-gray-500 mb-3">
                {banners.length.toLocaleString('fa-IR')} بنر
              </p>
              <Link to="/admin/content/banners">
                <AdminButton variant="outline" size="sm" className="w-full">
                  مشاهده همه
                </AdminButton>
              </Link>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="text-center">
              <Tag className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900 mb-1">مدیریت تخفیف‌ها</h3>
              <p className="text-sm text-gray-500 mb-3">
                {promotions.length.toLocaleString('fa-IR')} تخفیف
              </p>
              <Link to="/admin/content/promotions">
                <AdminButton variant="outline" size="sm" className="w-full">
                  مشاهده همه
                </AdminButton>
              </Link>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="text-center">
              <Mail className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900 mb-1">مدیریت خبرنامه</h3>
              <p className="text-sm text-gray-500 mb-3">
                {newsletters.length.toLocaleString('fa-IR')} کمپین
              </p>
              <Link to="/admin/content/newsletter">
                <AdminButton variant="outline" size="sm" className="w-full">
                  مشاهده همه
                </AdminButton>
              </Link>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="text-center">
              <FileText className="w-8 h-8 text-orange-600 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900 mb-1">مدیریت صفحات</h3>
              <p className="text-sm text-gray-500 mb-3">
                {pages.length.toLocaleString('fa-IR')} صفحه
              </p>
              <Link to="/admin/content/pages">
                <AdminButton variant="outline" size="sm" className="w-full">
                  مشاهده همه
                </AdminButton>
              </Link>
            </div>
          </AdminCard>
        </div>
      </div>
    </AdminLayout>
  );
};

export default ContentDashboardPage;
