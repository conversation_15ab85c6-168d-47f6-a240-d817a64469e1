import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { ArrowLeft, Save, Send, Users } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import NewsletterEditor from '../../../components/admin/content/NewsletterEditor';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { NewsletterCampaignFormData } from '../../../types/adminContent';

const NewsletterCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { createNewsletterCampaign } = useAdminContent();
  const [loading, setLoading] = useState(false);

  const handleSave = async (data: NewsletterCampaignFormData) => {
    try {
      setLoading(true);
      const newCampaign = await createNewsletterCampaign(data);
      toast.success('کمپین خبرنامه با موفقیت ایجاد شد');
      navigate(`/admin/content/newsletter/${newCampaign.id}`);
    } catch (error) {
      console.error('Error creating newsletter campaign:', error);
      toast.error('خطا در ایجاد کمپین خبرنامه');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/content/newsletter');
  };

  return (
    <AdminLayout 
      title="ایجاد کمپین خبرنامه جدید"
      subtitle="ایجاد کمپین ایمیل مارکتینگ جدید برای ارسال به مشترکین"
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="outline" 
            icon={ArrowLeft}
            onClick={handleCancel}
          >
            بازگشت
          </AdminButton>
        </div>
      }
    >
      <NewsletterEditor
        onSave={handleSave}
        onCancel={handleCancel}
        loading={loading}
        mode="create"
      />
    </AdminLayout>
  );
};

export default NewsletterCreatePage;
