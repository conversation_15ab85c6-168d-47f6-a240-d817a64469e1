import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  ArrowLeft, 
  Edit, 
  Eye, 
  Mail, 
  Users, 
  MousePointer, 
  TrendingUp, 
  Calendar,
  Send,
  BarChart3,
  Clock,
  CheckCircle
} from 'lucide-react';
import { motion } from 'framer-motion';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { AdminTableBadge } from '../../../components/admin/common/AdminTable';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { NewsletterCampaign } from '../../../types/adminContent';
import { formatContentStatus, formatPersianDate, formatPersianDateTime } from '../../../utils/contentUtils';

const NewsletterDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { getNewsletterById, getNewsletterAnalytics } = useAdminContent();
  
  const [newsletter, setNewsletter] = useState<NewsletterCampaign | null>(null);
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadNewsletterData = async () => {
      if (!id) {
        navigate('/admin/content/newsletter');
        return;
      }

      try {
        setLoading(true);
        const [newsletterData, analyticsData] = await Promise.all([
          getNewsletterById(id),
          getNewsletterAnalytics(id)
        ]);
        
        if (newsletterData) {
          setNewsletter(newsletterData);
          setAnalytics(analyticsData);
        } else {
          toast.error('کمپین مورد نظر یافت نشد');
          navigate('/admin/content/newsletter');
        }
      } catch (error) {
        console.error('Error loading newsletter:', error);
        toast.error('خطا در بارگذاری کمپین');
        navigate('/admin/content/newsletter');
      } finally {
        setLoading(false);
      }
    };

    loadNewsletterData();
  }, [id, getNewsletterById, getNewsletterAnalytics, navigate]);

  const handleEdit = () => {
    if (newsletter) {
      navigate(`/admin/content/newsletter/${newsletter.id}/edit`);
    }
  };

  const handlePreview = () => {
    if (newsletter) {
      window.open(`/newsletter/preview/${newsletter.id}`, '_blank');
    }
  };

  if (loading) {
    return (
      <AdminLayout title="جزئیات کمپین">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  if (!newsletter) {
    return (
      <AdminLayout title="جزئیات کمپین">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            کمپین یافت نشد
          </h3>
          <p className="text-gray-500 mb-6">
            کمپین مورد نظر وجود ندارد یا حذف شده است
          </p>
          <AdminButton 
            variant="primary" 
            onClick={() => navigate('/admin/content/newsletter')}
          >
            بازگشت به لیست کمپین‌ها
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  const openRate = analytics?.sent > 0 ? 
    ((analytics?.opened || 0) / analytics.sent * 100).toFixed(1) : '0';
  const clickRate = analytics?.opened > 0 ? 
    ((analytics?.clicked || 0) / analytics.opened * 100).toFixed(1) : '0';

  return (
    <AdminLayout 
      title="جزئیات کمپین"
      subtitle={newsletter.title}
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="ghost" 
            icon={ArrowLeft}
            onClick={() => navigate('/admin/content/newsletter')}
          >
            بازگشت
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Eye}
            onClick={handlePreview}
          >
            پیش‌نمایش
          </AdminButton>
          <AdminButton 
            variant="primary" 
            icon={Edit}
            onClick={handleEdit}
          >
            ویرایش
          </AdminButton>
        </div>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Campaign Info */}
          <AdminCard title="اطلاعات کمپین">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">{newsletter.title}</h2>
                <AdminTableBadge variant={
                  newsletter.status === 'sent' ? 'success' : 
                  newsletter.status === 'scheduled' ? 'info' : 'warning'
                }>
                  {formatContentStatus(newsletter.status)}
                </AdminTableBadge>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">موضوع:</span>
                  <span className="text-sm font-medium">{newsletter.subject}</span>
                </div>
                
                {newsletter.preheader && (
                  <div className="flex items-center gap-2">
                    <Eye className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">پیش‌نمایش:</span>
                    <span className="text-sm">{newsletter.preheader}</span>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">مخاطبان:</span>
                  <span className="text-sm font-medium">
                    {newsletter.recipientSegments?.join(', ') || 'همه مشترکین'}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">زمان ارسال:</span>
                  <span className="text-sm">
                    {newsletter.sendAt ? formatPersianDateTime(newsletter.sendAt) : 'فوری'}
                  </span>
                </div>
              </div>

              {newsletter.isABTest && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      تست A/B - نسخه {newsletter.abTestVariant}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </AdminCard>

          {/* Analytics */}
          {analytics && newsletter.status === 'sent' && (
            <AdminCard title="آمار عملکرد" icon={BarChart3}>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <motion.div 
                  className="text-center p-4 bg-blue-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <Send className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">
                    {analytics.sent?.toLocaleString('fa-IR') || '0'}
                  </div>
                  <div className="text-sm text-gray-600">ارسال شده</div>
                </motion.div>

                <motion.div 
                  className="text-center p-4 bg-green-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <Mail className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">
                    {analytics.opened?.toLocaleString('fa-IR') || '0'}
                  </div>
                  <div className="text-sm text-gray-600">باز شده</div>
                </motion.div>

                <motion.div 
                  className="text-center p-4 bg-purple-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <MousePointer className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-600">
                    {analytics.clicked?.toLocaleString('fa-IR') || '0'}
                  </div>
                  <div className="text-sm text-gray-600">کلیک شده</div>
                </motion.div>

                <motion.div 
                  className="text-center p-4 bg-orange-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <TrendingUp className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-orange-600">
                    {openRate}%
                  </div>
                  <div className="text-sm text-gray-600">نرخ بازدید</div>
                </motion.div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-lg font-bold text-gray-700">
                    {clickRate}%
                  </div>
                  <div className="text-sm text-gray-600">نرخ کلیک</div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-lg font-bold text-gray-700">
                    {analytics.unsubscribed?.toLocaleString('fa-IR') || '0'}
                  </div>
                  <div className="text-sm text-gray-600">لغو اشتراک</div>
                </div>
              </div>
            </AdminCard>
          )}

          {/* Content Preview */}
          <AdminCard title="پیش‌نمایش محتوا">
            <div className="prose prose-sm max-w-none">
              {newsletter.htmlContent ? (
                <div 
                  dangerouslySetInnerHTML={{ __html: newsletter.htmlContent }}
                  className="border rounded-lg p-4 bg-gray-50"
                />
              ) : (
                <div className="whitespace-pre-wrap text-gray-700 border rounded-lg p-4 bg-gray-50">
                  {newsletter.content}
                </div>
              )}
            </div>
          </AdminCard>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <AdminCard title="عملیات سریع">
            <div className="space-y-3">
              <AdminButton 
                variant="outline" 
                icon={Edit}
                onClick={handleEdit}
                className="w-full"
              >
                ویرایش کمپین
              </AdminButton>
              
              <AdminButton 
                variant="outline" 
                icon={Eye}
                onClick={handlePreview}
                className="w-full"
              >
                پیش‌نمایش
              </AdminButton>
            </div>
          </AdminCard>

          {/* Campaign Details */}
          <AdminCard title="جزئیات">
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">نوع کمپین:</span>
                <span className="font-medium">
                  {newsletter.campaignType === 'promotional' ? 'تبلیغاتی' : 
                   newsletter.campaignType === 'newsletter' ? 'خبرنامه' : 'اطلاع‌رسانی'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">قالب:</span>
                <span className="font-medium">
                  {newsletter.template || 'پیش‌فرض'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">منطقه زمانی:</span>
                <span className="font-medium">
                  {newsletter.timezone || 'Asia/Tehran'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">تاریخ ایجاد:</span>
                <span className="font-medium">
                  {formatPersianDateTime(newsletter.createdAt)}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">آخرین به‌روزرسانی:</span>
                <span className="font-medium">
                  {formatPersianDateTime(newsletter.updatedAt)}
                </span>
              </div>
            </div>
          </AdminCard>

          {/* Status Timeline */}
          {newsletter.status === 'sent' && (
            <AdminCard title="وضعیت ارسال">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <div>
                    <div className="text-sm font-medium">ارسال شده</div>
                    <div className="text-xs text-gray-500">
                      {formatPersianDateTime(newsletter.sentAt || newsletter.updatedAt)}
                    </div>
                  </div>
                </div>
              </div>
            </AdminCard>
          )}

          {newsletter.status === 'scheduled' && (
            <AdminCard title="زمان‌بندی">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Clock className="w-5 h-5 text-blue-500" />
                  <div>
                    <div className="text-sm font-medium">ارسال در:</div>
                    <div className="text-xs text-gray-500">
                      {formatPersianDateTime(newsletter.sendAt!)}
                    </div>
                  </div>
                </div>
              </div>
            </AdminCard>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default NewsletterDetailPage;
