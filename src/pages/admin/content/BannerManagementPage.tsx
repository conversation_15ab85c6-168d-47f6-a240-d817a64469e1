import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2, 
  Copy,
  Image as ImageIcon,
  ExternalLink
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminTable, { AdminTableColumn, AdminActionMenu, AdminTableBadge } from '../../../components/admin/common/AdminTable';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { Banner, ContentFilters } from '../../../types/adminContent';
import { formatContentStatus, getStatusColor, formatPersianDate } from '../../../utils/contentUtils';

const BannerManagementPage: React.FC = () => {
  const { 
    banners, 
    loading, 
    error, 
    filters, 
    setFilters, 
    deleteBanner 
  } = useAdminContent();

  const [selectedBanners, setSelectedBanners] = useState<string[]>([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [bannerToDelete, setBannerToDelete] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter banners based on search and filters
  const filteredBanners = banners.filter(banner => {
    const matchesSearch = !searchTerm || 
      banner.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      banner.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !filters.status?.length || 
      filters.status.includes(banner.status);
    
    return matchesSearch && matchesStatus;
  });

  const handleDeleteBanner = async (id: string) => {
    try {
      await deleteBanner(id);
      setDeleteModalOpen(false);
      setBannerToDelete(null);
    } catch (error) {
      console.error('Error deleting banner:', error);
    }
  };

  const handleBulkDelete = async () => {
    try {
      for (const id of selectedBanners) {
        await deleteBanner(id);
      }
      setSelectedBanners([]);
    } catch (error) {
      console.error('Error bulk deleting banners:', error);
    }
  };

  const columns: AdminTableColumn<Banner>[] = [
    {
      key: 'image',
      title: 'تصویر',
      width: '80px',
      render: (banner: Banner) => (
        <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
          <img
            src={banner.image}
            alt="Banner"
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/api/placeholder/48/48';
            }}
          />
        </div>
      )
    },
    {
      key: 'title',
      title: 'عنوان',
      sortable: true,
      render: (banner: Banner) => (
        <div>
          <div className="font-medium text-gray-900">{banner.title}</div>
          {banner.subtitle && (
            <div className="text-sm text-gray-500">{banner.subtitle}</div>
          )}
        </div>
      )
    },
    {
      key: 'type',
      title: 'نوع',
      render: (banner: Banner) => {
        const typeLabels = {
          hero: 'اصلی',
          promotional: 'تبلیغاتی',
          announcement: 'اعلان',
          category: 'دسته‌بندی'
        };
        return (
          <AdminTableBadge variant="info">
            {typeLabels[banner.type as keyof typeof typeLabels] || banner.type}
          </AdminTableBadge>
        );
      }
    },
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      render: (banner: Banner) => (
        <AdminTableBadge variant={getStatusColor(banner.status as any) as any}>
          {formatContentStatus(banner.status as any)}
        </AdminTableBadge>
      )
    },
    {
      key: 'position',
      title: 'موقعیت',
      align: 'center',
      render: (banner: Banner) => (
        <span className="font-mono text-sm">{banner.position}</span>
      )
    },
    {
      key: 'views',
      title: 'بازدید',
      align: 'center',
      sortable: true,
      render: (banner: Banner) => (
        <div className="text-center">
          <div className="font-medium">{banner.views.toLocaleString('fa-IR')}</div>
        </div>
      )
    },
    {
      key: 'clicks',
      title: 'کلیک',
      align: 'center',
      sortable: true,
      render: (banner: Banner) => (
        <div className="text-center">
          <div className="font-medium">{banner.clicks.toLocaleString('fa-IR')}</div>
          {banner.views > 0 && (
            <div className="text-xs text-gray-500">
              {((banner.clicks / banner.views) * 100).toFixed(1)}%
            </div>
          )}
        </div>
      )
    },
    {
      key: 'updatedAt',
      title: 'آخرین به‌روزرسانی',
      sortable: true,
      render: (banner: Banner) => (
        <div className="text-sm text-gray-600">
          {formatPersianDate(banner.updatedAt)}
        </div>
      )
    }
  ];

  const getRowActions = (banner: Banner, index: number) => (
    <AdminActionMenu
      actions={[
        {
          label: 'مشاهده',
          onClick: () => window.open(banner.ctaUrl || '#', '_blank'),
          disabled: !banner.ctaUrl
        },
        {
          label: 'ویرایش',
          onClick: () => {
            // Navigate to edit page
            window.location.href = `/admin/content/banners/${banner.id}/edit`;
          }
        },
        {
          label: 'کپی',
          onClick: () => {
            // Implement banner duplication
            console.log('Duplicate banner:', banner.id);
          }
        },
        {
          label: 'حذف',
          onClick: () => {
            setBannerToDelete(banner.id);
            setDeleteModalOpen(true);
          },
          variant: 'danger'
        }
      ]}
    />
  );

  const statsCards = [
    {
      title: 'کل بنرها',
      value: banners.length.toLocaleString('fa-IR'),
      icon: ImageIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'منتشر شده',
      value: banners.filter(b => b.status === 'published').length.toLocaleString('fa-IR'),
      icon: Eye,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'پیش‌نویس',
      value: banners.filter(b => b.status === 'draft').length.toLocaleString('fa-IR'),
      icon: Edit,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50'
    },
    {
      title: 'کل بازدید',
      value: banners.reduce((sum, b) => sum + b.views, 0).toLocaleString('fa-IR'),
      icon: Eye,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    }
  ];

  if (loading) {
    return (
      <AdminLayout title="مدیریت بنرها">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="مدیریت بنرها"
      subtitle="مدیریت بنرهای تبلیغاتی و اعلانات سایت"
      actions={
        <AdminButton 
          variant="primary" 
          icon={Plus}
          onClick={() => window.location.href = '/admin/content/banners/create'}
        >
          ایجاد بنر جدید
        </AdminButton>
      }
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Filters and Search */}
        <AdminCard>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="جستجو در بنرها..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <AdminButton variant="outline" icon={Filter} size="sm">
                فیلتر
              </AdminButton>
              {selectedBanners.length > 0 && (
                <AdminButton 
                  variant="danger" 
                  icon={Trash2} 
                  size="sm"
                  onClick={handleBulkDelete}
                >
                  حذف انتخاب شده ({selectedBanners.length})
                </AdminButton>
              )}
            </div>
          </div>
        </AdminCard>

        {/* Banners Table */}
        <AdminCard>
          <AdminTable
            columns={columns}
            data={filteredBanners}
            loading={loading}
            rowActions={getRowActions}
            emptyMessage="هیچ بنری یافت نشد"
            onRowClick={(banner, index) => {
              // Navigate to banner details
              window.location.href = `/admin/content/banners/${banner.id}`;
            }}
          />
        </AdminCard>

        {/* Delete Confirmation Modal */}
        <AdminConfirmModal
          isOpen={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setBannerToDelete(null);
          }}
          onConfirm={() => bannerToDelete && handleDeleteBanner(bannerToDelete)}
          title="حذف بنر"
          message="آیا از حذف این بنر اطمینان دارید؟ این عمل قابل بازگشت نیست."
          confirmText="حذف"
          cancelText="انصراف"
          variant="danger"
        />
      </div>
    </AdminLayout>
  );
};

export default BannerManagementPage;
