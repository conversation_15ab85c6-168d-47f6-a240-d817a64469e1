import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  ArrowLeft, 
  Edit, 
  Eye, 
  MousePointer, 
  TrendingUp, 
  Calendar,
  Users,
  ShoppingCart,
  Copy,
  BarChart3,
  Tag,
  Percent
} from 'lucide-react';
import { motion } from 'framer-motion';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { AdminTableBadge } from '../../../components/admin/common/AdminTable';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { Promotion } from '../../../types/adminContent';
import { formatContentStatus, formatPersianDate, formatPersianDateTime } from '../../../utils/contentUtils';

const PromotionDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { getPromotionById, getPromotionAnalytics } = useAdminContent();
  
  const [promotion, setPromotion] = useState<Promotion | null>(null);
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadPromotionData = async () => {
      if (!id) {
        navigate('/admin/content/promotions');
        return;
      }

      try {
        setLoading(true);
        const [promotionData, analyticsData] = await Promise.all([
          getPromotionById(id),
          getPromotionAnalytics(id)
        ]);
        
        if (promotionData) {
          setPromotion(promotionData);
          setAnalytics(analyticsData);
        } else {
          toast.error('تخفیف مورد نظر یافت نشد');
          navigate('/admin/content/promotions');
        }
      } catch (error) {
        console.error('Error loading promotion:', error);
        toast.error('خطا در بارگذاری تخفیف');
        navigate('/admin/content/promotions');
      } finally {
        setLoading(false);
      }
    };

    loadPromotionData();
  }, [id, getPromotionById, getPromotionAnalytics, navigate]);

  const handleEdit = () => {
    if (promotion) {
      navigate(`/admin/content/promotions/${promotion.id}/edit`);
    }
  };

  const handlePreview = () => {
    if (promotion) {
      window.open(`/promotions/${promotion.code}`, '_blank');
    }
  };

  const copyPromotionCode = () => {
    if (promotion) {
      navigator.clipboard.writeText(promotion.code);
      toast.success('کد تخفیف کپی شد');
    }
  };

  if (loading) {
    return (
      <AdminLayout title="جزئیات تخفیف">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  if (!promotion) {
    return (
      <AdminLayout title="جزئیات تخفیف">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            تخفیف یافت نشد
          </h3>
          <p className="text-gray-500 mb-6">
            تخفیف مورد نظر وجود ندارد یا حذف شده است
          </p>
          <AdminButton 
            variant="primary" 
            onClick={() => navigate('/admin/content/promotions')}
          >
            بازگشت به لیست تخفیف‌ها
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  const conversionRate = analytics?.totalUsage > 0 ? 
    ((analytics?.conversions || 0) / analytics.totalUsage * 100).toFixed(1) : '0';

  return (
    <AdminLayout 
      title="جزئیات تخفیف"
      subtitle={promotion.title}
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="ghost" 
            icon={ArrowLeft}
            onClick={() => navigate('/admin/content/promotions')}
          >
            بازگشت
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Eye}
            onClick={handlePreview}
          >
            پیش‌نمایش
          </AdminButton>
          <AdminButton 
            variant="primary" 
            icon={Edit}
            onClick={handleEdit}
          >
            ویرایش
          </AdminButton>
        </div>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Promotion Info */}
          <AdminCard title="اطلاعات تخفیف">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">{promotion.title}</h2>
                <AdminTableBadge variant={promotion.status === 'published' ? 'success' : 'warning'}>
                  {formatContentStatus(promotion.status)}
                </AdminTableBadge>
              </div>
              
              {promotion.description && (
                <p className="text-gray-600">{promotion.description}</p>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Tag className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">کد تخفیف:</span>
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                    {promotion.code}
                  </code>
                  <button 
                    onClick={copyPromotionCode}
                    className="text-admin-600 hover:text-admin-700"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                
                <div className="flex items-center gap-2">
                  <Percent className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">نوع تخفیف:</span>
                  <span className="text-sm font-medium">
                    {promotion.type === 'percentage' ? 'درصدی' : 
                     promotion.type === 'fixed_amount' ? 'مبلغ ثابت' :
                     promotion.type === 'buy_one_get_one' ? 'یکی بخر یکی بگیر' : 'ارسال رایگان'}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">تاریخ شروع:</span>
                  <span className="text-sm">{formatPersianDate(promotion.startDate)}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">تاریخ پایان:</span>
                  <span className="text-sm">{formatPersianDate(promotion.endDate)}</span>
                </div>
              </div>
            </div>
          </AdminCard>

          {/* Analytics */}
          {analytics && (
            <AdminCard title="آمار عملکرد" icon={BarChart3}>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <motion.div 
                  className="text-center p-4 bg-blue-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">
                    {analytics.totalUsage?.toLocaleString('fa-IR') || '0'}
                  </div>
                  <div className="text-sm text-gray-600">تعداد استفاده</div>
                </motion.div>

                <motion.div 
                  className="text-center p-4 bg-green-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <ShoppingCart className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">
                    {analytics.conversions?.toLocaleString('fa-IR') || '0'}
                  </div>
                  <div className="text-sm text-gray-600">تبدیل به خرید</div>
                </motion.div>

                <motion.div 
                  className="text-center p-4 bg-purple-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <TrendingUp className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-600">
                    {conversionRate}%
                  </div>
                  <div className="text-sm text-gray-600">نرخ تبدیل</div>
                </motion.div>

                <motion.div 
                  className="text-center p-4 bg-orange-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <MousePointer className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-orange-600">
                    {analytics.revenue?.toLocaleString('fa-IR') || '0'}
                  </div>
                  <div className="text-sm text-gray-600">درآمد (تومان)</div>
                </motion.div>
              </div>
            </AdminCard>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <AdminCard title="عملیات سریع">
            <div className="space-y-3">
              <AdminButton 
                variant="outline" 
                icon={Edit}
                onClick={handleEdit}
                className="w-full"
              >
                ویرایش تخفیف
              </AdminButton>
              
              <AdminButton 
                variant="outline" 
                icon={Eye}
                onClick={handlePreview}
                className="w-full"
              >
                پیش‌نمایش
              </AdminButton>
              
              <AdminButton 
                variant="outline" 
                icon={Copy}
                onClick={copyPromotionCode}
                className="w-full"
              >
                کپی کد تخفیف
              </AdminButton>
            </div>
          </AdminCard>

          {/* Promotion Details */}
          <AdminCard title="جزئیات">
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">مقدار تخفیف:</span>
                <span className="font-medium">
                  {promotion.type === 'percentage' ? `${promotion.discountValue}%` : 
                   `${promotion.discountValue?.toLocaleString('fa-IR')} تومان`}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">حداقل مبلغ سفارش:</span>
                <span className="font-medium">
                  {promotion.minimumOrderAmount?.toLocaleString('fa-IR') || '0'} تومان
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">محدودیت استفاده:</span>
                <span className="font-medium">
                  {promotion.usageLimit || 'نامحدود'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">تاریخ ایجاد:</span>
                <span className="font-medium">
                  {formatPersianDateTime(promotion.createdAt)}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">آخرین به‌روزرسانی:</span>
                <span className="font-medium">
                  {formatPersianDateTime(promotion.updatedAt)}
                </span>
              </div>
            </div>
          </AdminCard>
        </div>
      </div>
    </AdminLayout>
  );
};

export default PromotionDetailPage;
