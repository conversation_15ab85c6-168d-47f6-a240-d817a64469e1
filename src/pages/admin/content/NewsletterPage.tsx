import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Mail,
  Send,
  Users,
  TrendingUp,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Copy,
  Calendar,
  ExternalLink
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminTable, { AdminTableColumn, AdminActionMenu, AdminTableBadge } from '../../../components/admin/common/AdminTable';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { NewsletterCampaign, ContentFilters } from '../../../types/adminContent';
import { formatContentStatus, getStatusColor, formatPersianDate } from '../../../utils/contentUtils';

const NewsletterPage: React.FC = () => {
  const {
    newsletters,
    loading,
    error,
    filters,
    setFilters,
    deleteNewsletter
  } = useAdminContent();

  const [selectedNewsletters, setSelectedNewsletters] = useState<string[]>([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [newsletterToDelete, setNewsletterToDelete] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter newsletters based on search and filters
  const filteredNewsletters = newsletters.filter(newsletter => {
    const matchesSearch = !searchTerm ||
      newsletter.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      newsletter.subject.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !filters.status?.length ||
      filters.status.includes(newsletter.status);

    return matchesSearch && matchesStatus;
  });

  const handleDeleteNewsletter = async (id: string) => {
    try {
      await deleteNewsletter(id);
      setDeleteModalOpen(false);
      setNewsletterToDelete(null);
    } catch (error) {
      console.error('Error deleting newsletter:', error);
    }
  };

  const handleBulkDelete = async () => {
    try {
      for (const id of selectedNewsletters) {
        await deleteNewsletter(id);
      }
      setSelectedNewsletters([]);
    } catch (error) {
      console.error('Error bulk deleting newsletters:', error);
    }
  };

  const columns: AdminTableColumn<NewsletterCampaign>[] = [
    {
      key: 'title',
      title: 'عنوان کمپین',
      sortable: true,
      render: (newsletter: NewsletterCampaign) => (
        <div>
          <div className="font-medium text-gray-900">{newsletter.title}</div>
          <div className="text-sm text-gray-500">{newsletter.subject}</div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      render: (newsletter: NewsletterCampaign) => {
        const statusLabels = {
          draft: 'پیش‌نویس',
          scheduled: 'زمان‌بندی شده',
          published: 'ارسال شده',
          archived: 'بایگانی شده'
        };
        return (
          <AdminTableBadge variant={getStatusColor(newsletter.status as any) as any}>
            {statusLabels[newsletter.status as keyof typeof statusLabels] || newsletter.status}
          </AdminTableBadge>
        );
      }
    },
    {
      key: 'recipientCount',
      title: 'مخاطبان',
      align: 'center',
      sortable: true,
      render: (newsletter: NewsletterCampaign) => (
        <div className="text-center">
          <div className="font-medium">{newsletter.recipientCount.toLocaleString('fa-IR')}</div>
          <div className="text-xs text-gray-500">مخاطب</div>
        </div>
      )
    },
    {
      key: 'openRate',
      title: 'نرخ بازخوانی',
      align: 'center',
      sortable: true,
      render: (newsletter: NewsletterCampaign) => (
        <div className="text-center">
          <div className="font-medium text-green-600">{newsletter.openRate}%</div>
        </div>
      )
    },
    {
      key: 'clickRate',
      title: 'نرخ کلیک',
      align: 'center',
      sortable: true,
      render: (newsletter: NewsletterCampaign) => (
        <div className="text-center">
          <div className="font-medium text-blue-600">{newsletter.clickRate}%</div>
        </div>
      )
    },
    {
      key: 'sentAt',
      title: 'تاریخ ارسال',
      sortable: true,
      render: (newsletter: NewsletterCampaign) => (
        <div className="text-sm text-gray-600">
          {newsletter.sentAt ? formatPersianDate(newsletter.sentAt) : '-'}
        </div>
      )
    },
    {
      key: 'updatedAt',
      title: 'آخرین به‌روزرسانی',
      sortable: true,
      render: (newsletter: NewsletterCampaign) => (
        <div className="text-sm text-gray-600">
          {formatPersianDate(newsletter.updatedAt)}
        </div>
      )
    }
  ];

  const getRowActions = (newsletter: NewsletterCampaign, index: number) => (
    <AdminActionMenu
      actions={[
        {
          label: 'مشاهده',
          onClick: () => window.open(`/admin/content/newsletter/${newsletter.id}`, '_blank')
        },
        {
          label: 'ویرایش',
          onClick: () => {
            window.location.href = `/admin/content/newsletter/${newsletter.id}/edit`;
          }
        },
        {
          label: 'کپی',
          onClick: () => {
            console.log('Duplicate newsletter:', newsletter.id);
          }
        },
        {
          label: 'حذف',
          onClick: () => {
            setNewsletterToDelete(newsletter.id);
            setDeleteModalOpen(true);
          },
          variant: 'danger'
        }
      ]}
    />
  );

  const statsCards = [
    {
      title: 'کل کمپین‌ها',
      value: newsletters.length.toLocaleString('fa-IR'),
      icon: Mail,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'ارسال شده',
      value: newsletters.filter(n => n.status === 'published').length.toLocaleString('fa-IR'),
      icon: Send,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'کل مخاطبان',
      value: newsletters.reduce((sum, n) => sum + n.recipientCount, 0).toLocaleString('fa-IR'),
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'میانگین بازخوانی',
      value: newsletters.length > 0 ? 
        `${(newsletters.reduce((sum, n) => sum + n.openRate, 0) / newsletters.length).toFixed(1)}%` : 
        '0%',
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  if (loading) {
    return (
      <AdminLayout title="مدیریت خبرنامه">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="مدیریت خبرنامه"
      subtitle="مدیریت کمپین‌های ایمیل مارکتینگ و خبرنامه"
      actions={
        <AdminButton
          variant="primary"
          icon={Plus}
          onClick={() => window.location.href = '/admin/content/newsletter/create'}
        >
          ایجاد کمپین جدید
        </AdminButton>
      }
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Filters and Search */}
        <AdminCard>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="جستجو در کمپین‌ها..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <AdminButton variant="outline" icon={Filter} size="sm">
                فیلتر
              </AdminButton>
              {selectedNewsletters.length > 0 && (
                <AdminButton
                  variant="danger"
                  icon={Trash2}
                  size="sm"
                  onClick={handleBulkDelete}
                >
                  حذف انتخاب شده ({selectedNewsletters.length})
                </AdminButton>
              )}
            </div>
          </div>
        </AdminCard>

        {/* Newsletter Table */}
        <AdminCard>
          <AdminTable
            columns={columns}
            data={filteredNewsletters}
            loading={loading}
            rowActions={getRowActions}
            emptyMessage="هیچ کمپینی یافت نشد"
            onRowClick={(newsletter, index) => {
              window.location.href = `/admin/content/newsletter/${newsletter.id}`;
            }}
          />
        </AdminCard>

        {/* Delete Confirmation Modal */}
        <AdminConfirmModal
          isOpen={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setNewsletterToDelete(null);
          }}
          onConfirm={() => newsletterToDelete && handleDeleteNewsletter(newsletterToDelete)}
          title="حذف کمپین"
          message="آیا از حذف این کمپین اطمینان دارید؟ این عمل قابل بازگشت نیست."
          confirmText="حذف"
          cancelText="انصراف"
          variant="danger"
        />
      </div>
    </AdminLayout>
  );
};

export default NewsletterPage;
