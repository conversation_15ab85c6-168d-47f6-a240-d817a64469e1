import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { ArrowLeft, Save, Eye } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import BannerEditor from '../../../components/admin/content/BannerEditor';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { BannerFormData } from '../../../types/adminContent';

const BannerCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { createBanner } = useAdminContent();
  const [loading, setLoading] = useState(false);

  const handleSave = async (data: BannerFormData) => {
    try {
      setLoading(true);
      const newBanner = await createBanner(data);
      toast.success('بنر با موفقیت ایجاد شد');
      navigate(`/admin/content/banners/${newBanner.id}`);
    } catch (error) {
      console.error('Error creating banner:', error);
      toast.error('خطا در ایجاد بنر');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/content/banners');
  };

  return (
    <AdminLayout 
      title="ایجاد بنر جدید"
      subtitle="ایجاد بنر تبلیغاتی جدید برای نمایش در سایت"
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="outline" 
            icon={ArrowLeft}
            onClick={handleCancel}
          >
            بازگشت
          </AdminButton>
        </div>
      }
    >
      <BannerEditor
        onSave={handleSave}
        onCancel={handleCancel}
        loading={loading}
        mode="create"
      />
    </AdminLayout>
  );
};

export default BannerCreatePage;
