import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { <PERSON><PERSON>eft, Save, Eye, Trash2, Copy } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import PageEditor from '../../../components/admin/content/PageEditor';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { PageContentFormData, PageContent } from '../../../types/adminContent';

const PageEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { getPageById, updatePage, deletePage, duplicatePage, loading: contentLoading } = useAdminContent();

  const [page, setPage] = useState<PageContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  useEffect(() => {
    const loadPage = async () => {
      if (!id) {
        navigate('/admin/content/pages');
        return;
      }

      // Wait for content to be loaded first
      if (contentLoading) {
        return;
      }

      try {
        setLoading(true);
        const pageData = await getPageById(id);
        if (pageData) {
          setPage(pageData);
        } else {
          toast.error('صفحه مورد نظر یافت نشد');
          navigate('/admin/content/pages');
        }
      } catch (error) {
        console.error('Error loading page:', error);
        toast.error('خطا در بارگذاری صفحه');
        navigate('/admin/content/pages');
      } finally {
        setLoading(false);
      }
    };

    loadPage();
  }, [id, getPageById, navigate, contentLoading]);

  const handleSave = async (formData: PageContentFormData) => {
    if (!id) return;

    try {
      setSaving(true);
      await updatePage(id, formData);
      toast.success('صفحه با موفقیت به‌روزرسانی شد');
      navigate('/admin/content/pages');
    } catch (error) {
      console.error('Error updating page:', error);
      toast.error('خطا در به‌روزرسانی صفحه');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!id) return;

    try {
      await deletePage(id);
      toast.success('صفحه با موفقیت حذف شد');
      navigate('/admin/content/pages');
    } catch (error) {
      console.error('Error deleting page:', error);
      toast.error('خطا در حذف صفحه');
    }
  };

  const handleDuplicate = async () => {
    if (!id) return;

    try {
      const duplicatedPage = await duplicatePage(id);
      toast.success('صفحه با موفقیت کپی شد');
      navigate(`/admin/content/pages/${duplicatedPage.id}/edit`);
    } catch (error) {
      console.error('Error duplicating page:', error);
      toast.error('خطا در کپی کردن صفحه');
    }
  };

  const handlePreview = () => {
    if (page) {
      // Open preview in new tab
      window.open(`/pages/${page.slug || page.id}`, '_blank');
    }
  };

  if (loading || contentLoading) {
    return (
      <AdminLayout title="ویرایش صفحه">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  if (!page) {
    return (
      <AdminLayout title="ویرایش صفحه">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            صفحه یافت نشد
          </h3>
          <p className="text-gray-500 mb-6">
            صفحه مورد نظر وجود ندارد یا حذف شده است
          </p>
          <AdminButton 
            variant="primary" 
            onClick={() => navigate('/admin/content/pages')}
          >
            بازگشت به لیست صفحات
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="ویرایش صفحه"
      subtitle={`ویرایش صفحه: ${page.title}`}
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="ghost" 
            icon={ArrowLeft}
            onClick={() => navigate('/admin/content/pages')}
          >
            بازگشت
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Copy}
            onClick={handleDuplicate}
          >
            کپی
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Eye}
            onClick={handlePreview}
          >
            پیش‌نمایش
          </AdminButton>
          <AdminButton 
            variant="danger" 
            icon={Trash2}
            onClick={() => setDeleteModalOpen(true)}
          >
            حذف
          </AdminButton>
        </div>
      }
    >
      <PageEditor
        initialData={page}
        onSave={handleSave}
        onCancel={() => navigate('/admin/content/pages')}
        loading={saving}
        mode="edit"
      />

      <AdminConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title="حذف صفحه"
        message={`آیا از حذف صفحه "${page.title}" اطمینان دارید؟ این عمل قابل بازگشت نیست.`}
        confirmText="حذف"
        cancelText="انصراف"
        variant="danger"
      />
    </AdminLayout>
  );
};

export default PageEditPage;
