import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Upload, 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  Folder, 
  Image as ImageIcon,
  Video,
  FileText,
  Download,
  Trash2,
  Eye,
  Copy
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { MediaItem, MediaType } from '../../../types/adminContent';
import { formatPersianDate } from '../../../utils/contentUtils';

const MediaLibraryPage: React.FC = () => {
  const { mediaItems, loading } = useAdminContent();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<MediaType | 'all'>('all');
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);

  // Filter media items
  const filteredItems = mediaItems.filter(item => {
    const matchesSearch = !searchTerm || 
      item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'all' || item.type === filterType;
    
    return matchesSearch && matchesType;
  });

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };

  const handleDeleteItem = (id: string) => {
    setItemToDelete(id);
    setDeleteModalOpen(true);
  };

  const confirmDelete = () => {
    if (itemToDelete) {
      // TODO: Implement delete functionality
      console.log('Delete item:', itemToDelete);
      setDeleteModalOpen(false);
      setItemToDelete(null);
    }
  };

  const handleBulkDelete = () => {
    // TODO: Implement bulk delete
    console.log('Bulk delete:', selectedItems);
    setSelectedItems([]);
  };

  const handleUpload = () => {
    // TODO: Implement file upload
    console.log('Upload files');
  };

  const getFileIcon = (type: MediaType) => {
    switch (type) {
      case 'image': return ImageIcon;
      case 'video': return Video;
      case 'document': return FileText;
      default: return FileText;
    }
  };

  const getFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const statsCards = [
    {
      title: 'کل فایل‌ها',
      value: mediaItems.length.toLocaleString('fa-IR'),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'تصاویر',
      value: mediaItems.filter(item => item.type === 'image').length.toLocaleString('fa-IR'),
      icon: ImageIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'ویدیوها',
      value: mediaItems.filter(item => item.type === 'video').length.toLocaleString('fa-IR'),
      icon: Video,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'حجم کل',
      value: getFileSize(mediaItems.reduce((sum, item) => sum + item.size, 0)),
      icon: Folder,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  if (loading) {
    return (
      <AdminLayout title="کتابخانه رسانه">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="کتابخانه رسانه"
      subtitle="مدیریت فایل‌ها، تصاویر و رسانه‌های سایت"
      actions={
        <AdminButton 
          variant="primary" 
          icon={Upload}
          onClick={handleUpload}
        >
          آپلود فایل
        </AdminButton>
      }
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Filters and Controls */}
        <AdminCard>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="جستجو در فایل‌ها..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as MediaType | 'all')}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              >
                <option value="all">همه فایل‌ها</option>
                <option value="image">تصاویر</option>
                <option value="video">ویدیوها</option>
                <option value="document">اسناد</option>
              </select>

              <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-admin-100 text-admin-700' : 'text-gray-600 hover:bg-gray-100'}`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-admin-100 text-admin-700' : 'text-gray-600 hover:bg-gray-100'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>

              {selectedItems.length > 0 && (
                <AdminButton 
                  variant="danger" 
                  icon={Trash2} 
                  size="sm"
                  onClick={handleBulkDelete}
                >
                  حذف انتخاب شده ({selectedItems.length})
                </AdminButton>
              )}
            </div>
          </div>

          {filteredItems.length > 0 && (
            <div className="mt-4 flex items-center gap-2">
              <input
                type="checkbox"
                checked={selectedItems.length === filteredItems.length}
                onChange={handleSelectAll}
                className="rounded border-gray-300 text-admin-600 focus:ring-admin-500"
              />
              <span className="text-sm text-gray-600">
                انتخاب همه ({filteredItems.length} فایل)
              </span>
            </div>
          )}
        </AdminCard>

        {/* Media Grid/List */}
        <AdminCard>
          {filteredItems.length === 0 ? (
            <div className="text-center py-12">
              <ImageIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                هیچ فایلی یافت نشد
              </h3>
              <p className="text-gray-500 mb-6">
                {searchTerm || filterType !== 'all' 
                  ? 'فیلترهای خود را تغییر دهید یا فایل جدید آپلود کنید'
                  : 'اولین فایل خود را آپلود کنید'
                }
              </p>
              <AdminButton variant="primary" icon={Upload} onClick={handleUpload}>
                آپلود فایل
              </AdminButton>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {filteredItems.map((item, index) => {
                const Icon = getFileIcon(item.type);
                const isSelected = selectedItems.includes(item.id);
                
                return (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.05 }}
                    className={`relative group border-2 rounded-lg overflow-hidden cursor-pointer transition-all ${
                      isSelected ? 'border-admin-500 bg-admin-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleSelectItem(item.id)}
                  >
                    <div className="aspect-square bg-gray-100 flex items-center justify-center">
                      {item.type === 'image' ? (
                        <img 
                          src={item.thumbnailUrl || item.url} 
                          alt={item.altText || item.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : (
                        <Icon className="w-8 h-8 text-gray-400" />
                      )}
                      <div className="hidden flex items-center justify-center w-full h-full">
                        <Icon className="w-8 h-8 text-gray-400" />
                      </div>
                    </div>
                    
                    <div className="p-2">
                      <h4 className="text-xs font-medium text-gray-900 truncate">
                        {item.title || item.filename}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {getFileSize(item.size)}
                      </p>
                    </div>

                    {/* Selection checkbox */}
                    <div className="absolute top-2 right-2">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleSelectItem(item.id)}
                        className="rounded border-gray-300 text-admin-600 focus:ring-admin-500"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>

                    {/* Action buttons */}
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(item.url, '_blank');
                        }}
                        className="p-2 bg-white text-gray-700 rounded-lg hover:bg-gray-100"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          navigator.clipboard.writeText(item.url);
                        }}
                        className="p-2 bg-white text-gray-700 rounded-lg hover:bg-gray-100"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteItem(item.id);
                        }}
                        className="p-2 bg-white text-red-600 rounded-lg hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredItems.map((item, index) => {
                const Icon = getFileIcon(item.type);
                const isSelected = selectedItems.includes(item.id);
                
                return (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`flex items-center gap-4 p-4 border rounded-lg cursor-pointer transition-all ${
                      isSelected ? 'border-admin-500 bg-admin-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleSelectItem(item.id)}
                  >
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => handleSelectItem(item.id)}
                      className="rounded border-gray-300 text-admin-600 focus:ring-admin-500"
                      onClick={(e) => e.stopPropagation()}
                    />

                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      {item.type === 'image' ? (
                        <img 
                          src={item.thumbnailUrl || item.url} 
                          alt={item.altText || item.title}
                          className="w-full h-full object-cover rounded-lg"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : (
                        <Icon className="w-6 h-6 text-gray-400" />
                      )}
                      <div className="hidden">
                        <Icon className="w-6 h-6 text-gray-400" />
                      </div>
                    </div>

                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 truncate">
                        {item.title || item.filename}
                      </h4>
                      <p className="text-sm text-gray-500 truncate">
                        {item.description || item.originalName}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                        <span>{getFileSize(item.size)}</span>
                        <span>{formatPersianDate(item.uploadedAt)}</span>
                        <span>{item.usageCount} استفاده</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(item.url, '_blank');
                        }}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          navigator.clipboard.writeText(item.url);
                        }}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteItem(item.id);
                        }}
                        className="p-2 text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          )}
        </AdminCard>

        {/* Delete Confirmation Modal */}
        <AdminConfirmModal
          isOpen={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setItemToDelete(null);
          }}
          onConfirm={confirmDelete}
          title="حذف فایل"
          message="آیا از حذف این فایل اطمینان دارید؟ این عمل قابل بازگشت نیست."
          confirmText="حذف"
          cancelText="انصراف"
          variant="danger"
        />
      </div>
    </AdminLayout>
  );
};

export default MediaLibraryPage;
