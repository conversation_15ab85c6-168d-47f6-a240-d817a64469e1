import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  TrendingUp,
  TrendingDown,
  UserPlus,
  UserCheck,
  Crown,
  Calendar,
  Download,
  RefreshCw,
  Target,
  Heart,
  ShoppingBag,
  DollarSign
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard, { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminAnalytics } from '../../../hooks/useAdminAnalytics';
import { formatCurrency, formatPercentage, formatGrowthRate, getDateRangeOptions } from '../../../utils/analyticsUtils';
import { formatNumber } from '../../../utils/formatters';

const CustomersAnalyticsPage: React.FC = () => {
  const { customerData, overview, loading, error } = useAdminAnalytics();
  const [selectedPeriod, setSelectedPeriod] = useState('last30days');

  const handleExport = async () => {
    console.log('Exporting customer analytics...');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-red-600">{error}</p>
          <AdminButton onClick={handleRefresh} className="mt-4">
            تلاش مجدد
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">تحلیل مشتریان</h1>
            <p className="text-gray-600 mt-1">رفتار، بخش‌بندی و تحلیل ارزش مشتریان</p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
            >
              {getDateRangeOptions().map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              onClick={handleRefresh}
            >
              بروزرسانی
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
            >
              خروجی Excel
            </AdminButton>
          </div>
        </div>

        {/* Key Metrics */}
        {overview && customerData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <AdminStatsCard
              title="کل مشتریان"
              value={formatNumber(overview.totalCustomers)}
              change={formatGrowthRate(overview.customersGrowth).formatted}
              changeType={overview.customersGrowth >= 0 ? 'increase' : 'decrease'}
              icon={Users}
              color="bg-blue-500"
            />
            
            <AdminStatsCard
              title="مشتریان جدید"
              value={formatNumber(customerData.newCustomers.length)}
              change="+12.5%"
              changeType="increase"
              icon={UserPlus}
              color="bg-green-500"
            />
            
            <AdminStatsCard
              title="میانگین ارزش مشتری"
              value={formatCurrency(customerData.customerLifetime.averageLifetimeValue)}
              change="+8.3%"
              changeType="increase"
              icon={DollarSign}
              color="bg-purple-500"
            />
            
            <AdminStatsCard
              title="نرخ نگهداری"
              value={formatPercentage(customerData.customerLifetime.churnRate)}
              change="-2.1%"
              changeType="decrease"
              icon={Heart}
              color="bg-orange-500"
            />
          </div>
        )}

        {/* Customer Segments */}
        {customerData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AdminCard title="بخش‌بندی مشتریان" icon={Target}>
              <div className="space-y-4">
                {customerData.customerSegments.map((segment, index) => (
                  <motion.div
                    key={segment.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 rounded-full" style={{
                          backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index % 5]
                        }}></div>
                        <h3 className="font-medium text-gray-900">{segment.name}</h3>
                      </div>
                      <span className="text-sm font-bold text-gray-900">
                        {formatNumber(segment.customerCount)} نفر
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">{segment.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <span className="text-gray-500">میانگین خرید:</span>
                        <div className="font-medium">{formatCurrency(segment.averageOrderValue)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">تعداد سفارش:</span>
                        <div className="font-medium">{formatNumber(segment.averageOrderFrequency)}</div>
                      </div>
                    </div>
                    
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${(segment.customerCount / customerData.customerSegments.reduce((sum, s) => sum + s.customerCount, 0)) * 100}%` }}
                          transition={{ delay: index * 0.2, duration: 0.8 }}
                          className="h-2 rounded-full"
                          style={{
                            backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index % 5]
                          }}
                        />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </AdminCard>

            {/* Top Customers */}
            <AdminCard title="مشتریان برتر" icon={Crown}>
              <div className="space-y-4">
                {customerData.topCustomers.map((customer, index) => (
                  <motion.div
                    key={customer.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-admin-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-admin-600">
                          {formatNumber(index + 1)}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 text-sm">
                          {customer.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {customer.email}
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-sm">
                        {formatCurrency(customer.totalSpent)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatNumber(customer.totalOrders)} سفارش
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </AdminCard>
          </div>
        )}

        {/* Customer Lifetime Metrics */}
        {customerData && (
          <AdminCard title="متریک‌های ارزش مشتری" icon={TrendingUp}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {formatCurrency(customerData.customerLifetime.averageLifetimeValue)}
                </div>
                <div className="text-sm text-gray-600">میانگین ارزش مادام‌العمر</div>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <ShoppingBag className="w-6 h-6 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {formatNumber(customerData.customerLifetime.averageOrderFrequency)}
                </div>
                <div className="text-sm text-gray-600">میانگین تعداد سفارش</div>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Calendar className="w-6 h-6 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {formatNumber(customerData.customerLifetime.averageCustomerLifespan)} ماه
                </div>
                <div className="text-sm text-gray-600">میانگین طول عمر مشتری</div>
              </div>
              
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <TrendingDown className="w-6 h-6 text-orange-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {formatPercentage(customerData.customerLifetime.churnRate)}
                </div>
                <div className="text-sm text-gray-600">نرخ ترک مشتری</div>
              </div>
            </div>
          </AdminCard>
        )}
      </div>
    </AdminLayout>
  );
};

export default CustomersAnalyticsPage;
