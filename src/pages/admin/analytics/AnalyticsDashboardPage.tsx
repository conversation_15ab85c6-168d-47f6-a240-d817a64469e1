import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Eye,
  Download,
  Calendar,
  Filter,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard, { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminAnalytics } from '../../../hooks/useAdminAnalytics';
import { formatCurrency, formatPercentage, formatGrowthRate, getDateRangeOptions } from '../../../utils/analyticsUtils';
import { formatNumber } from '../../../utils/formatters';

const AnalyticsDashboardPage: React.FC = () => {
  const { 
    overview, 
    salesData, 
    customerData, 
    loading, 
    error, 
    filters, 
    setFilters,
    generateReport,
    exportData,
    refetch 
  } = useAdminAnalytics();

  const [selectedDateRange, setSelectedDateRange] = useState('last30days');
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const handleDateRangeChange = (range: string) => {
    setSelectedDateRange(range);
    const option = getDateRangeOptions().find(opt => opt.value === range);
    if (option) {
      setFilters(prev => ({
        ...prev,
        dateRange: {
          start: option.start,
          end: option.end
        }
      }));
    }
  };

  const handleGenerateReport = async () => {
    setIsGeneratingReport(true);
    try {
      const result = await generateReport({
        type: 'sales',
        format: 'pdf',
        dateRange: filters.dateRange,
        includeCharts: true,
        includeDetails: true
      });
      
      if (result.success) {
        // In a real app, this would trigger a download
        console.log('Report generated successfully');
      }
    } catch (error) {
      console.error('Report generation failed:', error);
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const handleExportData = async (type: string, format: string) => {
    try {
      const result = await exportData(type, format);
      if (result.success) {
        console.log('Data exported successfully');
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری آمار...</span>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <AdminButton onClick={refetch}>
              تلاش مجدد
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  const dateRangeOptions = getDateRangeOptions();

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <BarChart3 className="w-8 h-8 text-admin-600" />
              داشبورد آمار و گزارشات
            </h1>
            <p className="text-gray-600 mt-1">
              تحلیل جامع عملکرد فروشگاه و رفتار مشتریان
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={selectedDateRange}
              onChange={(e) => handleDateRangeChange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
            >
              {dateRangeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              onClick={refetch}
              size="sm"
            >
              بروزرسانی
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleGenerateReport}
              loading={isGeneratingReport}
              size="sm"
            >
              تولید گزارش
            </AdminButton>
          </div>
        </div>

        {/* Overview Stats */}
        {overview && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <AdminStatsCard
              title="کل درآمد"
              value={formatCurrency(overview.totalRevenue)}
              change={formatGrowthRate(overview.revenueGrowth).formatted}
              changeType={overview.revenueGrowth >= 0 ? 'increase' : 'decrease'}
              icon={DollarSign}
              color="bg-green-500"
            />
            
            <AdminStatsCard
              title="کل سفارشات"
              value={formatNumber(overview.totalOrders)}
              change={formatGrowthRate(overview.ordersGrowth).formatted}
              changeType={overview.ordersGrowth >= 0 ? 'increase' : 'decrease'}
              icon={ShoppingCart}
              color="bg-blue-500"
            />
            
            <AdminStatsCard
              title="کل مشتریان"
              value={formatNumber(overview.totalCustomers)}
              change={formatGrowthRate(overview.customersGrowth).formatted}
              changeType={overview.customersGrowth >= 0 ? 'increase' : 'decrease'}
              icon={Users}
              color="bg-purple-500"
            />
            
            <AdminStatsCard
              title="میانگین ارزش سفارش"
              value={formatCurrency(overview.averageOrderValue)}
              change={formatPercentage(overview.conversionRate)}
              changeType="neutral"
              icon={TrendingUp}
              color="bg-orange-500"
            />
          </div>
        )}

        {/* Quick Actions */}
        <AdminCard title="عملیات سریع" icon={BarChart3}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <AdminButton
              variant="outline"
              icon={BarChart3}
              onClick={() => window.location.href = '/admin/analytics/sales'}
              className="h-20 flex-col"
            >
              <span className="text-sm font-medium">گزارش فروش</span>
              <span className="text-xs text-gray-500">تحلیل درآمد و سفارشات</span>
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={Users}
              onClick={() => window.location.href = '/admin/analytics/customers'}
              className="h-20 flex-col"
            >
              <span className="text-sm font-medium">تحلیل مشتریان</span>
              <span className="text-xs text-gray-500">رفتار و بخش‌بندی</span>
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={ShoppingCart}
              onClick={() => window.location.href = '/admin/analytics/products'}
              className="h-20 flex-col"
            >
              <span className="text-sm font-medium">عملکرد محصولات</span>
              <span className="text-xs text-gray-500">فروش و محبوبیت</span>
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={Eye}
              onClick={() => window.location.href = '/admin/analytics/traffic'}
              className="h-20 flex-col"
            >
              <span className="text-sm font-medium">ترافیک سایت</span>
              <span className="text-xs text-gray-500">بازدید و تبدیل</span>
            </AdminButton>
          </div>
        </AdminCard>

        {/* Sales Overview */}
        {salesData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AdminCard title="محصولات پرفروش" icon={TrendingUp}>
              <div className="space-y-4">
                {salesData.topProducts.slice(0, 5).map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-admin-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-admin-600">
                          {formatNumber(index + 1)}
                        </span>
                      </div>
                      {product.image && (
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-10 h-10 rounded-lg object-cover"
                        />
                      )}
                      <div>
                        <p className="font-medium text-gray-900">{product.name}</p>
                        <p className="text-sm text-gray-500">{product.category}</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-green-600">
                        {formatCurrency(product.revenue)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatNumber(product.unitsSold)} فروش
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </AdminCard>

            <AdminCard title="کانال‌های فروش" icon={BarChart3}>
              <div className="space-y-4">
                {salesData.revenueByChannel.map((channel, index) => (
                  <div key={channel.channel} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        {channel.channel}
                      </span>
                      <div className="text-left">
                        <span className="text-sm font-bold text-gray-900">
                          {formatCurrency(channel.revenue)}
                        </span>
                        <span className="text-xs text-gray-500 mr-2">
                          ({formatPercentage(channel.percentage)})
                        </span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-admin-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${channel.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </AdminCard>
          </div>
        )}

        {/* Customer Segments */}
        {customerData && (
          <AdminCard title="بخش‌بندی مشتریان" icon={Users}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {customerData.customerSegments.map((segment, index) => (
                <motion.div
                  key={segment.segment}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg"
                >
                  <div className="text-center">
                    <h3 className="font-medium text-gray-900 mb-2">
                      {segment.segment}
                    </h3>
                    <p className="text-2xl font-bold text-admin-600 mb-1">
                      {formatNumber(segment.count)}
                    </p>
                    <p className="text-sm text-gray-500 mb-3">
                      {formatPercentage(segment.percentage)} از کل
                    </p>
                    <div className="text-xs text-gray-600">
                      <p>میانگین خرید: {formatCurrency(segment.averageOrderValue)}</p>
                      <p>کل درآمد: {formatCurrency(segment.totalRevenue)}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </AdminCard>
        )}
      </div>
    </AdminLayout>
  );
};

export default AnalyticsDashboardPage;
