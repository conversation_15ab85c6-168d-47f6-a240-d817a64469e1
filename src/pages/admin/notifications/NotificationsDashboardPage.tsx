import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  CheckCircle,
  Trash2,
  Filter,
  Settings,
  Eye,
  EyeOff,
  AlertTriangle,
  Info,
  Clock,
  User,
  Calendar,
  Search,
  Download,
  RefreshCw,
  Save
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminTable, { AdminTableColumn } from '../../../components/admin/common/AdminTable';
import { useAdminNotifications } from '../../../hooks/useAdminNotifications';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminNotification, AuditLogEntry, NotificationSettings, NotificationCategory, NOTIFICATION_LABELS, AUDIT_ACTIONS, AUDIT_RESOURCES } from '../../../types/adminNotifications';
import { formatPersianDate, formatNumber } from '../../../utils/formatters';
import { toast } from 'react-hot-toast';

const NotificationsDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();

  // Use hook for all notification functionality
  const {
    notifications,
    auditLogs,
    notificationSettings,
    loading,
    error,
    filters,
    setFilters,
    auditFilters,
    setAuditFilters,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    updateNotificationSettings,
    createTestNotification,
    refetch
  } = useAdminNotifications();

  const [activeTab, setActiveTab] = useState<'notifications' | 'audit' | 'settings'>('notifications');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  // Check permissions
  if (!checkPermission('notifications', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <div className="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Bell className="w-8 h-8 text-orange-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده اعلان‌ها را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  const handleMarkAsRead = async (notificationId: string) => {
    const result = await markAsRead(notificationId);
    if (result.success) {
      toast.success('اعلان به عنوان خوانده شده علامت‌گذاری شد');
    } else {
      toast.error(result.error || 'خطا در علامت‌گذاری');
    }
  };

  const handleMarkAllAsRead = async () => {
    const result = await markAllAsRead();
    if (result.success) {
      toast.success('همه اعلان‌ها به عنوان خوانده شده علامت‌گذاری شدند');
    } else {
      toast.error(result.error || 'خطا در علامت‌گذاری');
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    if (window.confirm('آیا از حذف این اعلان اطمینان دارید؟')) {
      const result = await deleteNotification(notificationId);
      if (result.success) {
        toast.success('اعلان حذف شد');
      } else {
        toast.error(result.error || 'خطا در حذف اعلان');
      }
    }
  };

  const handleCreateTestNotification = async () => {
    try {
      const result = await createTestNotification();
      if (result.success) {
        toast.success('اعلان تست ایجاد شد');
      }
    } catch (error) {
      toast.error('خطا در ایجاد اعلان تست');
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'low': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'orders': return 'text-blue-600';
      case 'products': return 'text-green-600';
      case 'customers': return 'text-purple-600';
      case 'reviews': return 'text-yellow-600';
      case 'payments': return 'text-red-600';
      case 'system': return 'text-gray-600';
      case 'security': return 'text-red-700';
      default: return 'text-gray-600';
    }
  };

  const notificationColumns: AdminTableColumn<AdminNotification>[] = [
    {
      key: 'title',
      title: 'اعلان',
      render: (notification) => (
        <div className="flex items-start gap-3">
          <div className={`w-2 h-2 rounded-full mt-2 ${notification.read ? 'bg-gray-300' : 'bg-blue-500'}`} />
          <div className="flex-1">
            <h4 className={`font-medium ${notification.read ? 'text-gray-600' : 'text-gray-900'}`}>
              {notification.title}
            </h4>
            <p className={`text-sm mt-1 ${notification.read ? 'text-gray-500' : 'text-gray-700'}`}>
              {notification.message}
            </p>
            <div className="flex items-center gap-2 mt-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(notification.priority)}`}>
                {NOTIFICATION_LABELS.priorities[notification.priority]}
              </span>
              <span className={`text-xs ${getCategoryColor(notification.category)}`}>
                {NOTIFICATION_LABELS.categories[notification.category]}
              </span>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'createdAt',
      title: 'زمان',
      sortable: true,
      render: (notification) => (
        <div className="text-sm text-gray-500">
          <div>{formatPersianDate(notification.createdAt)}</div>
          <div className="text-xs">
            {new Date(notification.createdAt).toLocaleTimeString('fa-IR', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        </div>
      )
    }
  ];

  const auditColumns: AdminTableColumn<AuditLogEntry>[] = [
    {
      key: 'timestamp',
      title: 'زمان',
      sortable: true,
      render: (log) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {formatPersianDate(log.timestamp)}
          </div>
          <div className="text-gray-500">
            {new Date(log.timestamp).toLocaleTimeString('fa-IR', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        </div>
      )
    },
    {
      key: 'user',
      title: 'کاربر',
      render: (log) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <div>
            <div className="font-medium text-gray-900">{log.userEmail}</div>
            <div className="text-sm text-gray-500">{log.userRole}</div>
          </div>
        </div>
      )
    },
    {
      key: 'action',
      title: 'عملیات',
      render: (log) => (
        <div>
          <div className="font-medium text-gray-900">
            {AUDIT_ACTIONS[log.action] || log.action}
          </div>
          <div className="text-sm text-gray-500">
            {AUDIT_RESOURCES[log.resource] || log.resource}
            {log.resourceId && ` #${log.resourceId}`}
          </div>
        </div>
      )
    },
    {
      key: 'success',
      title: 'وضعیت',
      render: (log) => (
        <div className="flex items-center gap-2">
          {log.success ? (
            <>
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-600">موفق</span>
            </>
          ) : (
            <>
              <AlertTriangle className="w-4 h-4 text-red-500" />
              <span className="text-sm text-red-600">ناموفق</span>
            </>
          )}
        </div>
      )
    },
    {
      key: 'ipAddress',
      title: 'IP',
      render: (log) => (
        <span className="text-sm font-mono text-gray-600">{log.ipAddress}</span>
      )
    }
  ];

  const notificationRowActions = (notification: AdminNotification) => (
    <div className="flex items-center gap-2">
      {!notification.read && (
        <AdminButton
          variant="ghost"
          size="sm"
          icon={Eye}
          onClick={() => handleMarkAsRead(notification.id)}
        >
          خوانده شده
        </AdminButton>
      )}
      {notification.actionUrl && (
        <AdminButton
          variant="ghost"
          size="sm"
          onClick={() => navigate(notification.actionUrl!)}
        >
          {notification.actionText || 'مشاهده'}
        </AdminButton>
      )}
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Trash2}
        onClick={() => handleDeleteNotification(notification.id)}
        className="text-red-600 hover:text-red-700"
      >
        حذف
      </AdminButton>
    </div>
  );

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری اعلان‌ها...</span>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <AdminButton onClick={refetch}>
              تلاش مجدد
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  const tabs = [
    { id: 'notifications', label: 'اعلان‌ها', icon: Bell, count: unreadCount },
    { id: 'audit', label: 'گزارش عملکرد', icon: Clock, count: auditLogs.length },
    { id: 'settings', label: 'تنظیمات', icon: Settings }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <Bell className="w-8 h-8 text-admin-600" />
              اعلان‌ها و گزارش عملکرد
            </h1>
            <p className="text-gray-600 mt-1">
              مدیریت اعلان‌های سیستم و بررسی گزارش عملکرد
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              onClick={refetch}
              size="sm"
            >
              بروزرسانی
            </AdminButton>

            {activeTab === 'notifications' && (
              <>
                <AdminButton
                  variant="outline"
                  icon={Bell}
                  onClick={handleCreateTestNotification}
                  size="sm"
                >
                  ایجاد اعلان تست
                </AdminButton>

                {unreadCount > 0 && (
                  <AdminButton
                    variant="outline"
                    icon={CheckCircle}
                    onClick={handleMarkAllAsRead}
                    size="sm"
                  >
                    همه را خوانده شده علامت‌گذاری کن
                  </AdminButton>
                )}
              </>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 space-x-reverse">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-admin-500 text-admin-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                  {tab.count !== undefined && tab.count > 0 && (
                    <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                      {formatNumber(tab.count)}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        {activeTab === 'notifications' && (
          <AdminCard title="اعلان‌های سیستم" icon={Bell}>
            <AdminTable
              columns={notificationColumns}
              data={notifications}
              loading={loading}
              rowActions={notificationRowActions}
              emptyMessage="هیچ اعلانی یافت نشد"
            />
          </AdminCard>
        )}

        {activeTab === 'audit' && (
          <AdminCard title="گزارش عملکرد" icon={Clock}>
            <AdminTable
              columns={auditColumns}
              data={auditLogs}
              loading={loading}
              emptyMessage="هیچ گزارش عملکردی یافت نشد"
            />
          </AdminCard>
        )}

        {activeTab === 'settings' && (
          <AdminCard title="تنظیمات اعلان‌ها" icon={Settings}>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">اعلان‌های سیستم</h4>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableSystemNotifications"
                        checked={notificationSettings?.enableSystemNotifications ?? true}
                        onChange={(e) => updateNotificationSettings({ enableSystemNotifications: e.target.checked })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableSystemNotifications" className="mr-2 block text-sm text-gray-900">
                        فعال‌سازی اعلان‌های سیستم
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableEmailNotifications"
                        checked={notificationSettings?.enableEmailNotifications ?? true}
                        onChange={(e) => updateNotificationSettings({ enableEmailNotifications: e.target.checked })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableEmailNotifications" className="mr-2 block text-sm text-gray-900">
                        اعلان‌های ایمیل
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableSmsNotifications"
                        checked={notificationSettings?.enableSmsNotifications ?? false}
                        onChange={(e) => updateNotificationSettings({ enableSmsNotifications: e.target.checked })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableSmsNotifications" className="mr-2 block text-sm text-gray-900">
                        اعلان‌های پیامک
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enablePushNotifications"
                        checked={notificationSettings?.enablePushNotifications ?? false}
                        onChange={(e) => updateNotificationSettings({ enablePushNotifications: e.target.checked })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enablePushNotifications" className="mr-2 block text-sm text-gray-900">
                        اعلان‌های Push
                      </label>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">دسته‌بندی اعلان‌ها</h4>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="notifyOrders"
                        checked={notificationSettings?.categories?.orders ?? true}
                        onChange={(e) => updateNotificationSettings({
                          categories: { ...notificationSettings?.categories, orders: e.target.checked }
                        })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notifyOrders" className="mr-2 block text-sm text-gray-900">
                        سفارشات
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="notifyProducts"
                        checked={notificationSettings?.categories?.products ?? true}
                        onChange={(e) => updateNotificationSettings({
                          categories: { ...notificationSettings?.categories, products: e.target.checked }
                        })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notifyProducts" className="mr-2 block text-sm text-gray-900">
                        محصولات
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="notifyCustomers"
                        checked={notificationSettings?.categories?.customers ?? true}
                        onChange={(e) => updateNotificationSettings({
                          categories: { ...notificationSettings?.categories, customers: e.target.checked }
                        })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notifyCustomers" className="mr-2 block text-sm text-gray-900">
                        مشتریان
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="notifyReviews"
                        checked={notificationSettings?.categories?.reviews ?? true}
                        onChange={(e) => updateNotificationSettings({
                          categories: { ...notificationSettings?.categories, reviews: e.target.checked }
                        })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notifyReviews" className="mr-2 block text-sm text-gray-900">
                        نظرات
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="notifySecurity"
                        checked={notificationSettings?.categories?.security ?? true}
                        onChange={(e) => updateNotificationSettings({
                          categories: { ...notificationSettings?.categories, security: e.target.checked }
                        })}
                        className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notifySecurity" className="mr-2 block text-sm text-gray-900">
                        امنیت
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      حداکثر اعلان‌های نمایش داده شده
                    </label>
                    <input
                      type="number"
                      value={notificationSettings?.maxDisplayNotifications ?? 50}
                      onChange={(e) => updateNotificationSettings({ maxDisplayNotifications: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      مدت نگهداری اعلان‌ها (روز)
                    </label>
                    <input
                      type="number"
                      value={notificationSettings?.retentionDays ?? 30}
                      onChange={(e) => updateNotificationSettings({ retentionDays: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <AdminButton
                  variant="primary"
                  icon={Save}
                  onClick={() => toast.success('تنظیمات اعلان‌ها ذخیره شد')}
                >
                  ذخیره تنظیمات
                </AdminButton>
              </div>
            </div>
          </AdminCard>
        )}
      </div>
    </AdminLayout>
  );
};

export default NotificationsDashboardPage;
