import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowRight, 
  Search, 
  Truck, 
  Package, 
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAdminOrders } from '../../../hooks/useAdminOrders';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminOrder } from '../../../types/adminOrder';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { formatPersianDate, formatCurrency } from '../../../utils/orderUtils';

const OrderTrackingPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const { orders, loading } = useAdminOrders();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCarrier, setSelectedCarrier] = useState<string>('all');

  // Filter orders that have tracking numbers
  const trackableOrders = orders.filter(order => 
    order.trackingNumber && ['shipped', 'delivered'].includes(order.status)
  );

  // Filter orders based on search and carrier
  const filteredOrders = trackableOrders.filter(order => {
    const matchesSearch = !searchTerm || 
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.trackingNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customerInfo.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCarrier = selectedCarrier === 'all' || 
      order.fulfillment.shippingCarrier === selectedCarrier;
    
    return matchesSearch && matchesCarrier;
  });

  // Get unique carriers
  const carriers = Array.from(new Set(
    trackableOrders.map(order => order.fulfillment.shippingCarrier).filter(Boolean)
  ));

  const getTrackingStatusIcon = (status: AdminOrder['status']) => {
    switch (status) {
      case 'shipped':
        return <Truck className="w-5 h-5 text-indigo-500" />;
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Package className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTrackingStatusColor = (status: AdminOrder['status']) => {
    switch (status) {
      case 'shipped':
        return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case 'delivered':
        return 'bg-green-50 text-green-700 border-green-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const getEstimatedDeliveryStatus = (order: AdminOrder) => {
    if (!order.estimatedDelivery) return null;
    
    const estimatedDate = new Date(order.estimatedDelivery);
    const now = new Date();
    
    if (order.status === 'delivered') {
      const deliveredEvent = order.timeline.find(event => 
        event.type === 'status_change' && event.metadata?.to === 'delivered'
      );
      
      if (deliveredEvent) {
        const deliveredDate = new Date(deliveredEvent.timestamp);
        const isOnTime = deliveredDate <= estimatedDate;
        
        return {
          status: isOnTime ? 'on-time' : 'late',
          message: isOnTime ? 'تحویل به موقع' : 'تحویل با تأخیر',
          color: isOnTime ? 'text-green-600' : 'text-red-600'
        };
      }
    }
    
    if (now > estimatedDate) {
      return {
        status: 'overdue',
        message: 'تأخیر در تحویل',
        color: 'text-red-600'
      };
    }
    
    const daysRemaining = Math.ceil((estimatedDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysRemaining <= 1) {
      return {
        status: 'due-soon',
        message: 'تحویل امروز یا فردا',
        color: 'text-orange-600'
      };
    }
    
    return {
      status: 'on-track',
      message: `${daysRemaining} روز تا تحویل`,
      color: 'text-blue-600'
    };
  };

  const handleViewOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}`);
  };

  const handleTrackExternal = (trackingNumber: string, carrier: string) => {
    // In a real implementation, this would open the carrier's tracking page
    console.log(`Tracking ${trackingNumber} with ${carrier}`);
    // Example: window.open(`https://tracking.${carrier}.com/${trackingNumber}`, '_blank');
  };

  if (!checkPermission('orders', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده رهگیری سفارشات را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="رهگیری سفارشات"
      subtitle="مشاهده وضعیت ارسال و تحویل سفارشات"
      actions={
        <AdminButton
          variant="outline"
          size="sm"
          onClick={() => navigate('/admin/orders')}
          icon={ArrowRight}
        >
          بازگشت به سفارشات
        </AdminButton>
      }
    >
      <div className="space-y-6">
        {/* Filters */}
        <AdminCard>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Search */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  جستجو
                </label>
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="شماره سفارش، کد رهگیری یا نام مشتری"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Carrier Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  شرکت حمل و نقل
                </label>
                <select
                  value={selectedCarrier}
                  onChange={(e) => setSelectedCarrier(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">همه شرکت‌ها</option>
                  {carriers.map(carrier => (
                    <option key={carrier} value={carrier}>
                      {carrier}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </AdminCard>

        {/* Tracking Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <AdminCard className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">کل مرسولات</p>
                <p className="text-2xl font-bold text-blue-600">
                  {trackableOrders.length.toLocaleString('fa-IR')}
                </p>
              </div>
              <Package className="w-8 h-8 text-blue-500" />
            </div>
          </AdminCard>

          <AdminCard className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">در حال ارسال</p>
                <p className="text-2xl font-bold text-indigo-600">
                  {trackableOrders.filter(o => o.status === 'shipped').length.toLocaleString('fa-IR')}
                </p>
              </div>
              <Truck className="w-8 h-8 text-indigo-500" />
            </div>
          </AdminCard>

          <AdminCard className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">تحویل شده</p>
                <p className="text-2xl font-bold text-green-600">
                  {trackableOrders.filter(o => o.status === 'delivered').length.toLocaleString('fa-IR')}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </AdminCard>

          <AdminCard className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">تأخیر در تحویل</p>
                <p className="text-2xl font-bold text-red-600">
                  {trackableOrders.filter(order => {
                    const deliveryStatus = getEstimatedDeliveryStatus(order);
                    return deliveryStatus?.status === 'overdue';
                  }).length.toLocaleString('fa-IR')}
                </p>
              </div>
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
          </AdminCard>
        </div>

        {/* Tracking List */}
        <AdminCard>
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">
                لیست رهگیری ({filteredOrders.length} مورد)
              </h3>
              <AdminButton
                variant="outline"
                size="sm"
                icon={RefreshCw}
                onClick={() => window.location.reload()}
              >
                بروزرسانی
              </AdminButton>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-12">
                <Truck className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  هیچ مرسوله‌ای یافت نشد
                </h3>
                <p className="text-gray-600">
                  هیچ سفارش قابل رهگیری با این فیلترها وجود ندارد.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredOrders.map((order, index) => {
                  const deliveryStatus = getEstimatedDeliveryStatus(order);
                  
                  return (
                    <motion.div
                      key={order.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          {getTrackingStatusIcon(order.status)}
                          
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-gray-900">
                                {order.orderNumber}
                              </h4>
                              <span className={`
                                inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border
                                ${getTrackingStatusColor(order.status)}
                              `}>
                                {order.status === 'shipped' ? 'ارسال شده' : 'تحویل شده'}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                              <span>{order.customerInfo.name}</span>
                              <span>•</span>
                              <span>{formatCurrency(order.orderSummary.total)}</span>
                              <span>•</span>
                              <span>{order.fulfillment.shippingCarrier}</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-4">
                          {/* Tracking Number */}
                          <div className="text-left">
                            <p className="text-sm text-gray-600">کد رهگیری</p>
                            <p className="font-mono text-sm text-gray-900">
                              {order.trackingNumber}
                            </p>
                          </div>

                          {/* Delivery Status */}
                          {deliveryStatus && (
                            <div className="text-left">
                              <p className="text-sm text-gray-600">وضعیت تحویل</p>
                              <p className={`text-sm font-medium ${deliveryStatus.color}`}>
                                {deliveryStatus.message}
                              </p>
                            </div>
                          )}

                          {/* Actions */}
                          <div className="flex items-center gap-2">
                            <AdminButton
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewOrder(order)}
                            >
                              جزئیات
                            </AdminButton>
                            
                            <AdminButton
                              variant="outline"
                              size="sm"
                              icon={ExternalLink}
                              onClick={() => handleTrackExternal(
                                order.trackingNumber!, 
                                order.fulfillment.shippingCarrier!
                              )}
                            >
                              رهگیری خارجی
                            </AdminButton>
                          </div>
                        </div>
                      </div>

                      {/* Additional Info */}
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4 text-gray-400" />
                            <span className="text-gray-600">ارسال:</span>
                            <span className="text-gray-900">
                              {formatPersianDate(order.createdAt)}
                            </span>
                          </div>
                          
                          {order.estimatedDelivery && (
                            <div className="flex items-center gap-2">
                              <MapPin className="w-4 h-4 text-gray-400" />
                              <span className="text-gray-600">تحویل تخمینی:</span>
                              <span className="text-gray-900">
                                {formatPersianDate(order.estimatedDelivery)}
                              </span>
                            </div>
                          )}
                          
                          <div className="flex items-center gap-2">
                            <Package className="w-4 h-4 text-gray-400" />
                            <span className="text-gray-600">مقصد:</span>
                            <span className="text-gray-900">
                              {order.shippingAddress.city}, {order.shippingAddress.province}
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>
        </AdminCard>
      </div>
    </AdminLayout>
  );
};

export default OrderTrackingPage;
