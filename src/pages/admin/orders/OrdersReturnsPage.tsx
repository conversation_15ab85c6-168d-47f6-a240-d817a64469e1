import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  RotateCcw, 
  DollarSign, 
  Clock, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  X,
  Package
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAdminOrders } from '../../../hooks/useAdminOrders';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminOrder } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import AdminLayout, { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import OrderTable from '../../../components/admin/orders/OrderTable';
import OrderStatusManager from '../../../components/admin/orders/OrderStatusManager';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { formatCurrency, formatPersianDate } from '../../../utils/orderUtils';
import toast from 'react-hot-toast';

const OrdersReturnsPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const {
    orders,
    loading,
    error,
    updateOrderStatus,
    generateShippingLabel,
    bulkUpdateStatus,
    analytics
  } = useAdminOrders();

  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [statusManagerOrder, setStatusManagerOrder] = useState<AdminOrder | null>(null);

  // Filter orders with refund requests or return status
  const returnOrders = orders.filter(order => 
    order.refundStatus && order.refundStatus !== 'none'
  );

  const handleViewOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}`);
  };

  const handleEditOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}/edit`);
  };

  const handleUpdateStatus = async (orderId: string, status: Order['status']) => {
    try {
      await updateOrderStatus(orderId, status);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleGenerateLabel = async (orderId: string) => {
    try {
      await generateShippingLabel(orderId);
    } catch (error) {
      console.error('Error generating label:', error);
    }
  };

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedOrders(selected ? returnOrders.map(order => order.id) : []);
  };

  const handleBulkApproveRefunds = async () => {
    if (selectedOrders.length === 0) {
      toast.error('لطفاً حداقل یک سفارش انتخاب کنید');
      return;
    }

    const confirmed = window.confirm(`آیا از تأیید مرجوعی ${selectedOrders.length} سفارش اطمینان دارید؟`);
    if (!confirmed) return;

    try {
      // In a real implementation, this would update refund status
      toast.success(`${selectedOrders.length} درخواست مرجوعی تأیید شد`);
      setSelectedOrders([]);
    } catch (error) {
      console.error('Error in bulk approve refunds:', error);
    }
  };

  const handleBulkRejectRefunds = async () => {
    if (selectedOrders.length === 0) {
      toast.error('لطفاً حداقل یک سفارش انتخاب کنید');
      return;
    }

    const confirmed = window.confirm(`آیا از رد مرجوعی ${selectedOrders.length} سفارش اطمینان دارید؟`);
    if (!confirmed) return;

    try {
      // In a real implementation, this would update refund status
      toast.success(`${selectedOrders.length} درخواست مرجوعی رد شد`);
      setSelectedOrders([]);
    } catch (error) {
      console.error('Error in bulk reject refunds:', error);
    }
  };

  const getReturnsStats = () => {
    const pendingRefunds = returnOrders.filter(order => order.refundStatus === 'requested').length;
    const approvedRefunds = returnOrders.filter(order => order.refundStatus === 'approved').length;
    const processedRefunds = returnOrders.filter(order => order.refundStatus === 'processed').length;
    const totalRefundValue = returnOrders.reduce((sum, order) => sum + (order.refundAmount || 0), 0);

    return [
      {
        title: 'کل درخواست‌ها',
        value: returnOrders.length.toLocaleString('fa-IR'),
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        icon: RotateCcw
      },
      {
        title: 'در انتظار بررسی',
        value: pendingRefunds.toLocaleString('fa-IR'),
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        icon: Clock
      },
      {
        title: 'تأیید شده',
        value: approvedRefunds.toLocaleString('fa-IR'),
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        icon: CheckCircle
      },
      {
        title: 'ارزش مرجوعی‌ها',
        value: formatCurrency(totalRefundValue),
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        icon: DollarSign
      }
    ];
  };

  const getRefundStatusBadge = (status: string) => {
    switch (status) {
      case 'requested':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'processed':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRefundStatusText = (status: string) => {
    switch (status) {
      case 'requested':
        return 'درخواست شده';
      case 'approved':
        return 'تأیید شده';
      case 'processed':
        return 'پردازش شده';
      case 'rejected':
        return 'رد شده';
      default:
        return 'نامشخص';
    }
  };

  if (!checkPermission('orders', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده سفارشات را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminListLayout
      title="مرجوعی‌ها و بازگشت‌ها"
      subtitle={`${returnOrders.length} درخواست مرجوعی و بازگشت`}
      filters={
        <div className="flex items-center gap-2">
          <AdminButton
            variant="outline"
            size="sm"
            icon={RefreshCw}
            onClick={() => window.location.reload()}
          >
            بروزرسانی
          </AdminButton>
          
          <AdminButton
            variant="outline"
            size="sm"
            onClick={() => navigate('/admin/orders')}
          >
            همه سفارشات
          </AdminButton>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Returns Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {getReturnsStats().map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">{stat.title}</p>
                      <p className={`text-2xl font-bold ${stat.color}`}>
                        {stat.value}
                      </p>
                    </div>
                    <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Quick Actions */}
        {checkPermission('orders', 'update') && (
          <AdminCard className="p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">عملیات سریع</h3>
              <div className="flex items-center gap-2">
                <AdminButton
                  variant="outline"
                  size="sm"
                  icon={X}
                  onClick={handleBulkRejectRefunds}
                  disabled={selectedOrders.length === 0}
                >
                  رد مرجوعی ({selectedOrders.length})
                </AdminButton>
                <AdminButton
                  variant="primary"
                  size="sm"
                  icon={CheckCircle}
                  onClick={handleBulkApproveRefunds}
                  disabled={selectedOrders.length === 0}
                >
                  تأیید مرجوعی ({selectedOrders.length})
                </AdminButton>
              </div>
            </div>
          </AdminCard>
        )}

        {/* Returns Table */}
        <AdminCard>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              لیست درخواست‌های مرجوعی
            </h3>
            
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : returnOrders.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-12 h-12 text-green-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  هیچ درخواست مرجوعی وجود ندارد
                </h3>
                <p className="text-gray-600 mb-4">
                  تمام سفارشات بدون مشکل تحویل داده شده‌اند.
                </p>
                <AdminButton
                  variant="outline"
                  onClick={() => navigate('/admin/orders')}
                >
                  مشاهده همه سفارشات
                </AdminButton>
              </div>
            ) : (
              <div className="space-y-4">
                {returnOrders.map((order, index) => (
                  <motion.div
                    key={order.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <input
                          type="checkbox"
                          checked={selectedOrders.includes(order.id)}
                          onChange={() => handleSelectOrder(order.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium text-gray-900">
                              {order.orderNumber}
                            </h4>
                            <span className={`
                              inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                              ${getRefundStatusBadge(order.refundStatus || 'none')}
                            `}>
                              {getRefundStatusText(order.refundStatus || 'none')}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                            <span>{order.customerInfo.name}</span>
                            <span>•</span>
                            <span>{formatCurrency(order.orderSummary.total)}</span>
                            {order.refundAmount && (
                              <>
                                <span>•</span>
                                <span className="text-red-600">
                                  مرجوعی: {formatCurrency(order.refundAmount)}
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <AdminButton
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewOrder(order)}
                        >
                          جزئیات
                        </AdminButton>
                        
                        {order.refundStatus === 'requested' && checkPermission('orders', 'update') && (
                          <>
                            <AdminButton
                              variant="outline"
                              size="sm"
                              icon={X}
                              onClick={() => {
                                // Handle individual refund rejection
                                toast.success('درخواست مرجوعی رد شد');
                              }}
                            >
                              رد
                            </AdminButton>
                            <AdminButton
                              variant="primary"
                              size="sm"
                              icon={CheckCircle}
                              onClick={() => {
                                // Handle individual refund approval
                                toast.success('درخواست مرجوعی تأیید شد');
                              }}
                            >
                              تأیید
                            </AdminButton>
                          </>
                        )}
                      </div>
                    </div>

                    {order.refundReason && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <p className="text-sm text-gray-600">
                          <strong>دلیل مرجوعی:</strong> {order.refundReason}
                        </p>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </AdminCard>

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <p className="text-red-600">{error}</p>
            <AdminButton
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              تلاش مجدد
            </AdminButton>
          </div>
        )}
      </div>

      {/* Status Manager Modal */}
      {statusManagerOrder && (
        <OrderStatusManager
          order={statusManagerOrder}
          onStatusUpdate={updateOrderStatus}
          onClose={() => setStatusManagerOrder(null)}
          isOpen={!!statusManagerOrder}
        />
      )}
    </AdminListLayout>
  );
};

export default OrdersReturnsPage;
