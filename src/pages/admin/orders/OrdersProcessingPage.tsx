import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Package, 
  Truck, 
  Clock, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Printer
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAdminOrders } from '../../../hooks/useAdminOrders';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminOrder } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import AdminLayout, { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import OrderTable from '../../../components/admin/orders/OrderTable';
import OrderStatusManager from '../../../components/admin/orders/OrderStatusManager';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { formatCurrency, calculateProcessingTime } from '../../../utils/orderUtils';
import toast from 'react-hot-toast';

const OrdersProcessingPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const {
    orders,
    loading,
    error,
    updateOrderStatus,
    generateShippingLabel,
    bulkUpdateStatus,
    analytics
  } = useAdminOrders();

  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [statusManagerOrder, setStatusManagerOrder] = useState<AdminOrder | null>(null);

  // Filter only processing orders
  const processingOrders = orders.filter(order => order.status === 'processing');

  const handleViewOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}`);
  };

  const handleEditOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}/edit`);
  };

  const handleUpdateStatus = async (orderId: string, status: Order['status']) => {
    try {
      await updateOrderStatus(orderId, status);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleGenerateLabel = async (orderId: string) => {
    try {
      await generateShippingLabel(orderId);
    } catch (error) {
      console.error('Error generating label:', error);
    }
  };

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedOrders(selected ? processingOrders.map(order => order.id) : []);
  };

  const handleBulkShip = async () => {
    if (selectedOrders.length === 0) {
      toast.error('لطفاً حداقل یک سفارش انتخاب کنید');
      return;
    }

    try {
      await bulkUpdateStatus(selectedOrders, 'shipped');
      setSelectedOrders([]);
      toast.success(`${selectedOrders.length} سفارش ارسال شد`);
    } catch (error) {
      console.error('Error in bulk ship:', error);
    }
  };

  const handleBulkPrintLabels = async () => {
    if (selectedOrders.length === 0) {
      toast.error('لطفاً حداقل یک سفارش انتخاب کنید');
      return;
    }

    try {
      // Generate labels for all selected orders
      for (const orderId of selectedOrders) {
        await generateShippingLabel(orderId);
      }
      toast.success(`برچسب ${selectedOrders.length} سفارش چاپ شد`);
    } catch (error) {
      console.error('Error in bulk print labels:', error);
    }
  };

  const getProcessingStats = () => {
    const totalProcessingValue = processingOrders.reduce((sum, order) => sum + order.orderSummary.total, 0);
    const urgentOrders = processingOrders.filter(order => order.priority === 'urgent').length;
    const readyToShip = processingOrders.filter(order => order.fulfillment.labelPrinted).length;
    const averageProcessingTime = processingOrders.length > 0 
      ? processingOrders.reduce((sum, order) => sum + (calculateProcessingTime(order) || 0), 0) / processingOrders.length
      : 0;

    return [
      {
        title: 'در حال پردازش',
        value: processingOrders.length.toLocaleString('fa-IR'),
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        icon: Package
      },
      {
        title: 'ارزش کل',
        value: formatCurrency(totalProcessingValue),
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        icon: Package
      },
      {
        title: 'آماده ارسال',
        value: readyToShip.toLocaleString('fa-IR'),
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        icon: CheckCircle
      },
      {
        title: 'میانگین زمان پردازش',
        value: `${Math.round(averageProcessingTime)} دقیقه`,
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        icon: Clock
      }
    ];
  };

  if (!checkPermission('orders', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده سفارشات را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminListLayout
      title="سفارشات در حال پردازش"
      subtitle={`${processingOrders.length} سفارش در حال آماده‌سازی و پردازش`}
      filters={
        <div className="flex items-center gap-2">
          <AdminButton
            variant="outline"
            size="sm"
            icon={RefreshCw}
            onClick={() => window.location.reload()}
          >
            بروزرسانی
          </AdminButton>
          
          <AdminButton
            variant="outline"
            size="sm"
            onClick={() => navigate('/admin/orders')}
          >
            همه سفارشات
          </AdminButton>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Processing Orders Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {getProcessingStats().map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">{stat.title}</p>
                      <p className={`text-2xl font-bold ${stat.color}`}>
                        {stat.value}
                      </p>
                    </div>
                    <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Quick Actions */}
        {checkPermission('orders', 'update') && (
          <AdminCard className="p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">عملیات سریع</h3>
              <div className="flex items-center gap-2">
                <AdminButton
                  variant="outline"
                  size="sm"
                  icon={Printer}
                  onClick={handleBulkPrintLabels}
                  disabled={selectedOrders.length === 0}
                >
                  چاپ برچسب ({selectedOrders.length})
                </AdminButton>
                <AdminButton
                  variant="primary"
                  size="sm"
                  icon={Truck}
                  onClick={handleBulkShip}
                  disabled={selectedOrders.length === 0}
                >
                  ارسال انتخاب شده ({selectedOrders.length})
                </AdminButton>
              </div>
            </div>
          </AdminCard>
        )}

        {/* Processing Orders Table */}
        <AdminCard>
          <OrderTable
            orders={processingOrders}
            loading={loading}
            onViewOrder={handleViewOrder}
            onEditOrder={handleEditOrder}
            onUpdateStatus={handleUpdateStatus}
            onGenerateLabel={handleGenerateLabel}
            selectedOrders={selectedOrders}
            onSelectOrder={handleSelectOrder}
            onSelectAll={handleSelectAll}
          />
        </AdminCard>

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <p className="text-red-600">{error}</p>
            <AdminButton
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              تلاش مجدد
            </AdminButton>
          </div>
        )}

        {/* Empty State */}
        {!loading && processingOrders.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Package className="w-12 h-12 text-purple-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              هیچ سفارش در حال پردازشی وجود ندارد
            </h3>
            <p className="text-gray-600 mb-4">
              تمام سفارشات پردازش شده یا در مراحل دیگر قرار دارند.
            </p>
            <AdminButton
              variant="outline"
              onClick={() => navigate('/admin/orders')}
            >
              مشاهده همه سفارشات
            </AdminButton>
          </div>
        )}
      </div>

      {/* Status Manager Modal */}
      {statusManagerOrder && (
        <OrderStatusManager
          order={statusManagerOrder}
          onStatusUpdate={updateOrderStatus}
          onClose={() => setStatusManagerOrder(null)}
          isOpen={!!statusManagerOrder}
        />
      )}
    </AdminListLayout>
  );
};

export default OrdersProcessingPage;
