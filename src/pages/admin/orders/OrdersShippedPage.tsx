import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Truck, 
  CheckCircle, 
  Clock, 
  RefreshCw,
  ExternalLink,
  MapPin,
  Package
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAdminOrders } from '../../../hooks/useAdminOrders';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminOrder } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import AdminLayout, { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import OrderTable from '../../../components/admin/orders/OrderTable';
import OrderStatusManager from '../../../components/admin/orders/OrderStatusManager';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { formatCurrency, formatPersianDate } from '../../../utils/orderUtils';
import toast from 'react-hot-toast';

const OrdersShippedPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const {
    orders,
    loading,
    error,
    updateOrderStatus,
    generateShippingLabel,
    bulkUpdateStatus,
    analytics
  } = useAdminOrders();

  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [statusManagerOrder, setStatusManagerOrder] = useState<AdminOrder | null>(null);

  // Filter only shipped orders
  const shippedOrders = orders.filter(order => order.status === 'shipped');

  const handleViewOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}`);
  };

  const handleEditOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}/edit`);
  };

  const handleUpdateStatus = async (orderId: string, status: Order['status']) => {
    try {
      await updateOrderStatus(orderId, status);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleGenerateLabel = async (orderId: string) => {
    try {
      await generateShippingLabel(orderId);
    } catch (error) {
      console.error('Error generating label:', error);
    }
  };

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedOrders(selected ? shippedOrders.map(order => order.id) : []);
  };

  const handleBulkMarkDelivered = async () => {
    if (selectedOrders.length === 0) {
      toast.error('لطفاً حداقل یک سفارش انتخاب کنید');
      return;
    }

    const confirmed = window.confirm(`آیا از تحویل ${selectedOrders.length} سفارش اطمینان دارید؟`);
    if (!confirmed) return;

    try {
      await bulkUpdateStatus(selectedOrders, 'delivered');
      setSelectedOrders([]);
      toast.success(`${selectedOrders.length} سفارش به عنوان تحویل شده علامت‌گذاری شد`);
    } catch (error) {
      console.error('Error in bulk mark delivered:', error);
    }
  };

  const handleTrackingPage = () => {
    navigate('/admin/orders/tracking');
  };

  const getShippedStats = () => {
    const totalShippedValue = shippedOrders.reduce((sum, order) => sum + order.orderSummary.total, 0);
    const overdueOrders = shippedOrders.filter(order => {
      if (!order.estimatedDelivery) return false;
      const estimatedDate = new Date(order.estimatedDelivery);
      const now = new Date();
      return now > estimatedDate;
    }).length;
    
    const dueTodayOrders = shippedOrders.filter(order => {
      if (!order.estimatedDelivery) return false;
      const estimatedDate = new Date(order.estimatedDelivery);
      const today = new Date();
      return estimatedDate.toDateString() === today.toDateString();
    }).length;

    const uniqueCarriers = new Set(shippedOrders.map(order => order.fulfillment.shippingCarrier).filter(Boolean)).size;

    return [
      {
        title: 'ارسال شده',
        value: shippedOrders.length.toLocaleString('fa-IR'),
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
        icon: Truck
      },
      {
        title: 'ارزش کل',
        value: formatCurrency(totalShippedValue),
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        icon: Package
      },
      {
        title: 'تحویل امروز',
        value: dueTodayOrders.toLocaleString('fa-IR'),
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        icon: CheckCircle
      },
      {
        title: 'تأخیر در تحویل',
        value: overdueOrders.toLocaleString('fa-IR'),
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        icon: Clock
      }
    ];
  };

  if (!checkPermission('orders', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده سفارشات را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminListLayout
      title="سفارشات ارسال شده"
      subtitle={`${shippedOrders.length} سفارش در حال حمل و نقل`}
      filters={
        <div className="flex items-center gap-2">
          <AdminButton
            variant="outline"
            size="sm"
            icon={MapPin}
            onClick={handleTrackingPage}
          >
            صفحه رهگیری
          </AdminButton>
          
          <AdminButton
            variant="outline"
            size="sm"
            icon={RefreshCw}
            onClick={() => window.location.reload()}
          >
            بروزرسانی
          </AdminButton>
          
          <AdminButton
            variant="outline"
            size="sm"
            onClick={() => navigate('/admin/orders')}
          >
            همه سفارشات
          </AdminButton>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Shipped Orders Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {getShippedStats().map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">{stat.title}</p>
                      <p className={`text-2xl font-bold ${stat.color}`}>
                        {stat.value}
                      </p>
                    </div>
                    <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Quick Actions */}
        {checkPermission('orders', 'update') && (
          <AdminCard className="p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">عملیات سریع</h3>
              <div className="flex items-center gap-2">
                <AdminButton
                  variant="outline"
                  size="sm"
                  icon={ExternalLink}
                  onClick={() => {
                    // Bulk tracking functionality
                    toast.info('قابلیت رهگیری گروهی در حال توسعه است');
                  }}
                  disabled={selectedOrders.length === 0}
                >
                  رهگیری گروهی ({selectedOrders.length})
                </AdminButton>
                <AdminButton
                  variant="primary"
                  size="sm"
                  icon={CheckCircle}
                  onClick={handleBulkMarkDelivered}
                  disabled={selectedOrders.length === 0}
                >
                  تحویل شده ({selectedOrders.length})
                </AdminButton>
              </div>
            </div>
          </AdminCard>
        )}

        {/* Shipped Orders Table */}
        <AdminCard>
          <OrderTable
            orders={shippedOrders}
            loading={loading}
            onViewOrder={handleViewOrder}
            onEditOrder={handleEditOrder}
            onUpdateStatus={handleUpdateStatus}
            onGenerateLabel={handleGenerateLabel}
            selectedOrders={selectedOrders}
            onSelectOrder={handleSelectOrder}
            onSelectAll={handleSelectAll}
          />
        </AdminCard>

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <p className="text-red-600">{error}</p>
            <AdminButton
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              تلاش مجدد
            </AdminButton>
          </div>
        )}

        {/* Empty State */}
        {!loading && shippedOrders.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Truck className="w-12 h-12 text-indigo-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              هیچ سفارش ارسال شده‌ای وجود ندارد
            </h3>
            <p className="text-gray-600 mb-4">
              هنوز هیچ سفارشی ارسال نشده است.
            </p>
            <AdminButton
              variant="outline"
              onClick={() => navigate('/admin/orders')}
            >
              مشاهده همه سفارشات
            </AdminButton>
          </div>
        )}
      </div>

      {/* Status Manager Modal */}
      {statusManagerOrder && (
        <OrderStatusManager
          order={statusManagerOrder}
          onStatusUpdate={updateOrderStatus}
          onClose={() => setStatusManagerOrder(null)}
          isOpen={!!statusManagerOrder}
        />
      )}
    </AdminListLayout>
  );
};

export default OrdersShippedPage;
