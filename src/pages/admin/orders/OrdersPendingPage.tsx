import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Clock, 
  CheckCircle, 
  X, 
  Filter, 
  Download, 
  RefreshCw,
  AlertTriangle,
  User,
  Package
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAdminOrders } from '../../../hooks/useAdminOrders';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminOrder, OrderFilters as OrderFiltersType } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import AdminLayout, { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import OrderTable from '../../../components/admin/orders/OrderTable';
import OrderStatusManager from '../../../components/admin/orders/OrderStatusManager';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { formatCurrency, formatPersianDate } from '../../../utils/orderUtils';
import toast from 'react-hot-toast';

const OrdersPendingPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const {
    orders,
    loading,
    error,
    updateOrderStatus,
    generateShippingLabel,
    bulkUpdateStatus,
    analytics
  } = useAdminOrders();

  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [statusManagerOrder, setStatusManagerOrder] = useState<AdminOrder | null>(null);

  // Filter only pending orders
  const pendingOrders = orders.filter(order => order.status === 'pending');

  const handleViewOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}`);
  };

  const handleEditOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}/edit`);
  };

  const handleUpdateStatus = async (orderId: string, status: Order['status']) => {
    try {
      await updateOrderStatus(orderId, status);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleGenerateLabel = async (orderId: string) => {
    try {
      await generateShippingLabel(orderId);
    } catch (error) {
      console.error('Error generating label:', error);
    }
  };

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedOrders(selected ? pendingOrders.map(order => order.id) : []);
  };

  const handleBulkConfirm = async () => {
    if (selectedOrders.length === 0) {
      toast.error('لطفاً حداقل یک سفارش انتخاب کنید');
      return;
    }

    try {
      await bulkUpdateStatus(selectedOrders, 'confirmed');
      setSelectedOrders([]);
      toast.success(`${selectedOrders.length} سفارش تأیید شد`);
    } catch (error) {
      console.error('Error in bulk confirm:', error);
    }
  };

  const handleBulkCancel = async () => {
    if (selectedOrders.length === 0) {
      toast.error('لطفاً حداقل یک سفارش انتخاب کنید');
      return;
    }

    const confirmed = window.confirm(`آیا از لغو ${selectedOrders.length} سفارش اطمینان دارید؟`);
    if (!confirmed) return;

    try {
      await bulkUpdateStatus(selectedOrders, 'cancelled');
      setSelectedOrders([]);
      toast.success(`${selectedOrders.length} سفارش لغو شد`);
    } catch (error) {
      console.error('Error in bulk cancel:', error);
    }
  };

  const getPendingStats = () => {
    const stats = analytics();
    const totalPendingValue = pendingOrders.reduce((sum, order) => sum + order.orderSummary.total, 0);
    const urgentOrders = pendingOrders.filter(order => order.priority === 'urgent').length;
    const oldOrders = pendingOrders.filter(order => {
      const orderDate = new Date(order.createdAt);
      const daysDiff = (Date.now() - orderDate.getTime()) / (1000 * 60 * 60 * 24);
      return daysDiff > 1;
    }).length;

    return [
      {
        title: 'سفارشات در انتظار',
        value: pendingOrders.length.toLocaleString('fa-IR'),
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        icon: Clock
      },
      {
        title: 'ارزش کل',
        value: formatCurrency(totalPendingValue),
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        icon: Package
      },
      {
        title: 'سفارشات فوری',
        value: urgentOrders.toLocaleString('fa-IR'),
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        icon: AlertTriangle
      },
      {
        title: 'بیش از ۱ روز',
        value: oldOrders.toLocaleString('fa-IR'),
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        icon: Clock
      }
    ];
  };

  if (!checkPermission('orders', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده سفارشات را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminListLayout
      title="سفارشات در انتظار تأیید"
      subtitle={`${pendingOrders.length} سفارش در انتظار بررسی و تأیید`}
      filters={
        <div className="flex items-center gap-2">
          <AdminButton
            variant="outline"
            size="sm"
            icon={RefreshCw}
            onClick={() => window.location.reload()}
          >
            بروزرسانی
          </AdminButton>
          
          <AdminButton
            variant="outline"
            size="sm"
            onClick={() => navigate('/admin/orders')}
          >
            همه سفارشات
          </AdminButton>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Pending Orders Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {getPendingStats().map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">{stat.title}</p>
                      <p className={`text-2xl font-bold ${stat.color}`}>
                        {stat.value}
                      </p>
                    </div>
                    <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Quick Actions */}
        {checkPermission('orders', 'update') && (
          <AdminCard className="p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">عملیات سریع</h3>
              <div className="flex items-center gap-2">
                <AdminButton
                  variant="primary"
                  size="sm"
                  icon={CheckCircle}
                  onClick={handleBulkConfirm}
                  disabled={selectedOrders.length === 0}
                >
                  تأیید انتخاب شده ({selectedOrders.length})
                </AdminButton>
                <AdminButton
                  variant="outline"
                  size="sm"
                  icon={X}
                  onClick={handleBulkCancel}
                  disabled={selectedOrders.length === 0}
                >
                  لغو انتخاب شده
                </AdminButton>
              </div>
            </div>
          </AdminCard>
        )}

        {/* Pending Orders Table */}
        <AdminCard>
          <OrderTable
            orders={pendingOrders}
            loading={loading}
            onViewOrder={handleViewOrder}
            onEditOrder={handleEditOrder}
            onUpdateStatus={handleUpdateStatus}
            onGenerateLabel={handleGenerateLabel}
            selectedOrders={selectedOrders}
            onSelectOrder={handleSelectOrder}
            onSelectAll={handleSelectAll}
          />
        </AdminCard>

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <p className="text-red-600">{error}</p>
            <AdminButton
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              تلاش مجدد
            </AdminButton>
          </div>
        )}

        {/* Empty State */}
        {!loading && pendingOrders.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-12 h-12 text-green-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              هیچ سفارش در انتظاری وجود ندارد
            </h3>
            <p className="text-gray-600 mb-4">
              تمام سفارشات بررسی و تأیید شده‌اند.
            </p>
            <AdminButton
              variant="outline"
              onClick={() => navigate('/admin/orders')}
            >
              مشاهده همه سفارشات
            </AdminButton>
          </div>
        )}
      </div>

      {/* Status Manager Modal */}
      {statusManagerOrder && (
        <OrderStatusManager
          order={statusManagerOrder}
          onStatusUpdate={updateOrderStatus}
          onClose={() => setStatusManagerOrder(null)}
          isOpen={!!statusManagerOrder}
        />
      )}
    </AdminListLayout>
  );
};

export default OrdersPendingPage;
