import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowRight, 
  Edit, 
  Package, 
  Truck, 
  User, 
  MapPin,
  CreditCard,
  MessageSquare,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useAdminOrders } from '../../../hooks/useAdminOrders';
import { AdminOrderService } from '../../../services/adminOrderService';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminOrder } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import OrderStatusManager from '../../../components/admin/orders/OrderStatusManager';
import OrderTimeline from '../../../components/admin/orders/OrderTimeline';
import { formatCurrency, formatPersianDate, getStatusColor, getPriorityColor } from '../../../utils/orderUtils';
import toast from 'react-hot-toast';

const OrderDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const {
    updateOrderStatus,
    addOrderNote,
    updateOrderPriority,
    generateShippingLabel
  } = useAdminOrders();

  const [order, setOrder] = useState<AdminOrder | null>(null);
  const [loading, setLoading] = useState(true);
  const [showStatusManager, setShowStatusManager] = useState(false);
  const [newNote, setNewNote] = useState('');
  const [addingNote, setAddingNote] = useState(false);

  useEffect(() => {
    const fetchOrder = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const fetchedOrder = await AdminOrderService.getOrderById(id);
        setOrder(fetchedOrder);
      } catch (error) {
        console.error('Error fetching order:', error);
        setOrder(null);
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [id]);

  const handleStatusUpdate = async (orderId: string, newStatus: Order['status'], note?: string) => {
    try {
      await updateOrderStatus(orderId, newStatus, note);
      // Refresh order data
      const updatedOrder = await AdminOrderService.getOrderById(orderId);
      setOrder(updatedOrder);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleAddNote = async () => {
    if (!order || !newNote.trim()) return;

    try {
      setAddingNote(true);
      await addOrderNote(order.id, newNote.trim());
      setNewNote('');
      // Refresh order data
      const updatedOrder = await AdminOrderService.getOrderById(order.id);
      setOrder(updatedOrder);
    } catch (error) {
      console.error('Error adding note:', error);
    } finally {
      setAddingNote(false);
    }
  };

  const handlePriorityUpdate = async (priority: Order['priority']) => {
    if (!order) return;

    try {
      await updateOrderPriority(order.id, priority);
      // Refresh order data
      const updatedOrder = await AdminOrderService.getOrderById(order.id);
      setOrder(updatedOrder);
    } catch (error) {
      console.error('Error updating priority:', error);
    }
  };

  const handleGenerateLabel = async () => {
    if (!order) return;

    try {
      await generateShippingLabel(order.id);
      // Refresh order data
      const updatedOrder = await AdminOrderService.getOrderById(order.id);
      setOrder(updatedOrder);
    } catch (error) {
      console.error('Error generating label:', error);
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'confirmed':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      case 'processing':
        return <Package className="w-5 h-5 text-purple-500" />;
      case 'shipped':
        return <Truck className="w-5 h-5 text-indigo-500" />;
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'cancelled':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  if (!checkPermission('orders', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده جزئیات سفارش را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!order) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            سفارش یافت نشد
          </h2>
          <p className="text-gray-600 mb-4">
            سفارش مورد نظر وجود ندارد یا حذف شده است.
          </p>
          <AdminButton
            variant="outline"
            onClick={() => navigate('/admin/orders')}
            icon={ArrowRight}
          >
            بازگشت به لیست سفارشات
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title={`سفارش ${order.orderNumber}`}
      subtitle={`جزئیات کامل سفارش`}
      actions={
        <div className="flex items-center gap-3">
          <AdminButton
            variant="outline"
            size="sm"
            onClick={() => navigate('/admin/orders')}
            icon={ArrowRight}
          >
            بازگشت
          </AdminButton>
          
          {checkPermission('orders', 'update') && (
            <>
              <AdminButton
                variant="outline"
                size="sm"
                onClick={() => setShowStatusManager(true)}
                icon={RefreshCw}
              >
                تغییر وضعیت
              </AdminButton>
              
              <AdminButton
                variant="outline"
                size="sm"
                onClick={() => navigate(`/admin/orders/${order.id}/edit`)}
                icon={Edit}
              >
                ویرایش
              </AdminButton>
            </>
          )}
        </div>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Header */}
          <AdminCard>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  {getStatusIcon(order.status)}
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">
                      سفارش {order.orderNumber}
                    </h2>
                    <p className="text-sm text-gray-500">
                      ایجاد شده در {formatPersianDate(order.createdAt)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                    {order.status === 'pending' && 'در انتظار تأیید'}
                    {order.status === 'confirmed' && 'تأیید شده'}
                    {order.status === 'processing' && 'در حال پردازش'}
                    {order.status === 'shipped' && 'ارسال شده'}
                    {order.status === 'delivered' && 'تحویل داده شده'}
                    {order.status === 'cancelled' && 'لغو شده'}
                  </span>
                  
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(order.priority || 'normal')}`}>
                    {order.priority === 'low' && 'کم'}
                    {order.priority === 'normal' && 'عادی'}
                    {order.priority === 'high' && 'بالا'}
                    {order.priority === 'urgent' && 'فوری'}
                    {!order.priority && 'عادی'}
                  </span>
                </div>
              </div>

              {/* Order Items */}
              <div>
                <h3 className="font-medium text-gray-900 mb-4">محصولات سفارش</h3>
                <div className="space-y-3">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                      <img
                        src={item.productImage}
                        alt={item.productName}
                        className="w-12 h-12 object-cover rounded-lg"
                        onError={(e) => {
                          e.currentTarget.src = '/images/placeholder-product.jpg';
                        }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.productName}</h4>
                        <p className="text-sm text-gray-500">
                          تعداد: {item.quantity} × {formatCurrency(item.price)}
                        </p>
                      </div>
                      <div className="text-left">
                        <p className="font-medium text-gray-900">
                          {formatCurrency(item.price * item.quantity)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Summary */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">مبلغ محصولات:</span>
                    <span className="text-gray-900">{formatCurrency(order.orderSummary.subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">هزینه ارسال:</span>
                    <span className="text-gray-900">{formatCurrency(order.orderSummary.shippingCost)}</span>
                  </div>
                  {order.orderSummary.discount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">تخفیف:</span>
                      <span className="text-green-600">-{formatCurrency(order.orderSummary.discount)}</span>
                    </div>
                  )}
                  <div className="flex justify-between text-lg font-bold pt-2 border-t border-gray-200">
                    <span className="text-gray-900">مبلغ کل:</span>
                    <span className="text-gray-900">{formatCurrency(order.orderSummary.total)}</span>
                  </div>
                </div>
              </div>
            </div>
          </AdminCard>

          {/* Timeline */}
          <AdminCard>
            <div className="p-6">
              <OrderTimeline order={order} />
            </div>
          </AdminCard>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Info */}
          <AdminCard>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <User className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">اطلاعات مشتری</h3>
              </div>
              
              <div className="space-y-3">
                <div>
                  <p className="font-medium text-gray-900">{order.customerInfo.name}</p>
                  <p className="text-sm text-gray-500">{order.customerInfo.email}</p>
                  <p className="text-sm text-gray-500">{order.customerInfo.phone}</p>
                </div>
                
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-600">تعداد سفارشات:</span>
                  <span className="font-medium">{order.customerInfo.totalOrders}</span>
                </div>
                
                {order.customerInfo.loyaltyTier && (
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-gray-600">سطح وفاداری:</span>
                    <span className="font-medium">{order.customerInfo.loyaltyTier}</span>
                  </div>
                )}
                
                {order.customerInfo.isVip && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    مشتری VIP
                  </span>
                )}
              </div>
            </div>
          </AdminCard>

          {/* Shipping Info */}
          <AdminCard>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <MapPin className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">اطلاعات ارسال</h3>
              </div>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">آدرس:</p>
                  <p className="text-sm text-gray-900">
                    {order.shippingAddress.address}<br />
                    {order.shippingAddress.city}, {order.shippingAddress.province}<br />
                    کد پستی: {order.shippingAddress.postalCode}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-600">روش ارسال:</p>
                  <p className="text-sm text-gray-900">{order.shippingMethod.name}</p>
                  <p className="text-xs text-gray-500">{order.shippingMethod.description}</p>
                </div>
                
                {order.trackingNumber && (
                  <div>
                    <p className="text-sm text-gray-600">کد رهگیری:</p>
                    <p className="text-sm font-mono text-gray-900">{order.trackingNumber}</p>
                  </div>
                )}
                
                {order.estimatedDelivery && (
                  <div>
                    <p className="text-sm text-gray-600">تاریخ تحویل تخمینی:</p>
                    <p className="text-sm text-gray-900">
                      {formatPersianDate(order.estimatedDelivery)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </AdminCard>

          {/* Payment Info */}
          <AdminCard>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <CreditCard className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">اطلاعات پرداخت</h3>
              </div>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">روش پرداخت:</p>
                  <p className="text-sm text-gray-900">{order.paymentMethod.title}</p>
                  <p className="text-xs text-gray-500">{order.paymentMethod.description}</p>
                </div>
              </div>
            </div>
          </AdminCard>

          {/* Quick Actions */}
          {checkPermission('orders', 'update') && (
            <AdminCard>
              <div className="p-6">
                <h3 className="font-medium text-gray-900 mb-4">عملیات سریع</h3>
                
                <div className="space-y-3">
                  {!order.fulfillment.labelPrinted && ['confirmed', 'processing'].includes(order.status) && (
                    <AdminButton
                      variant="outline"
                      size="sm"
                      fullWidth
                      icon={Package}
                      onClick={handleGenerateLabel}
                    >
                      چاپ برچسب ارسال
                    </AdminButton>
                  )}
                  
                  <AdminButton
                    variant="outline"
                    size="sm"
                    fullWidth
                    icon={Download}
                    onClick={() => {
                      // Generate invoice
                      toast.success('فاکتور در حال تولید است...');
                    }}
                  >
                    دانلود فاکتور
                  </AdminButton>
                </div>
              </div>
            </AdminCard>
          )}

          {/* Add Note */}
          {checkPermission('orders', 'update') && (
            <AdminCard>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <MessageSquare className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">افزودن یادداشت</h3>
                </div>
                
                <div className="space-y-3">
                  <textarea
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    placeholder="یادداشت جدید..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                  
                  <AdminButton
                    variant="primary"
                    size="sm"
                    fullWidth
                    onClick={handleAddNote}
                    loading={addingNote}
                    disabled={!newNote.trim()}
                  >
                    افزودن یادداشت
                  </AdminButton>
                </div>
              </div>
            </AdminCard>
          )}
        </div>
      </div>

      {/* Status Manager Modal */}
      <OrderStatusManager
        order={order}
        onStatusUpdate={handleStatusUpdate}
        onClose={() => setShowStatusManager(false)}
        isOpen={showStatusManager}
      />
    </AdminLayout>
  );
};

export default OrderDetailsPage;
