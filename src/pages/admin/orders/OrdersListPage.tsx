import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Filter,
  Download,
  RefreshCw,
  Search,
  MoreHorizontal,
  Eye,
  Edit
} from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAdminOrders } from '../../../hooks/useAdminOrders';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminOrder, OrderFilters as OrderFiltersType } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import AdminLayout, { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import OrderTable from '../../../components/admin/orders/OrderTable';
import OrderFilters from '../../../components/admin/orders/OrderFilters';
import OrderStatusManager from '../../../components/admin/orders/OrderStatusManager';
import OrderExport from '../../../components/admin/orders/OrderExport';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import toast from 'react-hot-toast';

const OrdersListPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const {
    orders,
    loading,
    error,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSortBy,
    setSortOrder,
    updateOrderStatus,
    generateShippingLabel,
    bulkUpdateStatus,
    analytics
  } = useAdminOrders();

  const [searchValue, setSearchValue] = useState('');
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showExport, setShowExport] = useState(false);
  const [statusManagerOrder, setStatusManagerOrder] = useState<AdminOrder | null>(null);

  // Update search filter when search value changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setFilters({
        ...filters,
        customerSearch: searchValue || undefined
      });
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchValue, setFilters]);

  const handleViewOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}`);
  };

  const handleEditOrder = (order: AdminOrder) => {
    navigate(`/admin/orders/${order.id}/edit`);
  };

  const handleUpdateStatus = async (orderId: string, status: Order['status']) => {
    try {
      await updateOrderStatus(orderId, status);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleGenerateLabel = async (orderId: string) => {
    try {
      await generateShippingLabel(orderId);
    } catch (error) {
      console.error('Error generating label:', error);
    }
  };

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedOrders(selected ? orders.map(order => order.id) : []);
  };

  const handleSort = (key: string) => {
    if (sortBy === key) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(key);
      setSortOrder('desc');
    }
  };

  const handleClearFilters = () => {
    setFilters({});
    setSearchValue('');
  };

  const handleBulkStatusUpdate = async (status: Order['status']) => {
    if (selectedOrders.length === 0) {
      toast.error('لطفاً حداقل یک سفارش انتخاب کنید');
      return;
    }

    try {
      await bulkUpdateStatus(selectedOrders, status);
      setSelectedOrders([]);
    } catch (error) {
      console.error('Error in bulk update:', error);
    }
  };

  const getOrderStats = () => {
    try {
      const stats = analytics();
      return [
        {
          title: 'کل سفارشات',
          value: stats.totalOrders.toLocaleString('fa-IR'),
          color: 'text-blue-600',
          bgColor: 'bg-blue-50'
        },
        {
          title: 'در انتظار تأیید',
          value: (stats.ordersByStatus.pending || 0).toLocaleString('fa-IR'),
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50'
        },
        {
          title: 'در حال پردازش',
          value: (stats.ordersByStatus.processing || 0).toLocaleString('fa-IR'),
          color: 'text-purple-600',
          bgColor: 'bg-purple-50'
        },
        {
          title: 'ارسال شده',
          value: (stats.ordersByStatus.shipped || 0).toLocaleString('fa-IR'),
          color: 'text-indigo-600',
          bgColor: 'bg-indigo-50'
        }
      ];
    } catch (error) {
      console.error('Error calculating order stats:', error);
      return [
        {
          title: 'کل سفارشات',
          value: '0',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50'
        },
        {
          title: 'در انتظار تأیید',
          value: '0',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50'
        },
        {
          title: 'در حال پردازش',
          value: '0',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50'
        },
        {
          title: 'ارسال شده',
          value: '0',
          color: 'text-indigo-600',
          bgColor: 'bg-indigo-50'
        }
      ];
    }
  };

  if (!checkPermission('orders', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده سفارشات را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminListLayout
      title="مدیریت سفارشات"
      subtitle="مشاهده و مدیریت تمام سفارشات"
      searchValue={searchValue}
      onSearchChange={setSearchValue}
      searchPlaceholder="جستجو در سفارشات..."
      filters={
        <div className="flex items-center gap-2">
          <AdminButton
            variant="outline"
            size="sm"
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
          >
            فیلترها
          </AdminButton>
          
          {checkPermission('orders', 'export') && (
            <AdminButton
              variant="outline"
              size="sm"
              icon={Download}
              onClick={() => setShowExport(true)}
            >
              خروجی
            </AdminButton>
          )}
          
          <AdminButton
            variant="outline"
            size="sm"
            icon={RefreshCw}
            onClick={() => window.location.reload()}
          >
            بروزرسانی
          </AdminButton>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Order Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {getOrderStats().map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <AdminCard className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">{stat.title}</p>
                    <p className={`text-2xl font-bold ${stat.color}`}>
                      {stat.value}
                    </p>
                  </div>
                  <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                    <div className={`w-6 h-6 ${stat.color}`}>
                      {/* Icon placeholder */}
                    </div>
                  </div>
                </div>
              </AdminCard>
            </motion.div>
          ))}
        </div>

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <OrderFilters
              filters={filters}
              onFiltersChange={setFilters}
              onClearFilters={handleClearFilters}
            />
          </motion.div>
        )}

        {/* Bulk Actions */}
        {selectedOrders.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-800">
                {selectedOrders.length} سفارش انتخاب شده
              </span>
              <div className="flex items-center gap-2">
                <AdminButton
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkStatusUpdate('confirmed')}
                >
                  تأیید همه
                </AdminButton>
                <AdminButton
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkStatusUpdate('processing')}
                >
                  شروع پردازش
                </AdminButton>
                <AdminButton
                  variant="outline"
                  size="sm"
                  onClick={() => setShowExport(true)}
                >
                  خروجی انتخاب شده
                </AdminButton>
              </div>
            </div>
          </motion.div>
        )}

        {/* Orders Table */}
        <AdminCard>
          <OrderTable
            orders={orders}
            loading={loading}
            onViewOrder={handleViewOrder}
            onEditOrder={handleEditOrder}
            onUpdateStatus={handleUpdateStatus}
            onGenerateLabel={handleGenerateLabel}
            selectedOrders={selectedOrders}
            onSelectOrder={handleSelectOrder}
            onSelectAll={handleSelectAll}
            sortBy={sortBy}
            sortOrder={sortOrder}
            onSort={handleSort}
          />
        </AdminCard>

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <p className="text-red-600">{error}</p>
            <AdminButton
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              تلاش مجدد
            </AdminButton>
          </div>
        )}

        {/* Empty State */}
        {!loading && orders.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              هیچ سفارشی یافت نشد
            </h3>
            <p className="text-gray-600 mb-4">
              با فیلترهای مختلف جستجو کنید یا فیلترها را پاک کنید.
            </p>
            <AdminButton
              variant="outline"
              onClick={handleClearFilters}
            >
              پاک کردن فیلترها
            </AdminButton>
          </div>
        )}
      </div>

      {/* Status Manager Modal */}
      {statusManagerOrder && (
        <OrderStatusManager
          order={statusManagerOrder}
          onStatusUpdate={updateOrderStatus}
          onClose={() => setStatusManagerOrder(null)}
          isOpen={!!statusManagerOrder}
        />
      )}

      {/* Export Modal */}
      <OrderExport
        orders={selectedOrders.length > 0 
          ? orders.filter(order => selectedOrders.includes(order.id))
          : orders
        }
        filters={filters}
        isOpen={showExport}
        onClose={() => setShowExport(false)}
      />
    </AdminListLayout>
  );
};

export default OrdersListPage;
