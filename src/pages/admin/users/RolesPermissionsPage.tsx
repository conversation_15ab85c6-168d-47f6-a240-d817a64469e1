import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Shield,
  Crown,
  User,
  ShieldOff,
  CheckCircle,
  XCircle,
  Info,
  Settings
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import { AdminTableBadge } from '../../../components/admin/common/AdminTable';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { 
  AdminRole, 
  AdminResource, 
  AdminAction, 
  ADMIN_ROLE_PERMISSIONS,
  PERSIAN_ADMIN_MESSAGES 
} from '../../../types/admin';
import SEOHead from '../../../components/seo/SEOHead';

const RolesPermissionsPage: React.FC = () => {
  const { user } = useAdminAuth();
  const [selectedRole, setSelectedRole] = useState<AdminRole>('super_admin');

  const roles: { role: AdminRole; name: string; description: string; icon: React.ReactNode; color: string }[] = [
    {
      role: 'super_admin',
      name: 'مدیر کل',
      description: 'دسترسی کامل به تمام بخش‌های سیستم',
      icon: <Crown className="w-5 h-5" />,
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200'
    },
    {
      role: 'admin',
      name: 'مدیر',
      description: 'دسترسی به اکثر بخش‌های سیستم',
      icon: <Shield className="w-5 h-5" />,
      color: 'bg-blue-100 text-blue-800 border-blue-200'
    },
    {
      role: 'moderator',
      name: 'ناظر',
      description: 'دسترسی محدود به بررسی و تأیید محتوا',
      icon: <ShieldOff className="w-5 h-5" />,
      color: 'bg-green-100 text-green-800 border-green-200'
    },
    {
      role: 'viewer',
      name: 'بازدیدکننده',
      description: 'دسترسی فقط خواندنی به اطلاعات',
      icon: <User className="w-5 h-5" />,
      color: 'bg-gray-100 text-gray-800 border-gray-200'
    }
  ];

  const resources: { resource: AdminResource; name: string; description: string }[] = [
    { resource: 'products', name: 'محصولات', description: 'مدیریت محصولات و دسته‌بندی‌ها' },
    { resource: 'orders', name: 'سفارشات', description: 'مدیریت سفارشات و پردازش' },
    { resource: 'customers', name: 'مشتریان', description: 'مدیریت اطلاعات مشتریان' },
    { resource: 'reviews', name: 'نظرات', description: 'مدیریت و بررسی نظرات' },
    { resource: 'loyalty', name: 'باشگاه مشتریان', description: 'مدیریت برنامه وفاداری' },
    { resource: 'content', name: 'محتوا', description: 'مدیریت محتوای سایت' },
    { resource: 'analytics', name: 'آمار و گزارشات', description: 'مشاهده آمار و گزارشات' },
    { resource: 'settings', name: 'تنظیمات', description: 'تنظیمات سیستم' },
    { resource: 'users', name: 'کاربران', description: 'مدیریت کاربران مدیر' },
    { resource: 'audit', name: 'گزارش عملکرد', description: 'مشاهده لاگ‌های سیستم' },
    { resource: 'notifications', name: 'اعلان‌ها', description: 'مدیریت اعلان‌ها' }
  ];

  const actions: { action: AdminAction; name: string; description: string }[] = [
    { action: 'create', name: 'ایجاد', description: 'ایجاد رکورد جدید' },
    { action: 'read', name: 'مشاهده', description: 'مشاهده اطلاعات' },
    { action: 'update', name: 'ویرایش', description: 'ویرایش اطلاعات موجود' },
    { action: 'delete', name: 'حذف', description: 'حذف رکورد' },
    { action: 'approve', name: 'تأیید', description: 'تأیید محتوا' },
    { action: 'reject', name: 'رد', description: 'رد محتوا' },
    { action: 'export', name: 'خروجی', description: 'دریافت خروجی' },
    { action: 'import', name: 'ورودی', description: 'وارد کردن داده' },
    { action: 'moderate', name: 'بررسی', description: 'بررسی و نظارت' },
    { action: 'configure', name: 'پیکربندی', description: 'تنظیم پیکربندی' }
  ];

  const hasPermission = (role: AdminRole, resource: AdminResource, action: AdminAction): boolean => {
    const rolePermissions = ADMIN_ROLE_PERMISSIONS[role];
    const resourcePermission = rolePermissions.find(p => p.resource === resource);
    return resourcePermission?.actions.includes(action) || false;
  };

  const getPermissionIcon = (hasAccess: boolean) => {
    return hasAccess ? 
      <CheckCircle className="w-4 h-4 text-green-500" /> : 
      <XCircle className="w-4 h-4 text-red-500" />;
  };

  return (
    <>
      <SEOHead 
        title="نقش‌ها و مجوزها - پنل مدیریت"
        description="مدیریت نقش‌ها و مجوزهای کاربران"
      />
      
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <Settings className="w-8 h-8 text-admin-600" />
                نقش‌ها و مجوزها
              </h1>
              <p className="text-gray-600 mt-1">
                مشاهده و مدیریت نقش‌های کاربری و مجوزهای دسترسی
              </p>
            </div>
          </div>

          {/* Roles Overview */}
          <AdminCard title="نقش‌های کاربری" icon={Shield}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {roles.map((roleInfo) => (
                <motion.div
                  key={roleInfo.role}
                  whileHover={{ scale: 1.02 }}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedRole === roleInfo.role 
                      ? roleInfo.color + ' ring-2 ring-offset-2 ring-blue-500' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedRole(roleInfo.role)}
                >
                  <div className="flex items-center gap-3 mb-2">
                    {roleInfo.icon}
                    <h3 className="font-medium text-gray-900">{roleInfo.name}</h3>
                  </div>
                  <p className="text-sm text-gray-600">{roleInfo.description}</p>
                  <div className="mt-3">
                    <AdminTableBadge
                      variant="info"
                    >
                      {`${ADMIN_ROLE_PERMISSIONS[roleInfo.role].length} منبع`}
                    </AdminTableBadge>
                  </div>
                </motion.div>
              ))}
            </div>
          </AdminCard>

          {/* Permission Matrix */}
          <AdminCard 
            title={`مجوزهای نقش: ${roles.find(r => r.role === selectedRole)?.name}`}
            icon={Info}
          >
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      منبع
                    </th>
                    {actions.map((action) => (
                      <th 
                        key={action.action}
                        className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                        title={action.description}
                      >
                        {action.name}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {resources.map((resource) => (
                    <tr key={resource.resource} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {resource.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {resource.description}
                          </div>
                        </div>
                      </td>
                      {actions.map((action) => (
                        <td 
                          key={action.action}
                          className="px-3 py-4 whitespace-nowrap text-center"
                        >
                          {getPermissionIcon(
                            hasPermission(selectedRole, resource.resource, action.action)
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </AdminCard>

          {/* Permission Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <AdminCard className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">مجوزهای فعال</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {ADMIN_ROLE_PERMISSIONS[selectedRole].reduce(
                      (total, perm) => total + perm.actions.length, 0
                    )}
                  </p>
                </div>
              </div>
            </AdminCard>

            <AdminCard className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Shield className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">منابع قابل دسترسی</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {ADMIN_ROLE_PERMISSIONS[selectedRole].length}
                  </p>
                </div>
              </div>
            </AdminCard>

            <AdminCard className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Settings className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">سطح دسترسی</p>
                  <p className="text-lg font-bold text-gray-900">
                    {selectedRole === 'super_admin' ? 'کامل' :
                     selectedRole === 'admin' ? 'بالا' :
                     selectedRole === 'moderator' ? 'متوسط' : 'محدود'}
                  </p>
                </div>
              </div>
            </AdminCard>
          </div>

          {/* Info Box */}
          <AdminCard className="bg-blue-50 border-blue-200">
            <div className="flex items-start gap-3">
              <Info className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900 mb-1">راهنمای نقش‌ها</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• <strong>مدیر کل:</strong> دسترسی کامل به تمام بخش‌های سیستم</li>
                  <li>• <strong>مدیر:</strong> دسترسی به اکثر عملیات بجز مدیریت کاربران</li>
                  <li>• <strong>ناظر:</strong> دسترسی به بررسی و تأیید محتوا</li>
                  <li>• <strong>بازدیدکننده:</strong> دسترسی فقط خواندنی به اطلاعات</li>
                </ul>
              </div>
            </div>
          </AdminCard>
        </div>
      </AdminLayout>
    </>
  );
};

export default RolesPermissionsPage;
