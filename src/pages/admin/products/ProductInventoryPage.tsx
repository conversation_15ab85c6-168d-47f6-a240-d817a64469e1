import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Package, AlertTriangle, TrendingUp, TrendingDown, Download } from 'lucide-react';
import { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import SEOHead from '../../../components/seo/SEOHead';
import { formatPrice } from '../../../utils/formatters';
import toast from 'react-hot-toast';

// Mock inventory data
const mockInventory = [
  {
    id: 1,
    name: 'کرم آبرسان گلو رویا',
    sku: 'GR-MOIST-001',
    category: 'مراقبت از پوست',
    brand: 'گلو رویا',
    currentStock: 45,
    reservedStock: 5,
    availableStock: 40,
    lowStockThreshold: 10,
    price: 250000,
    totalValue: 11250000,
    lastRestocked: '2024-03-10',
    status: 'in_stock'
  },
  {
    id: 2,
    name: 'سرم ویتامین C',
    sku: 'GR-SERUM-002',
    category: 'سرم و اسانس',
    brand: 'گلو رویا',
    currentStock: 8,
    reservedStock: 2,
    availableStock: 6,
    lowStockThreshold: 15,
    price: 450000,
    totalValue: 3600000,
    lastRestocked: '2024-03-05',
    status: 'low_stock'
  },
  {
    id: 3,
    name: 'کلنزر ملایم صورت',
    sku: 'GR-CLEAN-003',
    category: 'پاک‌کننده',
    brand: 'گلو رویا',
    currentStock: 0,
    reservedStock: 0,
    availableStock: 0,
    lowStockThreshold: 20,
    price: 180000,
    totalValue: 0,
    lastRestocked: '2024-02-28',
    status: 'out_of_stock'
  },
  {
    id: 4,
    name: 'ماسک طلا و کلاژن',
    sku: 'GR-MASK-004',
    category: 'ماسک صورت',
    brand: 'گلو رویا',
    currentStock: 120,
    reservedStock: 10,
    availableStock: 110,
    lowStockThreshold: 25,
    price: 320000,
    totalValue: 38400000,
    lastRestocked: '2024-03-12',
    status: 'in_stock'
  },
  {
    id: 5,
    name: 'کرم ضد آفتاب SPF50',
    sku: 'GR-SUN-005',
    category: 'محافظت از آفتاب',
    brand: 'گلو رویا',
    currentStock: 25,
    reservedStock: 3,
    availableStock: 22,
    lowStockThreshold: 30,
    price: 380000,
    totalValue: 9500000,
    lastRestocked: '2024-03-08',
    status: 'in_stock'
  }
];

const ProductInventoryPage: React.FC = () => {
  const [inventory] = useState(mockInventory);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Filter inventory based on search and status
  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Calculate statistics
  const stats = {
    totalProducts: inventory.length,
    inStock: inventory.filter(item => item.status === 'in_stock').length,
    lowStock: inventory.filter(item => item.status === 'low_stock').length,
    outOfStock: inventory.filter(item => item.status === 'out_of_stock').length,
    totalValue: inventory.reduce((sum, item) => sum + item.totalValue, 0),
    totalStock: inventory.reduce((sum, item) => sum + item.currentStock, 0)
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'in_stock':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">موجود</span>;
      case 'low_stock':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">موجودی کم</span>;
      case 'out_of_stock':
        return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">ناموجود</span>;
      default:
        return null;
    }
  };

  const handleExportInventory = () => {
    toast.success('گزارش موجودی در حال آماده‌سازی است');
  };

  const filters = (
    <>
      <select
        value={statusFilter}
        onChange={(e) => setStatusFilter(e.target.value)}
        className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
      >
        <option value="all">همه وضعیت‌ها</option>
        <option value="in_stock">موجود</option>
        <option value="low_stock">موجودی کم</option>
        <option value="out_of_stock">ناموجود</option>
      </select>
      <button
        onClick={handleExportInventory}
        className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
      >
        <Download className="w-4 h-4" />
        خروجی Excel
      </button>
    </>
  );

  return (
    <>
      <SEOHead
        title="مدیریت موجودی انبار | پنل مدیریت گلو رویا"
        description="مدیریت موجودی و انبار محصولات"
      />

      <AdminListLayout
        title="مدیریت موجودی انبار"
        subtitle="نظارت و مدیریت موجودی محصولات"
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="جستجو در موجودی..."
        filters={filters}
      >
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
            <AdminStatsCard
              title="کل محصولات"
              value={stats.totalProducts.toLocaleString('fa-IR')}
              icon={Package}
              color="bg-blue-500"
            />
            <AdminStatsCard
              title="موجود"
              value={stats.inStock.toLocaleString('fa-IR')}
              icon={TrendingUp}
              color="bg-green-500"
            />
            <AdminStatsCard
              title="موجودی کم"
              value={stats.lowStock.toLocaleString('fa-IR')}
              icon={AlertTriangle}
              color="bg-yellow-500"
            />
            <AdminStatsCard
              title="ناموجود"
              value={stats.outOfStock.toLocaleString('fa-IR')}
              icon={TrendingDown}
              color="bg-red-500"
            />
            <AdminStatsCard
              title="کل موجودی"
              value={stats.totalStock.toLocaleString('fa-IR')}
              icon={Package}
              color="bg-purple-500"
            />
            <AdminStatsCard
              title="ارزش کل"
              value={formatPrice(stats.totalValue)}
              icon={TrendingUp}
              color="bg-indigo-500"
            />
          </div>

          {/* Inventory Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      محصول
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      SKU
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      موجودی فعلی
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رزرو شده
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      قابل فروش
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      وضعیت
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ارزش کل
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      آخرین تامین
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredInventory.map((item) => (
                    <motion.tr
                      key={item.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{item.name}</div>
                          <div className="text-sm text-gray-500">{item.category}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.sku}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.currentStock.toLocaleString('fa-IR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.reservedStock.toLocaleString('fa-IR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.availableStock.toLocaleString('fa-IR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(item.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatPrice(item.totalValue)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(item.lastRestocked).toLocaleDateString('fa-IR')}
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredInventory.length === 0 && (
              <div className="text-center py-12">
                <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  موجودی یافت نشد
                </h3>
                <p className="text-gray-500">
                  {searchTerm ? 'محصول مورد نظر یافت نشد' : 'هنوز محصولی در انبار ثبت نشده است'}
                </p>
              </div>
            )}
          </div>
        </AdminListLayout>
    </>
  );
};

export default ProductInventoryPage;
