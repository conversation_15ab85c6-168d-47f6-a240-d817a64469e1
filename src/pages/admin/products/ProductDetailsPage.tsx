import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Edit,
  Trash2,
  Package,
  AlertTriangle,
  TrendingUp,
  Eye,
  Tag,
  Building2
} from 'lucide-react';
import { AdminPageLayout } from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton, { AdminButtonGroup } from '../../../components/admin/common/AdminButton';
import { AdminTableBadge } from '../../../components/admin/common/AdminTable';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import AdminImagePreview from '../../../components/admin/common/AdminImagePreview';
import { useAdminProducts } from '../../../hooks/useAdminProducts';
import { AdminProduct, ProductAnalytics } from '../../../types/adminProduct';
import { getBrandInfo } from '../../../utils/brandUtils';
import { formatPrice } from '../../../utils/formatters';

const ProductDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { getProduct, deleteProduct, getProductAnalytics, loading, allProducts } = useAdminProducts();
  const [product, setProduct] = useState<AdminProduct | undefined>(undefined);
  const [analytics, setAnalytics] = useState<ProductAnalytics | undefined>(undefined);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);

  useEffect(() => {
    const loadProductData = async () => {
      if (!id || loading) return;

      try {
        setPageLoading(true);
        const productId = Number(id);

        // Wait for products to be loaded
        if (allProducts.length === 0) {
          return; // Products not loaded yet
        }

        const foundProduct = getProduct(productId);
        setProduct(foundProduct);

        if (foundProduct) {
          try {
            const productAnalytics = getProductAnalytics(productId);
            setAnalytics(productAnalytics);
          } catch (error) {
            console.warn('Failed to load analytics:', error);
            // Analytics is optional, continue without it
          }
        }
      } catch (error) {
        console.error('Error loading product:', error);
      } finally {
        setPageLoading(false);
      }
    };

    loadProductData();
  }, [id, getProduct, getProductAnalytics, loading, allProducts]);

  // Show loading state
  if (loading || pageLoading) {
    return (
      <AdminPageLayout
        title="جزئیات محصول"
        subtitle="در حال بارگذاری..."
      >
        <AdminCard>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600 mx-auto mb-4"></div>
            <p className="text-gray-600">در حال بارگذاری اطلاعات محصول...</p>
          </div>
        </AdminCard>
      </AdminPageLayout>
    );
  }

  // Show not found state
  if (!product) {
    return (
      <AdminPageLayout
        title="جزئیات محصول"
        subtitle="محصول یافت نشد"
      >
        <AdminCard>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">محصول یافت نشد</h3>
            <p className="text-gray-600 mb-4">
              محصول با شناسه {id} در سیستم موجود نیست.
            </p>
            <AdminButton
              variant="primary"
              onClick={() => navigate('/admin/products')}
            >
              بازگشت به لیست محصولات
            </AdminButton>
          </div>
        </AdminCard>
      </AdminPageLayout>
    );
  }

  // Render product details
  return (
    <AdminPageLayout
      title={`جزئیات محصول: ${product.name}`}
      subtitle={`شناسه: ${product.id} | کد محصول: ${product.sku}`}
    >
      <div className="space-y-6">
        {/* Product Info Card */}
        <AdminCard>
          <div className="p-6">
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center space-x-4 space-x-reverse">
                <img
                  src={product.imageSrc}
                  alt={product.name}
                  className="w-20 h-20 object-cover rounded-lg"
                />
                <div>
                  <h2 className="text-xl font-bold text-gray-900 mb-2">{product.name}</h2>
                  <p className="text-gray-600">{product.category}</p>
                  {product.brand && (
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <span>برند:</span>
                      <AdminImagePreview
                        src={getBrandInfo(product.brand).logo}
                        alt={`لوگو ${product.brand}`}
                        size="xs"
                        shape="rounded"
                        fallbackIcon={Building2}
                      />
                      <span>{product.brand}</span>
                    </div>
                  )}
                </div>
              </div>

              <AdminButtonGroup>
                <AdminButton
                  variant="outline"
                  icon={Edit}
                  onClick={() => navigate(`/admin/products/${product.id}/edit`)}
                >
                  ویرایش
                </AdminButton>
                <AdminButton
                  variant="danger"
                  icon={Trash2}
                  onClick={() => setShowDeleteModal(true)}
                >
                  حذف
                </AdminButton>
              </AdminButtonGroup>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600 mb-1">قیمت</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatPrice(product.price)} تومان
                </p>
                {product.discountedPrice && (
                  <p className="text-sm text-green-600">
                    تخفیف: {formatPrice(product.discountedPrice)} تومان
                  </p>
                )}
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600 mb-1">موجودی</p>
                <p className="text-lg font-semibold text-gray-900">{product.stock}</p>
                {product.trackInventory && product.stock <= product.lowStockThreshold && (
                  <p className="text-sm text-red-600">موجودی کم</p>
                )}
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600 mb-1">وضعیت</p>
                <AdminTableBadge variant={product.status === 'active' ? 'success' : 'warning'}>
                  {product.status === 'active' ? 'فعال' : 'غیرفعال'}
                </AdminTableBadge>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600 mb-1">امتیاز</p>
                <p className="text-lg font-semibold text-gray-900">
                  {product.rating.toFixed(1)} ({product.reviewCount} نظر)
                </p>
              </div>
            </div>

            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-2">توضیحات</h3>
              <p className="text-gray-700">{product.description}</p>
            </div>
          </div>
        </AdminCard>

        {/* Analytics Cards */}
        {analytics && (
          <AdminCard>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">آمار محصول</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Eye className="w-8 h-8 text-blue-600 ml-3" />
                    <div>
                      <p className="text-sm text-gray-600">بازدید</p>
                      <p className="text-xl font-semibold text-gray-900">
                        {analytics.views.toLocaleString('fa-IR')}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <TrendingUp className="w-8 h-8 text-green-600 ml-3" />
                    <div>
                      <p className="text-sm text-gray-600">فروش</p>
                      <p className="text-xl font-semibold text-gray-900">
                        {analytics.sales.toLocaleString('fa-IR')}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Package className="w-8 h-8 text-purple-600 ml-3" />
                    <div>
                      <p className="text-sm text-gray-600">درآمد</p>
                      <p className="text-xl font-semibold text-gray-900">
                        {formatPrice(analytics.revenue)} تومان
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Tag className="w-8 h-8 text-orange-600 ml-3" />
                    <div>
                      <p className="text-sm text-gray-600">نرخ تبدیل</p>
                      <p className="text-xl font-semibold text-gray-900">
                        {analytics.conversionRate.toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AdminCard>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <AdminConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={async () => {
            try {
              await deleteProduct(product.id);
              navigate('/admin/products');
            } catch (error) {
              console.error('Delete failed:', error);
            }
          }}
          title="حذف محصول"
          message={`آیا از حذف محصول "${product.name}" اطمینان دارید؟`}
          confirmText="حذف"
          cancelText="انصراف"
          variant="danger"
        />
      )}
    </AdminPageLayout>
  );
};

export default ProductDetailsPage;
