import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Package } from 'lucide-react';
import { AdminFormLayout } from '../../../components/admin/layout/AdminLayout';
import ProductForm from '../../../components/admin/products/ProductForm';
import { useAdminProducts } from '../../../hooks/useAdminProducts';
import { ProductFormData } from '../../../types/adminProduct';
import { generateUniqueSku } from '../../../utils/productValidation';
import SEOHead from '../../../components/seo/SEOHead';
import toast from 'react-hot-toast';

const ProductCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { createProduct, allProducts, getProduct, loading } = useAdminProducts();
  const [submitting, setSubmitting] = useState(false);
  const [initialData, setInitialData] = useState<Partial<ProductFormData> | undefined>();

  // Handle duplication
  useEffect(() => {
    const duplicateId = searchParams.get('duplicate');
    if (duplicateId) {
      const productToDuplicate = getProduct(Number(duplicateId));
      if (productToDuplicate) {
        // Generate unique SKU for duplicated product
        const existingSkus = allProducts.map(p => p.sku);
        const newSku = generateUniqueSku(productToDuplicate.name + ' کپی', existingSkus);
        
        setInitialData({
          name: productToDuplicate.name + ' - کپی',
          description: productToDuplicate.description,
          category: productToDuplicate.category,
          brand: productToDuplicate.brand,
          sku: newSku,
          price: productToDuplicate.price,
          discountedPrice: productToDuplicate.discountedPrice,
          costPrice: productToDuplicate.costPrice,
          compareAtPrice: productToDuplicate.compareAtPrice,
          taxable: productToDuplicate.taxable,
          taxClass: productToDuplicate.taxClass,
          stock: 0, // Reset stock for new product
          trackInventory: productToDuplicate.trackInventory,
          allowBackorder: productToDuplicate.allowBackorder,
          lowStockThreshold: productToDuplicate.lowStockThreshold,
          benefits: [...productToDuplicate.benefits],
          ingredients: [...productToDuplicate.ingredients],
          howToUse: [...productToDuplicate.howToUse],
          size: productToDuplicate.size,
          weight: productToDuplicate.weight,
          imageSrc: productToDuplicate.imageSrc,
          images: [...productToDuplicate.images],
          hasVariants: productToDuplicate.hasVariants,
          variants: productToDuplicate.variants.map(group => ({
            ...group,
            variants: group.variants.map(variant => ({
              ...variant,
              stock: 0 // Reset variant stock
            }))
          })),
          seoTitle: productToDuplicate.seoTitle,
          seoDescription: productToDuplicate.seoDescription,
          seoKeywords: [...(productToDuplicate.seoKeywords || [])],
          status: 'draft', // New products start as draft
          visibility: productToDuplicate.visibility,
          featured: false, // Reset featured status
          isNew: true, // Mark as new
          isBestSeller: false, // Reset bestseller status
          requiresShipping: productToDuplicate.requiresShipping,
          shippingWeight: productToDuplicate.shippingWeight,
          shippingDimensions: productToDuplicate.shippingDimensions,
          tags: [...productToDuplicate.tags],
          vendor: productToDuplicate.vendor,
          productType: productToDuplicate.productType,
          collections: [...productToDuplicate.collections]
        });
        
        toast.success(`محصول "${productToDuplicate.name}" برای کپی آماده شد`);
      }
    }
  }, [searchParams, getProduct, allProducts]);

  const handleSubmit = async (data: ProductFormData) => {
    try {
      setSubmitting(true);
      const newProduct = await createProduct(data);
      toast.success('محصول با موفقیت ایجاد شد');
      navigate(`/admin/products/${newProduct.id}`);
    } catch (error) {
      console.error('Error creating product:', error);
      toast.error('خطا در ایجاد محصول');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/products');
  };

  const existingSkus = allProducts.map(p => p.sku);

  return (
    <>
      <SEOHead
        title="افزودن محصول جدید | پنل مدیریت گلو رویا"
        description="افزودن محصول جدید به فروشگاه گلو رویا"
        robots="noindex, nofollow"
      />

      <AdminFormLayout
        title="افزودن محصول جدید"
        subtitle="محصول جدید را به فروشگاه اضافه کنید"
      >
        <ProductForm
          initialData={initialData}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={submitting}
          existingSkus={existingSkus}
        />
      </AdminFormLayout>
    </>
  );
};

export default ProductCreatePage;
