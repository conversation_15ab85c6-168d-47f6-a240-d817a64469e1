import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { Edit, Trash2, Package, Building2, Eye, Globe, MapPin, Calendar } from 'lucide-react';
import { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import AdminModal, { AdminConfirmModal, AdminFormModal } from '../../../components/admin/common/AdminModal';
import { AdminFormField, AdminInput, AdminTextarea, AdminSelect, AdminCheckbox } from '../../../components/admin/common/AdminForm';
import AdminFileUpload from '../../../components/admin/common/AdminFileUpload';
import AdminImagePreview from '../../../components/admin/common/AdminImagePreview';
import SEOHead from '../../../components/seo/SEOHead';
import toast from 'react-hot-toast';

// Services
import AdminBrandService, { Brand as ApiBrand, CreateBrandData, UpdateBrandData, BrandStats } from '../../../services/adminBrandService';
import { AdminLoginHelper } from '../../../utils/adminLoginHelper';

// Brand form types
interface BrandFormData {
  name: string;
  nameEn: string;
  description: string;
  logo?: string;
  website?: string;
  country: string;
  isActive?: boolean;
}

// Use the API Brand type
type Brand = ApiBrand;

// Countries list
const countries = [
  'ایران',
  'آمریکا',
  'فرانسه',
  'آلمان',
  'انگلستان',
  'ایتالیا',
  'ژاپن',
  'کره جنوبی',
  'کانادا',
  'استرالیا',
  'سوئیس',
  'سوئد',
  'هلند',
  'بلژیک',
  'اسپانیا'
];

const ProductBrandsPage: React.FC = () => {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [stats, setStats] = useState<BrandStats>({
    total: 0,
    active: 0,
    inactive: 0,
    totalProducts: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Logo upload states
  const [addLogoFile, setAddLogoFile] = useState<File | null>(null);
  const [addLogoUrl, setAddLogoUrl] = useState<string>('');
  const [editLogoFile, setEditLogoFile] = useState<File | null>(null);
  const [editLogoUrl, setEditLogoUrl] = useState<string>('');

  // Form handling
  const {
    control: addControl,
    handleSubmit: handleAddSubmit,
    formState: { errors: addErrors },
    reset: resetAddForm
  } = useForm<BrandFormData>({
    defaultValues: {
      name: '',
      nameEn: '',
      description: '',
      website: '',
      country: '',
      isActive: true
    }
  });

  const {
    control: editControl,
    handleSubmit: handleEditSubmit,
    formState: { errors: editErrors },
    reset: resetEditForm,
    setValue: setEditValue
  } = useForm<BrandFormData>();

  // Load data on component mount
  useEffect(() => {
    loadBrands();
    loadStats();
  }, []);

  const loadBrands = async () => {
    try {
      setLoading(true);
      const result = await AdminBrandService.getBrands({
        includeInactive: true,
        search: searchTerm || undefined,
      });

      setBrands(result.brands);
    } catch (error) {
      console.error('Failed to load brands:', error);
      toast.error('خطا در بارگذاری برندها');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const brandStats = await AdminBrandService.getBrandStats();
      setStats(brandStats);
    } catch (error) {
      console.error('Failed to load brand stats:', error);
      // Set default stats based on loaded brands when API fails
      setStats({
        total: brands.length,
        active: brands.filter(b => b.isActive).length,
        inactive: brands.filter(b => !b.isActive).length,
        totalProducts: brands.reduce((sum, b) => sum + (b.productsCount || 0), 0)
      });
    }
  };

  // Filter brands based on search
  const filteredBrands = brands.filter(brand =>
    brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (brand.nameEn && brand.nameEn.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handler functions
  const handleAddBrand = () => {
    resetAddForm();
    setAddLogoFile(null);
    setAddLogoUrl('');
    setShowAddModal(true);
  };

  const handleViewBrand = (brandId: string) => {
    const brand = brands.find(b => b.id === brandId);
    if (brand) {
      setSelectedBrand(brand);
      setShowViewModal(true);
    }
  };

  const handleEditBrand = (brandId: string) => {
    const brand = brands.find(b => b.id === brandId);
    if (brand) {
      setSelectedBrand(brand);
      // Populate edit form
      setEditValue('name', brand.name);
      setEditValue('nameEn', brand.nameEn || '');
      setEditValue('description', brand.description || '');
      setEditValue('website', brand.website || '');
      setEditValue('country', brand.country || 'ایران');
      setEditValue('isActive', brand.isActive);
      // Set logo states - important: set the existing logo URL
      setEditLogoFile(null);
      setEditLogoUrl(brand.logo || '');
      setShowEditModal(true);
    }
  };

  const handleDeleteBrand = (brandId: string) => {
    const brand = brands.find(b => b.id === brandId);
    if (brand) {
      setSelectedBrand(brand);
      setShowDeleteModal(true);
    }
  };

  // Form submission handlers
  const onAddSubmit = async (data: BrandFormData) => {
    try {
      setIsSubmitting(true);

      // Generate slug from nameEn
      const slug = AdminBrandService.generateSlug(data.nameEn || data.name);

      // Prepare brand data - include logo URL if available
      const brandData: CreateBrandData = {
        name: data.name,
        nameEn: data.nameEn,
        slug,
        description: data.description,
        website: data.website,
        country: data.country,
        isActive: data.isActive ?? true,
        logo: addLogoUrl || undefined, // Include the logo URL
      };

      // Create brand
      let newBrand = await AdminBrandService.createBrand(brandData);
      console.log('Brand created:', newBrand);

      // Small delay to ensure brand is properly created
      await new Promise(resolve => setTimeout(resolve, 100));

      // Upload logo if provided (this will update the brand with the final logo URL)
      if (addLogoFile) {
        try {
          console.log('Uploading logo for brand:', newBrand.id);
          // Verify brand exists before upload
          const brandExists = await AdminBrandService.getBrandById(newBrand.id);
          console.log('Brand verified before upload:', brandExists);

          const uploadResult = await AdminBrandService.uploadBrandLogo(newBrand.id, addLogoFile);
          newBrand = uploadResult.brand;
          console.log('Logo upload successful:', uploadResult);
        } catch (uploadError) {
          console.error('Logo upload failed:', uploadError);
          toast.error('برند ایجاد شد اما آپلود لوگو با خطا مواجه شد');
        }
      }

      // Refresh data
      await loadBrands();
      await loadStats();

      setShowAddModal(false);
      resetAddForm();
      setAddLogoFile(null);
      setAddLogoUrl('');
      toast.success('برند جدید با موفقیت اضافه شد');
    } catch (error) {
      console.error('Failed to create brand:', error);
      toast.error('خطا در افزودن برند');
    } finally {
      setIsSubmitting(false);
    }
  };

  const onEditSubmit = async (data: BrandFormData) => {
    if (!selectedBrand) return;

    try {
      setIsSubmitting(true);

      // Generate slug from nameEn if changed
      const slug = data.nameEn ? AdminBrandService.generateSlug(data.nameEn) : undefined;

      // Prepare update data - include logo URL
      const updateData: UpdateBrandData = {
        name: data.name,
        nameEn: data.nameEn,
        slug,
        description: data.description,
        website: data.website,
        country: data.country,
        isActive: data.isActive,
        logo: editLogoUrl || undefined, // Include the current logo URL
      };

      // Update brand
      let updatedBrand = await AdminBrandService.updateBrand(selectedBrand.id, updateData);
      console.log('Brand updated:', updatedBrand);

      // Upload new logo if provided (this will override the logo URL)
      if (editLogoFile) {
        try {
          console.log('Uploading logo for brand:', selectedBrand.id);
          // Verify brand exists before upload
          const brandExists = await AdminBrandService.getBrandById(selectedBrand.id);
          console.log('Brand verified before upload:', brandExists);

          const uploadResult = await AdminBrandService.uploadBrandLogo(selectedBrand.id, editLogoFile);
          updatedBrand = uploadResult.brand;
          console.log('Logo upload successful:', uploadResult);
        } catch (uploadError) {
          console.error('Logo upload failed:', uploadError);
          toast.error('برند بروزرسانی شد اما آپلود لوگو با خطا مواجه شد');
        }
      }

      // Refresh data
      await loadBrands();
      await loadStats();

      setShowEditModal(false);
      resetEditForm();
      setSelectedBrand(null);
      setEditLogoFile(null);
      setEditLogoUrl('');
      toast.success('برند با موفقیت بروزرسانی شد');
    } catch (error) {
      console.error('Failed to update brand:', error);
      toast.error('خطا در بروزرسانی برند');
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDeleteBrand = async () => {
    if (!selectedBrand) return;

    try {
      setIsSubmitting(true);

      await AdminBrandService.deleteBrand(selectedBrand.id);

      // Refresh data
      await loadBrands();
      await loadStats();

      setShowDeleteModal(false);
      setSelectedBrand(null);
      toast.success('برند با موفقیت حذف شد');
    } catch (error) {
      console.error('Failed to delete brand:', error);
      toast.error('خطا در حذف برند');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <SEOHead
        title="مدیریت برندها | پنل مدیریت گلو رویا"
        description="مدیریت برندهای محصولات فروشگاه"
      />

      <AdminListLayout
        title="مدیریت برندها"
        subtitle="مدیریت برندهای محصولات فروشگاه"
        onAdd={handleAddBrand}
        addText="افزودن برند"
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="جستجو در برندها..."
      >
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <AdminStatsCard
              title="کل برندها"
              value={stats.total.toLocaleString('fa-IR')}
              icon={Building2}
              color="bg-blue-500"
            />
            <AdminStatsCard
              title="برندهای فعال"
              value={stats.active.toLocaleString('fa-IR')}
              icon={Building2}
              color="bg-green-500"
            />
            <AdminStatsCard
              title="برندهای غیرفعال"
              value={stats.inactive.toLocaleString('fa-IR')}
              icon={Building2}
              color="bg-red-500"
            />
            <AdminStatsCard
              title="کل محصولات"
              value={stats.totalProducts.toLocaleString('fa-IR')}
              icon={Package}
              color="bg-purple-500"
            />
          </div>

          {/* Brands Grid */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredBrands.map((brand) => (
                  <motion.div
                    key={brand.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <AdminImagePreview
                          src={brand.logo}
                          alt={`لوگو ${brand.name}`}
                          size="md"
                          shape="rounded"
                          fallbackIcon={Building2}
                          showError
                        />
                        <div>
                          <h3 className="font-semibold text-gray-900">{brand.name}</h3>
                          <p className="text-sm text-gray-500">{brand.nameEn}</p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        brand.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {brand.isActive ? 'فعال' : 'غیرفعال'}
                      </span>
                    </div>

                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {brand.description}
                    </p>

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>وب‌سایت: {brand.website || 'ندارد'}</span>
                      <span>{brand.productsCount} محصول</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleViewBrand(brand.id)}
                        className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center justify-center gap-1 text-sm"
                      >
                        <Eye className="w-4 h-4" />
                        مشاهده
                      </button>
                      <button
                        onClick={() => handleEditBrand(brand.id)}
                        className="flex-1 bg-primary-100 text-primary-700 px-3 py-2 rounded-md hover:bg-primary-200 transition-colors flex items-center justify-center gap-1 text-sm"
                      >
                        <Edit className="w-4 h-4" />
                        ویرایش
                      </button>
                      <button
                        onClick={() => handleDeleteBrand(brand.id)}
                        className="bg-red-100 text-red-700 px-3 py-2 rounded-md hover:bg-red-200 transition-colors flex items-center justify-center text-sm"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>

              {filteredBrands.length === 0 && (
                <div className="text-center py-12">
                  <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    برندی یافت نشد
                  </h3>
                  <p className="text-gray-500">
                    {searchTerm ? 'برند مورد نظر یافت نشد' : 'هنوز برندی اضافه نشده است'}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Add Brand Modal */}
          <AdminFormModal
            isOpen={showAddModal}
            onClose={() => setShowAddModal(false)}
            onSubmit={handleAddSubmit(onAddSubmit)}
            title="افزودن برند جدید"
            subtitle="اطلاعات برند جدید را وارد کنید"
            loading={isSubmitting}
            size="lg"
          >
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <AdminFormField
                  label="نام برند"
                  required
                  error={addErrors.name?.message}
                >
                  <Controller
                    name="name"
                    control={addControl}
                    render={({ field }) => (
                      <AdminInput
                        {...field}
                        placeholder="نام برند را وارد کنید"
                        error={!!addErrors.name}
                      />
                    )}
                  />
                </AdminFormField>

                <AdminFormField
                  label="نام انگلیسی"
                  required
                  error={addErrors.nameEn?.message}
                >
                  <Controller
                    name="nameEn"
                    control={addControl}
                    render={({ field }) => (
                      <AdminInput
                        {...field}
                        placeholder="Brand Name"
                        error={!!addErrors.nameEn}
                      />
                    )}
                  />
                </AdminFormField>
              </div>

              <AdminFormField
                label="توضیحات"
                required
                error={addErrors.description?.message}
              >
                <Controller
                  name="description"
                  control={addControl}
                  render={({ field }) => (
                    <AdminTextarea
                      {...field}
                      placeholder="توضیحات برند را وارد کنید"
                      rows={3}
                      error={!!addErrors.description}
                    />
                  )}
                />
              </AdminFormField>

              <AdminFormField
                label="لوگو برند"
                help="فرمت‌های مجاز: JPG, PNG, WebP - حداکثر 5 مگابایت"
              >
                <AdminFileUpload
                  value={addLogoUrl}
                  onChange={(file, url) => {

                    setAddLogoFile(file);
                    setAddLogoUrl(url || '');
                  }}
                  accept="image/*"
                  maxSize={5}
                  placeholder="لوگو برند را انتخاب کنید"
                  aspectRatio="square"
                />
              </AdminFormField>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <AdminFormField
                  label="وب‌سایت"
                  error={addErrors.website?.message}
                >
                  <Controller
                    name="website"
                    control={addControl}
                    render={({ field }) => (
                      <AdminInput
                        {...field}
                        placeholder="https://example.com"
                        error={!!addErrors.website}
                      />
                    )}
                  />
                </AdminFormField>

                <AdminFormField
                  label="کشور"
                  required
                  error={addErrors.country?.message}
                >
                  <Controller
                    name="country"
                    control={addControl}
                    render={({ field }) => (
                      <AdminSelect
                        {...field}
                        error={!!addErrors.country}
                        placeholder="کشور را انتخاب کنید"
                      >
                        {countries.map(country => (
                          <option key={country} value={country}>
                            {country}
                          </option>
                        ))}
                      </AdminSelect>
                    )}
                  />
                </AdminFormField>
              </div>

              <AdminFormField label="وضعیت">
                <Controller
                  name="isActive"
                  control={addControl}
                  render={({ field }) => (
                    <AdminCheckbox
                      checked={field.value}
                      onChange={field.onChange}
                      label="برند فعال است"
                    />
                  )}
                />
              </AdminFormField>
            </div>
          </AdminFormModal>

          {/* Edit Brand Modal */}
          <AdminFormModal
            isOpen={showEditModal}
            onClose={() => setShowEditModal(false)}
            onSubmit={handleEditSubmit(onEditSubmit)}
            title="ویرایش برند"
            subtitle={`ویرایش اطلاعات برند ${selectedBrand?.name}`}
            loading={isSubmitting}
            size="lg"
          >
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <AdminFormField
                  label="نام برند"
                  required
                  error={editErrors.name?.message}
                >
                  <Controller
                    name="name"
                    control={editControl}
                    render={({ field }) => (
                      <AdminInput
                        {...field}
                        placeholder="نام برند را وارد کنید"
                        error={!!editErrors.name}
                      />
                    )}
                  />
                </AdminFormField>

                <AdminFormField
                  label="نام انگلیسی"
                  required
                  error={editErrors.nameEn?.message}
                >
                  <Controller
                    name="nameEn"
                    control={editControl}
                    render={({ field }) => (
                      <AdminInput
                        {...field}
                        placeholder="Brand Name"
                        error={!!editErrors.nameEn}
                      />
                    )}
                  />
                </AdminFormField>
              </div>

              <AdminFormField
                label="توضیحات"
                required
                error={editErrors.description?.message}
              >
                <Controller
                  name="description"
                  control={editControl}
                  render={({ field }) => (
                    <AdminTextarea
                      {...field}
                      placeholder="توضیحات برند را وارد کنید"
                      rows={3}
                      error={!!editErrors.description}
                    />
                  )}
                />
              </AdminFormField>

              <AdminFormField
                label="لوگو برند"
                help="فرمت‌های مجاز: JPG, PNG, WebP - حداکثر 5 مگابایت"
              >
                <AdminFileUpload
                  value={editLogoUrl}
                  onChange={(file, url) => {

                    setEditLogoFile(file);
                    setEditLogoUrl(url || '');
                  }}
                  accept="image/*"
                  maxSize={5}
                  placeholder="لوگو برند را انتخاب کنید"
                  aspectRatio="square"
                />
              </AdminFormField>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <AdminFormField
                  label="وب‌سایت"
                  error={editErrors.website?.message}
                >
                  <Controller
                    name="website"
                    control={editControl}
                    render={({ field }) => (
                      <AdminInput
                        {...field}
                        placeholder="https://example.com"
                        error={!!editErrors.website}
                      />
                    )}
                  />
                </AdminFormField>

                <AdminFormField
                  label="کشور"
                  required
                  error={editErrors.country?.message}
                >
                  <Controller
                    name="country"
                    control={editControl}
                    render={({ field }) => (
                      <AdminSelect
                        {...field}
                        error={!!editErrors.country}
                        placeholder="کشور را انتخاب کنید"
                      >
                        {countries.map(country => (
                          <option key={country} value={country}>
                            {country}
                          </option>
                        ))}
                      </AdminSelect>
                    )}
                  />
                </AdminFormField>
              </div>

              <AdminFormField label="وضعیت">
                <Controller
                  name="isActive"
                  control={editControl}
                  render={({ field }) => (
                    <AdminCheckbox
                      checked={field.value}
                      onChange={field.onChange}
                      label="برند فعال است"
                    />
                  )}
                />
              </AdminFormField>
            </div>
          </AdminFormModal>

          {/* View Brand Modal */}
          <AdminModal
            isOpen={showViewModal}
            onClose={() => setShowViewModal(false)}
            title="جزئیات برند"
            subtitle={selectedBrand?.name}
            icon={Building2}
            size="lg"
          >
            {selectedBrand && (
              <div className="space-y-6">
                {/* Brand Logo */}
                {selectedBrand.logo && (
                  <div className="flex justify-center">
                    <AdminImagePreview
                      src={selectedBrand.logo}
                      alt={`لوگو ${selectedBrand.name}`}
                      size="xl"
                      shape="rounded"
                      showError
                    />
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">نام برند</h4>
                    <p className="text-gray-900">{selectedBrand.name}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">نام انگلیسی</h4>
                    <p className="text-gray-900">{selectedBrand.nameEn}</p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">توضیحات</h4>
                  <p className="text-gray-900">{selectedBrand.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">کشور</h4>
                    <p className="text-gray-900 flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-gray-500" />
                      {selectedBrand.country}
                    </p>
                  </div>
                  {selectedBrand.website && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">وب‌سایت</h4>
                      <a
                        href={selectedBrand.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary-600 hover:text-primary-700 flex items-center gap-2"
                      >
                        <Globe className="w-4 h-4" />
                        {selectedBrand.website}
                      </a>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">وضعیت</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      selectedBrand.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {selectedBrand.isActive ? 'فعال' : 'غیرفعال'}
                    </span>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">تعداد محصولات</h4>
                    <p className="text-gray-900 flex items-center gap-2">
                      <Package className="w-4 h-4 text-gray-500" />
                      {selectedBrand.productsCount} محصول
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">تاریخ ایجاد</h4>
                    <p className="text-gray-900 flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-500" />
                      {new Date(selectedBrand.createdAt).toLocaleDateString('fa-IR')}
                    </p>
                  </div>
                </div>

                {selectedBrand.productsCount > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">محصولات این برند</h4>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-sm text-gray-600">
                        این برند دارای {selectedBrand.productsCount} محصول است.
                        برای مشاهده لیست کامل محصولات، به بخش مدیریت محصولات مراجعه کنید.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </AdminModal>

          {/* Delete Brand Modal */}
          <AdminConfirmModal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            onConfirm={confirmDeleteBrand}
            title="حذف برند"
            message={`آیا از حذف برند "${selectedBrand?.name}" اطمینان دارید؟ این عمل قابل بازگشت نیست.`}
            confirmText="حذف"
            cancelText="انصراف"
            variant="danger"
            loading={isSubmitting}
          />
        </AdminListLayout>
    </>
  );
};

export default ProductBrandsPage;
