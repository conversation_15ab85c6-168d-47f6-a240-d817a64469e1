import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Filter, X, ChevronDown, Search, Grid, List, SlidersHorizontal } from 'lucide-react';
import ProductCard from '../components/common/ProductCard';
import FilterSidebar from '../components/filters/FilterSidebar';
import MobileProductGrid from '../components/mobile/MobileProductGrid';
import MobileFilters from '../components/mobile/MobileFilters';
import { useMobileDetection } from '../hooks/useMobileDetection';
import { products } from '../data/products';
import { categories } from '../data/categories';
import { Product, ProductFilters } from '../types';
import { searchProducts, highlightSearchTerms } from '../utils/persianSearch';
import { useProductFilters } from '../hooks/useProductFilters';
import SEOHead from '../components/seo/SEOHead';
import { generatePageTitle } from '../utils/seoUtils';

const ProductsPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(location.search);
  const { isMobile, isTablet } = useMobileDetection();

  const [searchQuery, setSearchQuery] = useState<string>(
    searchParams.get('search') || ''
  );
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Use the advanced filter hook
  const {
    filters,
    filteredProducts,
    filterConfig,
    activeFilterCount,
    hasActiveFilters,
    filterSummary,
    updateCategories,
    toggleCategory,
    updateBrands,
    toggleBrand,
    updatePriceRange,
    updateStockStatus,
    updateProductTypes,
    updateRating,
    updateSortBy,
    clearFilters,
    clearFilter
  } = useProductFilters({
    products,
    searchQuery,
    updateUrl: true
  });
  
  useEffect(() => {
    window.scrollTo(0, 0);

    // Update search query from URL
    const urlSearchQuery = searchParams.get('search') || '';
    if (urlSearchQuery !== searchQuery) {
      setSearchQuery(urlSearchQuery);
    }
  }, [location.search]);
  
  const handleClearSearch = () => {
    setSearchQuery('');
    const params = new URLSearchParams(location.search);
    params.delete('search');
    navigate({
      pathname: location.pathname,
      search: params.toString()
    }, { replace: true });
  };

  const handleMobileFiltersChange = (newFilters: ProductFilters) => {
    // Update filters using the hook's methods
    updateCategories(newFilters.category ? [newFilters.category] : []);
    updateBrands(newFilters.brand ? [newFilters.brand] : []);
    updatePriceRange(newFilters.priceRange);
    updateRating(newFilters.rating);
    updateStockStatus(newFilters.inStock);
    // Handle other filter updates as needed
  };

  const handleMobileFilterApply = () => {
    setIsMobileFilterOpen(false);
  };

  const handleMobileClearFilters = () => {
    clearFilters();
  };
  
  return (
    <div className="bg-background py-20">
      <SEOHead
        title={searchQuery ? `نتایج جستجو برای "${searchQuery}"` : 'فروشگاه محصولات'}
        description={searchQuery
          ? `نتایج جستجو برای "${searchQuery}" در فروشگاه آرامش پوست. ${filteredProducts.length} محصول یافت شد.`
          : 'فروشگاه آنلاین محصولات مراقبت از پوست و زیبایی. انواع سرم، کرم، ماسک و محصولات طبیعی برای پوست سالم.'
        }
        keywords={searchQuery
          ? [searchQuery, 'جستجو محصولات', 'فروشگاه آنلاین', 'مراقبت از پوست']
          : ['فروشگاه محصولات', 'مراقبت از پوست', 'کرم صورت', 'سرم پوست', 'ماسک صورت', 'آرایشی بهداشتی']
        }
        type="website"
      />

      <div className="container-custom py-10">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">
            {searchQuery ? `نتایج جستجو برای "${searchQuery}"` : 'فروشگاه محصولات'}
          </h1>
          <p className="text-text-secondary">
            {filteredProducts.length} محصول
            {searchQuery && ` برای "${searchQuery}"`}
            {hasActiveFilters && ` با فیلترهای اعمال شده`}
          </p>

          {/* Search Query Display */}
          {searchQuery && (
            <div className="mt-4 flex items-center gap-2 text-sm">
              <Search className="w-4 h-4 text-primary-500" />
              <span className="text-text-secondary">جستجو:</span>
              <span className="bg-primary-100 text-primary-700 px-3 py-1 rounded-full font-medium">
                {searchQuery}
              </span>
              <button
                onClick={handleClearSearch}
                className="text-text-muted hover:text-red-500 transition-colors"
                title="حذف جستجو"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div className="mt-4 flex flex-wrap items-center gap-2">
              <span className="text-sm text-text-secondary">فیلترهای فعال:</span>
              {filterSummary.slice(0, 3).map((summary, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs"
                >
                  {summary}
                </span>
              ))}
              {filterSummary.length > 3 && (
                <span className="text-xs text-text-muted">
                  و {filterSummary.length - 3} فیلتر دیگر
                </span>
              )}
              <button
                onClick={clearFilters}
                className="text-xs text-red-500 hover:text-red-600 transition-colors"
              >
                پاک کردن همه
              </button>
            </div>
          )}
        </div>
        
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Mobile Filter Toggle */}
          <div className="block lg:hidden mb-4">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center justify-center w-full py-3 px-4 bg-white rounded-lg shadow-sm border border-gray-200"
            >
              <SlidersHorizontal className="ml-2 h-5 w-5 text-primary-500" />
              <span>فیلترها</span>
              {activeFilterCount > 0 && (
                <span className="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full mr-2">
                  {activeFilterCount}
                </span>
              )}
              <ChevronDown className={`mr-2 h-5 w-5 text-gray-500 transition-transform ${isFilterOpen ? 'rotate-180' : ''}`} />
            </button>
          </div>
          
          {/* Desktop Filter Sidebar */}
          <div className="hidden lg:block">
            <FilterSidebar
              filters={filters}
              filterConfig={filterConfig}
              onUpdateCategories={updateCategories}
              onToggleCategory={toggleCategory}
              onUpdateBrands={updateBrands}
              onToggleBrand={toggleBrand}
              onUpdatePriceRange={updatePriceRange}
              onUpdateStockStatus={updateStockStatus}
              onUpdateProductTypes={updateProductTypes}
              onUpdateRating={updateRating}
              onClearFilters={clearFilters}
              onClearFilter={clearFilter}
              activeFilterCount={activeFilterCount}
              filterSummary={filterSummary}
            />
          </div>

          {/* Mobile Filter Sidebar */}
          <FilterSidebar
            filters={filters}
            filterConfig={filterConfig}
            onUpdateCategories={updateCategories}
            onToggleCategory={toggleCategory}
            onUpdateBrands={updateBrands}
            onToggleBrand={toggleBrand}
            onUpdatePriceRange={updatePriceRange}
            onUpdateStockStatus={updateStockStatus}
            onUpdateProductTypes={updateProductTypes}
            onUpdateRating={updateRating}
            onClearFilters={clearFilters}
            onClearFilter={clearFilter}
            activeFilterCount={activeFilterCount}
            filterSummary={filterSummary}
            isOpen={isFilterOpen}
            onClose={() => setIsFilterOpen(false)}
            isMobile={true}
          />
          
          {/* Products */}
          <div className="flex-grow">
            {/* Sort Options */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6 flex flex-wrap justify-between items-center">
              <span className="text-text-secondary text-sm mb-2 md:mb-0">
                نمایش {filteredProducts.length} محصول از {products.length} محصول
              </span>
              
              <div className="flex items-center">
                <label htmlFor="sort" className="text-sm ml-2">مرتب‌سازی:</label>
                <select
                  id="sort"
                  value={filters.sortBy}
                  onChange={(e) => updateSortBy(e.target.value)}
                  className="py-2 px-3 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  {filterConfig.sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            {/* Product Grid - Mobile vs Desktop */}
            {isMobile || isTablet ? (
              <MobileProductGrid
                products={filteredProducts}
                loading={false}
                showFilterButton={true}
                showSortButton={true}
                onFilterClick={() => setIsMobileFilterOpen(true)}
                onSortClick={() => {
                  // Handle sort click - could open a sort modal
                  console.log('Sort clicked');
                }}
                gridConfig={{
                  mobile: 1,
                  tablet: 2,
                  desktop: 3,
                  wide: 4,
                  gap: '1rem',
                  minItemWidth: 280
                }}
              />
            ) : filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProducts.map((product) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ProductCard product={product} />
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                <div className="mb-4 text-gray-400">
                  <X className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium mb-2">محصولی یافت نشد!</h3>
                <p className="text-text-secondary mb-4">با معیارهای فیلتر انتخاب شده محصولی یافت نشد. لطفاً فیلترها را تغییر دهید.</p>
                <button
                  onClick={clearFilters}
                  className="btn-primary"
                >
                  پاک کردن فیلترها
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Filters */}
        <MobileFilters
          isOpen={isMobileFilterOpen}
          onClose={() => setIsMobileFilterOpen(false)}
          filters={{
            category: filters.categories[0] || '',
            brand: filters.brands[0] || '',
            priceRange: filters.priceRange,
            rating: filters.rating,
            inStock: filters.stockStatus.includes('in-stock'),
            onSale: filters.stockStatus.includes('on-sale'),
            sortBy: filters.sortBy
          }}
          onFiltersChange={handleMobileFiltersChange}
          onApplyFilters={handleMobileFilterApply}
          onClearFilters={handleMobileClearFilters}
        />
      </div>
    </div>
  );
};

export default ProductsPage;