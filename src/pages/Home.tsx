import React, { useEffect, useState } from 'react';
import Hero from '../components/sections/Hero';
import FeaturedProducts from '../components/sections/FeaturedProducts';
import Categories from '../components/sections/Categories';
import Testimonials from '../components/sections/Testimonials';
import Features from '../components/sections/Features';
import SEOHead from '../components/seo/SEOHead';
import StructuredData from '../components/seo/StructuredData';
import { AutoNewsletterModal } from '../components/newsletter/NewsletterModal';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getNewProducts, getDiscountedProducts, getNewProductsSync, getDiscountedProductsSync, initializeProducts } from '../data/products';
import ProductCard from '../components/common/ProductCard';
import { Product } from '../types';

const Home: React.FC = () => {
  const [newProducts, setNewProducts] = useState<Product[]>(getNewProductsSync());
  const [discountedProducts, setDiscountedProducts] = useState<Product[]>(getDiscountedProductsSync());
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);

    // Initialize products and load fresh data
    const loadProducts = async () => {
      setIsLoading(true);
      try {
        await initializeProducts();
        const [newProds, discountedProds] = await Promise.all([
          getNewProducts(),
          getDiscountedProducts()
        ]);
        setNewProducts(newProds);
        setDiscountedProducts(discountedProds);
      } catch (error) {
        console.warn('Failed to load products:', error);
        // Keep using sync fallback data
      } finally {
        setIsLoading(false);
      }
    };

    loadProducts();
  }, []);

  return (
    <div>
      <SEOHead
        title="صفحه اصلی"
        description="فروشگاه آنلاین محصولات مراقبت از پوست و زیبایی آرامش پوست. بهترین برندهای ایرانی و خارجی، سرم، کرم، ماسک و محصولات طبیعی برای پوست سالم و درخشان."
        keywords={[
          'فروشگاه آنلاین زیبایی',
          'محصولات مراقبت از پوست',
          'کرم صورت',
          'سرم پوست',
          'ماسک صورت',
          'آرایشی بهداشتی',
          'گلو رویا',
          'پوست سالم',
          'زیبایی طبیعی'
        ]}
        type="website"
      />

      <StructuredData
        type="organization"
        data={{
          address: "تهران، ایران",
          phone: "+98-21-12345678"
        }}
      />

      <Hero />

      <FeaturedProducts />
      
      {/* New Products */}
      <section className="py-16 bg-background">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-8">
            <h2 className="section-title">محصولات جدید</h2>
            <Link to="/products?new=true" className="text-primary-500 hover:text-primary-600 flex items-center gap-1">
              <span>مشاهده همه</span>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {newProducts.map((product) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Categories />

      {/* Promotional Banner */}
      <section className="py-16 relative overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center z-0 opacity-20"
          style={{ backgroundImage: 'url(https://images.pexels.com/photos/3321416/pexels-photo-3321416.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2)' }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/80 to-secondary-500/80 z-0"></div>
        
        <div className="container-custom relative z-10">
          <div className="max-w-3xl mx-auto text-center text-white">
            <motion.h2 
              className="text-3xl sm:text-4xl font-bold mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              زیبایی طبیعی پوست شما با گلورویا
            </motion.h2>
            
            <motion.p 
              className="text-white/90 text-lg mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              با استفاده از محصولات مراقبت از پوست گلورویا، پوستی شاداب، درخشان و سالم داشته باشید.
              همین الان خرید کنید و از ۲۰٪ تخفیف ویژه استفاده کنید!
            </motion.p>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Link to="/products" className="btn-accent">
                خرید با ۲۰٪ تخفیف
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Discounted Products */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-8">
            <h2 className="section-title">تخفیف‌های ویژه</h2>
            <Link to="/products?discounted=true" className="text-primary-500 hover:text-primary-600 flex items-center gap-1">
              <span>مشاهده همه</span>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {discountedProducts.map((product) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Features />

      <Testimonials />

      {/* Auto Newsletter Modal */}
      <AutoNewsletterModal />
    </div>
  );
};

export default Home;