import React, { create<PERSON>ontext, useContext, useReducer, useEffect, ReactNode } from 'react';
import toast from 'react-hot-toast';
import {
  User,
  AuthState,
  AuthContextType,
  LoginCredentials,
  RegisterData,
  ResetPasswordData,
  ChangePasswordData,
  UpdateProfileData,
  UserAddress,
  PERSIAN_AUTH_MESSAGES
} from '../types/auth';
import {
  AuthStorage,
  TokenUtils,
  MockAuthAPI,
  AuthErrorHandler
} from '../utils/authUtils';

// Auth actions
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_LOADING'; payload: boolean };

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true, // Start with loading to check existing session
  error: null,
  token: null
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload
      };

    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      };

    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      };

    default:
      return state;
  }
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing session on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = AuthStorage.getToken();
        const user = AuthStorage.getUser();
        const rememberMe = AuthStorage.getRememberMe();

        // Check if we have valid session data
        if (token && user && TokenUtils.isTokenValid(token)) {
          // Check if session is expired based on remember me setting
          if (!AuthStorage.isSessionExpired()) {
            dispatch({
              type: 'AUTH_SUCCESS',
              payload: { user, token }
            });
          } else {
            // Session expired, clear all data
            AuthStorage.clearAll();
            dispatch({ type: 'SET_LOADING', payload: false });
          }
        } else {
          // Clear invalid session
          AuthStorage.clearAll();
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      } catch (error) {
        // console.error('Auth initialization error:', error);
        AuthStorage.clearAll();
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeAuth();
  }, []);

  // Auto-refresh token before expiry
  useEffect(() => {
    if (!state.token || !state.isAuthenticated) return;

    const checkTokenExpiry = () => {
      const expiry = TokenUtils.getTokenExpiry(state.token!);
      if (expiry) {
        const timeUntilExpiry = expiry - Date.now();
        const refreshThreshold = 5 * 60 * 1000; // 5 minutes

        if (timeUntilExpiry <= refreshThreshold) {
          refreshToken();
        }
      }
    };

    const interval = setInterval(checkTokenExpiry, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [state.token, state.isAuthenticated]);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await MockAuthAPI.login(credentials);

      const rememberMe = credentials.rememberMe || false;

      // Store auth data
      AuthStorage.setToken(response.token, rememberMe);
      AuthStorage.setRefreshToken(response.refreshToken);
      AuthStorage.setUser(response.user, rememberMe);
      AuthStorage.setRememberMe(rememberMe);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token
        }
      });

      toast.success(PERSIAN_AUTH_MESSAGES.success.loginSuccess);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  const register = async (data: RegisterData): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await MockAuthAPI.register(data);

      // Store auth data (don't remember for new registrations)
      AuthStorage.setToken(response.token, false);
      AuthStorage.setRefreshToken(response.refreshToken);
      AuthStorage.setUser(response.user, false);
      AuthStorage.setRememberMe(false);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token
        }
      });

      toast.success(PERSIAN_AUTH_MESSAGES.success.registerSuccess);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      // Clear storage
      AuthStorage.clearAll();
      
      dispatch({ type: 'LOGOUT' });
      toast.success(PERSIAN_AUTH_MESSAGES.success.logoutSuccess);
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      AuthStorage.clearAll();
      dispatch({ type: 'LOGOUT' });
    }
  };

  const resetPassword = async (data: ResetPasswordData): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.passwordResetSent);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const changePassword = async (data: ChangePasswordData): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.passwordChanged);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const updateProfile = async (data: UpdateProfileData): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      if (!state.user) throw new Error('User not authenticated');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedUser: User = {
        ...state.user,
        firstName: data.firstName,
        lastName: data.lastName,
        phone: data.phone,
        preferences: data.preferences,
        updatedAt: new Date().toISOString()
      };

      AuthStorage.setUser(updatedUser);
      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.profileUpdated);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const verifyEmail = async (token: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (state.user) {
        const updatedUser = { ...state.user, isEmailVerified: true };
        AuthStorage.setUser(updatedUser);
        dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      }
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.emailVerified);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const verifyPhone = async (code: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (state.user) {
        const updatedUser = { ...state.user, isPhoneVerified: true };
        AuthStorage.setUser(updatedUser);
        dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      }
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.phoneVerified);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const resendVerification = async (type: 'email' | 'phone'): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.verificationSent);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const refreshToken = async (): Promise<void> => {
    try {
      const refreshToken = AuthStorage.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      // Simulate API call to refresh token
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real app, you would call your API here
      // For now, we'll just extend the current session
      if (state.user && state.token) {
        const newToken = state.token; // In real app, get new token from API
        AuthStorage.setToken(newToken, AuthStorage.getRememberMe());
        
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: state.user,
            token: newToken
          }
        });
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      // Force logout on refresh failure
      await logout();
    }
  };

  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Address management functions
  const addAddress = async (address: Omit<UserAddress, 'id' | 'createdAt'>): Promise<void> => {
    try {
      if (!state.user) throw new Error('User not authenticated');

      const newAddress: UserAddress = {
        ...address,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };

      const updatedUser = {
        ...state.user,
        addresses: [...state.user.addresses, newAddress]
      };

      AuthStorage.setUser(updatedUser);
      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.addressAdded);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    }
  };

  const updateAddress = async (id: string, addressData: Partial<UserAddress>): Promise<void> => {
    try {
      if (!state.user) throw new Error('User not authenticated');

      const updatedAddresses = state.user.addresses.map(addr =>
        addr.id === id ? { ...addr, ...addressData } : addr
      );

      const updatedUser = {
        ...state.user,
        addresses: updatedAddresses
      };

      AuthStorage.setUser(updatedUser);
      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.addressUpdated);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    }
  };

  const deleteAddress = async (id: string): Promise<void> => {
    try {
      if (!state.user) throw new Error('User not authenticated');

      const updatedAddresses = state.user.addresses.filter(addr => addr.id !== id);

      const updatedUser = {
        ...state.user,
        addresses: updatedAddresses
      };

      AuthStorage.setUser(updatedUser);
      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      
      toast.success(PERSIAN_AUTH_MESSAGES.success.addressDeleted);
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    }
  };

  const setDefaultAddress = async (id: string): Promise<void> => {
    try {
      if (!state.user) throw new Error('User not authenticated');

      const updatedAddresses = state.user.addresses.map(addr => ({
        ...addr,
        isDefault: addr.id === id
      }));

      const updatedUser = {
        ...state.user,
        addresses: updatedAddresses
      };

      AuthStorage.setUser(updatedUser);
      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
    } catch (error) {
      const errorMessage = AuthErrorHandler.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    }
  };

  const value: AuthContextType = {
    // State
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    
    // Actions
    login,
    register,
    logout,
    resetPassword,
    changePassword,
    updateProfile,
    verifyEmail,
    verifyPhone,
    resendVerification,
    refreshToken,
    clearError,
    
    // Address management
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
