/**
 * Admin Authentication API Service
 * Handles admin login, logout, and session management with backend API
 */

import { ApiService } from './apiService';
import { AdminUser, AdminRole, ADMIN_ROLE_PERMISSIONS, AdminPermission } from '../types/admin';

// Transform backend user to frontend admin user
const transformBackendUser = (backendUser: any): AdminUser => {
  // Map backend roles to frontend roles
  const roleMapping: Record<string, AdminRole> = {
    'SUPER_ADMIN': 'super_admin',
    'ADMIN': 'admin',
    'MODERATOR': 'moderator',
    'VIEWER': 'viewer'
  };

  const frontendRole = roleMapping[backendUser.role] || 'viewer';

  // Get permissions based on role
  const rolePermissions = ADMIN_ROLE_PERMISSIONS[frontendRole] || [];

  return {
    id: backendUser.id,
    email: backendUser.email,
    firstName: backendUser.firstName,
    lastName: backendUser.lastName,
    role: frontendRole,
    permissions: rolePermissions,
    avatar: backendUser.avatar,
    isActive: backendUser.status === 'ACTIVE',
    lastLogin: backendUser.lastLoginAt,
    createdAt: backendUser.createdAt,
    updatedAt: backendUser.updatedAt,
    twoFactorEnabled: backendUser.twoFactorEnabled || false,
    department: backendUser.department,
    phone: backendUser.phone
  };
};

// Transform backend user to admin profile
const transformBackendUserToProfile = (backendUser: any): AdminProfile => {
  // Map backend roles to frontend roles
  const roleMapping: Record<string, AdminRole> = {
    'SUPER_ADMIN': 'super_admin',
    'ADMIN': 'admin',
    'MODERATOR': 'moderator',
    'VIEWER': 'viewer'
  };

  const frontendRole = roleMapping[backendUser.role] || 'viewer';

  // Get permissions based on role (as string array for AdminProfile)
  const rolePermissions = ADMIN_ROLE_PERMISSIONS[frontendRole] || [];
  const permissionStrings = rolePermissions.flatMap((p: AdminPermission) =>
    p.actions.map((action: string) => `${p.resource}:${action}`)
  );

  return {
    id: backendUser.id,
    email: backendUser.email,
    firstName: backendUser.firstName,
    lastName: backendUser.lastName,
    role: frontendRole,
    permissions: permissionStrings,
    isActive: backendUser.status === 'ACTIVE',
    department: backendUser.department,
    avatar: backendUser.avatar,
    lastLoginAt: backendUser.lastLoginAt,
    createdAt: backendUser.createdAt,
    updatedAt: backendUser.updatedAt
  };
};

export interface LoginRequest {
  email: string;
  password: string;
  twoFactorCode?: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: AdminUser;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenResponse {
  token: string;
  expiresIn: number;
}

export interface AdminProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'super_admin' | 'admin' | 'moderator' | 'viewer';
  permissions: string[];
  isActive: boolean;
  department?: string;
  avatar?: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Admin Authentication Service
 */
export class AdminAuthService {
  /**
   * Admin login with email and password
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await ApiService.Http.post<{
        user: any;
        token: string;
        refreshToken: string;
        expiresIn: string;
      }>(
        '/auth/login',
        {
          email: credentials.email,
          password: credentials.password,
          twoFactorCode: credentials.twoFactorCode,
          rememberMe: credentials.rememberMe || false,
        },
        false // No auth required for login
      );

      if (response.success && response.data) {
        // Store tokens
        ApiService.TokenManager.setToken(
          response.data.token,
          credentials.rememberMe || false
        );
        ApiService.TokenManager.setRefreshToken(
          response.data.refreshToken,
          credentials.rememberMe || false
        );

        // Transform and return the data in the expected format
        return {
          user: transformBackendUser(response.data.user),
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          expiresIn: 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
        };
      }

      throw new Error('INVALID_CREDENTIALS');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Admin logout
   */
  static async logout(): Promise<void> {
    try {
      await ApiService.Http.post('/auth/logout', {});
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local tokens
      ApiService.TokenManager.clearTokens();
    }
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      const refreshToken = ApiService.TokenManager.getRefreshToken();
      if (!refreshToken) {
        throw new Error('NO_REFRESH_TOKEN');
      }

      const response = await ApiService.Http.post<{
        token: string;
        expiresIn: string;
      }>(
        '/auth/refresh',
        { refreshToken },
        false
      );

      if (response.success && response.data) {
        // Update stored token
        ApiService.TokenManager.setToken(response.data.token);
        return {
          token: response.data.token,
          expiresIn: 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
        };
      }

      throw new Error('REFRESH_FAILED');
    } catch (error) {
      // Clear tokens on refresh failure
      ApiService.TokenManager.clearTokens();
      throw error;
    }
  }

  /**
   * Get current admin profile
   */
  static async getProfile(): Promise<AdminProfile> {
    try {
      const response = await ApiService.Http.get<{ user: any }>('/auth/me');

      if (response.success && response.data && response.data.user) {
        return transformBackendUserToProfile(response.data.user);
      }

      throw new Error('PROFILE_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update admin profile
   */
  static async updateProfile(profileData: Partial<AdminProfile>): Promise<AdminProfile> {
    try {
      const response = await ApiService.Http.put<AdminProfile>(
        '/auth/admin/profile',
        profileData
      );

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('PROFILE_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Change admin password
   */
  static async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<void> {
    try {
      const response = await ApiService.Http.put(
        '/auth/admin/change-password',
        passwordData
      );

      if (!response.success) {
        throw new Error('PASSWORD_CHANGE_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Enable/disable two-factor authentication
   */
  static async toggleTwoFactor(enable: boolean, code?: string): Promise<{
    enabled: boolean;
    qrCode?: string;
    backupCodes?: string[];
  }> {
    try {
      const response = await ApiService.Http.post(
        '/auth/admin/two-factor',
        { enable, code }
      );

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('TWO_FACTOR_TOGGLE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get admin session information
   */
  static async getSessionInfo(): Promise<{
    isValid: boolean;
    expiresAt: string;
    lastActivity: string;
    ipAddress: string;
    userAgent: string;
  }> {
    try {
      const response = await ApiService.Http.get('/auth/admin/session');

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('SESSION_INFO_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Logout from all devices
   */
  static async logoutAllDevices(): Promise<void> {
    try {
      await ApiService.Http.post('/auth/logout-all');
    } catch (error) {
      throw error;
    } finally {
      // Clear local tokens
      ApiService.TokenManager.clearTokens();
    }
  }

  /**
   * Check if current token is valid
   */
  static async validateToken(): Promise<boolean> {
    try {
      const token = ApiService.TokenManager.getToken();
      if (!token) {
        return false;
      }

      // Check if token is expired locally first
      if (ApiService.TokenManager.isTokenExpired(token)) {
        return false;
      }

      // Validate with server by getting current user
      const response = await ApiService.Http.get<{ user: any }>('/auth/me');
      return response.success && response.data && response.data.user;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get admin permissions
   */
  static async getPermissions(): Promise<string[]> {
    try {
      // Get current user which includes role information
      const response = await ApiService.Http.get<{ user: { role: string } }>(
        '/auth/me'
      );

      if (response.success && response.data && response.data.user) {
        // Return permissions based on role
        const role = response.data.user.role;
        if (role === 'ADMIN' || role === 'SUPER_ADMIN') {
          return ['read', 'write', 'delete', 'admin'];
        }
      }

      return [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Check if admin has specific permission
   */
  static async hasPermission(permission: string): Promise<boolean> {
    try {
      const permissions = await this.getPermissions();
      return permissions.includes(permission) || permissions.includes('*');
    } catch (error) {
      return false;
    }
  }

  /**
   * Get admin activity log
   */
  static async getActivityLog(params?: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    activities: Array<{
      id: string;
      action: string;
      resource: string;
      details: any;
      ipAddress: string;
      userAgent: string;
      createdAt: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const response = await ApiService.Http.get('/auth/admin/activity', params);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('ACTIVITY_LOG_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<void> {
    try {
      const response = await ApiService.Http.post(
        '/auth/admin/forgot-password',
        { email },
        false
      );

      if (!response.success) {
        throw new Error('PASSWORD_RESET_REQUEST_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Reset password with token
   */
  static async resetPassword(data: {
    token: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<void> {
    try {
      const response = await ApiService.Http.post(
        '/auth/admin/reset-password',
        data,
        false
      );

      if (!response.success) {
        throw new Error('PASSWORD_RESET_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }
}

export default AdminAuthService;
