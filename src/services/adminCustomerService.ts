/**
 * Admin Customer Service
 * Handles all customer-related API operations for admin panel
 */

import { ApiService } from './apiService';
import { AdminCustomer, CustomerFilters, CustomerSortOptions } from '../types/adminCustomer';

// Backend user interface (matches Prisma schema)
interface BackendUser {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatar?: string;
  role: string;
  status: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  addresses?: {
    id: string;
    title: string;
    firstName: string;
    lastName: string;
    phone: string;
    province: string;
    city: string;
    district?: string;
    street: string;
    postalCode: string;
    isDefault: boolean;
  }[];
  orders?: {
    id: string;
    orderNumber: string;
    status: string;
    totalAmount: number;
    createdAt: string;
  }[];
  loyaltyMember?: {
    id: string;
    points: number;
    tier: string;
    totalSpent: number;
    totalOrders: number;
  };
}

// Transform backend user to admin customer format
const transformBackendUser = (backendUser: BackendUser): AdminCustomer => {
  return {
    id: backendUser.id,
    email: backendUser.email,
    phone: backendUser.phone || '',
    firstName: backendUser.firstName || '',
    lastName: backendUser.lastName || '',
    avatar: backendUser.avatar,
    isEmailVerified: backendUser.isEmailVerified,
    isPhoneVerified: backendUser.isPhoneVerified,
    createdAt: backendUser.createdAt,
    updatedAt: backendUser.updatedAt,
    preferences: {
      language: 'fa',
      newsletter: true,
      smsNotifications: true,
      emailNotifications: true,
      theme: 'light'
    },
    addresses: backendUser.addresses?.map(addr => ({
      id: addr.id,
      title: addr.title,
      firstName: addr.firstName,
      lastName: addr.lastName,
      phone: addr.phone,
      province: addr.province,
      city: addr.city,
      district: addr.district,
      address: addr.street,
      postalCode: addr.postalCode,
      isDefault: addr.isDefault
    })) || [],
    orders: {
      total: backendUser.loyaltyMember?.totalOrders || backendUser.orders?.length || 0,
      completed: backendUser.orders?.filter(o => o.status === 'delivered').length || 0,
      cancelled: backendUser.orders?.filter(o => o.status === 'cancelled').length || 0,
      pending: backendUser.orders?.filter(o => ['pending', 'confirmed', 'processing'].includes(o.status)).length || 0,
      totalValue: backendUser.loyaltyMember?.totalSpent || 0,
      averageValue: backendUser.loyaltyMember?.totalOrders ?
        (backendUser.loyaltyMember.totalSpent / backendUser.loyaltyMember.totalOrders) : 0,
      lastOrderDate: backendUser.orders?.[0]?.createdAt,
      recentOrders: backendUser.orders?.slice(0, 5).map(order => ({
        id: order.id,
        orderNumber: order.orderNumber,
        status: order.status,
        total: order.totalAmount,
        date: order.createdAt
      })) || []
    },
    loyalty: {
      tier: backendUser.loyaltyMember?.tier || 'bronze',
      points: backendUser.loyaltyMember?.points || 0,
      totalSpent: backendUser.loyaltyMember?.totalSpent || 0,
      joinDate: backendUser.createdAt,
      nextTierPoints: calculateNextTierPoints(backendUser.loyaltyMember?.tier || 'bronze'),
      benefits: getTierBenefits(backendUser.loyaltyMember?.tier || 'bronze')
    },
    // Required AdminCustomer fields
    analytics: {
      totalOrders: backendUser.loyaltyMember?.totalOrders || backendUser.orders?.length || 0,
      totalSpent: backendUser.loyaltyMember?.totalSpent || 0,
      averageOrderValue: backendUser.loyaltyMember?.totalOrders ?
        (backendUser.loyaltyMember.totalSpent / backendUser.loyaltyMember.totalOrders) : 0,
      lastOrderDate: backendUser.orders?.[0]?.createdAt,
      firstOrderDate: backendUser.orders?.[backendUser.orders.length - 1]?.createdAt,
      lifetimeValue: backendUser.loyaltyMember?.totalSpent || 0,
      orderFrequency: calculateOrderFrequency(backendUser.orders || []),
      returnRate: 0,
      cancelationRate: 0,
      favoriteCategories: [],
      preferredPaymentMethod: undefined,
      averageDeliveryTime: 3,
      customerSatisfactionScore: undefined
    },
    status: (backendUser.status.toLowerCase() as any) || 'active',
    segment: 'regular',
    tags: [],
    notes: [],
    isVip: false,
    vipSince: undefined,
    loyaltyMember: backendUser.loyaltyMember,
    communicationHistory: [],
    marketingConsent: {
      email: true,
      sms: true,
      push: true,
      phone: false,
      updatedAt: backendUser.updatedAt
    },
    supportTickets: [],
    createdBy: 'system',
    lastModifiedBy: 'system',
    lastModifiedAt: backendUser.updatedAt,
    riskScore: 0,
    fraudFlags: [],
    location: {
      country: 'ایران',
      province: backendUser.addresses?.[0]?.province || 'تهران',
      city: backendUser.addresses?.[0]?.city || 'تهران',
      timezone: 'Asia/Tehran'
    },
    behavior: {
      loginFrequency: 5,
      sessionDuration: 30,
      pageViews: 50,
      cartAbandonmentRate: 20,
      wishlistItems: 0,
      reviewsCount: 0,
      referralsCount: 0
    }
  };
};

// Helper functions
const calculateNextTierPoints = (currentTier: string): number => {
  const tierPoints: Record<string, number> = {
    'bronze': 1000,
    'silver': 5000,
    'gold': 10000,
    'platinum': 0
  };
  return tierPoints[currentTier] || 1000;
};

const getTierBenefits = (tier: string): string[] => {
  const benefits: Record<string, string[]> = {
    'bronze': ['تخفیف ۵٪ در خریدهای بعدی'],
    'silver': ['تخفیف ۱۰٪ در خریدهای بعدی', 'ارسال رایگان'],
    'gold': ['تخفیف ۱۵٪ در خریدهای بعدی', 'ارسال رایگان', 'پشتیبانی اولویت‌دار'],
    'platinum': ['تخفیف ۲۰٪ در خریدهای بعدی', 'ارسال رایگان', 'پشتیبانی اختصاصی', 'دسترسی زودهنگام']
  };
  return benefits[tier] || [];
};

const calculateOrderFrequency = (orders: any[]): number => {
  if (orders.length < 2) return 0;
  
  const sortedOrders = orders.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  const firstOrder = new Date(sortedOrders[0].createdAt);
  const lastOrder = new Date(sortedOrders[sortedOrders.length - 1].createdAt);
  const daysDiff = (lastOrder.getTime() - firstOrder.getTime()) / (1000 * 60 * 60 * 24);
  
  return daysDiff > 0 ? orders.length / daysDiff * 30 : 0; // Orders per month
};

export interface CustomersResponse {
  customers: AdminCustomer[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class AdminCustomerService {
  /**
   * Get customers with filtering and pagination
   */
  static async getCustomers(params: {
    page?: number;
    limit?: number;
    filters?: CustomerFilters;
    sort?: CustomerSortOptions;
  }): Promise<CustomersResponse> {
    try {
      const queryParams: Record<string, string | number> = {
        page: params.page || 1,
        limit: params.limit || 20,
      };

      // Add filters
      if (params.filters) {
        if (params.filters.search) queryParams.search = params.filters.search;
        if (params.filters.status) queryParams.status = params.filters.status;
        if (params.filters.tier) queryParams.tier = params.filters.tier;
        if (params.filters.dateFrom) queryParams.dateFrom = params.filters.dateFrom;
        if (params.filters.dateTo) queryParams.dateTo = params.filters.dateTo;
      }

      // Add sorting
      if (params.sort) {
        queryParams.sortBy = params.sort.field;
        queryParams.sortOrder = params.sort.direction;
      }

      const response = await ApiService.Http.get<{customers: BackendUser[], pagination: any}>('/customers', queryParams);

      if (response.success && response.data) {
        // Backend returns { customers: BackendUser[], pagination: {...} }
        const customers = response.data.customers.map(transformBackendUser);
        const pagination = response.data.pagination || {
          page: 1,
          limit: customers.length,
          total: customers.length,
          totalPages: 1
        };

        return { customers, pagination };
      }

      throw new Error('CUSTOMERS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get customer by ID
   */
  static async getCustomerById(id: string): Promise<AdminCustomer> {
    try {
      const response = await ApiService.Http.get<BackendUser>(`/customers/${id}`);

      if (response.success && response.data) {
        return transformBackendUser(response.data);
      }

      throw new Error('CUSTOMER_NOT_FOUND');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update customer status
   */
  static async updateCustomerStatus(id: string, status: string): Promise<AdminCustomer> {
    try {
      const response = await ApiService.Http.patch<BackendUser>(`/customers/${id}`, { status });

      if (response.success && response.data) {
        return transformBackendUser(response.data);
      }

      throw new Error('CUSTOMER_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update customer information
   */
  static async updateCustomer(id: string, data: Partial<AdminCustomer>): Promise<AdminCustomer> {
    try {
      const updateData = {
        firstName: data.firstName,
        lastName: data.lastName,
        phone: data.phone,
        // Add other updatable fields as needed
      };

      const response = await ApiService.Http.patch<BackendUser>(`/customers/${id}`, updateData);

      if (response.success && response.data) {
        return transformBackendUser(response.data);
      }

      throw new Error('CUSTOMER_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get customer analytics
   */
  static async getCustomerAnalytics(): Promise<any> {
    try {
      const response = await ApiService.Http.get('/customers/statistics');

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('ANALYTICS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }
}
