// API Service for GlowRoya Backend Integration
// This service provides centralized API calls to the backend server

const API_BASE_URL = 'http://localhost:3001/api/v1';

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface Product {
  id: string;
  name: string;
  nameEn: string;
  slug: string;
  description: string;
  shortDescription: string;
  sku: string;
  price: string;
  comparePrice?: string;
  isActive: boolean;
  isFeatured: boolean;
  tags: string[];
  brand: {
    id: string;
    name: string;
    nameEn: string;
    slug: string;
    logo?: string;
  };
  categories: Array<{
    category: {
      id: string;
      name: string;
      nameEn: string;
      slug: string;
    };
  }>;
  images: Array<{
    id: string;
    url: string;
    alt: string;
    isPrimary: boolean;
    sortOrder: number;
  }>;
  inventory: {
    quantity: number;
    lowStockThreshold: number;
  };
}

interface Category {
  id: string;
  name: string;
  nameEn: string;
  slug: string;
  description: string;
  parentId?: string;
  isActive: boolean;
  sortOrder: number;
}

interface Brand {
  id: string;
  name: string;
  nameEn: string;
  slug: string;
  description: string;
  logo?: string;
  isActive: boolean;
}

class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Product API methods
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    brand?: string;
    search?: string;
    featured?: boolean;
  }): Promise<ApiResponse<Product[]>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/products${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request<Product[]>(endpoint);
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return this.request<Product>(`/products/${id}`);
  }

  async getProductBySlug(slug: string): Promise<ApiResponse<Product>> {
    return this.request<Product>(`/products/slug/${slug}`);
  }

  async getFeaturedProducts(): Promise<ApiResponse<Product[]>> {
    return this.request<Product[]>('/products?featured=true');
  }

  async getNewProducts(): Promise<ApiResponse<Product[]>> {
    return this.request<Product[]>('/products?isNew=true');
  }

  async getBestSellers(): Promise<ApiResponse<Product[]>> {
    return this.request<Product[]>('/products?isBestSeller=true');
  }

  async getDiscountedProducts(): Promise<ApiResponse<Product[]>> {
    return this.request<Product[]>('/products?hasDiscount=true');
  }

  // Category API methods
  async getCategories(): Promise<ApiResponse<Category[]>> {
    return this.request<Category[]>('/categories');
  }

  async getCategory(id: string): Promise<ApiResponse<Category>> {
    return this.request<Category>(`/categories/${id}`);
  }

  async getCategoryBySlug(slug: string): Promise<ApiResponse<Category>> {
    return this.request<Category>(`/categories/slug/${slug}`);
  }

  async getProductsByCategory(categorySlug: string): Promise<ApiResponse<Product[]>> {
    return this.request<Product[]>(`/products?category=${categorySlug}`);
  }

  // Brand API methods
  async getBrands(): Promise<ApiResponse<Brand[]>> {
    return this.request<Brand[]>('/brands');
  }

  async getBrand(id: string): Promise<ApiResponse<Brand>> {
    return this.request<Brand>(`/brands/${id}`);
  }

  async getBrandBySlug(slug: string): Promise<ApiResponse<Brand>> {
    return this.request<Brand>(`/brands/slug/${slug}`);
  }

  async getProductsByBrand(brandSlug: string): Promise<ApiResponse<Product[]>> {
    return this.request<Product[]>(`/products?brand=${brandSlug}`);
  }

  // Search API methods
  async searchProducts(query: string, filters?: {
    category?: string;
    brand?: string;
    minPrice?: number;
    maxPrice?: number;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<Product[]>> {
    const searchParams = new URLSearchParams({ search: query });
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    return this.request<Product[]>(`/products?${searchParams.toString()}`);
  }

  // Static file URL helpers
  getImageUrl(path: string): string {
    if (path.startsWith('http')) {
      return path; // External URL
    }
    return `${this.baseUrl.replace('/api/v1', '')}${path}`;
  }

  getBrandLogoUrl(brand: Brand): string {
    if (brand.logo) {
      return this.getImageUrl(brand.logo);
    }
    return '/logo.png'; // Fallback to app logo
  }

  getProductImageUrl(product: Product, index: number = 0): string {
    if (product.images && product.images.length > index) {
      return this.getImageUrl(product.images[index].url);
    }
    return '/logo.png'; // Fallback image
  }

  getPrimaryProductImage(product: Product): string {
    const primaryImage = product.images?.find(img => img.isPrimary);
    if (primaryImage) {
      return this.getImageUrl(primaryImage.url);
    }
    return this.getProductImageUrl(product, 0);
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await fetch(`${this.baseUrl.replace('/api/v1', '')}/health`);
    return response.json();
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();

// Export types for use in components
export type { Product, Category, Brand, ApiResponse };

// Export the class for custom instances if needed
export { ApiService };

// Example usage:
/*
// Get all products
const products = await apiService.getProducts();

// Get featured products
const featured = await apiService.getFeaturedProducts();

// Get products by category
const serums = await apiService.getProductsByCategory('serums');

// Search products
const searchResults = await apiService.searchProducts('ویتامین C');

// Get product image URL
const imageUrl = apiService.getPrimaryProductImage(product);

// Get brand logo URL
const logoUrl = apiService.getBrandLogoUrl(brand);
*/
