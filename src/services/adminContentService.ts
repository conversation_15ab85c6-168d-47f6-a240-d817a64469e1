/**
 * Admin Content Service
 * Handles all content management API operations for admin panel
 */

import { ApiService } from './apiService';
import { 
  Banner, 
  Promotion, 
  NewsletterCampaign, 
  PageContent, 
  MediaItem,
  BannerFormData,
  PromotionFormData,
  NewsletterFormData,
  PageContentFormData,
  ContentFilters,
  ContentAnalytics
} from '../types/adminContent';

// Backend content interfaces (matches backend API response)
interface BackendBanner {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  type: string;
  image: string;
  mobileImage?: string;
  altText: string;
  ctaText?: string;
  ctaUrl?: string;
  ctaType?: string;
  position: number;
  showOnPages: string[];
  backgroundColor?: string;
  textColor?: string;
  overlayOpacity?: number;
  animationType?: string;
  autoplay: boolean;
  duration?: number;
  status: string;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  views: number;
  clicks: number;
  conversions: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  publishedAt?: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface BackendPromotion {
  id: string;
  title: string;
  description?: string;
  type: string;
  discountValue: number;
  minimumOrderAmount?: number;
  maximumDiscountAmount?: number;
  applicableCategories?: string[];
  applicableProducts?: string[];
  usageLimit?: number;
  usagePerCustomer?: number;
  currentUsage: number;
  code?: string;
  isCodeRequired: boolean;
  showOnHomepage: boolean;
  showInCart: boolean;
  showOnProductPages: boolean;
  bannerImage?: string;
  terms?: string;
  conditions?: string[];
  status: string;
  isActive: boolean;
  startDate: string;
  endDate: string;
  views: number;
  clicks: number;
  conversions: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  publishedAt?: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface BackendNewsletter {
  id: string;
  title: string;
  subject: string;
  preheader?: string;
  content: string;
  htmlContent?: string;
  campaignType: string;
  template?: string;
  recipientSegments: string[];
  recipientCount: number;
  sendAt?: string;
  timezone: string;
  sentCount: number;
  deliveredCount: number;
  openCount: number;
  clickCount: number;
  unsubscribeCount: number;
  bounceCount: number;
  openRate: number;
  clickRate: number;
  unsubscribeRate: number;
  bounceRate: number;
  status: string;
  isActive: boolean;
  views: number;
  clicks: number;
  conversions: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  publishedAt?: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface BackendPage {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  template: string;
  parentPage?: string;
  menuOrder: number;
  showInMenu: boolean;
  metaTitle?: string;
  metaDescription?: string;
  canonicalUrl?: string;
  noIndex: boolean;
  noFollow: boolean;
  featuredImage?: string;
  featuredImageAlt?: string;
  customFields?: any;
  status: string;
  isActive: boolean;
  slug: string;
  views: number;
  clicks: number;
  conversions: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  publishedAt?: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  parent?: {
    id: string;
    title: string;
    slug: string;
  };
  children?: {
    id: string;
    title: string;
    slug: string;
  }[];
}

interface BackendMedia {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  altText?: string;
  caption?: string;
  description?: string;
  tags?: string[];
  folder?: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  uploadedBy: string;
  uploader: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

// Transform functions
const transformBackendBanner = (banner: BackendBanner): Banner => ({
  id: banner.id,
  title: banner.title,
  subtitle: banner.subtitle,
  description: banner.description,
  type: banner.type as any,
  image: banner.image,
  mobileImage: banner.mobileImage,
  altText: banner.altText,
  ctaText: banner.ctaText,
  ctaUrl: banner.ctaUrl,
  ctaType: banner.ctaType as any,
  position: banner.position,
  showOnPages: banner.showOnPages,
  backgroundColor: banner.backgroundColor,
  textColor: banner.textColor,
  overlayOpacity: banner.overlayOpacity,
  animationType: banner.animationType as any,
  autoplay: banner.autoplay,
  duration: banner.duration,
  status: banner.status as any,
  isActive: banner.isActive,
  startDate: banner.startDate,
  endDate: banner.endDate,
  views: banner.views,
  clicks: banner.clicks,
  conversions: banner.conversions,
  seoTitle: banner.seoTitle,
  seoDescription: banner.seoDescription,
  seoKeywords: banner.seoKeywords || [],
  createdAt: banner.createdAt,
  updatedAt: banner.updatedAt,
  createdBy: banner.createdBy,
  publishedAt: banner.publishedAt
});

const transformBackendPromotion = (promotion: BackendPromotion): Promotion => ({
  id: promotion.id,
  title: promotion.title,
  description: promotion.description,
  type: promotion.type as any,
  discountValue: promotion.discountValue,
  minimumOrderAmount: promotion.minimumOrderAmount,
  maximumDiscountAmount: promotion.maximumDiscountAmount,
  applicableCategories: promotion.applicableCategories || [],
  applicableProducts: promotion.applicableProducts || [],
  usageLimit: promotion.usageLimit,
  usagePerCustomer: promotion.usagePerCustomer,
  currentUsage: promotion.currentUsage,
  code: promotion.code,
  isCodeRequired: promotion.isCodeRequired,
  showOnHomepage: promotion.showOnHomepage,
  showInCart: promotion.showInCart,
  showOnProductPages: promotion.showOnProductPages,
  bannerImage: promotion.bannerImage,
  terms: promotion.terms,
  conditions: promotion.conditions || [],
  status: promotion.status as any,
  isActive: promotion.isActive,
  startDate: promotion.startDate,
  endDate: promotion.endDate,
  views: promotion.views,
  clicks: promotion.clicks,
  conversions: promotion.conversions,
  seoTitle: promotion.seoTitle,
  seoDescription: promotion.seoDescription,
  seoKeywords: promotion.seoKeywords || [],
  createdAt: promotion.createdAt,
  updatedAt: promotion.updatedAt,
  createdBy: promotion.createdBy,
  publishedAt: promotion.publishedAt
});

const transformBackendNewsletter = (newsletter: BackendNewsletter): NewsletterCampaign => ({
  id: newsletter.id,
  title: newsletter.title,
  subject: newsletter.subject,
  preheader: newsletter.preheader,
  content: newsletter.content,
  htmlContent: newsletter.htmlContent,
  campaignType: newsletter.campaignType as any,
  template: newsletter.template,
  recipientSegments: newsletter.recipientSegments,
  recipientCount: newsletter.recipientCount,
  sendAt: newsletter.sendAt,
  timezone: newsletter.timezone,
  sentCount: newsletter.sentCount,
  deliveredCount: newsletter.deliveredCount,
  openCount: newsletter.openCount,
  clickCount: newsletter.clickCount,
  unsubscribeCount: newsletter.unsubscribeCount,
  bounceCount: newsletter.bounceCount,
  openRate: newsletter.openRate,
  clickRate: newsletter.clickRate,
  unsubscribeRate: newsletter.unsubscribeRate,
  bounceRate: newsletter.bounceRate,
  status: newsletter.status as any,
  isActive: newsletter.isActive,
  views: newsletter.views,
  clicks: newsletter.clicks,
  conversions: newsletter.conversions,
  seoTitle: newsletter.seoTitle,
  seoDescription: newsletter.seoDescription,
  seoKeywords: newsletter.seoKeywords || [],
  createdAt: newsletter.createdAt,
  updatedAt: newsletter.updatedAt,
  createdBy: newsletter.createdBy,
  publishedAt: newsletter.publishedAt
});

const transformBackendPage = (page: BackendPage): PageContent => ({
  id: page.id,
  title: page.title,
  content: page.content,
  excerpt: page.excerpt,
  template: page.template as any,
  parentPage: page.parentPage,
  menuOrder: page.menuOrder,
  showInMenu: page.showInMenu,
  metaTitle: page.metaTitle,
  metaDescription: page.metaDescription,
  canonicalUrl: page.canonicalUrl,
  noIndex: page.noIndex,
  noFollow: page.noFollow,
  featuredImage: page.featuredImage,
  featuredImageAlt: page.featuredImageAlt,
  customFields: page.customFields,
  status: page.status as any,
  isActive: page.isActive,
  slug: page.slug,
  views: page.views,
  clicks: page.clicks,
  conversions: page.conversions,
  seoTitle: page.seoTitle,
  seoDescription: page.seoDescription,
  seoKeywords: page.seoKeywords || [],
  createdAt: page.createdAt,
  updatedAt: page.updatedAt,
  createdBy: page.createdBy,
  publishedAt: page.publishedAt
});

const transformBackendMedia = (media: BackendMedia): MediaItem => ({
  id: media.id,
  filename: media.filename,
  originalName: media.originalName,
  mimeType: media.mimeType,
  size: media.size,
  url: media.url,
  altText: media.altText,
  caption: media.caption,
  description: media.description,
  tags: media.tags || [],
  folder: media.folder,
  isPublic: media.isPublic,
  createdAt: media.createdAt,
  updatedAt: media.updatedAt,
  uploadedBy: media.uploadedBy
});

export class AdminContentService {
  /**
   * Banner Management
   */
  static async getBanners(filters: ContentFilters = {}, page = 1, limit = 20) {
    try {
      const params: any = { page, limit };
      
      if (filters.status) params.status = filters.status;
      if (filters.isActive !== undefined) params.isActive = filters.isActive;
      if (filters.type) params.type = filters.type;
      if (filters.search) params.search = filters.search;
      if (filters.startDate) params.startDate = filters.startDate;
      if (filters.endDate) params.endDate = filters.endDate;
      if (filters.createdBy) params.createdBy = filters.createdBy;

      const response = await ApiService.Http.get<BackendBanner[]>('/content/admin/banners', params);

      if (response.success && response.data) {
        const backendBanners = Array.isArray(response.data) ? response.data : [];
        const banners = backendBanners.map(transformBackendBanner);
        const pagination = response.pagination || {
          page: 1,
          limit: banners.length,
          total: banners.length,
          totalPages: 1
        };

        return { banners, pagination };
      }

      throw new Error('BANNERS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async getBannerById(id: string): Promise<Banner> {
    try {
      const response = await ApiService.Http.get<BackendBanner>(`/content/admin/banners/${id}`);

      if (response.success && response.data) {
        return transformBackendBanner(response.data);
      }

      throw new Error('BANNER_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async createBanner(bannerData: BannerFormData): Promise<Banner> {
    try {
      const response = await ApiService.Http.post<BackendBanner>('/content/admin/banners', bannerData);

      if (response.success && response.data) {
        return transformBackendBanner(response.data);
      }

      throw new Error('BANNER_CREATION_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async updateBanner(id: string, bannerData: Partial<BannerFormData>): Promise<Banner> {
    try {
      const response = await ApiService.Http.put<BackendBanner>(`/content/admin/banners/${id}`, bannerData);

      if (response.success && response.data) {
        return transformBackendBanner(response.data);
      }

      throw new Error('BANNER_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async deleteBanner(id: string): Promise<void> {
    try {
      const response = await ApiService.Http.delete(`/content/admin/banners/${id}`);

      if (!response.success) {
        throw new Error('BANNER_DELETE_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Promotion Management
   */
  static async getPromotions(filters: ContentFilters = {}, page = 1, limit = 20) {
    try {
      const params: any = { page, limit };

      if (filters.status) params.status = filters.status;
      if (filters.isActive !== undefined) params.isActive = filters.isActive;
      if (filters.type) params.type = filters.type;
      if (filters.search) params.search = filters.search;
      if (filters.startDate) params.startDate = filters.startDate;
      if (filters.endDate) params.endDate = filters.endDate;
      if (filters.createdBy) params.createdBy = filters.createdBy;

      const response = await ApiService.Http.get<BackendPromotion[]>('/content/admin/promotions', params);

      if (response.success && response.data) {
        const backendPromotions = Array.isArray(response.data) ? response.data : [];
        const promotions = backendPromotions.map(transformBackendPromotion);
        const pagination = response.pagination || {
          page: 1,
          limit: promotions.length,
          total: promotions.length,
          totalPages: 1
        };

        return { promotions, pagination };
      }

      throw new Error('PROMOTIONS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async getPromotionById(id: string): Promise<Promotion> {
    try {
      const response = await ApiService.Http.get<BackendPromotion>(`/content/admin/promotions/${id}`);

      if (response.success && response.data) {
        return transformBackendPromotion(response.data);
      }

      throw new Error('PROMOTION_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async createPromotion(promotionData: PromotionFormData): Promise<Promotion> {
    try {
      const response = await ApiService.Http.post<BackendPromotion>('/content/admin/promotions', promotionData);

      if (response.success && response.data) {
        return transformBackendPromotion(response.data);
      }

      throw new Error('PROMOTION_CREATION_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async updatePromotion(id: string, promotionData: Partial<PromotionFormData>): Promise<Promotion> {
    try {
      const response = await ApiService.Http.put<BackendPromotion>(`/content/admin/promotions/${id}`, promotionData);

      if (response.success && response.data) {
        return transformBackendPromotion(response.data);
      }

      throw new Error('PROMOTION_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async deletePromotion(id: string): Promise<void> {
    try {
      const response = await ApiService.Http.delete(`/content/admin/promotions/${id}`);

      if (!response.success) {
        throw new Error('PROMOTION_DELETE_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Newsletter Management
   */
  static async getNewsletters(filters: ContentFilters = {}, page = 1, limit = 20) {
    try {
      const params: any = { page, limit };

      if (filters.status) params.status = filters.status;
      if (filters.isActive !== undefined) params.isActive = filters.isActive;
      if (filters.type) params.type = filters.type;
      if (filters.search) params.search = filters.search;
      if (filters.startDate) params.startDate = filters.startDate;
      if (filters.endDate) params.endDate = filters.endDate;
      if (filters.createdBy) params.createdBy = filters.createdBy;

      const response = await ApiService.Http.get<BackendNewsletter[]>('/content/admin/newsletters', params);

      if (response.success && response.data) {
        const backendNewsletters = Array.isArray(response.data) ? response.data : [];
        const newsletters = backendNewsletters.map(transformBackendNewsletter);
        const pagination = response.pagination || {
          page: 1,
          limit: newsletters.length,
          total: newsletters.length,
          totalPages: 1
        };

        return { newsletters, pagination };
      }

      throw new Error('NEWSLETTERS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async getNewsletterById(id: string): Promise<NewsletterCampaign> {
    try {
      const response = await ApiService.Http.get<BackendNewsletter>(`/content/admin/newsletters/${id}`);

      if (response.success && response.data) {
        return transformBackendNewsletter(response.data);
      }

      throw new Error('NEWSLETTER_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async createNewsletter(newsletterData: NewsletterFormData): Promise<NewsletterCampaign> {
    try {
      const response = await ApiService.Http.post<BackendNewsletter>('/content/admin/newsletters', newsletterData);

      if (response.success && response.data) {
        return transformBackendNewsletter(response.data);
      }

      throw new Error('NEWSLETTER_CREATION_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async updateNewsletter(id: string, newsletterData: Partial<NewsletterFormData>): Promise<NewsletterCampaign> {
    try {
      const response = await ApiService.Http.put<BackendNewsletter>(`/content/admin/newsletters/${id}`, newsletterData);

      if (response.success && response.data) {
        return transformBackendNewsletter(response.data);
      }

      throw new Error('NEWSLETTER_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async deleteNewsletter(id: string): Promise<void> {
    try {
      const response = await ApiService.Http.delete(`/content/admin/newsletters/${id}`);

      if (!response.success) {
        throw new Error('NEWSLETTER_DELETE_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Page Content Management
   */
  static async getPages(filters: ContentFilters = {}, page = 1, limit = 20) {
    try {
      const params: any = { page, limit };

      if (filters.status) params.status = filters.status;
      if (filters.isActive !== undefined) params.isActive = filters.isActive;
      if (filters.search) params.search = filters.search;
      if (filters.createdBy) params.createdBy = filters.createdBy;

      const response = await ApiService.Http.get<BackendPage[]>('/content/admin/pages', params);

      if (response.success && response.data) {
        const backendPages = Array.isArray(response.data) ? response.data : [];
        const pages = backendPages.map(transformBackendPage);
        const pagination = response.pagination || {
          page: 1,
          limit: pages.length,
          total: pages.length,
          totalPages: 1
        };

        return { pages, pagination };
      }

      throw new Error('PAGES_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async getPageById(id: string): Promise<PageContent> {
    try {
      const response = await ApiService.Http.get<BackendPage>(`/content/admin/pages/${id}`);

      if (response.success && response.data) {
        return transformBackendPage(response.data);
      }

      throw new Error('PAGE_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async getPageBySlug(slug: string): Promise<PageContent> {
    try {
      const response = await ApiService.Http.get<BackendPage>(`/content/pages/slug/${slug}`);

      if (response.success && response.data) {
        return transformBackendPage(response.data);
      }

      throw new Error('PAGE_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async createPage(pageData: PageContentFormData): Promise<PageContent> {
    try {
      const response = await ApiService.Http.post<BackendPage>('/content/admin/pages', pageData);

      if (response.success && response.data) {
        return transformBackendPage(response.data);
      }

      throw new Error('PAGE_CREATION_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async updatePage(id: string, pageData: Partial<PageContentFormData>): Promise<PageContent> {
    try {
      const response = await ApiService.Http.put<BackendPage>(`/content/admin/pages/${id}`, pageData);

      if (response.success && response.data) {
        return transformBackendPage(response.data);
      }

      throw new Error('PAGE_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async deletePage(id: string): Promise<void> {
    try {
      const response = await ApiService.Http.delete(`/content/admin/pages/${id}`);

      if (!response.success) {
        throw new Error('PAGE_DELETE_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Media Management
   */
  static async getMediaItems(filters: ContentFilters = {}, page = 1, limit = 20) {
    try {
      const params: any = { page, limit };

      if (filters.search) params.search = filters.search;

      const response = await ApiService.Http.get<BackendMedia[]>('/content/admin/media', params);

      if (response.success && response.data) {
        const backendMedia = Array.isArray(response.data) ? response.data : [];
        const mediaItems = backendMedia.map(transformBackendMedia);
        const pagination = response.pagination || {
          page: 1,
          limit: mediaItems.length,
          total: mediaItems.length,
          totalPages: 1
        };

        return { mediaItems, pagination };
      }

      throw new Error('MEDIA_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async createMediaItem(mediaData: any): Promise<MediaItem> {
    try {
      const response = await ApiService.Http.post<BackendMedia>('/content/admin/media', mediaData);

      if (response.success && response.data) {
        return transformBackendMedia(response.data);
      }

      throw new Error('MEDIA_CREATION_FAILED');
    } catch (error) {
      throw error;
    }
  }

  static async deleteMediaItem(id: string): Promise<void> {
    try {
      const response = await ApiService.Http.delete(`/content/admin/media/${id}`);

      if (!response.success) {
        throw new Error('MEDIA_DELETE_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Content Analytics
   */
  static async getContentAnalytics(): Promise<ContentAnalytics> {
    try {
      const response = await ApiService.Http.get<any>('/content/admin/analytics');

      if (response.success && response.data) {
        return {
          totalContent: response.data.totalContent || 0,
          publishedContent: response.data.publishedContent || 0,
          draftContent: response.data.totalContent - response.data.publishedContent || 0,
          scheduledContent: 0, // Backend doesn't provide this yet
          totalViews: 0, // Backend doesn't provide this yet
          totalClicks: 0, // Backend doesn't provide this yet
          totalConversions: 0, // Backend doesn't provide this yet
          contentByType: response.data.contentByType || {
            banner: 0,
            promotion: 0,
            newsletter: 0,
            page: 0,
            media: 0
          },
          recentActivity: [], // Backend doesn't provide this yet
          topBanners: [], // Backend doesn't provide this yet
          topPromotions: [], // Backend doesn't provide this yet
          topPages: [] // Backend doesn't provide this yet
        };
      }

      throw new Error('ANALYTICS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }
}
