/**
 * Admin Loyalty Service
 * Handles all loyalty program API operations for admin panel
 */

import { ApiService } from './apiService';
import {
  AdminLoyaltyMember,
  AdminLoyaltyTier,
  AdminLoyaltyReward,
  AdminPointTransaction,
  LoyaltyMemberFilters,
  LoyaltyRewardFilters
} from '../types/adminLoyalty';
import { LoyaltyMember, LoyaltyTier, LoyaltyReward, PointTransaction } from '../types/loyalty';

// Backend loyalty interfaces (matches Prisma schema)
interface BackendLoyaltyMember {
  id: string;
  userId: string;
  membershipNumber: string;
  tier: string;
  points: number;
  totalEarned: number;
  totalSpent: number;
  joinDate: string;
  lastActivity: string;
  isActive: boolean;
  user: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
  };
  transactions: {
    id: string;
    type: string;
    amount: number;
    description: string;
    orderId?: string;
    createdAt: string;
  }[];
}

interface BackendTier {
  id: string;
  name: string;
  nameEn?: string;
  minPoints: number;
  maxPoints?: number;
  benefits: string[];
  discountPercentage: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface BackendReward {
  id: string;
  title: string;
  description: string;
  pointsCost: number;
  type: string;
  value?: number;
  isActive: boolean;
  stock?: number;
  validUntil?: string;
  createdAt: string;
  updatedAt: string;
}

interface BackendPointTransaction {
  id: string;
  userId: string;
  type: string;
  amount: number;
  description: string;
  orderId?: string;
  rewardId?: string;
  expiresAt?: string;
  createdAt: string;
  user: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
}

// Transform functions
const transformBackendMember = (backendMember: BackendLoyaltyMember): AdminLoyaltyMember => {
  const tierInfo = {
    id: backendMember.tier,
    name: backendMember.tier,
    persianName: backendMember.tier,
    level: 1,
    minPoints: 0,
    maxPoints: 1000,
    color: '#3B82F6',
    icon: '🏆',
    benefits: getTierBenefits(backendMember.tier),
    discountPercentage: 5,
    freeShippingThreshold: 0,
    birthdayBonus: 0,
    description: ''
  };

  return {
    id: backendMember.id,
    userId: backendMember.userId,
    membershipNumber: backendMember.membershipNumber,
    tier: tierInfo,
    points: backendMember.points,
    totalEarned: backendMember.totalEarned,
    totalSpent: backendMember.totalSpent,
    joinDate: backendMember.joinDate,
    lastActivity: backendMember.lastActivity,
    isActive: backendMember.isActive,
    benefits: getTierBenefits(backendMember.tier),
    nextTierProgress: calculateNextTierProgress(backendMember.tier, backendMember.points),
    customerInfo: {
      id: backendMember.user.id,
      name: `${backendMember.user.firstName || ''} ${backendMember.user.lastName || ''}`.trim() || backendMember.user.email,
      email: backendMember.user.email,
      phone: backendMember.user.phone || '',
      registrationDate: backendMember.joinDate,
      totalOrders: 0,
      totalSpent: backendMember.totalSpent,
      averageOrderValue: 0,
      lastOrderDate: backendMember.lastActivity
    },
    adminNotes: [],
    flags: [],
    analytics: {
      engagementScore: 75,
      redemptionRate: 25,
      pointsEarnedLastMonth: 100,
      pointsRedeemedLastMonth: 50,
      riskScore: 10
    },
    audit: {
      createdBy: 'system',
      lastModifiedBy: 'system',
      lastModifiedAt: new Date().toISOString(),
      statusHistory: []
    }
  };
};

const transformBackendTier = (backendTier: BackendTier): AdminLoyaltyTier => ({
  id: backendTier.id,
  name: backendTier.name,
  nameEn: backendTier.nameEn,
  persianName: backendTier.name,
  level: 1,
  minPoints: backendTier.minPoints,
  maxPoints: backendTier.maxPoints,
  color: '#3B82F6',
  icon: '🏆',
  benefits: backendTier.benefits,
  discountPercentage: backendTier.discountPercentage,
  freeShippingThreshold: 0,
  birthdayBonus: 0,
  description: '',
  memberCount: 0,
  averageSpend: 0,
  retentionRate: 0,
  upgradeRate: 0,
  downgradeRate: 0,
  isActive: backendTier.isActive,
  createdAt: backendTier.createdAt,
  updatedAt: backendTier.updatedAt,
  createdBy: 'system',
  analytics: {
    totalMembers: 0,
    newMembersThisMonth: 0,
    totalPointsEarned: 0,
    totalPointsRedeemed: 0,
    averageEngagement: 0
  }
});

const transformBackendReward = (backendReward: BackendReward): AdminLoyaltyReward => ({
  id: backendReward.id,
  title: backendReward.title,
  description: backendReward.description,
  pointsCost: backendReward.pointsCost,
  type: backendReward.type as 'discount' | 'product' | 'shipping' | 'experience',
  value: backendReward.value || 0,
  isAvailable: backendReward.isActive,
  stock: backendReward.stock,
  image: '',
  validUntil: backendReward.validUntil,
  terms: [],
  targetTiers: [],
  maxRedemptionsPerMember: undefined,
  redemptionCount: 0,
  totalCost: 0,
  profitMargin: 0,
  isActive: backendReward.isActive,
  createdAt: backendReward.createdAt,
  updatedAt: backendReward.updatedAt,
  createdBy: 'system',
  analytics: {
    totalRedemptions: 0,
    redemptionsThisMonth: 0,
    averageRating: 0,
    customerSatisfaction: 0,
    costPerRedemption: 0
  },
  inventory: backendReward.stock ? {
    totalStock: backendReward.stock,
    availableStock: backendReward.stock,
    reservedStock: 0,
    lowStockThreshold: 10
  } : undefined
});

const transformBackendTransaction = (backendTransaction: BackendPointTransaction): AdminPointTransaction => ({
  id: backendTransaction.id,
  memberId: backendTransaction.userId,
  type: backendTransaction.type as 'earned' | 'redeemed' | 'bonus' | 'expired',
  points: backendTransaction.amount,
  description: backendTransaction.description,
  orderId: backendTransaction.orderId,
  createdAt: backendTransaction.createdAt,
  expiryDate: backendTransaction.expiresAt,
  customerInfo: {
    name: `${backendTransaction.user.firstName || ''} ${backendTransaction.user.lastName || ''}`.trim() || backendTransaction.user.email,
    email: backendTransaction.user.email,
    tier: 'برنز'
  },
  status: 'completed',
  metadata: {
    source: 'purchase'
  }
});

// Helper functions
const getTierBenefits = (tier: string): string[] => {
  const benefits: Record<string, string[]> = {
    'bronze': ['تخفیف ۵٪ در خریدهای بعدی'],
    'silver': ['تخفیف ۱۰٪ در خریدهای بعدی', 'ارسال رایگان'],
    'gold': ['تخفیف ۱۵٪ در خریدهای بعدی', 'ارسال رایگان', 'پشتیبانی اولویت‌دار'],
    'platinum': ['تخفیف ۲۰٪ در خریدهای بعدی', 'ارسال رایگان', 'پشتیبانی اختصاصی', 'دسترسی زودهنگام']
  };
  return benefits[tier] || [];
};

const calculateNextTierProgress = (currentTier: string, currentPoints: number) => {
  const tierThresholds: Record<string, number> = {
    'bronze': 1000,
    'silver': 5000,
    'gold': 10000,
    'platinum': 0
  };

  const nextTierPoints = tierThresholds[currentTier] || 0;
  if (nextTierPoints === 0) {
    return {
      currentPoints,
      requiredPoints: 0,
      percentage: 100
    };
  }

  return {
    currentPoints,
    requiredPoints: nextTierPoints,
    percentage: Math.min((currentPoints / nextTierPoints) * 100, 100)
  };
};

export interface LoyaltyMembersResponse {
  members: AdminLoyaltyMember[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class AdminLoyaltyService {
  /**
   * Get loyalty members with filtering and pagination
   */
  static async getMembers(params: {
    page?: number;
    limit?: number;
    filters?: LoyaltyMemberFilters;
    sort?: any;
  }): Promise<LoyaltyMembersResponse> {
    try {
      const queryParams: Record<string, string | number> = {
        page: params.page || 1,
        limit: params.limit || 20,
      };

      // Add filters
      if (params.filters) {
        if (params.filters.tier) queryParams.tier = params.filters.tier;
        if (params.filters.search) queryParams.search = params.filters.search;
        if (params.filters.isActive !== undefined) queryParams.isActive = params.filters.isActive;
        if (params.filters.minPoints) queryParams.minPoints = params.filters.minPoints;
        if (params.filters.maxPoints) queryParams.maxPoints = params.filters.maxPoints;
      }

      // Add sorting
      if (params.sort) {
        queryParams.sortBy = params.sort.field;
        queryParams.sortOrder = params.sort.direction;
      }

      const response = await ApiService.Http.get<BackendLoyaltyMember[]>('/loyalty/accounts', queryParams);

      if (response.success && response.data) {
        const members = response.data.map(transformBackendMember);
        const pagination = response.pagination || {
          page: 1,
          limit: members.length,
          total: members.length,
          totalPages: 1
        };

        return { members, pagination };
      }

      throw new Error('MEMBERS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get loyalty tiers
   */
  static async getTiers(): Promise<AdminLoyaltyTier[]> {
    try {
      // Note: There's no specific tiers endpoint in the backend, so we'll return static tier data
      return [
        { id: 'bronze', name: 'برنز', nameEn: 'Bronze', minPoints: 0, maxPoints: 999, benefits: ['تخفیف ۵٪'], discountPercentage: 5, isActive: true, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString(), memberCount: 0 },
        { id: 'silver', name: 'نقره', nameEn: 'Silver', minPoints: 1000, maxPoints: 4999, benefits: ['تخفیف ۱۰٪', 'ارسال رایگان'], discountPercentage: 10, isActive: true, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString(), memberCount: 0 },
        { id: 'gold', name: 'طلا', nameEn: 'Gold', minPoints: 5000, maxPoints: 19999, benefits: ['تخفیف ۱۵٪', 'ارسال رایگان', 'پشتیبانی اولویت‌دار'], discountPercentage: 15, isActive: true, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString(), memberCount: 0 },
        { id: 'platinum', name: 'پلاتین', nameEn: 'Platinum', minPoints: 20000, benefits: ['تخفیف ۲۰٪', 'ارسال رایگان', 'پشتیبانی اختصاصی'], discountPercentage: 20, isActive: true, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString(), memberCount: 0 }
      ];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get loyalty rewards
   */
  static async getRewards(): Promise<AdminLoyaltyReward[]> {
    try {
      const response = await ApiService.Http.get<BackendReward[]>('/loyalty/rewards');

      if (response.success && response.data) {
        return response.data.map(transformBackendReward);
      }

      throw new Error('REWARDS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get point transactions
   */
  static async getTransactions(params: {
    page?: number;
    limit?: number;
    userId?: string;
    type?: string;
  }): Promise<{ transactions: AdminPointTransaction[]; pagination: any }> {
    try {
      const queryParams: Record<string, string | number> = {
        page: params.page || 1,
        limit: params.limit || 20,
      };

      if (params.userId) queryParams.userId = params.userId;
      if (params.type) queryParams.type = params.type;

      const response = await ApiService.Http.get<BackendPointTransaction[]>('/loyalty/transactions', queryParams);

      if (response.success && response.data) {
        const transactions = response.data.map(transformBackendTransaction);
        const pagination = response.pagination || {
          page: 1,
          limit: transactions.length,
          total: transactions.length,
          totalPages: 1
        };

        return { transactions, pagination };
      }

      throw new Error('TRANSACTIONS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Adjust member points
   */
  static async adjustPoints(userId: string, amount: number, description: string): Promise<void> {
    try {
      const response = await ApiService.Http.post('/loyalty/points/adjust', {
        userId,
        amount,
        description
      });

      if (!response.success) {
        throw new Error('POINTS_ADJUSTMENT_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update member tier
   */
  static async updateMemberTier(userId: string, tier: string): Promise<LoyaltyMember> {
    try {
      // Note: There's no specific tier update endpoint, so we'll use adjust points
      throw new Error('TIER_UPDATE_NOT_IMPLEMENTED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create new tier
   */
  static async createTier(tierData: Omit<AdminLoyaltyTier, 'id' | 'memberCount' | 'createdAt' | 'updatedAt'>): Promise<AdminLoyaltyTier> {
    try {
      const response = await ApiService.Http.post<BackendTier>('/loyalty/admin/tiers', tierData);

      if (response.success && response.data) {
        return transformBackendTier(response.data);
      }

      throw new Error('TIER_CREATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update tier
   */
  static async updateTier(id: string, tierData: Partial<AdminLoyaltyTier>): Promise<AdminLoyaltyTier> {
    try {
      const response = await ApiService.Http.patch<BackendTier>(`/loyalty/admin/tiers/${id}`, tierData);

      if (response.success && response.data) {
        return transformBackendTier(response.data);
      }

      throw new Error('TIER_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create new reward
   */
  static async createReward(rewardData: Omit<AdminLoyaltyReward, 'id' | 'redemptionCount' | 'createdAt' | 'updatedAt'>): Promise<AdminLoyaltyReward> {
    try {
      const response = await ApiService.Http.post<BackendReward>('/loyalty/admin/rewards', rewardData);

      if (response.success && response.data) {
        return transformBackendReward(response.data);
      }

      throw new Error('REWARD_CREATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update reward
   */
  static async updateReward(id: string, rewardData: Partial<AdminLoyaltyReward>): Promise<AdminLoyaltyReward> {
    try {
      const response = await ApiService.Http.patch<BackendReward>(`/loyalty/admin/rewards/${id}`, rewardData);

      if (response.success && response.data) {
        return transformBackendReward(response.data);
      }

      throw new Error('REWARD_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get loyalty analytics
   */
  static async getAnalytics(): Promise<any> {
    try {
      const response = await ApiService.Http.get('/loyalty/statistics');

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('ANALYTICS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }
}
