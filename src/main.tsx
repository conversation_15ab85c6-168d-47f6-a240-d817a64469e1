import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { pwaManager } from './utils/pwaUtils';
import { errorReporter } from './utils/errorReporting';
// import { resourcePreloader, preconnect, dnsPrefetch } from './utils/performanceUtils';

// Initialize error monitoring
errorReporter.configure({
  maxReports: 100,
  batchSize: 10,
  flushInterval: 30000
});

// Initialize performance optimizations
// Preconnect to external domains
// preconnect('https://fonts.googleapis.com', true);
// preconnect('https://fonts.gstatic.com', true);

// DNS prefetch for potential external resources
// dnsPrefetch('https://cdn.jsdelivr.net');

// Preload critical resources
// resourcePreloader.preloadFonts([
//   'https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;700&display=swap'
// ]);

// Preload critical images (if any)
// resourcePreloader.preloadImages([
//   '/icons/icon-192x192.png',
//   '/icons/icon-512x512.png'
// ]);

// Service Worker is automatically registered by VitePWA plugin
// No manual registration needed

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
