import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';
import Layout from './components/layout/Layout';
import Home from './pages/Home';
import ProductsPage from './pages/ProductsPage';
import ProductDetail from './pages/ProductDetail';
import CheckoutPage from './pages/CheckoutPage';
import LoyaltyPage from './pages/LoyaltyPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ProfilePage from './pages/ProfilePage';
import WishlistPage from './pages/WishlistPage';
import ComparisonPage from './pages/ComparisonPage';
import AdminLoginPage from './pages/admin/AdminLoginPage';
import AdminDashboard from './pages/admin/AdminDashboard';
import ProductsListPage from './pages/admin/products/ProductsListPage';
import ProductCreatePage from './pages/admin/products/ProductCreatePage';
import ProductEditPage from './pages/admin/products/ProductEditPage';
import ProductDetailsPage from './pages/admin/products/ProductDetailsPage';
import ProductCategoriesPage from './pages/admin/products/ProductCategoriesPage';
import ProductBrandsPage from './pages/admin/products/ProductBrandsPage';
import ProductInventoryPage from './pages/admin/products/ProductInventoryPage';
import OrdersListPage from './pages/admin/orders/OrdersListPage';
import OrderDetailsPage from './pages/admin/orders/OrderDetailsPage';
import OrderEditPage from './pages/admin/orders/OrderEditPage';
import OrderTrackingPage from './pages/admin/orders/OrderTrackingPage';
import OrdersPendingPage from './pages/admin/orders/OrdersPendingPage';
import OrdersProcessingPage from './pages/admin/orders/OrdersProcessingPage';
import OrdersShippedPage from './pages/admin/orders/OrdersShippedPage';
import OrdersReturnsPage from './pages/admin/orders/OrdersReturnsPage';
import CustomersListPage from './pages/admin/customers/CustomersListPage';
import CustomerDetailsPage from './pages/admin/customers/CustomerDetailsPage';
import CustomerEditPage from './pages/admin/customers/CustomerEditPage';
import CustomerSegmentationPage from './pages/admin/customers/CustomerSegmentationPage';
import CustomerAnalyticsPage from './pages/admin/customers/CustomerAnalyticsPage';
import CustomerCommunicationPage from './pages/admin/customers/CustomerCommunicationPage';
import ReviewsListPage from './pages/admin/reviews/ReviewsListPage';
import { initializeBrands } from './utils/brandUtils';

// Loyalty Admin Pages
import LoyaltyDashboardPage from './pages/admin/loyalty/LoyaltyDashboardPage';
import TierManagementPage from './pages/admin/loyalty/TierManagementPage';
import RewardsManagementPage from './pages/admin/loyalty/RewardsManagementPage';
import PointTransactionsPage from './pages/admin/loyalty/PointTransactionsPage';
import ReviewDetailsPage from './pages/admin/reviews/ReviewDetailsPage';
import ReviewModerationPage from './pages/admin/reviews/ReviewModerationPage';
import ReviewAnalyticsPage from './pages/admin/reviews/ReviewAnalyticsPage';

// Analytics Pages
import AnalyticsDashboardPage from './pages/admin/analytics/AnalyticsDashboardPage';
import SalesAnalyticsPage from './pages/admin/analytics/SalesAnalyticsPage';
import CustomersAnalyticsPage from './pages/admin/analytics/CustomersAnalyticsPage';
import ProductsAnalyticsPage from './pages/admin/analytics/ProductsAnalyticsPage';
import TrafficAnalyticsPage from './pages/admin/analytics/TrafficAnalyticsPage';

// Audit Pages
import AuditDashboardPage from './pages/admin/audit/AuditDashboardPage';

// Settings Pages
import SettingsDashboardPage from './pages/admin/settings/SettingsDashboardPage';

// Notifications Pages
import NotificationsDashboardPage from './pages/admin/notifications/NotificationsDashboardPage';

// User Management Pages
import UsersListPage from './pages/admin/users/UsersListPage';
import AdminUsersPage from './pages/admin/users/AdminUsersPage';
import RolesPermissionsPage from './pages/admin/users/RolesPermissionsPage';

// Content Management Pages
import ContentDashboardPage from './pages/admin/content/ContentDashboardPage';
import BannerManagementPage from './pages/admin/content/BannerManagementPage';
import BannerCreatePage from './pages/admin/content/BannerCreatePage';
import BannerEditPage from './pages/admin/content/BannerEditPage';
import BannerDetailPage from './pages/admin/content/BannerDetailPage';
import PromotionsPage from './pages/admin/content/PromotionsPage';
import PromotionCreatePage from './pages/admin/content/PromotionCreatePage';
import PromotionEditPage from './pages/admin/content/PromotionEditPage';
import PromotionDetailPage from './pages/admin/content/PromotionDetailPage';
import NewsletterPage from './pages/admin/content/NewsletterPage';
import NewsletterCreatePage from './pages/admin/content/NewsletterCreatePage';
import NewsletterEditPage from './pages/admin/content/NewsletterEditPage';
import NewsletterDetailPage from './pages/admin/content/NewsletterDetailPage';
import PagesPage from './pages/admin/content/PagesPage';
import PageCreatePage from './pages/admin/content/PageCreatePage';
import PageEditPage from './pages/admin/content/PageEditPage';
import MediaLibraryPage from './pages/admin/content/MediaLibraryPage';
import HomePageContentPage from './pages/admin/content/HomePageContentPage';

import ProtectedRoute from './components/auth/ProtectedRoute';
import AdminProtectedRoute from './components/admin/AdminProtectedRoute';
import { CartProvider } from './context/CartContext';
import { AuthProvider } from './context/AuthContext';
import { AdminAuthProvider } from './context/AdminAuthContext';
import { WishlistProvider } from './context/WishlistContext';
import InstallPrompt from './components/pwa/InstallPrompt';
import OfflineIndicator from './components/pwa/OfflineIndicator';
import ErrorBoundary from './components/common/ErrorBoundary';
import PerformanceMonitor from './components/performance/PerformanceMonitor';

function App() {
  // Initialize brands cache on app startup
  useEffect(() => {
    initializeBrands();
  }, []);

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <AuthProvider>
          <AdminAuthProvider>
            <CartProvider>
              <WishlistProvider>
            <Router future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true
            }}>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />

                {/* Admin routes */}
                <Route path="/admin/login" element={<AdminLoginPage />} />
                <Route path="/admin" element={
                  <AdminProtectedRoute>
                    <AdminDashboard />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/dashboard" element={
                  <AdminProtectedRoute>
                    <AdminDashboard />
                  </AdminProtectedRoute>
                } />

                {/* Admin Product Management Routes */}
                <Route path="/admin/products" element={
                  <AdminProtectedRoute>
                    <ProductsListPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/products/categories" element={
                  <AdminProtectedRoute>
                    <ProductCategoriesPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/products/brands" element={
                  <AdminProtectedRoute>
                    <ProductBrandsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/products/inventory" element={
                  <AdminProtectedRoute>
                    <ProductInventoryPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/products/create" element={
                  <AdminProtectedRoute>
                    <ProductCreatePage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/products/:id" element={
                  <AdminProtectedRoute>
                    <ProductDetailsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/products/:id/edit" element={
                  <AdminProtectedRoute>
                    <ProductEditPage />
                  </AdminProtectedRoute>
                } />

                {/* Admin Order Management Routes */}
                <Route path="/admin/orders" element={
                  <AdminProtectedRoute>
                    <OrdersListPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/orders/pending" element={
                  <AdminProtectedRoute>
                    <OrdersPendingPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/orders/processing" element={
                  <AdminProtectedRoute>
                    <OrdersProcessingPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/orders/shipped" element={
                  <AdminProtectedRoute>
                    <OrdersShippedPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/orders/returns" element={
                  <AdminProtectedRoute>
                    <OrdersReturnsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/orders/tracking" element={
                  <AdminProtectedRoute>
                    <OrderTrackingPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/orders/:id" element={
                  <AdminProtectedRoute>
                    <OrderDetailsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/orders/:id/edit" element={
                  <AdminProtectedRoute>
                    <OrderEditPage />
                  </AdminProtectedRoute>
                } />

                {/* Admin Customer Management Routes */}
                <Route path="/admin/customers" element={
                  <AdminProtectedRoute>
                    <CustomersListPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/customers/segments" element={
                  <AdminProtectedRoute>
                    <CustomerSegmentationPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/customers/analytics" element={
                  <AdminProtectedRoute>
                    <CustomerAnalyticsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/customers/:id" element={
                  <AdminProtectedRoute>
                    <CustomerDetailsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/customers/:id/edit" element={
                  <AdminProtectedRoute>
                    <CustomerEditPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/customers/:id/communication" element={
                  <AdminProtectedRoute>
                    <CustomerCommunicationPage />
                  </AdminProtectedRoute>
                } />

                {/* Admin Review Management Routes */}
                <Route path="/admin/reviews" element={
                  <AdminProtectedRoute>
                    <ReviewsListPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/reviews/moderation" element={
                  <AdminProtectedRoute>
                    <ReviewModerationPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/reviews/analytics" element={
                  <AdminProtectedRoute>
                    <ReviewAnalyticsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/reviews/:id" element={
                  <AdminProtectedRoute>
                    <ReviewDetailsPage />
                  </AdminProtectedRoute>
                } />

                {/* Loyalty Program Management Routes */}
                <Route path="/admin/loyalty" element={
                  <AdminProtectedRoute>
                    <LoyaltyDashboardPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/loyalty/dashboard" element={
                  <AdminProtectedRoute>
                    <LoyaltyDashboardPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/loyalty/programs" element={
                  <AdminProtectedRoute>
                    <LoyaltyDashboardPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/loyalty/members" element={
                  <AdminProtectedRoute>
                    <TierManagementPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/loyalty/tiers" element={
                  <AdminProtectedRoute>
                    <TierManagementPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/loyalty/rewards" element={
                  <AdminProtectedRoute>
                    <RewardsManagementPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/loyalty/transactions" element={
                  <AdminProtectedRoute>
                    <PointTransactionsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/loyalty/points" element={
                  <AdminProtectedRoute>
                    <PointTransactionsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/loyalty/programs" element={
                  <AdminProtectedRoute>
                    <LoyaltyDashboardPage />
                  </AdminProtectedRoute>
                } />

                {/* Content Management Routes */}
                <Route path="/admin/content" element={
                  <AdminProtectedRoute>
                    <ContentDashboardPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/dashboard" element={
                  <AdminProtectedRoute>
                    <ContentDashboardPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/banners" element={
                  <AdminProtectedRoute>
                    <BannerManagementPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/banners/create" element={
                  <AdminProtectedRoute>
                    <BannerCreatePage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/banners/:id" element={
                  <AdminProtectedRoute>
                    <BannerDetailPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/banners/:id/edit" element={
                  <AdminProtectedRoute>
                    <BannerEditPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/promotions" element={
                  <AdminProtectedRoute>
                    <PromotionsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/promotions/create" element={
                  <AdminProtectedRoute>
                    <PromotionCreatePage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/promotions/:id" element={
                  <AdminProtectedRoute>
                    <PromotionDetailPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/promotions/:id/edit" element={
                  <AdminProtectedRoute>
                    <PromotionEditPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/newsletter" element={
                  <AdminProtectedRoute>
                    <NewsletterPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/newsletter/create" element={
                  <AdminProtectedRoute>
                    <NewsletterCreatePage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/newsletter/:id" element={
                  <AdminProtectedRoute>
                    <NewsletterDetailPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/newsletter/:id/edit" element={
                  <AdminProtectedRoute>
                    <NewsletterEditPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/homepage" element={
                  <AdminProtectedRoute>
                    <HomePageContentPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/pages" element={
                  <AdminProtectedRoute>
                    <PagesPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/pages/create" element={
                  <AdminProtectedRoute>
                    <PageCreatePage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/pages/:id/edit" element={
                  <AdminProtectedRoute>
                    <PageEditPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/content/media" element={
                  <AdminProtectedRoute>
                    <MediaLibraryPage />
                  </AdminProtectedRoute>
                } />

                {/* Analytics Routes */}
                <Route path="/admin/analytics" element={
                  <AdminProtectedRoute>
                    <AnalyticsDashboardPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/analytics/sales" element={
                  <AdminProtectedRoute>
                    <SalesAnalyticsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/analytics/customers" element={
                  <AdminProtectedRoute>
                    <CustomersAnalyticsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/analytics/products" element={
                  <AdminProtectedRoute>
                    <ProductsAnalyticsPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/analytics/traffic" element={
                  <AdminProtectedRoute>
                    <TrafficAnalyticsPage />
                  </AdminProtectedRoute>
                } />

                {/* Settings Routes */}
                <Route path="/admin/settings" element={
                  <AdminProtectedRoute>
                    <SettingsDashboardPage />
                  </AdminProtectedRoute>
                } />

                {/* Notifications Routes */}
                <Route path="/admin/notifications" element={
                  <AdminProtectedRoute>
                    <NotificationsDashboardPage />
                  </AdminProtectedRoute>
                } />

                {/* Audit Routes */}
                <Route path="/admin/audit" element={
                  <AdminProtectedRoute>
                    <AuditDashboardPage />
                  </AdminProtectedRoute>
                } />

                {/* User Management Routes */}
                <Route path="/admin/users" element={
                  <AdminProtectedRoute
                    requiredResource="users"
                    requiredAction="read"
                    requiredRole="super_admin"
                  >
                    <UsersListPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/users/admins" element={
                  <AdminProtectedRoute
                    requiredResource="users"
                    requiredAction="read"
                    requiredRole="super_admin"
                  >
                    <AdminUsersPage />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/users/roles" element={
                  <AdminProtectedRoute
                    requiredResource="users"
                    requiredAction="read"
                    requiredRole="super_admin"
                  >
                    <RolesPermissionsPage />
                  </AdminProtectedRoute>
                } />

                {/* Routes with layout */}
                <Route path="/*" element={
                  <Layout>
                    <Routes>
                      <Route path="/" element={
                        <ErrorBoundary isolate>
                          <Home />
                        </ErrorBoundary>
                      } />
                      <Route path="/products" element={
                        <ErrorBoundary isolate>
                          <ProductsPage />
                        </ErrorBoundary>
                      } />
                      <Route path="/product/:id" element={
                        <ErrorBoundary isolate>
                          <ProductDetail />
                        </ErrorBoundary>
                      } />
                      <Route path="/wishlist" element={
                        <ErrorBoundary isolate>
                          <WishlistPage />
                        </ErrorBoundary>
                      } />
                      <Route path="/comparison" element={
                        <ErrorBoundary isolate>
                          <ComparisonPage />
                        </ErrorBoundary>
                      } />

                      {/* Protected routes */}
                      <Route path="/checkout" element={
                        <ProtectedRoute>
                          <CheckoutPage />
                        </ProtectedRoute>
                      } />
                      <Route path="/loyalty" element={
                        <ProtectedRoute>
                          <LoyaltyPage />
                        </ProtectedRoute>
                      } />
                      <Route path="/profile" element={
                        <ProtectedRoute>
                          <ProfilePage />
                        </ProtectedRoute>
                      } />
                    </Routes>
                  </Layout>
                } />
              </Routes>
            </Router>
            </WishlistProvider>
          </CartProvider>

          {/* Performance Monitoring - Only visible to authenticated admins */}
          <PerformanceMonitor
            position="bottom-right"
            showInProduction={false}
            autoHide={true}
            autoHideDelay={10000}
          />
        </AdminAuthProvider>
      </AuthProvider>
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
            fontFamily: 'inherit',
            direction: 'rtl'
          },
          success: {
            style: {
              background: '#10B981',
            },
          },
          error: {
            style: {
              background: '#EF4444',
            },
          },
        }}
      />

        {/* PWA Components */}
        <InstallPrompt variant="banner" autoShow={true} showDelay={5000} />
        <OfflineIndicator position="bottom" showConnectionInfo={true} />
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;