# GlowRoya E-commerce Platform Environment Configuration
# Copy this file to .env and update the values

# Application Environment
NODE_ENV=production
PORT=3001

# Database Configuration (Remote VPS PostgreSQL)
DATABASE_URL=*******************************************************/glowroya

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,image/gif

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# CORS Configuration
FRONTEND_URL=http://localhost:5173
ADMIN_URL=http://localhost:5173/admin

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production
SESSION_MAX_AGE=86400000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# Payment Gateway Configuration (Iranian)
ZARINPAL_MERCHANT_ID=your-zarinpal-merchant-id
MELLAT_TERMINAL_ID=your-mellat-terminal-id
MELLAT_USERNAME=your-mellat-username
MELLAT_PASSWORD=your-mellat-password

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn-if-using
HEALTH_CHECK_INTERVAL=30000

# API Configuration
API_VERSION=v1
API_PREFIX=/api/v1

# Persian/RTL Configuration
DEFAULT_LANGUAGE=fa
SUPPORTED_LANGUAGES=fa,en
DEFAULT_TIMEZONE=Asia/Tehran
DEFAULT_CURRENCY=IRR

# Docker Configuration
COMPOSE_PROJECT_NAME=glowroya
