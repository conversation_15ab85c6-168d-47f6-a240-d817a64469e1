<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Test</h1>
        
        <button class="button" onclick="loginSuperAdmin()">Login as Super Admin</button>
        <button class="button" onclick="checkAuth()">Check Authentication</button>
        <button class="button" onclick="clearAuth()">Clear Authentication</button>
        
        <div id="status"></div>
        <div id="details"></div>
        
        <h3>Quick Links:</h3>
        <a href="http://localhost:5174/admin/dashboard" target="_blank">Dashboard</a> |
        <a href="http://localhost:5174/admin/users" target="_blank">Users</a> |
        <a href="http://localhost:5174/admin/users/admins" target="_blank">Admin Users</a> |
        <a href="http://localhost:5174/admin/users/roles" target="_blank">Roles</a>
    </div>

    <script>
        const ADMIN_STORAGE_KEYS = {
            TOKEN: 'admin_token',
            REFRESH_TOKEN: 'admin_refresh_token',
            USER: 'admin_user',
            SESSION_EXPIRY: 'admin_session_expiry',
            REMEMBER_ME: 'admin_remember_me'
        };

        function createMockToken(user, rememberMe = false) {
            const now = Math.floor(Date.now() / 1000);
            const expiry = now + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60);
            
            const tokenPayload = {
                sub: user.id,
                email: user.email,
                role: user.role,
                type: 'admin',
                iat: now,
                exp: expiry
            };

            return btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' })) + '.' +
                   btoa(JSON.stringify(tokenPayload)) + '.' +
                   btoa('mock_signature');
        }

        function loginSuperAdmin() {
            const user = {
                id: 'admin-1',
                email: '<EMAIL>',
                firstName: 'مدیر',
                lastName: 'سیستم',
                role: 'super_admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'مدیریت',
                permissions: [
                    {
                        resource: 'products',
                        actions: ['create', 'read', 'update', 'delete', 'export', 'import']
                    },
                    {
                        resource: 'orders',
                        actions: ['create', 'read', 'update', 'delete', 'export']
                    },
                    {
                        resource: 'customers',
                        actions: ['create', 'read', 'update', 'delete', 'export']
                    },
                    {
                        resource: 'reviews',
                        actions: ['read', 'update', 'delete', 'approve', 'reject', 'moderate', 'export']
                    },
                    {
                        resource: 'loyalty',
                        actions: ['create', 'read', 'update', 'delete', 'configure']
                    },
                    {
                        resource: 'content',
                        actions: ['create', 'read', 'update', 'delete']
                    },
                    {
                        resource: 'analytics',
                        actions: ['read', 'export']
                    },
                    {
                        resource: 'settings',
                        actions: ['create', 'read', 'update', 'delete']
                    },
                    {
                        resource: 'users',
                        actions: ['create', 'read', 'update', 'delete']
                    },
                    {
                        resource: 'audit',
                        actions: ['read', 'export']
                    },
                    {
                        resource: 'notifications',
                        actions: ['create', 'read', 'update', 'delete']
                    }
                ]
            };

            const token = createMockToken(user, true);
            const refreshToken = 'mock_refresh_token_' + Date.now();
            const sessionExpiry = Date.now() + (24 * 60 * 60 * 1000);

            localStorage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
            localStorage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
            localStorage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, sessionExpiry.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, 'true');

            document.getElementById('status').innerHTML = 
                '<div class="status success">✅ Successfully logged in as Super Admin!</div>';
            
            checkAuth();
        }

        function checkAuth() {
            const token = localStorage.getItem(ADMIN_STORAGE_KEYS.TOKEN);
            const user = localStorage.getItem(ADMIN_STORAGE_KEYS.USER);
            const sessionExpiry = localStorage.getItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY);

            let status = '';
            let details = '';

            if (!token) {
                status = '<div class="status error">❌ No token found</div>';
            } else {
                status = '<div class="status success">✅ Token exists</div>';
                try {
                    const tokenParts = token.split('.');
                    const payload = JSON.parse(atob(tokenParts[1]));
                    details += '<h4>Token Payload:</h4><pre>' + JSON.stringify(payload, null, 2) + '</pre>';
                } catch (e) {
                    status += '<div class="status error">❌ Invalid token format</div>';
                }
            }

            if (!user) {
                status += '<div class="status error">❌ No user data found</div>';
            } else {
                status += '<div class="status success">✅ User data exists</div>';
                try {
                    const userData = JSON.parse(user);
                    details += '<h4>User Data:</h4><pre>' + JSON.stringify(userData, null, 2) + '</pre>';
                    
                    if (userData.role === 'super_admin') {
                        status += '<div class="status success">✅ User has super_admin role</div>';
                        
                        // Check if user has 'users' resource permission
                        const hasUsersPermission = userData.permissions && 
                            userData.permissions.some(p => p.resource === 'users' && p.actions.includes('read'));
                        
                        if (hasUsersPermission) {
                            status += '<div class="status success">✅ User has users resource permission</div>';
                        } else {
                            status += '<div class="status error">❌ User missing users resource permission</div>';
                        }
                    } else {
                        status += '<div class="status warning">⚠️ User does not have super_admin role</div>';
                    }
                } catch (e) {
                    status += '<div class="status error">❌ Invalid user data format</div>';
                }
            }

            if (sessionExpiry) {
                const expiry = parseInt(sessionExpiry);
                const now = Date.now();
                if (expiry > now) {
                    status += '<div class="status success">✅ Session is valid</div>';
                } else {
                    status += '<div class="status error">❌ Session has expired</div>';
                }
            }

            document.getElementById('status').innerHTML = status;
            document.getElementById('details').innerHTML = details;
        }

        function clearAuth() {
            Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            
            document.getElementById('status').innerHTML = 
                '<div class="status warning">🗑️ Authentication cleared</div>';
            document.getElementById('details').innerHTML = '';
        }

        // Check auth on page load
        window.onload = checkAuth;
    </script>
</body>
</html>
