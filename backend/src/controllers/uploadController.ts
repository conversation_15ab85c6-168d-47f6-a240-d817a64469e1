import { Request, Response, NextFunction } from 'express';
import UploadService from '../services/uploadService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

export class UploadController {
  // Upload single image
  static async uploadSingleImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const file = req.file;

      if (!file) {
        throw new AppError('هیچ فایلی آپلود نشده است', 400, 'NO_FILE_UPLOADED');
      }

      // Validate uploaded file
      UploadService.validateUploadedFile(file);

      // Get file info
      const imageInfo = UploadService.getFileInfo(file);

      logger.info(`Single image uploaded by admin: ${req.user?.email}, file: ${file.filename}`);

      res.json({
        success: true,
        message: 'تصویر با موفقیت آپلود شد',
        data: imageInfo,
      });
    } catch (error) {
      next(error);
    }
  }

  // Upload multiple images
  static async uploadMultipleImages(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const files = req.files as Express.Multer.File[];

      if (!files || files.length === 0) {
        throw new AppError('هیچ فایلی آپلود نشده است', 400, 'NO_FILES_UPLOADED');
      }

      // Validate uploaded files
      UploadService.validateUploadedFiles(files);

      // Process files
      const imagesInfo = files.map(file => UploadService.getFileInfo(file));

      logger.info(`Multiple images uploaded by admin: ${req.user?.email}, count: ${files.length}`);

      res.json({
        success: true,
        message: `${files.length} تصویر با موفقیت آپلود شد`,
        data: imagesInfo,
      });
    } catch (error) {
      next(error);
    }
  }

  // Upload product images (primary + gallery)
  static async uploadProductImages(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const files = req.files as { [fieldname: string]: Express.Multer.File[] };

      if (!files || (!files.primaryImage && !files.galleryImages)) {
        throw new AppError('هیچ فایلی آپلود نشده است', 400, 'NO_FILES_UPLOADED');
      }

      const result: {
        primaryImage?: any;
        galleryImages?: any[];
      } = {};

      // Process primary image
      if (files.primaryImage && files.primaryImage[0]) {
        const file = files.primaryImage[0];
        UploadService.validateUploadedFile(file);
        result.primaryImage = UploadService.getFileInfo(file, 'products');
      }

      // Process gallery images
      if (files.galleryImages) {
        UploadService.validateUploadedFiles(files.galleryImages);
        result.galleryImages = files.galleryImages.map(file => 
          UploadService.getFileInfo(file, 'products')
        );
      }

      const totalFiles = (files.primaryImage?.length || 0) + (files.galleryImages?.length || 0);
      logger.info(`Product images uploaded by admin: ${req.user?.email}, count: ${totalFiles}`);

      res.json({
        success: true,
        message: 'تصاویر محصول با موفقیت آپلود شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete uploaded file
  static async deleteFile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { filename } = req.params;
      const { subfolder } = req.query;

      if (!filename) {
        throw new AppError('نام فایل الزامی است', 400, 'FILENAME_REQUIRED');
      }

      const deleted = await UploadService.deleteFile(filename, subfolder as string);

      if (!deleted) {
        throw new AppError('فایل یافت نشد', 404, 'FILE_NOT_FOUND');
      }

      logger.info(`File deleted by admin: ${req.user?.email}, file: ${filename}`);

      res.json({
        success: true,
        message: 'فایل با موفقیت حذف شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get file info
  static async getFileInfo(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { filename } = req.params;
      const { subfolder } = req.query;

      if (!filename) {
        throw new AppError('نام فایل الزامی است', 400, 'FILENAME_REQUIRED');
      }

      const url = UploadService.getFileUrl(filename, subfolder as string);

      res.json({
        success: true,
        message: 'اطلاعات فایل با موفقیت دریافت شد',
        data: {
          filename,
          url,
          subfolder: subfolder || null,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Upload category image
  static async uploadCategoryImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const file = req.file;

      if (!file) {
        throw new AppError('هیچ فایلی آپلود نشده است', 400, 'NO_FILE_UPLOADED');
      }

      // Validate uploaded file
      UploadService.validateUploadedFile(file);

      // Get file info
      const imageInfo = UploadService.getFileInfo(file, 'categories');

      logger.info(`Category image uploaded by admin: ${req.user?.email}, file: ${file.filename}`);

      res.json({
        success: true,
        message: 'تصویر دسته‌بندی با موفقیت آپلود شد',
        data: imageInfo,
      });
    } catch (error) {
      next(error);
    }
  }

  // Upload brand image
  static async uploadBrandImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const file = req.file;

      if (!file) {
        throw new AppError('هیچ فایلی آپلود نشده است', 400, 'NO_FILE_UPLOADED');
      }

      // Validate uploaded file
      UploadService.validateUploadedFile(file);

      // Get file info
      const imageInfo = UploadService.getFileInfo(file, 'brands');

      logger.info(`Brand image uploaded by admin: ${req.user?.email}, file: ${file.filename}`);

      res.json({
        success: true,
        message: 'تصویر برند با موفقیت آپلود شد',
        data: imageInfo,
      });
    } catch (error) {
      next(error);
    }
  }

  // Upload temporary image (for editors, etc.)
  static async uploadTempImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const file = req.file;

      if (!file) {
        throw new AppError('هیچ فایلی آپلود نشده است', 400, 'NO_FILE_UPLOADED');
      }

      // Validate uploaded file
      UploadService.validateUploadedFile(file);

      // Get file info
      const imageInfo = UploadService.getFileInfo(file, 'temp');

      logger.info(`Temporary image uploaded by admin: ${req.user?.email}, file: ${file.filename}`);

      res.json({
        success: true,
        message: 'تصویر موقت با موفقیت آپلود شد',
        data: imageInfo,
      });
    } catch (error) {
      next(error);
    }
  }
}

export default UploadController;
