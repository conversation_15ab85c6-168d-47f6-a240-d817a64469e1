import { Request, Response, NextFunction } from 'express';
import { PaymentService, CreatePaymentData, UpdatePaymentData, PaymentFilters } from '../services/paymentService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

export class PaymentController {
  // Get all payments (admin only)
  static async getPayments(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        orderId,
        status,
        method,
        gateway,
        dateFrom,
        dateTo,
        minAmount,
        maxAmount,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        include = 'order'
      } = req.query;

      // Parse filters
      const filters: PaymentFilters = {};
      if (orderId) filters.orderId = orderId as string;
      if (status) filters.status = status as any;
      if (method) filters.method = method as any;
      if (gateway) filters.gateway = gateway as string;
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);
      if (minAmount) filters.minAmount = parseFloat(minAmount as string);
      if (maxAmount) filters.maxAmount = parseFloat(maxAmount as string);

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      // Get payments
      const result = await PaymentService.getPayments(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'پرداخت‌ها با موفقیت دریافت شد',
        data: result.payments,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get payment by ID (admin only)
  static async getPaymentById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { include = 'order' } = req.query;

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      const payment = await PaymentService.getPaymentById(id, {
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'پرداخت با موفقیت دریافت شد',
        data: payment,
      });
    } catch (error) {
      next(error);
    }
  }

  // Create new payment (admin only)
  static async createPayment(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const paymentData: CreatePaymentData = req.body;

      // Validate required fields
      if (!paymentData.orderId || !paymentData.amount || !paymentData.method) {
        throw new AppError('فیلدهای الزامی کامل نیست', 400, 'MISSING_REQUIRED_FIELDS');
      }

      const payment = await PaymentService.createPayment(paymentData);

      logger.info(`Payment created by admin: ${req.user?.email}, payment: ${payment.id}`);

      res.status(201).json({
        success: true,
        message: 'پرداخت با موفقیت ایجاد شد',
        data: payment,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update payment (admin only)
  static async updatePayment(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: UpdatePaymentData = req.body;

      const payment = await PaymentService.updatePayment(id, updateData);

      logger.info(`Payment updated by admin: ${req.user?.email}, payment: ${payment.id}`);

      res.json({
        success: true,
        message: 'پرداخت با موفقیت به‌روزرسانی شد',
        data: payment,
      });
    } catch (error) {
      next(error);
    }
  }

  // Process payment (simulate payment gateway callback)
  static async processPayment(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { gatewayResponse } = req.body;

      if (!gatewayResponse) {
        throw new AppError('پاسخ درگاه پرداخت الزامی است', 400, 'MISSING_GATEWAY_RESPONSE');
      }

      const payment = await PaymentService.processPayment(id, gatewayResponse);

      logger.info(`Payment processed: ${payment.id}, status: ${payment.status}`);

      res.json({
        success: true,
        message: 'پرداخت با موفقیت پردازش شد',
        data: payment,
      });
    } catch (error) {
      next(error);
    }
  }

  // Refund payment (admin only)
  static async refundPayment(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { refundAmount, reason } = req.body;

      const refundPayment = await PaymentService.refundPayment(id, refundAmount, reason);

      logger.info(`Payment refunded by admin: ${req.user?.email}, payment: ${id}, amount: ${refundAmount || 'full'}`);

      res.json({
        success: true,
        message: 'پرداخت با موفقیت بازگشت داده شد',
        data: refundPayment,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get payment statistics (admin only)
  static async getPaymentStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { dateFrom, dateTo } = req.query;

      // Build date filter
      const dateFilter: any = {};
      if (dateFrom) dateFilter.gte = new Date(dateFrom as string);
      if (dateTo) dateFilter.lte = new Date(dateTo as string);

      const filters: PaymentFilters = {};
      if (Object.keys(dateFilter).length > 0) {
        filters.dateFrom = dateFilter.gte;
        filters.dateTo = dateFilter.lte;
      }

      // Get statistics
      const [
        allPayments,
        completedPayments,
        failedPayments,
        pendingPayments,
        refundedPayments
      ] = await Promise.all([
        PaymentService.getPayments(filters, { limit: 10000 }),
        PaymentService.getPayments({ ...filters, status: 'COMPLETED' }, { limit: 10000 }),
        PaymentService.getPayments({ ...filters, status: 'FAILED' }, { limit: 10000 }),
        PaymentService.getPayments({ ...filters, status: 'PENDING' }, { limit: 10000 }),
        PaymentService.getPayments({ ...filters, status: 'REFUNDED' }, { limit: 10000 }),
      ]);

      const totalRevenue = completedPayments.payments.reduce(
        (sum, payment) => sum + Number(payment.amount), 0
      );

      const totalRefunds = refundedPayments.payments.reduce(
        (sum, payment) => sum + Math.abs(Number(payment.amount)), 0
      );

      const netRevenue = totalRevenue - totalRefunds;

      // Payment method distribution
      const methodCounts: Record<string, number> = {};
      allPayments.payments.forEach(payment => {
        methodCounts[payment.method] = (methodCounts[payment.method] || 0) + 1;
      });

      // Gateway distribution
      const gatewayCounts: Record<string, number> = {};
      allPayments.payments.forEach(payment => {
        if (payment.gateway) {
          gatewayCounts[payment.gateway] = (gatewayCounts[payment.gateway] || 0) + 1;
        }
      });

      const statistics = {
        overview: {
          totalPayments: allPayments.pagination.total,
          totalRevenue,
          totalRefunds,
          netRevenue,
          averagePaymentAmount: allPayments.pagination.total > 0 ? totalRevenue / allPayments.pagination.total : 0,
        },
        statusDistribution: {
          completed: completedPayments.pagination.total,
          failed: failedPayments.pagination.total,
          pending: pendingPayments.pagination.total,
          refunded: refundedPayments.pagination.total,
        },
        methodDistribution: methodCounts,
        gatewayDistribution: gatewayCounts,
        successRate: allPayments.pagination.total > 0 
          ? (completedPayments.pagination.total / allPayments.pagination.total) * 100 
          : 0,
      };

      res.json({
        success: true,
        message: 'آمار پرداخت‌ها با موفقیت دریافت شد',
        data: statistics,
      });
    } catch (error) {
      next(error);
    }
  }

  // Simulate payment gateway (for testing purposes)
  static async simulatePaymentGateway(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { paymentId, success = true, transactionId } = req.body;

      if (!paymentId) {
        throw new AppError('شناسه پرداخت الزامی است', 400, 'MISSING_PAYMENT_ID');
      }

      // Simulate gateway response
      const gatewayResponse = {
        success: success === true || success === 'true',
        transactionId: transactionId || `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        gateway: 'simulation',
        ...(success !== true && success !== 'true' && {
          error: 'Payment failed in simulation',
          errorCode: 'SIM_FAIL_001'
        })
      };

      const payment = await PaymentService.processPayment(paymentId, gatewayResponse);

      logger.info(`Payment gateway simulated: ${paymentId}, success: ${gatewayResponse.success}`);

      res.json({
        success: true,
        message: 'شبیه‌سازی درگاه پرداخت انجام شد',
        data: {
          payment,
          gatewayResponse,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default PaymentController;
