import { Request, Response, NextFunction } from 'express';
import ReviewService from '../services/reviewService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

class ReviewController {
  // Create a new review
  static async createReview(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const {
        productId,
        rating,
        title,
        content,
        pros,
        cons,
        isRecommended,
        skinType,
        ageRange,
        usageDuration,
        images,
      } = req.body;

      const review = await ReviewService.createReview({
        productId,
        userId,
        rating,
        title,
        content,
        pros,
        cons,
        isRecommended,
        skinType,
        ageRange,
        usageDuration,
        images,
      });

      res.status(201).json({
        success: true,
        message: 'نظر شما با موفقیت ثبت شد و در انتظار تأیید است',
        data: { review },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get reviews with filtering and pagination
  static async getReviews(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        productId,
        userId,
        rating,
        isVerified,
        isApproved,
        moderationStatus,
        isRecommended,
        skinType,
        ageRange,
        search,
        sortBy,
        page,
        limit,
      } = req.query;

      const filters = {
        productId: productId as string,
        userId: userId as string,
        rating: rating ? parseInt(rating as string) : undefined,
        isVerified: isVerified === 'true' ? true : isVerified === 'false' ? false : undefined,
        isApproved: isApproved === 'true' ? true : isApproved === 'false' ? false : undefined,
        moderationStatus: moderationStatus as any,
        isRecommended: isRecommended === 'true' ? true : isRecommended === 'false' ? false : undefined,
        skinType: skinType as string,
        ageRange: ageRange as string,
        search: search as string,
        sortBy: sortBy as any,
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined,
      };

      const result = await ReviewService.getReviews(filters);

      res.json({
        success: true,
        message: 'نظرات با موفقیت دریافت شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get review by ID
  static async getReviewById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const review = await ReviewService.getReviewById(id);

      res.json({
        success: true,
        message: 'نظر با موفقیت دریافت شد',
        data: { review },
      });
    } catch (error) {
      next(error);
    }
  }

  // Update review (only by owner)
  static async updateReview(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const updateData = req.body;

      const review = await ReviewService.updateReview(id, userId, updateData);

      res.json({
        success: true,
        message: 'نظر با موفقیت به‌روزرسانی شد',
        data: { review },
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete review (only by owner or admin)
  static async deleteReview(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const isAdmin = req.user!.role === 'ADMIN' || req.user!.role === 'SUPER_ADMIN';

      await ReviewService.deleteReview(id, userId, isAdmin);

      res.json({
        success: true,
        message: 'نظر با موفقیت حذف شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Vote on review helpfulness
  static async voteReview(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { isHelpful } = req.body;
      const userId = req.user!.id;

      await ReviewService.voteReview(id, userId, isHelpful);

      res.json({
        success: true,
        message: 'رأی شما با موفقیت ثبت شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Add response to review (admin only)
  static async addReviewResponse(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { content, isOfficial } = req.body;
      const userId = req.user!.id;

      const response = await ReviewService.addReviewResponse(id, userId, content, isOfficial);

      res.status(201).json({
        success: true,
        message: 'پاسخ با موفقیت افزوده شد',
        data: { response },
      });
    } catch (error) {
      next(error);
    }
  }

  // Moderate review (admin only)
  static async moderateReview(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { moderationStatus, moderationNotes } = req.body;

      const review = await ReviewService.moderateReview(id, moderationStatus, moderationNotes);

      res.json({
        success: true,
        message: 'نظر با موفقیت تعدیل شد',
        data: { review },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get product review statistics
  static async getProductReviewStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { productId } = req.params;
      const stats = await ReviewService.getProductReviewStats(productId);

      res.json({
        success: true,
        message: 'آمار نظرات محصول با موفقیت دریافت شد',
        data: { stats },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get review analytics (admin only)
  static async getReviewAnalytics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const analytics = await ReviewService.getReviewAnalytics();

      res.json({
        success: true,
        message: 'آمار نظرات با موفقیت دریافت شد',
        data: { analytics },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get user's own reviews
  static async getUserReviews(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const { page, limit, sortBy } = req.query;

      const filters = {
        userId,
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined,
        sortBy: sortBy as any,
      };

      const result = await ReviewService.getReviews(filters);

      res.json({
        success: true,
        message: 'نظرات شما با موفقیت دریافت شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Check if user can review product
  static async canUserReviewProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { productId } = req.params;
      const userId = req.user!.id;

      // Check if user already reviewed this product
      const filters = { productId, userId, limit: 1 };
      const result = await ReviewService.getReviews(filters);
      const hasReviewed = result.total > 0;

      res.json({
        success: true,
        message: 'وضعیت امکان نظردهی بررسی شد',
        data: {
          canReview: !hasReviewed,
          hasReviewed,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default ReviewController;
