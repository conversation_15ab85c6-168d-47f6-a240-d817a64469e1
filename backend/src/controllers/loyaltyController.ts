import { Request, Response, NextFunction } from 'express';
import { LoyaltyTier, LoyaltyTransactionType } from '@prisma/client';
import LoyaltyService, { LoyaltyFilters, LoyaltyListOptions, CreateLoyaltyAccountData, EarnPointsData, RedeemPointsData } from '../services/loyaltyService';
import RewardService, { RewardFilters, RewardListOptions, RedemptionData } from '../services/rewardService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

export class LoyaltyController {
  // Get all loyalty accounts with filtering and pagination (admin only)
  static async getLoyaltyAccounts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        tier,
        minPoints,
        maxPoints,
        hasTransactions,
        createdFrom,
        createdTo,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        include = 'user,transactions,_count',
      } = req.query;

      // Parse filters
      const filters: LoyaltyFilters = {};
      if (tier) filters.tier = tier as LoyaltyTier;
      if (minPoints) filters.minPoints = parseInt(minPoints as string);
      if (maxPoints) filters.maxPoints = parseInt(maxPoints as string);
      if (hasTransactions !== undefined) filters.hasTransactions = hasTransactions === 'true';
      if (createdFrom) filters.createdFrom = new Date(createdFrom as string);
      if (createdTo) filters.createdTo = new Date(createdTo as string);
      if (search) filters.search = search as string;

      // Parse options
      const options: LoyaltyListOptions = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        include: {},
      };

      // Parse include options
      const includeOptions = (include as string).split(',');
      if (includeOptions.includes('user')) options.include!.user = true;
      if (includeOptions.includes('transactions')) options.include!.transactions = true;
      if (includeOptions.includes('_count')) options.include!._count = true;

      const result = await LoyaltyService.getLoyaltyAccounts(filters, options);

      res.json({
        success: true,
        message: 'لیست حساب‌های وفاداری با موفقیت دریافت شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get loyalty account by user ID
  static async getLoyaltyAccountByUserId(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId } = req.params;

      const account = await LoyaltyService.getLoyaltyAccountByUserId(userId);

      if (!account) {
        throw new AppError('حساب وفاداری یافت نشد', 404, 'LOYALTY_ACCOUNT_NOT_FOUND');
      }

      res.json({
        success: true,
        message: 'حساب وفاداری با موفقیت دریافت شد',
        data: { account },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get current user's loyalty account
  static async getMyLoyaltyAccount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHORIZED');
      }

      let account = await LoyaltyService.getLoyaltyAccountByUserId(userId);

      // Create account if it doesn't exist
      if (!account) {
        account = await LoyaltyService.createLoyaltyAccount({ userId });
      }

      res.json({
        success: true,
        message: 'حساب وفاداری شما با موفقیت دریافت شد',
        data: { account },
      });
    } catch (error) {
      next(error);
    }
  }

  // Create loyalty account (admin only)
  static async createLoyaltyAccount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const accountData: CreateLoyaltyAccountData = req.body;

      const account = await LoyaltyService.createLoyaltyAccount(accountData);

      res.status(201).json({
        success: true,
        message: 'حساب وفاداری با موفقیت ایجاد شد',
        data: { account },
      });
    } catch (error) {
      next(error);
    }
  }

  // Earn points (admin only or system)
  static async earnPoints(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const earnData: EarnPointsData = req.body;

      const transaction = await LoyaltyService.earnPoints(earnData);

      res.json({
        success: true,
        message: 'امتیاز با موفقیت اضافه شد',
        data: { transaction },
      });
    } catch (error) {
      next(error);
    }
  }

  // Redeem points
  static async redeemPoints(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const redeemData: RedeemPointsData = req.body;

      // If not admin, ensure user can only redeem their own points
      if (req.user?.role !== 'ADMIN' && req.user?.role !== 'SUPER_ADMIN') {
        redeemData.userId = req.user?.id || '';
      }

      const transaction = await LoyaltyService.redeemPoints(redeemData);

      res.json({
        success: true,
        message: 'امتیاز با موفقیت استفاده شد',
        data: { transaction },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get loyalty transactions
  static async getLoyaltyTransactions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        accountId,
        userId,
        type,
        orderId,
        dateFrom,
        dateTo,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = req.query;

      // If not admin, ensure user can only see their own transactions
      let finalUserId = userId as string;
      if (req.user?.role !== 'ADMIN' && req.user?.role !== 'SUPER_ADMIN') {
        finalUserId = req.user?.id || '';
      }

      const filters = {
        accountId: accountId as string,
        userId: finalUserId,
        type: type as LoyaltyTransactionType,
        orderId: orderId as string,
        dateFrom: dateFrom ? new Date(dateFrom as string) : undefined,
        dateTo: dateTo ? new Date(dateTo as string) : undefined,
      };

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
      };

      const result = await LoyaltyService.getLoyaltyTransactions(filters, options);

      res.json({
        success: true,
        message: 'تراکنش‌های امتیاز با موفقیت دریافت شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get loyalty statistics (admin only)
  static async getLoyaltyStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await LoyaltyService.getLoyaltyStatistics();

      res.json({
        success: true,
        message: 'آمار وفاداری با موفقیت دریافت شد',
        data: { statistics },
      });
    } catch (error) {
      next(error);
    }
  }

  // Adjust points (admin only)
  static async adjustPoints(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId, points, description } = req.body;
      const adminId = req.user?.id;

      if (!adminId) {
        throw new AppError('شناسه مدیر یافت نشد', 401, 'ADMIN_ID_REQUIRED');
      }

      const transaction = await LoyaltyService.adjustPoints(userId, points, description, adminId);

      res.json({
        success: true,
        message: 'امتیاز با موفقیت تعدیل شد',
        data: { transaction },
      });
    } catch (error) {
      next(error);
    }
  }

  // Expire points (admin only - typically called by cron job)
  static async expirePoints(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const result = await LoyaltyService.expirePoints();

      res.json({
        success: true,
        message: 'امتیازات منقضی شده با موفقیت پردازش شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get tier benefits
  static async getTierBenefits(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { tier } = req.params;

      if (!Object.values(LoyaltyTier).includes(tier as LoyaltyTier)) {
        throw new AppError('سطح وفاداری نامعتبر است', 400, 'INVALID_TIER');
      }

      const benefits = LoyaltyService.getTierBenefits(tier as LoyaltyTier);

      res.json({
        success: true,
        message: 'مزایای سطح وفاداری با موفقیت دریافت شد',
        data: { benefits },
      });
    } catch (error) {
      next(error);
    }
  }

  // Calculate points for order amount
  static async calculatePointsForOrder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { amount, tier = 'BRONZE' } = req.query;

      if (!amount) {
        throw new AppError('مبلغ سفارش الزامی است', 400, 'ORDER_AMOUNT_REQUIRED');
      }

      const orderAmount = parseFloat(amount as string);
      const points = LoyaltyService.calculatePointsForOrder(orderAmount, tier as LoyaltyTier);

      res.json({
        success: true,
        message: 'امتیاز قابل کسب محاسبه شد',
        data: { 
          orderAmount,
          tier,
          points,
          basePoints: Math.floor(orderAmount / 10000),
          multiplier: LoyaltyService.getTierBenefits(tier as LoyaltyTier).multiplier,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get rewards catalog
  static async getRewards(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        type,
        isActive,
        minPointsCost,
        maxPointsCost,
        hasStock,
        validOnly,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = req.query;

      // Parse filters
      const filters: RewardFilters = {};
      if (type) filters.type = type as any;
      if (isActive !== undefined) filters.isActive = isActive === 'true';
      if (minPointsCost) filters.minPointsCost = parseInt(minPointsCost as string);
      if (maxPointsCost) filters.maxPointsCost = parseInt(maxPointsCost as string);
      if (hasStock !== undefined) filters.hasStock = hasStock === 'true';
      if (validOnly !== undefined) filters.validOnly = validOnly === 'true';
      if (search) filters.search = search as string;

      // Parse options
      const options: RewardListOptions = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
      };

      const result = await RewardService.getRewards(filters, options);

      res.json({
        success: true,
        message: 'لیست جوایز با موفقیت دریافت شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get available rewards for current user
  static async getMyAvailableRewards(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHORIZED');
      }

      const rewards = await RewardService.getAvailableRewardsForUser(userId);

      res.json({
        success: true,
        message: 'جوایز قابل دسترس شما با موفقیت دریافت شد',
        data: { rewards },
      });
    } catch (error) {
      next(error);
    }
  }

  // Redeem reward
  static async redeemReward(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const redemptionData: RedemptionData = req.body;

      // If not admin, ensure user can only redeem for themselves
      if (req.user?.role !== 'ADMIN' && req.user?.role !== 'SUPER_ADMIN') {
        redemptionData.userId = req.user?.id || '';
      }

      const redemption = await RewardService.redeemReward(redemptionData);

      res.json({
        success: true,
        message: 'جایزه با موفقیت دریافت شد',
        data: { redemption },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default LoyaltyController;
