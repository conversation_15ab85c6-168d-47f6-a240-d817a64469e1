import { Request, Response, NextFunction } from 'express';
import { CategoryService, CreateCategoryData, UpdateCategoryData, CategoryFilters } from '../services/categoryService';
import UploadService from '../services/uploadService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

export class CategoryController {
  // Get all categories with filtering and pagination
  static async getCategories(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 50,
        search,
        parentId,
        isActive,
        sortBy = 'sortOrder',
        sortOrder = 'asc',
        include = 'parent,children,_count'
      } = req.query;

      // Parse filters
      const filters: CategoryFilters = {};
      if (search) filters.search = search as string;
      if (parentId !== undefined) filters.parentId = parentId === 'null' ? undefined : parentId as string;
      if (isActive !== undefined) filters.isActive = isActive === 'true';

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      // Get categories
      const result = await CategoryService.getCategories(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'دسته‌بندی‌ها با موفقیت دریافت شد',
        data: result.categories,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get category tree (hierarchical structure)
  static async getCategoryTree(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const categories = await CategoryService.getCategoryTree();

      res.json({
        success: true,
        message: 'درخت دسته‌بندی‌ها با موفقیت دریافت شد',
        data: categories,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get category by ID
  static async getCategoryById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { include = 'parent,children,products,_count' } = req.query;

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      const category = await CategoryService.getCategoryById(id, {
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'دسته‌بندی با موفقیت دریافت شد',
        data: category,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get category by slug
  static async getCategoryBySlug(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { slug } = req.params;
      const { include = 'parent,children,products,_count' } = req.query;

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      const category = await CategoryService.getCategoryBySlug(slug, {
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'دسته‌بندی با موفقیت دریافت شد',
        data: category,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get category breadcrumbs
  static async getCategoryBreadcrumbs(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const breadcrumbs = await CategoryService.getCategoryBreadcrumbs(id);

      res.json({
        success: true,
        message: 'مسیر دسته‌بندی با موفقیت دریافت شد',
        data: breadcrumbs,
      });
    } catch (error) {
      next(error);
    }
  }

  // Create new category (admin only)
  static async createCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const categoryData: CreateCategoryData = req.body;

      // Validate required fields
      if (!categoryData.name || !categoryData.slug) {
        throw new AppError('فیلدهای الزامی کامل نیست', 400, 'MISSING_REQUIRED_FIELDS');
      }

      const category = await CategoryService.createCategory(categoryData);

      logger.info(`Category created by admin: ${req.user?.email}, category: ${category.name}`);

      res.status(201).json({
        success: true,
        message: 'دسته‌بندی با موفقیت ایجاد شد',
        data: category,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update category (admin only)
  static async updateCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: UpdateCategoryData = req.body;

      const category = await CategoryService.updateCategory(id, updateData);

      logger.info(`Category updated by admin: ${req.user?.email}, category: ${category.name}`);

      res.json({
        success: true,
        message: 'دسته‌بندی با موفقیت به‌روزرسانی شد',
        data: category,
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete category (admin only)
  static async deleteCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await CategoryService.deleteCategory(id);

      logger.info(`Category deleted by admin: ${req.user?.email}, categoryId: ${id}`);

      res.json({
        success: true,
        message: 'دسته‌بندی با موفقیت حذف شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Upload category image (admin only)
  static async uploadCategoryImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const file = req.file;

      if (!file) {
        throw new AppError('هیچ فایلی آپلود نشده است', 400, 'NO_FILE_UPLOADED');
      }

      // Validate uploaded file
      UploadService.validateUploadedFile(file);

      // Get file info
      const imageInfo = UploadService.getFileInfo(file, 'categories');

      // Update category with new image URL
      const category = await CategoryService.updateCategory(id, {
        image: imageInfo.url,
      });

      logger.info(`Category image uploaded by admin: ${req.user?.email}, categoryId: ${id}`);

      res.json({
        success: true,
        message: 'تصویر دسته‌بندی با موفقیت آپلود شد',
        data: {
          category,
          image: imageInfo,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Remove category image (admin only)
  static async removeCategoryImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      // Get current category to find image URL
      const currentCategory = await CategoryService.getCategoryById(id);

      if (!currentCategory.image) {
        throw new AppError('دسته‌بندی تصویری ندارد', 400, 'NO_IMAGE_TO_REMOVE');
      }

      // Extract filename from URL and delete file
      const urlParts = currentCategory.image.split('/');
      const filename = urlParts[urlParts.length - 1];
      await UploadService.deleteFile(filename, 'categories');

      // Update category to remove image URL
      const category = await CategoryService.updateCategory(id, {
        image: undefined,
      });

      logger.info(`Category image removed by admin: ${req.user?.email}, categoryId: ${id}`);

      res.json({
        success: true,
        message: 'تصویر دسته‌بندی با موفقیت حذف شد',
        data: category,
      });
    } catch (error) {
      next(error);
    }
  }
}

export default CategoryController;
