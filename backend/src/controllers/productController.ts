import { Request, Response, NextFunction } from 'express';
import { ProductService, CreateProductData, UpdateProductData, ProductFilters } from '../services/productService';
import UploadService from '../services/uploadService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

export class ProductController {
  // Get all products with filtering and pagination
  static async getProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        categoryId,
        brandId,
        isActive,
        isFeatured,
        isDigital,
        minPrice,
        maxPrice,
        tags,
        inStock,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        include = 'brand,categories,images,inventory'
      } = req.query;

      // Parse filters
      const filters: ProductFilters = {};
      if (search) filters.search = search as string;
      if (categoryId) filters.categoryId = categoryId as string;
      if (brandId) filters.brandId = brandId as string;
      if (isActive !== undefined) filters.isActive = isActive === 'true';
      if (isFeatured !== undefined) filters.isFeatured = isFeatured === 'true';
      if (isDigital !== undefined) filters.isDigital = isDigital === 'true';
      if (minPrice) filters.minPrice = parseFloat(minPrice as string);
      if (maxPrice) filters.maxPrice = parseFloat(maxPrice as string);
      if (tags) filters.tags = (tags as string).split(',');
      if (inStock !== undefined) filters.inStock = inStock === 'true';

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      // Get products
      const result = await ProductService.getProducts(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'محصولات با موفقیت دریافت شد',
        data: result.products,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get product by ID
  static async getProductById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { include = 'brand,categories,images,variants,inventory,reviews' } = req.query;

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      const product = await ProductService.getProductById(id, {
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'محصول با موفقیت دریافت شد',
        data: product,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get product by slug
  static async getProductBySlug(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { slug } = req.params;
      const { include = 'brand,categories,images,variants,inventory,reviews' } = req.query;

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      const product = await ProductService.getProductBySlug(slug, {
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'محصول با موفقیت دریافت شد',
        data: product,
      });
    } catch (error) {
      next(error);
    }
  }

  // Create new product (admin only)
  static async createProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const productData: CreateProductData = req.body;

      // Validate required fields
      if (!productData.name || !productData.slug || !productData.sku || !productData.price) {
        throw new AppError('فیلدهای الزامی کامل نیست', 400, 'MISSING_REQUIRED_FIELDS');
      }

      const product = await ProductService.createProduct(productData);

      logger.info(`Product created by admin: ${req.user?.email}, product: ${product.name}`);

      res.status(201).json({
        success: true,
        message: 'محصول با موفقیت ایجاد شد',
        data: product,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update product (admin only)
  static async updateProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: UpdateProductData = req.body;

      const product = await ProductService.updateProduct(id, updateData);

      logger.info(`Product updated by admin: ${req.user?.email}, product: ${product.name}`);

      res.json({
        success: true,
        message: 'محصول با موفقیت به‌روزرسانی شد',
        data: product,
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete product (admin only)
  static async deleteProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await ProductService.deleteProduct(id);

      logger.info(`Product deleted by admin: ${req.user?.email}, productId: ${id}`);

      res.json({
        success: true,
        message: 'محصول با موفقیت حذف شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Upload product images (admin only)
  static async uploadProductImages(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const files = req.files as { [fieldname: string]: Express.Multer.File[] };

      if (!files || (!files.primaryImage && !files.galleryImages)) {
        throw new AppError('هیچ فایلی آپلود نشده است', 400, 'NO_FILES_UPLOADED');
      }

      const imagesToAdd: Array<{
        url: string;
        alt?: string;
        isPrimary?: boolean;
        sortOrder?: number;
      }> = [];

      // Process primary image
      if (files.primaryImage && files.primaryImage[0]) {
        const file = files.primaryImage[0];
        UploadService.validateUploadedFile(file);
        
        const imageInfo = UploadService.getFileInfo(file, 'products');
        imagesToAdd.push({
          url: imageInfo.url,
          alt: `تصویر اصلی محصول`,
          isPrimary: true,
          sortOrder: 0,
        });
      }

      // Process gallery images
      if (files.galleryImages) {
        UploadService.validateUploadedFiles(files.galleryImages);
        
        files.galleryImages.forEach((file, index) => {
          const imageInfo = UploadService.getFileInfo(file, 'products');
          imagesToAdd.push({
            url: imageInfo.url,
            alt: `تصویر گالری محصول ${index + 1}`,
            isPrimary: false,
            sortOrder: index + 1,
          });
        });
      }

      const createdImages = await ProductService.addProductImages(id, imagesToAdd);

      logger.info(`Product images uploaded by admin: ${req.user?.email}, productId: ${id}, count: ${createdImages.length}`);

      res.json({
        success: true,
        message: 'تصاویر محصول با موفقیت آپلود شد',
        data: createdImages,
      });
    } catch (error) {
      next(error);
    }
  }

  // Remove product image (admin only)
  static async removeProductImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id, imageId } = req.params;

      await ProductService.removeProductImage(id, imageId);

      logger.info(`Product image removed by admin: ${req.user?.email}, productId: ${id}, imageId: ${imageId}`);

      res.json({
        success: true,
        message: 'تصویر محصول با موفقیت حذف شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Update product inventory (admin only)
  static async updateInventory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity, lowStockThreshold, location } = req.body;

      const inventory = await ProductService.updateInventory(id, {
        quantity,
        lowStockThreshold,
        location,
      });

      logger.info(`Product inventory updated by admin: ${req.user?.email}, productId: ${id}, quantity: ${inventory.quantity}`);

      res.json({
        success: true,
        message: 'موجودی محصول با موفقیت به‌روزرسانی شد',
        data: inventory,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get low stock products (admin only)
  static async getLowStockProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const products = await ProductService.getLowStockProducts();

      res.json({
        success: true,
        message: 'محصولات کم‌موجود با موفقیت دریافت شد',
        data: products,
      });
    } catch (error) {
      next(error);
    }
  }

  // Bulk update products (admin only)
  static async bulkUpdateProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { productIds, ...updateData } = req.body;

      if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
        throw new AppError('شناسه محصولات الزامی است', 400, 'MISSING_PRODUCT_IDS');
      }

      const updatedCount = await ProductService.bulkUpdateProducts(productIds, updateData);

      logger.info(`Bulk update performed by admin: ${req.user?.email}, products: ${updatedCount}`);

      res.json({
        success: true,
        message: `${updatedCount} محصول با موفقیت به‌روزرسانی شد`,
        data: { updatedCount },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default ProductController;
