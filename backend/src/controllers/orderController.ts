import { Request, Response, NextFunction } from 'express';
import { OrderService, CreateOrderData, UpdateOrderData, OrderFilters } from '../services/orderService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

export class OrderController {
  // Get all orders (admin only)
  static async getOrders(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        userId,
        status,
        paymentStatus,
        fulfillmentStatus,
        dateFrom,
        dateTo,
        minAmount,
        maxAmount,
        tags,
        hasTrackingNumber,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        include = 'user,items,shippingAddress'
      } = req.query;

      // Parse filters
      const filters: OrderFilters = {};
      if (search) filters.search = search as string;
      if (userId) filters.userId = userId as string;
      if (status) filters.status = status as any;
      if (paymentStatus) filters.paymentStatus = paymentStatus as any;
      if (fulfillmentStatus) filters.fulfillmentStatus = fulfillmentStatus as any;
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);
      if (minAmount) filters.minAmount = parseFloat(minAmount as string);
      if (maxAmount) filters.maxAmount = parseFloat(maxAmount as string);
      if (tags) filters.tags = (tags as string).split(',');
      if (hasTrackingNumber !== undefined) filters.hasTrackingNumber = hasTrackingNumber === 'true';

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      // Get orders
      const result = await OrderService.getOrders(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'سفارشات با موفقیت دریافت شد',
        data: result.orders,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get order by ID (admin or order owner)
  static async getOrderById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { include = 'user,items,payments,shippingAddress,notifications' } = req.query;

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      const order = await OrderService.getOrderById(id, {
        include: includeOptions,
      });

      // Check if user can access this order
      if (req.user?.role !== 'ADMIN' && req.user?.role !== 'SUPER_ADMIN') {
        if (order.userId !== req.user?.id) {
          throw new AppError('شما مجاز به مشاهده این سفارش نیستید', 403, 'ORDER_ACCESS_DENIED');
        }
      }

      res.json({
        success: true,
        message: 'سفارش با موفقیت دریافت شد',
        data: order,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get order by order number (admin or order owner)
  static async getOrderByNumber(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { orderNumber } = req.params;
      const { include = 'user,items,payments,shippingAddress' } = req.query;

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      const order = await OrderService.getOrderByNumber(orderNumber, {
        include: includeOptions,
      });

      // Check if user can access this order
      if (req.user?.role !== 'ADMIN' && req.user?.role !== 'SUPER_ADMIN') {
        if (order.userId !== req.user?.id) {
          throw new AppError('شما مجاز به مشاهده این سفارش نیستید', 403, 'ORDER_ACCESS_DENIED');
        }
      }

      res.json({
        success: true,
        message: 'سفارش با موفقیت دریافت شد',
        data: order,
      });
    } catch (error) {
      next(error);
    }
  }

  // Create new order (authenticated users or guests)
  static async createOrder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const orderData: CreateOrderData = {
        ...req.body,
        userId: req.user?.id, // Will be undefined for guest orders
      };

      // Validate required fields
      if (!orderData.items || orderData.items.length === 0) {
        throw new AppError('سفارش باید حداقل یک آیتم داشته باشد', 400, 'MISSING_ORDER_ITEMS');
      }

      // For guest orders, email is required
      if (!orderData.userId && !orderData.guestEmail) {
        throw new AppError('برای سفارش مهمان، ایمیل الزامی است', 400, 'MISSING_GUEST_EMAIL');
      }

      // Shipping address is required
      if (!orderData.shippingAddressId && !orderData.shippingAddress) {
        throw new AppError('آدرس ارسال الزامی است', 400, 'MISSING_SHIPPING_ADDRESS');
      }

      const order = await OrderService.createOrder(orderData);

      logger.info(`Order created: ${order.orderNumber} by ${req.user?.email || orderData.guestEmail}`);

      res.status(201).json({
        success: true,
        message: 'سفارش با موفقیت ایجاد شد',
        data: order,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update order (admin only)
  static async updateOrder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: UpdateOrderData = req.body;

      const order = await OrderService.updateOrder(id, updateData);

      logger.info(`Order updated by admin: ${req.user?.email}, order: ${order.orderNumber}`);

      res.json({
        success: true,
        message: 'سفارش با موفقیت به‌روزرسانی شد',
        data: order,
      });
    } catch (error) {
      next(error);
    }
  }

  // Cancel order (admin or order owner)
  static async cancelOrder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      // For non-admin users, pass userId to check ownership
      const userId = req.user?.role === 'ADMIN' || req.user?.role === 'SUPER_ADMIN' 
        ? undefined 
        : req.user?.id;

      const order = await OrderService.cancelOrder(id, reason, userId);

      logger.info(`Order cancelled: ${order.orderNumber} by ${req.user?.email}, reason: ${reason || 'No reason provided'}`);

      res.json({
        success: true,
        message: 'سفارش با موفقیت لغو شد',
        data: order,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get customer orders (authenticated user only)
  static async getCustomerOrders(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 10,
        include = 'items,shippingAddress'
      } = req.query;

      if (!req.user?.id) {
        throw new AppError('احراز هویت الزامی است', 401, 'AUTHENTICATION_REQUIRED');
      }

      // Parse include options
      const includeOptions = (include as string).split(',').reduce((acc, item) => {
        acc[item.trim()] = true;
        return acc;
      }, {} as any);

      const result = await OrderService.getCustomerOrders(req.user.id, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        include: includeOptions,
      });

      res.json({
        success: true,
        message: 'سفارشات شما با موفقیت دریافت شد',
        data: result.orders,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get order statistics (admin only)
  static async getOrderStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { dateFrom, dateTo } = req.query;

      // Build date filter
      const dateFilter: any = {};
      if (dateFrom) dateFilter.gte = new Date(dateFrom as string);
      if (dateTo) dateFilter.lte = new Date(dateTo as string);

      const whereClause = Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {};

      // Get statistics using raw queries for better performance
      const [
        totalOrders,
        totalRevenue,
        statusCounts,
        paymentStatusCounts,
        recentOrders
      ] = await Promise.all([
        // Total orders count
        OrderService.getOrders({}, { limit: 1 }).then(result => result.pagination.total),
        
        // Total revenue (completed orders only)
        OrderService.getOrders({ paymentStatus: 'COMPLETED' }, { limit: 1 })
          .then(result => result.orders.reduce((sum, order) => sum + Number(order.totalAmount), 0)),
        
        // Status distribution
        OrderService.getOrders({}, { limit: 1000 })
          .then(result => {
            const counts: Record<string, number> = {};
            result.orders.forEach(order => {
              counts[order.status] = (counts[order.status] || 0) + 1;
            });
            return counts;
          }),
        
        // Payment status distribution
        OrderService.getOrders({}, { limit: 1000 })
          .then(result => {
            const counts: Record<string, number> = {};
            result.orders.forEach(order => {
              counts[order.paymentStatus] = (counts[order.paymentStatus] || 0) + 1;
            });
            return counts;
          }),
        
        // Recent orders
        OrderService.getOrders({}, { 
          limit: 10, 
          sortBy: 'createdAt', 
          sortOrder: 'desc',
          include: { user: true, items: true }
        }).then(result => result.orders)
      ]);

      const statistics = {
        overview: {
          totalOrders,
          totalRevenue,
          averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0,
        },
        statusDistribution: statusCounts,
        paymentStatusDistribution: paymentStatusCounts,
        recentOrders,
      };

      res.json({
        success: true,
        message: 'آمار سفارشات با موفقیت دریافت شد',
        data: statistics,
      });
    } catch (error) {
      next(error);
    }
  }
}

export default OrderController;
