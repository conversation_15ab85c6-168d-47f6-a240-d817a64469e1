import { Server } from 'http';
import app from './app';
import { config } from './config';
import { logger } from './config/logger';
// Use real database now that VPS connection is working
import { connectDatabase, disconnectDatabase } from './config/database';
import EmailService from './services/emailService';
// import { connectRedis, disconnectRedis } from './config/redis';

// Server instance
let server: Server;

// Graceful shutdown handler
const gracefulShutdown = async (signal: string): Promise<void> => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  // Close HTTP server
  if (server) {
    server.close(async () => {
      logger.info('HTTP server closed');

      try {
        // Disconnect from database
        await disconnectDatabase();

        // Disconnect from Redis (disabled for testing)
        // await disconnectRedis();

        logger.info('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('❌ Error during graceful shutdown:', error);
        process.exit(1);
      }
    });
  } else {
    process.exit(0);
  }
};

// Start server
const startServer = async (): Promise<void> => {
  try {
    // Connect to database
    await connectDatabase();

    // Initialize email service
    await EmailService.initialize();

    // Connect to Redis (disabled for testing)
    // await connectRedis();

    // Start HTTP server
    server = app.listen(config.server.port, config.server.host, () => {
      logger.info(`🚀 Server running on ${config.server.host}:${config.server.port}`);
      logger.info(`📚 API Documentation: http://${config.server.host}:${config.server.port}${config.server.apiPrefix}/docs`);
      logger.info(`🏥 Health Check: http://${config.server.host}:${config.server.port}/health`);
      logger.info(`🌍 Environment: ${config.server.env}`);
    });

    // Handle server errors
    server.on('error', (error: Error) => {
      logger.error('Server error:', error);
    });

  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Handle uncaught exceptions
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle graceful shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start the server
startServer();