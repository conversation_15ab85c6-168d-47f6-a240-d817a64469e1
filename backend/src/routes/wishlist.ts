import { Router } from 'express';
import { authenticate } from '../middleware/auth';

const router = Router();

router.get('/', authenticate, (req, res) => {
  res.status(501).json({
    success: false,
    message: 'Wishlist endpoint not implemented yet',
    note: 'Will be implemented in future tasks'
  });
});

router.post('/', authenticate, (req, res) => {
  res.status(501).json({
    success: false,
    message: 'Add to wishlist endpoint not implemented yet',
    note: 'Will be implemented in future tasks'
  });
});

export default router;
