import { Router } from 'express';
import { authenticate, adminOnly } from '../middleware/auth';
import LoyaltyController from '../controllers/loyaltyController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';

const router = Router();

// All loyalty routes require authentication
router.use(authenticate);

// === LOYALTY ACCOUNT MANAGEMENT ===

// Get all loyalty accounts (admin only)
router.get('/accounts',
  adminOnly,
  LoyaltyController.getLoyaltyAccounts
);

// Get loyalty statistics (admin only)
router.get('/statistics',
  adminOnly,
  LoyaltyController.getLoyaltyStatistics
);

// Get current user's loyalty account
router.get('/my-account',
  LoyaltyController.getMyLoyaltyAccount
);

// Create loyalty account (admin only)
router.post('/accounts',
  adminOnly,
  ValidationSets.createLoyaltyAccount(),
  handleValidationErrors,
  LoyaltyController.createLoyaltyAccount
);

// Get loyalty account by user ID (admin only)
router.get('/accounts/user/:userId',
  adminOnly,
  LoyaltyController.getLoyaltyAccountByUserId
);

// === POINTS MANAGEMENT ===

// Earn points (admin only or system)
router.post('/points/earn',
  adminOnly,
  ValidationSets.earnPoints(),
  handleValidationErrors,
  LoyaltyController.earnPoints
);

// Redeem points
router.post('/points/redeem',
  ValidationSets.redeemPoints(),
  handleValidationErrors,
  LoyaltyController.redeemPoints
);

// Adjust points (admin only)
router.post('/points/adjust',
  adminOnly,
  ValidationSets.adjustPoints(),
  handleValidationErrors,
  LoyaltyController.adjustPoints
);

// Expire points (admin only - typically called by cron job)
router.post('/points/expire',
  adminOnly,
  LoyaltyController.expirePoints
);

// Calculate points for order amount
router.get('/points/calculate',
  LoyaltyController.calculatePointsForOrder
);

// === TRANSACTIONS ===

// Get loyalty transactions
router.get('/transactions',
  LoyaltyController.getLoyaltyTransactions
);

// === TIERS ===

// Get tier benefits
router.get('/tiers/:tier/benefits',
  LoyaltyController.getTierBenefits
);

// === REWARDS ===

// Get rewards catalog
router.get('/rewards',
  LoyaltyController.getRewards
);

// Get available rewards for current user
router.get('/my-rewards',
  LoyaltyController.getMyAvailableRewards
);

// Redeem reward
router.post('/rewards/redeem',
  ValidationSets.redeemReward(),
  handleValidationErrors,
  LoyaltyController.redeemReward
);

export default router;
