import { Router, Request, Response } from 'express';
// Use real database now that VPS connection is working
import { checkDatabaseHealth } from '../config/database';
// import { checkRedisHealth } from '../config/redis';
import { config } from '../config';

const router = Router();

// Health check interface
interface HealthCheck {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  environment: string;
  version: string;
  services: {
    database: {
      status: 'connected' | 'disconnected';
      responseTime?: number;
    };
    redis: {
      status: 'connected' | 'disconnected';
      responseTime?: number;
    };
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
}

// Basic health check
router.get('/', async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    // Check database
    const dbStart = Date.now();
    const isDatabaseHealthy = await checkDatabaseHealth();
    const dbResponseTime = Date.now() - dbStart;

    // Check Redis (disabled for testing)
    const redisStart = Date.now();
    const isRedisHealthy = true; // Mock Redis as healthy
    const redisResponseTime = Date.now() - redisStart;

    // Memory usage
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.heapTotal;
    const usedMemory = memoryUsage.heapUsed;

    // Overall status
    const isHealthy = isDatabaseHealthy && isRedisHealthy;

    const healthCheck: HealthCheck = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.server.env,
      version: config.server.apiVersion,
      services: {
        database: {
          status: isDatabaseHealthy ? 'connected' : 'disconnected',
          responseTime: dbResponseTime,
        },
        redis: {
          status: isRedisHealthy ? 'connected' : 'disconnected',
          responseTime: redisResponseTime,
        },
      },
      memory: {
        used: Math.round(usedMemory / 1024 / 1024), // MB
        total: Math.round(totalMemory / 1024 / 1024), // MB
        percentage: Math.round((usedMemory / totalMemory) * 100),
      },
    };

    const statusCode = isHealthy ? 200 : 503;
    res.status(statusCode).json(healthCheck);
  } catch (error) {
    const healthCheck: HealthCheck = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.server.env,
      version: config.server.apiVersion,
      services: {
        database: {
          status: 'disconnected',
        },
        redis: {
          status: 'disconnected',
        },
      },
      memory: {
        used: 0,
        total: 0,
        percentage: 0,
      },
    };

    res.status(503).json(healthCheck);
  }
});

// Liveness probe (simple check)
router.get('/live', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
  });
});

// Readiness probe (checks dependencies)
router.get('/ready', async (req: Request, res: Response) => {
  try {
    const isDatabaseHealthy = await checkDatabaseHealth();
    const isRedisHealthy = true; // Mock Redis as healthy
    
    const isReady = isDatabaseHealthy && isRedisHealthy;
    
    res.status(isReady ? 200 : 503).json({
      status: isReady ? 'ready' : 'not ready',
      timestamp: new Date().toISOString(),
      services: {
        database: isDatabaseHealthy,
        redis: isRedisHealthy,
      },
    });
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: 'Service check failed',
    });
  }
});

export default router;
