import { Router } from 'express';
import { authenticate, adminOnly, optionalAuth } from '../middleware/auth';
import CategoryController from '../controllers/categoryController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';
import { uploadSingleImage } from '../services/uploadService';

const router = Router();

// Public routes (with optional authentication)

// Get category tree (hierarchical structure)
router.get('/tree', optionalAuth, CategoryController.getCategoryTree);

// Get all categories with filtering and pagination
router.get('/', optionalAuth, CategoryController.getCategories);

// Get category by slug (SEO-friendly)
router.get('/slug/:slug', optionalAuth, CategoryController.getCategoryBySlug);

// Get category breadcrumbs
router.get('/:id/breadcrumbs', optionalAuth, CategoryController.getCategoryBreadcrumbs);

// Get category by ID
router.get('/:id', optionalAuth, CategoryController.getCategoryById);

// Admin-only routes (require authentication and admin role)

// Create new category
router.post('/',
  authenticate,
  adminOnly,
  ValidationSets.createCategory(),
  handleValidationErrors,
  CategoryController.createCategory
);

// Update category
router.put('/:id',
  authenticate,
  adminOnly,
  ValidationSets.updateCategory(),
  handleValidationErrors,
  CategoryController.updateCategory
);

// Delete category
router.delete('/:id',
  authenticate,
  adminOnly,
  CategoryController.deleteCategory
);

// Upload category image
router.post('/:id/image',
  authenticate,
  adminOnly,
  uploadSingleImage,
  CategoryController.uploadCategoryImage
);

// Remove category image
router.delete('/:id/image',
  authenticate,
  adminOnly,
  CategoryController.removeCategoryImage
);

export default router;
