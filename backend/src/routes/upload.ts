import { Router } from 'express';
import { authenticate, adminOnly } from '../middleware/auth';
import UploadController from '../controllers/uploadController';
import {
  uploadSingleImage,
  uploadMultipleImages,
  uploadProductImages
} from '../services/uploadService';

const router = Router();

// All upload routes require admin authentication

// Upload single image
router.post('/image',
  authenticate,
  adminOnly,
  uploadSingleImage,
  UploadController.uploadSingleImage
);

// Upload multiple images
router.post('/images',
  authenticate,
  adminOnly,
  uploadMultipleImages,
  UploadController.uploadMultipleImages
);

// Upload product images (primary + gallery)
router.post('/product-images',
  authenticate,
  adminOnly,
  uploadProductImages,
  UploadController.uploadProductImages
);

// Upload category image
router.post('/category-image',
  authenticate,
  adminOnly,
  uploadSingleImage,
  UploadController.uploadCategoryImage
);

// Upload brand image
router.post('/brand-image',
  authenticate,
  adminOnly,
  uploadSingleImage,
  UploadController.uploadBrandImage
);

// Upload temporary image (for editors, etc.)
router.post('/temp-image',
  authenticate,
  adminOnly,
  uploadSingleImage,
  UploadController.uploadTempImage
);

// Delete uploaded file
router.delete('/file/:filename',
  authenticate,
  adminOnly,
  UploadController.deleteFile
);

// Get file info
router.get('/file/:filename',
  authenticate,
  adminOnly,
  UploadController.getFileInfo
);

export default router;
