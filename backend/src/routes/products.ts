import { Router } from 'express';
import { authenticate, adminOnly, optionalAuth } from '../middleware/auth';
import ProductController from '../controllers/productController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';
import { uploadProductImages } from '../services/uploadService';

const router = Router();

// Public routes (with optional authentication for personalization)

// Get all products with filtering and pagination
router.get('/', optionalAuth, ProductController.getProducts);

// Get product by slug (SEO-friendly)
router.get('/slug/:slug', optionalAuth, ProductController.getProductBySlug);

// Get product by ID
router.get('/:id', optionalAuth, ProductController.getProductById);

// Admin-only routes (require authentication and admin role)

// Create new product
router.post('/',
  authenticate,
  adminOnly,
  ValidationSets.createProduct(),
  handleValidationErrors,
  ProductController.createProduct
);

// Update product
router.put('/:id',
  authenticate,
  adminOnly,
  ValidationSets.updateProduct(),
  handleValidationErrors,
  ProductController.updateProduct
);

// Delete product
router.delete('/:id',
  authenticate,
  adminOnly,
  ProductController.deleteProduct
);

// Upload product images (primary + gallery)
router.post('/:id/images',
  authenticate,
  adminOnly,
  uploadProductImages,
  ProductController.uploadProductImages
);

// Remove product image
router.delete('/:id/images/:imageId',
  authenticate,
  adminOnly,
  ProductController.removeProductImage
);

// Update product inventory
router.put('/:id/inventory',
  authenticate,
  adminOnly,
  ProductController.updateInventory
);

// Get low stock products
router.get('/admin/low-stock',
  authenticate,
  adminOnly,
  ProductController.getLowStockProducts
);

// Bulk update products
router.patch('/admin/bulk-update',
  authenticate,
  adminOnly,
  ProductController.bulkUpdateProducts
);

export default router;
