import { Router } from 'express';
import { authenticate, adminOnly } from '../middleware/auth';
import UserController from '../controllers/userController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';

const router = Router();

// Get current user profile
router.get('/profile',
  authenticate,
  UserController.getProfile
);

// Update user profile
router.put('/profile',
  authenticate,
  ValidationSets.updateProfile(),
  handleValidationErrors,
  UserController.updateProfile
);

// Change password
router.put('/change-password',
  authenticate,
  ValidationSets.changePassword(),
  handleValidationErrors,
  UserController.changePassword
);

// Add address
router.post('/addresses',
  authenticate,
  ValidationSets.addAddress(),
  handleValidationErrors,
  UserController.addAddress
);

// Update address
router.put('/addresses/:id',
  authenticate,
  ValidationSets.updateAddress(),
  handleValidationErrors,
  UserController.updateAddress
);

// Delete address
router.delete('/addresses/:id',
  authenticate,
  UserController.deleteAddress
);

// Set default address
router.put('/addresses/:id/default',
  authenticate,
  UserController.setDefaultAddress
);

// Admin routes
// Get all users (admin only)
router.get('/',
  authenticate,
  adminOnly,
  UserController.getAllUsers
);

// Get user by ID (admin only)
router.get('/:id',
  authenticate,
  adminOnly,
  UserController.getUserById
);

// Update user status (admin only)
router.put('/:id/status',
  authenticate,
  adminOnly,
  UserController.updateUserStatus
);

export default router;
