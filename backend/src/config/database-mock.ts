// Mock database configuration for testing without actual database connection
import { logger } from './logger';

// Mock Prisma client for testing
export const mockPrisma = {
  $connect: async () => {
    logger.info('✅ Mock database connected successfully');
    return Promise.resolve();
  },
  $disconnect: async () => {
    logger.info('✅ Mock database disconnected successfully');
    return Promise.resolve();
  },
  $queryRaw: async () => {
    return Promise.resolve([{ result: 1 }]);
  },
  user: {
    findUnique: async (args: any) => {
      // Mock user for testing
      if (args?.where?.id) {
        return {
          id: args.where.id,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          role: 'CUSTOMER' as any,
          status: 'ACTIVE',
          isEmailVerified: true,
        };
      }
      return null;
    },
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
  },
  product: {
    findMany: async () => [],
    findUnique: async () => null,
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
  },
};

// Mock database connection functions
export const connectDatabase = async (): Promise<void> => {
  try {
    logger.info('✅ Mock database connected successfully');
    logger.info('✅ Mock database query test successful');
  } catch (error) {
    logger.error('❌ Mock database connection failed:', error);
    throw error;
  }
};

export const disconnectDatabase = async (): Promise<void> => {
  try {
    logger.info('✅ Mock database disconnected successfully');
  } catch (error) {
    logger.error('❌ Mock database disconnection failed:', error);
    throw error;
  }
};

export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    return true;
  } catch (error) {
    logger.error('Mock database health check failed:', error);
    return false;
  }
};

export const withTransaction = async <T>(
  callback: (prisma: any) => Promise<T>
): Promise<T> => {
  return await callback(mockPrisma);
};

export { mockPrisma as prisma };
export default mockPrisma;
