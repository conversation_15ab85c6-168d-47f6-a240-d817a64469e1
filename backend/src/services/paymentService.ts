import { Payment, PaymentMethod, PaymentStatus, Prisma } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

// Types for payment operations
export interface CreatePaymentData {
  orderId: string;
  amount: number;
  method: PaymentMethod;
  gateway?: string;
  gatewayTransactionId?: string;
  gatewayResponse?: any;
}

export interface UpdatePaymentData {
  status?: PaymentStatus;
  gatewayTransactionId?: string;
  gatewayResponse?: any;
  failureReason?: string;
  processedAt?: Date;
}

export interface PaymentFilters {
  orderId?: string;
  status?: PaymentStatus;
  method?: PaymentMethod;
  gateway?: string;
  dateFrom?: Date;
  dateTo?: Date;
  minAmount?: number;
  maxAmount?: number;
}

export interface PaymentQueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  include?: {
    order?: boolean;
  };
}

// Payment with relations type
export type PaymentWithRelations = Payment & {
  order?: any;
};

export class PaymentService {
  // Create a new payment
  static async createPayment(data: CreatePaymentData): Promise<PaymentWithRelations> {
    try {
      // Validate order exists
      const order = await prisma.order.findUnique({
        where: { id: data.orderId },
      });

      if (!order) {
        throw new AppError('سفارش یافت نشد', 404, 'ORDER_NOT_FOUND');
      }

      // Validate amount
      if (data.amount <= 0) {
        throw new AppError('مبلغ پرداخت باید مثبت باشد', 400, 'INVALID_AMOUNT');
      }

      if (data.amount > Number(order.totalAmount)) {
        throw new AppError('مبلغ پرداخت نمی‌تواند بیش از مبلغ سفارش باشد', 400, 'AMOUNT_EXCEEDS_ORDER');
      }

      // Check if order already has a successful payment
      const existingPayment = await prisma.payment.findFirst({
        where: {
          orderId: data.orderId,
          status: PaymentStatus.COMPLETED,
        },
      });

      if (existingPayment && data.amount >= Number(order.totalAmount)) {
        throw new AppError('این سفارش قبلاً پرداخت شده است', 400, 'ORDER_ALREADY_PAID');
      }

      // Create payment
      const payment = await prisma.payment.create({
        data: {
          orderId: data.orderId,
          amount: data.amount,
          method: data.method,
          gateway: data.gateway || 'unknown',
          gatewayTransactionId: data.gatewayTransactionId,
          gatewayResponse: data.gatewayResponse,
          status: PaymentStatus.PENDING,
        },
        include: {
          order: {
            select: {
              id: true,
              orderNumber: true,
              totalAmount: true,
              status: true,
            },
          },
        },
      });

      logger.info(`Payment created: ${payment.id} for order: ${order.orderNumber}, amount: ${payment.amount}`);

      return payment as PaymentWithRelations;
    } catch (error) {
      logger.error('Payment creation failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در ایجاد پرداخت', 500, 'PAYMENT_CREATION_FAILED');
    }
  }

  // Get payment by ID
  static async getPaymentById(
    id: string,
    options: PaymentQueryOptions = {}
  ): Promise<PaymentWithRelations> {
    try {
      const include: any = {};

      if (options.include?.order) {
        include.order = {
          select: {
            id: true,
            orderNumber: true,
            totalAmount: true,
            status: true,
            paymentStatus: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        };
      }

      const payment = await prisma.payment.findUnique({
        where: { id },
        include,
      });

      if (!payment) {
        throw new AppError('پرداخت یافت نشد', 404, 'PAYMENT_NOT_FOUND');
      }

      return payment as PaymentWithRelations;
    } catch (error) {
      logger.error('Get payment by ID failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت پرداخت', 500, 'GET_PAYMENT_FAILED');
    }
  }

  // Get all payments with filtering and pagination
  static async getPayments(
    filters: PaymentFilters = {},
    options: PaymentQueryOptions = {}
  ): Promise<{
    payments: PaymentWithRelations[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100); // Max 100 items per page
      const skip = (page - 1) * limit;

      // Build where clause
      const where: Prisma.PaymentWhereInput = {};

      if (filters.orderId) {
        where.orderId = filters.orderId;
      }

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.method) {
        where.method = filters.method;
      }

      if (filters.gateway) {
        where.gateway = { contains: filters.gateway, mode: 'insensitive' };
      }

      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) {
          where.createdAt.gte = filters.dateFrom;
        }
        if (filters.dateTo) {
          where.createdAt.lte = filters.dateTo;
        }
      }

      if (filters.minAmount !== undefined || filters.maxAmount !== undefined) {
        where.amount = {};
        if (filters.minAmount !== undefined) {
          where.amount.gte = filters.minAmount;
        }
        if (filters.maxAmount !== undefined) {
          where.amount.lte = filters.maxAmount;
        }
      }

      // Build include clause
      const include: any = {};
      if (options.include?.order) {
        include.order = {
          select: {
            id: true,
            orderNumber: true,
            totalAmount: true,
            status: true,
            paymentStatus: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        };
      }

      // Build order by clause
      const orderBy: Prisma.PaymentOrderByWithRelationInput = {};
      const sortBy = options.sortBy || 'createdAt';
      const sortOrder = options.sortOrder || 'desc';

      switch (sortBy) {
        case 'amount':
          orderBy.amount = sortOrder;
          break;
        case 'status':
          orderBy.status = sortOrder;
          break;
        case 'processedAt':
          orderBy.processedAt = sortOrder;
          break;
        case 'createdAt':
        default:
          orderBy.createdAt = sortOrder;
          break;
      }

      // Get total count
      const total = await prisma.payment.count({ where });

      // Get payments
      const payments = await prisma.payment.findMany({
        where,
        include,
        orderBy,
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      return {
        payments: payments as PaymentWithRelations[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get payments failed:', error);
      throw new AppError('خطا در دریافت پرداخت‌ها', 500, 'GET_PAYMENTS_FAILED');
    }
  }

  // Update payment status
  static async updatePayment(
    id: string,
    data: UpdatePaymentData
  ): Promise<PaymentWithRelations> {
    try {
      // Check if payment exists
      const existingPayment = await prisma.payment.findUnique({
        where: { id },
        include: {
          order: true,
        },
      });

      if (!existingPayment) {
        throw new AppError('پرداخت یافت نشد', 404, 'PAYMENT_NOT_FOUND');
      }

      // Update payment with transaction
      const result = await prisma.$transaction(async (tx) => {
        // Update the payment
        const updatedPayment = await tx.payment.update({
          where: { id },
          data: {
            ...data,
            processedAt: data.status === PaymentStatus.COMPLETED ? new Date() : data.processedAt,
          },
        });

        // Update order payment status if payment is completed
        if (data.status === PaymentStatus.COMPLETED) {
          // Check if this payment covers the full order amount
          const orderPayments = await tx.payment.findMany({
            where: {
              orderId: existingPayment.orderId,
              status: PaymentStatus.COMPLETED,
            },
          });

          const totalPaid = orderPayments.reduce((sum, payment) => sum + Number(payment.amount), 0);
          const orderTotal = Number(existingPayment.order.totalAmount);

          let newPaymentStatus: any = PaymentStatus.PENDING;
          if (totalPaid >= orderTotal) {
            newPaymentStatus = PaymentStatus.COMPLETED;
          } else if (totalPaid > 0) {
            newPaymentStatus = PaymentStatus.PROCESSING;
          }

          await tx.order.update({
            where: { id: existingPayment.orderId },
            data: {
              paymentStatus: newPaymentStatus,
            },
          });
        }

        // Update order payment status if payment failed
        if (data.status === PaymentStatus.FAILED) {
          await tx.order.update({
            where: { id: existingPayment.orderId },
            data: {
              paymentStatus: PaymentStatus.FAILED,
            },
          });
        }

        return updatedPayment;
      });

      logger.info(`Payment updated: ${result.id}, status: ${result.status}`);

      // Return payment with relations
      return await this.getPaymentById(result.id, {
        include: { order: true },
      });
    } catch (error) {
      logger.error('Payment update failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در به‌روزرسانی پرداخت', 500, 'PAYMENT_UPDATE_FAILED');
    }
  }

  // Process payment (simulate payment gateway)
  static async processPayment(
    paymentId: string,
    gatewayResponse: any
  ): Promise<PaymentWithRelations> {
    try {
      const payment = await this.getPaymentById(paymentId, { include: { order: true } });

      if (payment.status !== PaymentStatus.PENDING) {
        throw new AppError('این پرداخت قبلاً پردازش شده است', 400, 'PAYMENT_ALREADY_PROCESSED');
      }

      // Simulate payment processing logic
      const isSuccessful = gatewayResponse.success === true;
      const newStatus = isSuccessful ? PaymentStatus.COMPLETED : PaymentStatus.FAILED;

      const updatedPayment = await this.updatePayment(paymentId, {
        status: newStatus,
        gatewayTransactionId: gatewayResponse.transactionId,
        gatewayResponse,
        failureReason: isSuccessful ? undefined : gatewayResponse.error,
        processedAt: new Date(),
      });

      logger.info(`Payment processed: ${paymentId}, success: ${isSuccessful}`);

      return updatedPayment;
    } catch (error) {
      logger.error('Payment processing failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در پردازش پرداخت', 500, 'PAYMENT_PROCESSING_FAILED');
    }
  }

  // Refund payment
  static async refundPayment(
    paymentId: string,
    refundAmount?: number,
    reason?: string
  ): Promise<PaymentWithRelations> {
    try {
      const payment = await this.getPaymentById(paymentId, { include: { order: true } });

      if (payment.status !== PaymentStatus.COMPLETED) {
        throw new AppError('فقط پرداخت‌های تکمیل شده قابل بازگشت هستند', 400, 'PAYMENT_NOT_REFUNDABLE');
      }

      const amountToRefund = refundAmount || Number(payment.amount);

      if (amountToRefund > Number(payment.amount)) {
        throw new AppError('مبلغ بازگشت نمی‌تواند بیش از مبلغ پرداخت باشد', 400, 'REFUND_AMOUNT_EXCEEDS_PAYMENT');
      }

      // Create refund payment record
      const refundPayment = await prisma.payment.create({
        data: {
          orderId: payment.orderId,
          amount: -amountToRefund, // Negative amount for refund
          method: payment.method,
          gateway: payment.gateway,
          status: PaymentStatus.REFUNDED,
          gatewayResponse: {
            type: 'refund',
            originalPaymentId: paymentId,
            reason,
          },
          processedAt: new Date(),
        },
        include: {
          order: {
            select: {
              id: true,
              orderNumber: true,
              totalAmount: true,
              status: true,
            },
          },
        },
      });

      logger.info(`Payment refunded: ${paymentId}, amount: ${amountToRefund}, reason: ${reason || 'No reason provided'}`);

      return refundPayment as PaymentWithRelations;
    } catch (error) {
      logger.error('Payment refund failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در بازگشت پرداخت', 500, 'PAYMENT_REFUND_FAILED');
    }
  }
}

export default PaymentService;
