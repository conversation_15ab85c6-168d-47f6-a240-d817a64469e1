import { PrismaClient } from '@prisma/client';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

const prisma = new PrismaClient();

// Content Management Service Types
export interface CreateBannerData {
  title: string;
  subtitle?: string;
  description?: string;
  type: string;
  image: string;
  mobileImage?: string;
  altText: string;
  ctaText?: string;
  ctaUrl?: string;
  ctaType?: string;
  position?: number;
  showOnPages: string[];
  backgroundColor?: string;
  textColor?: string;
  overlayOpacity?: number;
  animationType?: string;
  autoplay?: boolean;
  duration?: number;
  status?: string;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

export interface UpdateBannerData extends Partial<CreateBannerData> {}

export interface CreatePromotionData {
  title: string;
  description?: string;
  type: string;
  discountValue: number;
  minimumOrderAmount?: number;
  maximumDiscountAmount?: number;
  applicableCategories?: string[];
  applicableProducts?: string[];
  usageLimit?: number;
  usagePerCustomer?: number;
  code?: string;
  isCodeRequired?: boolean;
  showOnHomepage?: boolean;
  showInCart?: boolean;
  showOnProductPages?: boolean;
  bannerImage?: string;
  terms?: string;
  conditions?: string[];
  status?: string;
  isActive?: boolean;
  startDate: string;
  endDate: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

export interface UpdatePromotionData extends Partial<CreatePromotionData> {}

export interface CreateNewsletterData {
  title: string;
  subject: string;
  preheader?: string;
  content: string;
  htmlContent?: string;
  campaignType: string;
  template?: string;
  recipientSegments: string[];
  sendAt?: string;
  timezone?: string;
  status?: string;
  isActive?: boolean;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

export interface UpdateNewsletterData extends Partial<CreateNewsletterData> {}

export interface CreatePageData {
  title: string;
  content: string;
  excerpt?: string;
  template?: string;
  parentPage?: string;
  menuOrder?: number;
  showInMenu?: boolean;
  metaTitle?: string;
  metaDescription?: string;
  canonicalUrl?: string;
  noIndex?: boolean;
  noFollow?: boolean;
  featuredImage?: string;
  featuredImageAlt?: string;
  customFields?: any;
  status?: string;
  isActive?: boolean;
  slug: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

export interface UpdatePageData extends Partial<CreatePageData> {}

export interface CreateMediaData {
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  altText?: string;
  caption?: string;
  description?: string;
  tags?: string[];
  folder?: string;
  isPublic?: boolean;
}

export interface UpdateMediaData extends Partial<CreateMediaData> {}

export interface ContentFilters {
  status?: string;
  isActive?: boolean;
  type?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
  createdBy?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class ContentService {
  // Banner Management
  static async getBanners(
    filters: ContentFilters = {},
    options: PaginationOptions = { page: 1, limit: 20 }
  ) {
    try {
      const { page, limit, sortBy = 'createdAt', sortOrder = 'desc' } = options;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (filters.status) where.status = filters.status;
      if (filters.isActive !== undefined) where.isActive = filters.isActive;
      if (filters.type) where.type = filters.type;
      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } }
        ];
      }
      if (filters.createdBy) where.createdBy = filters.createdBy;

      const [banners, total] = await Promise.all([
        prisma.banner.findMany({
          where,
          include: {
            creator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: limit
        }),
        prisma.banner.count({ where })
      ]);

      return {
        banners,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error fetching banners:', error);
      throw new AppError('خطا در دریافت بنرها', 500, 'BANNERS_FETCH_FAILED');
    }
  }

  static async getBannerById(id: string) {
    try {
      const banner = await prisma.banner.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      if (!banner) {
        throw new AppError('بنر یافت نشد', 404, 'BANNER_NOT_FOUND');
      }

      return banner;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error fetching banner:', error);
      throw new AppError('خطا در دریافت بنر', 500, 'BANNER_FETCH_FAILED');
    }
  }

  static async createBanner(data: CreateBannerData, createdBy: string) {
    try {
      const banner = await prisma.banner.create({
        data: {
          ...data,
          showOnPages: data.showOnPages,
          seoKeywords: data.seoKeywords || [],
          createdBy,
          publishedAt: data.status === 'published' ? new Date() : null
        },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Banner created: ${banner.title} by ${createdBy}`);
      return banner;
    } catch (error) {
      logger.error('Error creating banner:', error);
      throw new AppError('خطا در ایجاد بنر', 500, 'BANNER_CREATION_FAILED');
    }
  }

  static async updateBanner(id: string, data: UpdateBannerData) {
    try {
      const existingBanner = await prisma.banner.findUnique({ where: { id } });
      if (!existingBanner) {
        throw new AppError('بنر یافت نشد', 404, 'BANNER_NOT_FOUND');
      }

      const updateData: any = { ...data };
      
      // Handle status change to published
      if (data.status === 'published' && existingBanner.status !== 'published') {
        updateData.publishedAt = new Date();
      }

      const banner = await prisma.banner.update({
        where: { id },
        data: updateData,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Banner updated: ${banner.title}`);
      return banner;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error updating banner:', error);
      throw new AppError('خطا در به‌روزرسانی بنر', 500, 'BANNER_UPDATE_FAILED');
    }
  }

  static async deleteBanner(id: string) {
    try {
      const banner = await prisma.banner.findUnique({ where: { id } });
      if (!banner) {
        throw new AppError('بنر یافت نشد', 404, 'BANNER_NOT_FOUND');
      }

      await prisma.banner.delete({ where: { id } });
      logger.info(`Banner deleted: ${banner.title}`);

      return { success: true };
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error deleting banner:', error);
      throw new AppError('خطا در حذف بنر', 500, 'BANNER_DELETE_FAILED');
    }
  }

  // Promotion Management
  static async getPromotions(
    filters: ContentFilters = {},
    options: PaginationOptions = { page: 1, limit: 20 }
  ) {
    try {
      const { page, limit, sortBy = 'createdAt', sortOrder = 'desc' } = options;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (filters.status) where.status = filters.status;
      if (filters.isActive !== undefined) where.isActive = filters.isActive;
      if (filters.type) where.type = filters.type;
      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } }
        ];
      }
      if (filters.createdBy) where.createdBy = filters.createdBy;

      const [promotions, total] = await Promise.all([
        prisma.promotion.findMany({
          where,
          include: {
            creator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: limit
        }),
        prisma.promotion.count({ where })
      ]);

      return {
        promotions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error fetching promotions:', error);
      throw new AppError('خطا در دریافت تخفیف‌ها', 500, 'PROMOTIONS_FETCH_FAILED');
    }
  }

  static async getPromotionById(id: string) {
    try {
      const promotion = await prisma.promotion.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      if (!promotion) {
        throw new AppError('تخفیف یافت نشد', 404, 'PROMOTION_NOT_FOUND');
      }

      return promotion;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error fetching promotion:', error);
      throw new AppError('خطا در دریافت تخفیف', 500, 'PROMOTION_FETCH_FAILED');
    }
  }

  static async createPromotion(data: CreatePromotionData, createdBy: string) {
    try {
      const promotion = await prisma.promotion.create({
        data: {
          ...data,
          applicableCategories: data.applicableCategories || [],
          applicableProducts: data.applicableProducts || [],
          conditions: data.conditions || [],
          seoKeywords: data.seoKeywords || [],
          startDate: new Date(data.startDate),
          endDate: new Date(data.endDate),
          createdBy,
          publishedAt: data.status === 'published' ? new Date() : null
        },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Promotion created: ${promotion.title} by ${createdBy}`);
      return promotion;
    } catch (error) {
      logger.error('Error creating promotion:', error);
      throw new AppError('خطا در ایجاد تخفیف', 500, 'PROMOTION_CREATION_FAILED');
    }
  }

  static async updatePromotion(id: string, data: UpdatePromotionData) {
    try {
      const existingPromotion = await prisma.promotion.findUnique({ where: { id } });
      if (!existingPromotion) {
        throw new AppError('تخفیف یافت نشد', 404, 'PROMOTION_NOT_FOUND');
      }

      const updateData: any = { ...data };

      // Handle date conversions
      if (data.startDate) updateData.startDate = new Date(data.startDate);
      if (data.endDate) updateData.endDate = new Date(data.endDate);

      // Handle status change to published
      if (data.status === 'published' && existingPromotion.status !== 'published') {
        updateData.publishedAt = new Date();
      }

      const promotion = await prisma.promotion.update({
        where: { id },
        data: updateData,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Promotion updated: ${promotion.title}`);
      return promotion;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error updating promotion:', error);
      throw new AppError('خطا در به‌روزرسانی تخفیف', 500, 'PROMOTION_UPDATE_FAILED');
    }
  }

  static async deletePromotion(id: string) {
    try {
      const promotion = await prisma.promotion.findUnique({ where: { id } });
      if (!promotion) {
        throw new AppError('تخفیف یافت نشد', 404, 'PROMOTION_NOT_FOUND');
      }

      await prisma.promotion.delete({ where: { id } });
      logger.info(`Promotion deleted: ${promotion.title}`);

      return { success: true };
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error deleting promotion:', error);
      throw new AppError('خطا در حذف تخفیف', 500, 'PROMOTION_DELETE_FAILED');
    }
  }

  // Newsletter Management
  static async getNewsletters(
    filters: ContentFilters = {},
    options: PaginationOptions = { page: 1, limit: 20 }
  ) {
    try {
      const { page, limit, sortBy = 'createdAt', sortOrder = 'desc' } = options;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (filters.status) where.status = filters.status;
      if (filters.isActive !== undefined) where.isActive = filters.isActive;
      if (filters.type) where.campaignType = filters.type;
      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { subject: { contains: filters.search, mode: 'insensitive' } }
        ];
      }
      if (filters.createdBy) where.createdBy = filters.createdBy;

      const [newsletters, total] = await Promise.all([
        prisma.newsletterCampaign.findMany({
          where,
          include: {
            creator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: limit
        }),
        prisma.newsletterCampaign.count({ where })
      ]);

      return {
        newsletters,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error fetching newsletters:', error);
      throw new AppError('خطا در دریافت خبرنامه‌ها', 500, 'NEWSLETTERS_FETCH_FAILED');
    }
  }

  static async getNewsletterById(id: string) {
    try {
      const newsletter = await prisma.newsletterCampaign.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      if (!newsletter) {
        throw new AppError('خبرنامه یافت نشد', 404, 'NEWSLETTER_NOT_FOUND');
      }

      return newsletter;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error fetching newsletter:', error);
      throw new AppError('خطا در دریافت خبرنامه', 500, 'NEWSLETTER_FETCH_FAILED');
    }
  }

  static async createNewsletter(data: CreateNewsletterData, createdBy: string) {
    try {
      const newsletter = await prisma.newsletterCampaign.create({
        data: {
          ...data,
          recipientSegments: data.recipientSegments,
          seoKeywords: data.seoKeywords || [],
          sendAt: data.sendAt ? new Date(data.sendAt) : null,
          createdBy,
          publishedAt: data.status === 'published' ? new Date() : null
        },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Newsletter created: ${newsletter.title} by ${createdBy}`);
      return newsletter;
    } catch (error) {
      logger.error('Error creating newsletter:', error);
      throw new AppError('خطا در ایجاد خبرنامه', 500, 'NEWSLETTER_CREATION_FAILED');
    }
  }

  static async updateNewsletter(id: string, data: UpdateNewsletterData) {
    try {
      const existingNewsletter = await prisma.newsletterCampaign.findUnique({ where: { id } });
      if (!existingNewsletter) {
        throw new AppError('خبرنامه یافت نشد', 404, 'NEWSLETTER_NOT_FOUND');
      }

      const updateData: any = { ...data };

      // Handle date conversion
      if (data.sendAt) updateData.sendAt = new Date(data.sendAt);

      // Handle status change to published
      if (data.status === 'published' && existingNewsletter.status !== 'published') {
        updateData.publishedAt = new Date();
      }

      const newsletter = await prisma.newsletterCampaign.update({
        where: { id },
        data: updateData,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Newsletter updated: ${newsletter.title}`);
      return newsletter;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error updating newsletter:', error);
      throw new AppError('خطا در به‌روزرسانی خبرنامه', 500, 'NEWSLETTER_UPDATE_FAILED');
    }
  }

  static async deleteNewsletter(id: string) {
    try {
      const newsletter = await prisma.newsletterCampaign.findUnique({ where: { id } });
      if (!newsletter) {
        throw new AppError('خبرنامه یافت نشد', 404, 'NEWSLETTER_NOT_FOUND');
      }

      await prisma.newsletterCampaign.delete({ where: { id } });
      logger.info(`Newsletter deleted: ${newsletter.title}`);

      return { success: true };
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error deleting newsletter:', error);
      throw new AppError('خطا در حذف خبرنامه', 500, 'NEWSLETTER_DELETE_FAILED');
    }
  }

  // Page Content Management
  static async getPages(
    filters: ContentFilters = {},
    options: PaginationOptions = { page: 1, limit: 20 }
  ) {
    try {
      const { page, limit, sortBy = 'createdAt', sortOrder = 'desc' } = options;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (filters.status) where.status = filters.status;
      if (filters.isActive !== undefined) where.isActive = filters.isActive;
      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { content: { contains: filters.search, mode: 'insensitive' } }
        ];
      }
      if (filters.createdBy) where.createdBy = filters.createdBy;

      const [pages, total] = await Promise.all([
        prisma.pageContent.findMany({
          where,
          include: {
            creator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            },
            parent: {
              select: {
                id: true,
                title: true,
                slug: true
              }
            },
            children: {
              select: {
                id: true,
                title: true,
                slug: true
              }
            }
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: limit
        }),
        prisma.pageContent.count({ where })
      ]);

      return {
        pages,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error fetching pages:', error);
      throw new AppError('خطا در دریافت صفحات', 500, 'PAGES_FETCH_FAILED');
    }
  }

  static async getPageById(id: string) {
    try {
      const page = await prisma.pageContent.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          parent: {
            select: {
              id: true,
              title: true,
              slug: true
            }
          },
          children: {
            select: {
              id: true,
              title: true,
              slug: true
            }
          }
        }
      });

      if (!page) {
        throw new AppError('صفحه یافت نشد', 404, 'PAGE_NOT_FOUND');
      }

      return page;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error fetching page:', error);
      throw new AppError('خطا در دریافت صفحه', 500, 'PAGE_FETCH_FAILED');
    }
  }

  static async getPageBySlug(slug: string) {
    try {
      const page = await prisma.pageContent.findUnique({
        where: { slug },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          parent: {
            select: {
              id: true,
              title: true,
              slug: true
            }
          }
        }
      });

      if (!page) {
        throw new AppError('صفحه یافت نشد', 404, 'PAGE_NOT_FOUND');
      }

      return page;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error fetching page by slug:', error);
      throw new AppError('خطا در دریافت صفحه', 500, 'PAGE_FETCH_FAILED');
    }
  }

  static async createPage(data: CreatePageData, createdBy: string) {
    try {
      // Check if slug already exists
      const existingPage = await prisma.pageContent.findUnique({
        where: { slug: data.slug }
      });

      if (existingPage) {
        throw new AppError('نامک صفحه قبلاً استفاده شده است', 400, 'SLUG_ALREADY_EXISTS');
      }

      const page = await prisma.pageContent.create({
        data: {
          ...data,
          seoKeywords: data.seoKeywords || [],
          createdBy,
          publishedAt: data.status === 'published' ? new Date() : null
        },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Page created: ${page.title} by ${createdBy}`);
      return page;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error creating page:', error);
      throw new AppError('خطا در ایجاد صفحه', 500, 'PAGE_CREATION_FAILED');
    }
  }

  static async updatePage(id: string, data: UpdatePageData) {
    try {
      const existingPage = await prisma.pageContent.findUnique({ where: { id } });
      if (!existingPage) {
        throw new AppError('صفحه یافت نشد', 404, 'PAGE_NOT_FOUND');
      }

      // Check slug uniqueness if slug is being updated
      if (data.slug && data.slug !== existingPage.slug) {
        const slugExists = await prisma.pageContent.findUnique({
          where: { slug: data.slug }
        });
        if (slugExists) {
          throw new AppError('نامک صفحه قبلاً استفاده شده است', 400, 'SLUG_ALREADY_EXISTS');
        }
      }

      const updateData: any = { ...data };

      // Handle status change to published
      if (data.status === 'published' && existingPage.status !== 'published') {
        updateData.publishedAt = new Date();
      }

      const page = await prisma.pageContent.update({
        where: { id },
        data: updateData,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Page updated: ${page.title}`);
      return page;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error updating page:', error);
      throw new AppError('خطا در به‌روزرسانی صفحه', 500, 'PAGE_UPDATE_FAILED');
    }
  }

  static async deletePage(id: string) {
    try {
      const page = await prisma.pageContent.findUnique({ where: { id } });
      if (!page) {
        throw new AppError('صفحه یافت نشد', 404, 'PAGE_NOT_FOUND');
      }

      await prisma.pageContent.delete({ where: { id } });
      logger.info(`Page deleted: ${page.title}`);

      return { success: true };
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error deleting page:', error);
      throw new AppError('خطا در حذف صفحه', 500, 'PAGE_DELETE_FAILED');
    }
  }

  // Media Management
  static async getMediaItems(
    filters: ContentFilters = {},
    options: PaginationOptions = { page: 1, limit: 20 }
  ) {
    try {
      const { page, limit, sortBy = 'createdAt', sortOrder = 'desc' } = options;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (filters.search) {
        where.OR = [
          { filename: { contains: filters.search, mode: 'insensitive' } },
          { originalName: { contains: filters.search, mode: 'insensitive' } },
          { altText: { contains: filters.search, mode: 'insensitive' } }
        ];
      }

      const [mediaItems, total] = await Promise.all([
        prisma.mediaItem.findMany({
          where,
          include: {
            uploader: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: limit
        }),
        prisma.mediaItem.count({ where })
      ]);

      return {
        mediaItems,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error fetching media items:', error);
      throw new AppError('خطا در دریافت فایل‌های رسانه', 500, 'MEDIA_FETCH_FAILED');
    }
  }

  static async createMediaItem(data: CreateMediaData, uploadedBy: string) {
    try {
      const mediaItem = await prisma.mediaItem.create({
        data: {
          ...data,
          tags: data.tags || [],
          uploadedBy
        },
        include: {
          uploader: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      logger.info(`Media item created: ${mediaItem.filename} by ${uploadedBy}`);
      return mediaItem;
    } catch (error) {
      logger.error('Error creating media item:', error);
      throw new AppError('خطا در ایجاد فایل رسانه', 500, 'MEDIA_CREATION_FAILED');
    }
  }

  static async deleteMediaItem(id: string) {
    try {
      const mediaItem = await prisma.mediaItem.findUnique({ where: { id } });
      if (!mediaItem) {
        throw new AppError('فایل رسانه یافت نشد', 404, 'MEDIA_NOT_FOUND');
      }

      await prisma.mediaItem.delete({ where: { id } });
      logger.info(`Media item deleted: ${mediaItem.filename}`);

      return { success: true };
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error deleting media item:', error);
      throw new AppError('خطا در حذف فایل رسانه', 500, 'MEDIA_DELETE_FAILED');
    }
  }

  // Analytics and Statistics
  static async getContentAnalytics() {
    try {
      const [
        totalBanners,
        totalPromotions,
        totalNewsletters,
        totalPages,
        totalMedia,
        publishedBanners,
        publishedPromotions,
        publishedNewsletters,
        publishedPages
      ] = await Promise.all([
        prisma.banner.count(),
        prisma.promotion.count(),
        prisma.newsletterCampaign.count(),
        prisma.pageContent.count(),
        prisma.mediaItem.count(),
        prisma.banner.count({ where: { status: 'published' } }),
        prisma.promotion.count({ where: { status: 'published' } }),
        prisma.newsletterCampaign.count({ where: { status: 'published' } }),
        prisma.pageContent.count({ where: { status: 'published' } })
      ]);

      return {
        totalContent: totalBanners + totalPromotions + totalNewsletters + totalPages,
        publishedContent: publishedBanners + publishedPromotions + publishedNewsletters + publishedPages,
        contentByType: {
          banner: totalBanners,
          promotion: totalPromotions,
          newsletter: totalNewsletters,
          page: totalPages,
          media: totalMedia
        },
        publishedByType: {
          banner: publishedBanners,
          promotion: publishedPromotions,
          newsletter: publishedNewsletters,
          page: publishedPages
        }
      };
    } catch (error) {
      logger.error('Error fetching content analytics:', error);
      throw new AppError('خطا در دریافت آمار محتوا', 500, 'ANALYTICS_FETCH_FAILED');
    }
  }
}
