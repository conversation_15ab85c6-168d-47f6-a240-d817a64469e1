import { User, UserRole, UserStatus, Address, Order } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';
import AuthService from './authService';

// Types for customer management
export interface CustomerFilters {
  search?: string;
  role?: UserRole;
  status?: UserStatus;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  hasOrders?: boolean;
  registrationDateFrom?: Date;
  registrationDateTo?: Date;
  lastLoginFrom?: Date;
  lastLoginTo?: Date;
  province?: string;
  city?: string;
}

export interface CustomerListOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  include?: {
    addresses?: boolean;
    orders?: boolean;
    orderStats?: boolean;
    loyaltyAccount?: boolean;
    _count?: boolean;
  };
}

export interface CreateCustomerData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: UserRole;
  status?: UserStatus;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
}

export interface UpdateCustomerData {
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatar?: string;
  status?: UserStatus;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
}

export interface CustomerStatistics {
  totalCustomers: number;
  activeCustomers: number;
  newCustomersThisMonth: number;
  customersWithOrders: number;
  averageOrderValue: number;
  topCustomersByOrders: Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    totalOrders: number;
    totalSpent: number;
  }>;
  customersByProvince: Array<{
    province: string;
    count: number;
  }>;
  registrationTrends: Array<{
    date: string;
    count: number;
  }>;
}

export interface CustomerPreferences {
  newsletter: boolean;
  smsNotifications: boolean;
  emailNotifications: boolean;
  language: string;
  currency: string;
  timezone: string;
}

export class CustomerService {
  // Get customers with filtering and pagination
  static async getCustomers(
    filters: CustomerFilters = {},
    options: CustomerListOptions = {}
  ): Promise<{
    customers: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        search,
        role,
        status,
        isEmailVerified,
        isPhoneVerified,
        hasOrders,
        registrationDateFrom,
        registrationDateTo,
        lastLoginFrom,
        lastLoginTo,
        province,
        city,
      } = filters;

      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        include = {},
      } = options;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      // Search in name and email
      if (search) {
        where.OR = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Filter by role
      if (role) {
        where.role = role;
      }

      // Filter by status
      if (status) {
        where.status = status;
      }

      // Filter by email verification
      if (typeof isEmailVerified === 'boolean') {
        where.isEmailVerified = isEmailVerified;
      }

      // Filter by phone verification
      if (typeof isPhoneVerified === 'boolean') {
        where.isPhoneVerified = isPhoneVerified;
      }

      // Filter by registration date range
      if (registrationDateFrom || registrationDateTo) {
        where.createdAt = {};
        if (registrationDateFrom) {
          where.createdAt.gte = registrationDateFrom;
        }
        if (registrationDateTo) {
          where.createdAt.lte = registrationDateTo;
        }
      }

      // Filter by last login date range
      if (lastLoginFrom || lastLoginTo) {
        where.lastLoginAt = {};
        if (lastLoginFrom) {
          where.lastLoginAt.gte = lastLoginFrom;
        }
        if (lastLoginTo) {
          where.lastLoginAt.lte = lastLoginTo;
        }
      }

      // Filter by location
      if (province || city) {
        where.addresses = {
          some: {
            ...(province && { province: { contains: province, mode: 'insensitive' } }),
            ...(city && { city: { contains: city, mode: 'insensitive' } }),
          },
        };
      }

      // Filter customers with orders
      if (typeof hasOrders === 'boolean') {
        if (hasOrders) {
          where.orders = { some: {} };
        } else {
          where.orders = { none: {} };
        }
      }

      // Build include clause
      const includeClause: any = {};

      if (include.addresses) {
        includeClause.addresses = {
          select: {
            id: true,
            title: true,
            firstName: true,
            lastName: true,
            phone: true,
            province: true,
            city: true,
            district: true,
            street: true,
            postalCode: true,
            isDefault: true,
          },
        };
      }

      if (include.orders) {
        includeClause.orders = {
          select: {
            id: true,
            orderNumber: true,
            status: true,
            totalAmount: true,
            createdAt: true,
          },
          take: 5,
          orderBy: { createdAt: 'desc' },
        };
      }

      if (include.orderStats) {
        includeClause._count = {
          select: {
            orders: true,
          },
        };
      }

      if (include._count) {
        includeClause._count = {
          select: {
            orders: true,
            addresses: true,
            reviews: true,
            wishlistItems: true,
          },
        };
      }

      // Execute queries
      const [customers, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            phone: true,
            avatar: true,
            role: true,
            status: true,
            isEmailVerified: true,
            isPhoneVerified: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
            ...includeClause,
          },
        }),
        prisma.user.count({ where }),
      ]);

      // Calculate order statistics for each customer if requested
      if (include.orderStats) {
        for (const customer of customers) {
          const customerId = String(customer.id);
          const orderStats = await prisma.order.aggregate({
            where: { userId: customerId },
            _sum: { totalAmount: true },
            _count: true,
          });

          (customer as any).orderStats = {
            totalOrders: orderStats._count || 0,
            totalSpent: Number(orderStats._sum?.totalAmount) || 0,
          };
        }
      }

      const totalPages = Math.ceil(total / limit);

      return {
        customers,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Error getting customers:', error);
      throw new AppError('خطا در دریافت لیست مشتریان', 500, 'GET_CUSTOMERS_ERROR');
    }
  }

  // Get customer by ID with detailed information
  static async getCustomerById(
    customerId: string,
    options: { includeOrders?: boolean; includeAddresses?: boolean; includeStats?: boolean } = {}
  ): Promise<any> {
    try {
      const { includeOrders = true, includeAddresses = true, includeStats = true } = options;

      const customer = await prisma.user.findUnique({
        where: { id: customerId },
        include: {
          ...(includeAddresses && {
            addresses: {
              select: {
                id: true,
                title: true,
                firstName: true,
                lastName: true,
                phone: true,
                province: true,
                city: true,
                district: true,
                street: true,
                postalCode: true,
                isDefault: true,
                createdAt: true,
                updatedAt: true,
              },
              orderBy: { isDefault: 'desc' },
            },
          }),
          ...(includeOrders && {
            orders: {
              select: {
                id: true,
                orderNumber: true,
                status: true,
                totalAmount: true,
                paymentStatus: true,
                fulfillmentStatus: true,
                createdAt: true,
                updatedAt: true,
                items: {
                  select: {
                    id: true,
                    quantity: true,
                    unitPrice: true,
                    product: {
                      select: {
                        id: true,
                        name: true,
                        slug: true,
                        images: {
                          select: { url: true, isPrimary: true },
                          where: { isPrimary: true },
                          take: 1,
                        },
                      },
                    },
                  },
                },
              },
              orderBy: { createdAt: 'desc' },
              take: 10,
            },
          }),
          _count: {
            select: {
              orders: true,
              addresses: true,
              reviews: true,
              wishlistItems: true,
            },
          },
        },
      });

      if (!customer) {
        throw new AppError('مشتری یافت نشد', 404, 'CUSTOMER_NOT_FOUND');
      }

      // Calculate customer statistics if requested
      if (includeStats) {
        const [orderStats, recentActivity] = await Promise.all([
          prisma.order.aggregate({
            where: { userId: customerId },
            _sum: { totalAmount: true },
            _count: true,
            _avg: { totalAmount: true },
          }),
          prisma.order.findMany({
            where: { userId: customerId },
            select: {
              id: true,
              orderNumber: true,
              status: true,
              totalAmount: true,
              createdAt: true,
            },
            orderBy: { createdAt: 'desc' },
            take: 5,
          }),
        ]);

        (customer as any).statistics = {
          totalOrders: orderStats._count || 0,
          totalSpent: Number(orderStats._sum?.totalAmount) || 0,
          averageOrderValue: Number(orderStats._avg?.totalAmount) || 0,
          recentActivity,
        };
      }

      return customer;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error getting customer by ID:', error);
      throw new AppError('خطا در دریافت اطلاعات مشتری', 500, 'GET_CUSTOMER_ERROR');
    }
  }

  // Create new customer (admin only)
  static async createCustomer(data: CreateCustomerData): Promise<any> {
    try {
      const {
        email,
        password,
        firstName,
        lastName,
        phone,
        role = UserRole.CUSTOMER,
        status = UserStatus.ACTIVE,
        isEmailVerified = false,
        isPhoneVerified = false,
      } = data;

      // Check if email already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw new AppError('کاربری با این ایمیل قبلاً ثبت‌نام کرده است', 400, 'EMAIL_ALREADY_EXISTS');
      }

      // Check if phone already exists (if provided)
      if (phone) {
        const existingPhone = await prisma.user.findUnique({
          where: { phone },
        });

        if (existingPhone) {
          throw new AppError('کاربری با این شماره تلفن قبلاً ثبت‌نام کرده است', 400, 'PHONE_ALREADY_EXISTS');
        }
      }

      // Hash password
      const hashedPassword = await AuthService.hashPassword(password);

      // Create customer
      const customer = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName,
          lastName,
          phone,
          role,
          status,
          isEmailVerified,
          isPhoneVerified,
          ...(isEmailVerified && { emailVerifiedAt: new Date() }),
          ...(isPhoneVerified && { phoneVerifiedAt: new Date() }),
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          role: true,
          status: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info(`New customer created: ${customer.email} (ID: ${customer.id})`);

      return customer;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error creating customer:', error);
      throw new AppError('خطا در ایجاد مشتری جدید', 500, 'CREATE_CUSTOMER_ERROR');
    }
  }

  // Update customer information (admin only)
  static async updateCustomer(customerId: string, data: UpdateCustomerData): Promise<any> {
    try {
      const { firstName, lastName, phone, avatar, status, isEmailVerified, isPhoneVerified } = data;

      // Check if customer exists
      const existingCustomer = await prisma.user.findUnique({
        where: { id: customerId },
      });

      if (!existingCustomer) {
        throw new AppError('مشتری یافت نشد', 404, 'CUSTOMER_NOT_FOUND');
      }

      // Check if phone already exists (if provided and different from current)
      if (phone && phone !== existingCustomer.phone) {
        const existingPhone = await prisma.user.findUnique({
          where: { phone },
        });

        if (existingPhone) {
          throw new AppError('کاربری با این شماره تلفن قبلاً ثبت‌نام کرده است', 400, 'PHONE_ALREADY_EXISTS');
        }
      }

      // Prepare update data
      const updateData: any = {};

      if (firstName !== undefined) updateData.firstName = firstName;
      if (lastName !== undefined) updateData.lastName = lastName;
      if (phone !== undefined) updateData.phone = phone;
      if (avatar !== undefined) updateData.avatar = avatar;
      if (status !== undefined) updateData.status = status;
      if (isEmailVerified !== undefined) {
        updateData.isEmailVerified = isEmailVerified;
        if (isEmailVerified && !existingCustomer.emailVerifiedAt) {
          updateData.emailVerifiedAt = new Date();
        } else if (!isEmailVerified) {
          updateData.emailVerifiedAt = null;
        }
      }
      if (isPhoneVerified !== undefined) {
        updateData.isPhoneVerified = isPhoneVerified;
        if (isPhoneVerified && !existingCustomer.phoneVerifiedAt) {
          updateData.phoneVerifiedAt = new Date();
        } else if (!isPhoneVerified) {
          updateData.phoneVerifiedAt = null;
        }
      }

      // Update customer
      const updatedCustomer = await prisma.user.update({
        where: { id: customerId },
        data: updateData,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          avatar: true,
          role: true,
          status: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // If customer is suspended or banned, invalidate all sessions
      if (status === UserStatus.SUSPENDED || status === UserStatus.BANNED) {
        await AuthService.invalidateAllUserSessions(customerId);
      }

      logger.info(`Customer updated: ${updatedCustomer.email} (ID: ${customerId})`);

      return updatedCustomer;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error updating customer:', error);
      throw new AppError('خطا در به‌روزرسانی اطلاعات مشتری', 500, 'UPDATE_CUSTOMER_ERROR');
    }
  }

  // Delete customer (admin only) - soft delete by changing status
  static async deleteCustomer(customerId: string): Promise<void> {
    try {
      // Check if customer exists
      const existingCustomer = await prisma.user.findUnique({
        where: { id: customerId },
        include: {
          orders: {
            where: {
              status: {
                in: ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED'],
              },
            },
          },
        },
      });

      if (!existingCustomer) {
        throw new AppError('مشتری یافت نشد', 404, 'CUSTOMER_NOT_FOUND');
      }

      // Check if customer has active orders
      if (existingCustomer.orders.length > 0) {
        throw new AppError(
          'امکان حذف مشتری با سفارش‌های فعال وجود ندارد',
          400,
          'CUSTOMER_HAS_ACTIVE_ORDERS'
        );
      }

      // Soft delete by changing status to BANNED
      await prisma.user.update({
        where: { id: customerId },
        data: {
          status: UserStatus.BANNED,
          email: `deleted_${Date.now()}_${existingCustomer.email}`,
        },
      });

      // Invalidate all sessions
      await AuthService.invalidateAllUserSessions(customerId);

      logger.info(`Customer deleted: ${existingCustomer.email} (ID: ${customerId})`);
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error deleting customer:', error);
      throw new AppError('خطا در حذف مشتری', 500, 'DELETE_CUSTOMER_ERROR');
    }
  }

  // Get customer statistics
  static async getCustomerStatistics(): Promise<CustomerStatistics> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfYear = new Date(now.getFullYear(), 0, 1);

      const [
        totalCustomers,
        activeCustomers,
        newCustomersThisMonth,
        customersWithOrders,
        orderStats,
        topCustomers,
        customersByProvince,
        registrationTrends,
      ] = await Promise.all([
        // Total customers
        prisma.user.count({
          where: { role: UserRole.CUSTOMER },
        }),

        // Active customers
        prisma.user.count({
          where: {
            role: UserRole.CUSTOMER,
            status: UserStatus.ACTIVE,
          },
        }),

        // New customers this month
        prisma.user.count({
          where: {
            role: UserRole.CUSTOMER,
            createdAt: { gte: startOfMonth },
          },
        }),

        // Customers with orders
        prisma.user.count({
          where: {
            role: UserRole.CUSTOMER,
            orders: { some: {} },
          },
        }),

        // Order statistics
        prisma.order.aggregate({
          _avg: { totalAmount: true },
        }),

        // Top customers by orders
        prisma.user.findMany({
          where: { role: UserRole.CUSTOMER },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            _count: { select: { orders: true } },
            orders: {
              select: { totalAmount: true },
            },
          },
          orderBy: {
            orders: { _count: 'desc' },
          },
          take: 10,
        }),

        // Customers by province
        prisma.address.groupBy({
          by: ['province'],
          _count: { userId: true },
          orderBy: { _count: { userId: 'desc' } },
          take: 10,
        }),

        // Registration trends (last 30 days)
        prisma.$queryRaw`
          SELECT DATE(created_at) as date, COUNT(*) as count
          FROM users
          WHERE role = 'CUSTOMER' AND created_at >= ${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `,
      ]);

      // Process top customers data
      const topCustomersByOrders = topCustomers.map((customer) => ({
        id: customer.id,
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email,
        totalOrders: customer._count.orders,
        totalSpent: customer.orders.reduce((sum, order) => sum + Number(order.totalAmount), 0),
      }));

      // Process customers by province data
      const customersByProvinceData = customersByProvince.map((item) => ({
        province: item.province,
        count: item._count.userId,
      }));

      // Process registration trends data
      const registrationTrendsData = (registrationTrends as any[]).map((item) => ({
        date: item.date.toISOString().split('T')[0],
        count: Number(item.count),
      }));

      return {
        totalCustomers,
        activeCustomers,
        newCustomersThisMonth,
        customersWithOrders,
        averageOrderValue: Number(orderStats._avg?.totalAmount) || 0,
        topCustomersByOrders,
        customersByProvince: customersByProvinceData,
        registrationTrends: registrationTrendsData,
      };
    } catch (error) {
      logger.error('Error getting customer statistics:', error);
      throw new AppError('خطا در دریافت آمار مشتریان', 500, 'GET_CUSTOMER_STATS_ERROR');
    }
  }

  // Search customers by various criteria
  static async searchCustomers(
    query: string,
    options: { limit?: number; includeInactive?: boolean } = {}
  ): Promise<any[]> {
    try {
      const { limit = 10, includeInactive = false } = options;

      const where: any = {
        OR: [
          { firstName: { contains: query, mode: 'insensitive' } },
          { lastName: { contains: query, mode: 'insensitive' } },
          { email: { contains: query, mode: 'insensitive' } },
          { phone: { contains: query, mode: 'insensitive' } },
        ],
      };

      if (!includeInactive) {
        where.status = UserStatus.ACTIVE;
      }

      const customers = await prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          avatar: true,
          status: true,
          role: true,
          createdAt: true,
          _count: {
            select: {
              orders: true,
            },
          },
        },
        take: limit,
        orderBy: [
          { status: 'asc' }, // Active customers first
          { createdAt: 'desc' },
        ],
      });

      return customers;
    } catch (error) {
      logger.error('Error searching customers:', error);
      throw new AppError('خطا در جستجوی مشتریان', 500, 'SEARCH_CUSTOMERS_ERROR');
    }
  }

  // Bulk update customer status
  static async bulkUpdateCustomerStatus(
    customerIds: string[],
    status: UserStatus,
    reason?: string
  ): Promise<{ updated: number; errors: string[] }> {
    try {
      const errors: string[] = [];
      let updated = 0;

      for (const customerId of customerIds) {
        try {
          // Check if customer exists
          const customer = await prisma.user.findUnique({
            where: { id: customerId },
            select: { id: true, email: true, status: true },
          });

          if (!customer) {
            errors.push(`مشتری با شناسه ${customerId} یافت نشد`);
            continue;
          }

          // Update status
          await prisma.user.update({
            where: { id: customerId },
            data: { status },
          });

          // If suspending or banning, invalidate sessions
          if (status === UserStatus.SUSPENDED || status === UserStatus.BANNED) {
            await AuthService.invalidateAllUserSessions(customerId);
          }

          updated++;
          logger.info(`Customer status updated: ${customer.email} -> ${status}`);
        } catch (error) {
          errors.push(`خطا در به‌روزرسانی مشتری ${customerId}: ${error}`);
        }
      }

      return { updated, errors };
    } catch (error) {
      logger.error('Error bulk updating customer status:', error);
      throw new AppError('خطا در به‌روزرسانی گروهی وضعیت مشتریان', 500, 'BULK_UPDATE_ERROR');
    }
  }

  // Export customers data
  static async exportCustomers(
    filters: CustomerFilters = {},
    format: 'csv' | 'json' = 'csv'
  ): Promise<any> {
    try {
      const { customers } = await this.getCustomers(filters, {
        limit: 10000, // Large limit for export
        include: {
          addresses: true,
          orderStats: true,
          _count: true,
        },
      });

      if (format === 'json') {
        return customers;
      }

      // Convert to CSV format
      const csvHeaders = [
        'شناسه',
        'ایمیل',
        'نام',
        'نام خانوادگی',
        'تلفن',
        'نقش',
        'وضعیت',
        'ایمیل تأیید شده',
        'تلفن تأیید شده',
        'تعداد سفارش‌ها',
        'مجموع خرید',
        'تعداد آدرس‌ها',
        'آخرین ورود',
        'تاریخ ثبت‌نام',
      ];

      const csvData = customers.map((customer) => [
        customer.id,
        customer.email,
        customer.firstName,
        customer.lastName,
        customer.phone || '',
        customer.role,
        customer.status,
        customer.isEmailVerified ? 'بله' : 'خیر',
        customer.isPhoneVerified ? 'بله' : 'خیر',
        (customer as any).orderStats?.totalOrders || 0,
        (customer as any).orderStats?.totalSpent || 0,
        customer._count?.addresses || 0,
        customer.lastLoginAt ? new Date(customer.lastLoginAt).toLocaleDateString('fa-IR') : '',
        new Date(customer.createdAt).toLocaleDateString('fa-IR'),
      ]);

      return {
        headers: csvHeaders,
        data: csvData,
      };
    } catch (error) {
      logger.error('Error exporting customers:', error);
      throw new AppError('خطا در صادرات اطلاعات مشتریان', 500, 'EXPORT_CUSTOMERS_ERROR');
    }
  }

  // Get customer order history with detailed information
  static async getCustomerOrderHistory(
    customerId: string,
    options: { page?: number; limit?: number; status?: string } = {}
  ): Promise<{
    orders: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    summary: {
      totalOrders: number;
      totalSpent: number;
      averageOrderValue: number;
    };
  }> {
    try {
      const { page = 1, limit = 10, status } = options;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { userId: customerId };
      if (status) {
        where.status = status;
      }

      // Get orders and total count
      const [orders, total, summary] = await Promise.all([
        prisma.order.findMany({
          where,
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    slug: true,
                    images: {
                      where: { isPrimary: true },
                      select: { url: true },
                      take: 1,
                    },
                  },
                },
              },
            },
            shippingAddress: true,
            payments: {
              select: {
                id: true,
                amount: true,
                status: true,
                method: true,
                createdAt: true,
              },
            },
          },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        prisma.order.count({ where }),
        prisma.order.aggregate({
          where: { userId: customerId },
          _count: true,
          _sum: { totalAmount: true },
          _avg: { totalAmount: true },
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        orders,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
        summary: {
          totalOrders: summary._count || 0,
          totalSpent: Number(summary._sum?.totalAmount) || 0,
          averageOrderValue: Number(summary._avg?.totalAmount) || 0,
        },
      };
    } catch (error) {
      logger.error('Error getting customer order history:', error);
      throw new AppError('خطا در دریافت تاریخچه سفارش‌های مشتری', 500, 'GET_ORDER_HISTORY_ERROR');
    }
  }
}

export default CustomerService;
