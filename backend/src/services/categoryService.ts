import { Category, Prisma } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';
import UploadService from './uploadService';

// Types for category operations
export interface CreateCategoryData {
  name: string;
  nameEn?: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {}

export interface CategoryFilters {
  search?: string;
  parentId?: string;
  isActive?: boolean;
  level?: number;
}

export interface CategoryQueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  include?: {
    parent?: boolean;
    children?: boolean;
    products?: boolean;
    _count?: boolean;
  };
}

// Category with relations type
export type CategoryWithRelations = Category & {
  parent?: Category;
  children?: Category[];
  products?: any[];
  _count?: {
    products: number;
    children: number;
  };
};

export class CategoryService {
  // Create a new category
  static async createCategory(data: CreateCategoryData): Promise<CategoryWithRelations> {
    try {
      // Check if slug already exists
      const existingCategory = await prisma.category.findUnique({
        where: { slug: data.slug },
      });

      if (existingCategory) {
        throw new AppError('نامک دسته‌بندی قبلاً استفاده شده است', 409, 'SLUG_EXISTS');
      }

      // Validate parent category if provided
      if (data.parentId) {
        const parentCategory = await prisma.category.findUnique({
          where: { id: data.parentId },
        });

        if (!parentCategory) {
          throw new AppError('دسته‌بندی والد یافت نشد', 404, 'PARENT_CATEGORY_NOT_FOUND');
        }

        // Check for circular reference (prevent category being its own parent)
        if (data.parentId === data.parentId) {
          throw new AppError('دسته‌بندی نمی‌تواند والد خودش باشد', 400, 'CIRCULAR_REFERENCE');
        }
      }

      // Create category
      const category = await prisma.category.create({
        data: {
          name: data.name,
          nameEn: data.nameEn,
          slug: data.slug,
          description: data.description,
          image: data.image,
          parentId: data.parentId,
          isActive: data.isActive ?? true,
          sortOrder: data.sortOrder ?? 0,
        },
        include: {
          parent: true,
          children: true,
          _count: {
            select: {
              products: true,
              children: true,
            },
          },
        },
      });

      logger.info(`Category created successfully: ${category.name} (${category.slug})`);
      return category as CategoryWithRelations;
    } catch (error) {
      logger.error('Category creation failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در ایجاد دسته‌بندی', 500, 'CATEGORY_CREATION_FAILED');
    }
  }

  // Get category by ID
  static async getCategoryById(
    id: string,
    options: CategoryQueryOptions = {}
  ): Promise<CategoryWithRelations> {
    try {
      const include: any = {};

      if (options.include?.parent) include.parent = true;
      if (options.include?.children) {
        include.children = {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.products) {
        include.products = {
          include: { product: true },
          where: { product: { isActive: true } },
        };
      }
      if (options.include?._count) {
        include._count = {
          select: {
            products: true,
            children: true,
          },
        };
      }

      const category = await prisma.category.findUnique({
        where: { id },
        include,
      });

      if (!category) {
        throw new AppError('دسته‌بندی یافت نشد', 404, 'CATEGORY_NOT_FOUND');
      }

      return category as CategoryWithRelations;
    } catch (error) {
      logger.error('Get category by ID failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت دسته‌بندی', 500, 'GET_CATEGORY_FAILED');
    }
  }

  // Get category by slug
  static async getCategoryBySlug(
    slug: string,
    options: CategoryQueryOptions = {}
  ): Promise<CategoryWithRelations> {
    try {
      const include: any = {};

      if (options.include?.parent) include.parent = true;
      if (options.include?.children) {
        include.children = {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.products) {
        include.products = {
          include: { product: true },
          where: { product: { isActive: true } },
        };
      }
      if (options.include?._count) {
        include._count = {
          select: {
            products: true,
            children: true,
          },
        };
      }

      const category = await prisma.category.findUnique({
        where: { slug },
        include,
      });

      if (!category) {
        throw new AppError('دسته‌بندی یافت نشد', 404, 'CATEGORY_NOT_FOUND');
      }

      return category as CategoryWithRelations;
    } catch (error) {
      logger.error('Get category by slug failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت دسته‌بندی', 500, 'GET_CATEGORY_FAILED');
    }
  }

  // Get all categories with filtering and pagination
  static async getCategories(
    filters: CategoryFilters = {},
    options: CategoryQueryOptions = {}
  ): Promise<{
    categories: CategoryWithRelations[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 50, 100); // Max 100 items per page
      const skip = (page - 1) * limit;

      // Build where clause
      const where: Prisma.CategoryWhereInput = {};

      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { nameEn: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      if (filters.parentId !== undefined) {
        where.parentId = filters.parentId;
      }

      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive;
      }

      // Build include clause
      const include: any = {};
      if (options.include?.parent) include.parent = true;
      if (options.include?.children) {
        include.children = {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.products) {
        include.products = {
          include: { product: true },
          where: { product: { isActive: true } },
        };
      }
      if (options.include?._count) {
        include._count = {
          select: {
            products: true,
            children: true,
          },
        };
      }

      // Build order by clause
      const orderBy: Prisma.CategoryOrderByWithRelationInput = {};
      const sortBy = options.sortBy || 'sortOrder';
      const sortOrder = options.sortOrder || 'asc';

      switch (sortBy) {
        case 'name':
          orderBy.name = sortOrder;
          break;
        case 'createdAt':
          orderBy.createdAt = sortOrder;
          break;
        case 'updatedAt':
          orderBy.updatedAt = sortOrder;
          break;
        case 'sortOrder':
        default:
          orderBy.sortOrder = sortOrder;
          break;
      }

      // Get total count
      const total = await prisma.category.count({ where });

      // Get categories
      const categories = await prisma.category.findMany({
        where,
        include,
        orderBy,
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      return {
        categories: categories as CategoryWithRelations[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get categories failed:', error);
      throw new AppError('خطا در دریافت دسته‌بندی‌ها', 500, 'GET_CATEGORIES_FAILED');
    }
  }

  // Update category
  static async updateCategory(
    id: string,
    data: UpdateCategoryData
  ): Promise<CategoryWithRelations> {
    try {
      // Check if category exists
      const existingCategory = await prisma.category.findUnique({
        where: { id },
      });

      if (!existingCategory) {
        throw new AppError('دسته‌بندی یافت نشد', 404, 'CATEGORY_NOT_FOUND');
      }

      // Check if slug is being updated and already exists
      if (data.slug && data.slug !== existingCategory.slug) {
        const slugExists = await prisma.category.findUnique({
          where: { slug: data.slug },
        });

        if (slugExists) {
          throw new AppError('نامک دسته‌بندی قبلاً استفاده شده است', 409, 'SLUG_EXISTS');
        }
      }

      // Validate parent category if provided
      if (data.parentId) {
        const parentCategory = await prisma.category.findUnique({
          where: { id: data.parentId },
        });

        if (!parentCategory) {
          throw new AppError('دسته‌بندی والد یافت نشد', 404, 'PARENT_CATEGORY_NOT_FOUND');
        }

        // Check for circular reference
        if (data.parentId === id) {
          throw new AppError('دسته‌بندی نمی‌تواند والد خودش باشد', 400, 'CIRCULAR_REFERENCE');
        }

        // Check if the new parent is a descendant of this category
        const isDescendant = await this.isDescendant(id, data.parentId);
        if (isDescendant) {
          throw new AppError('دسته‌بندی نمی‌تواند فرزند خودش را به عنوان والد انتخاب کند', 400, 'CIRCULAR_REFERENCE');
        }
      }

      // Update category
      const updatedCategory = await prisma.category.update({
        where: { id },
        data: {
          name: data.name,
          nameEn: data.nameEn,
          slug: data.slug,
          description: data.description,
          image: data.image,
          parentId: data.parentId,
          isActive: data.isActive,
          sortOrder: data.sortOrder,
        },
        include: {
          parent: true,
          children: true,
          _count: {
            select: {
              products: true,
              children: true,
            },
          },
        },
      });

      logger.info(`Category updated successfully: ${updatedCategory.name} (${updatedCategory.slug})`);
      return updatedCategory as CategoryWithRelations;
    } catch (error) {
      logger.error('Category update failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در به‌روزرسانی دسته‌بندی', 500, 'CATEGORY_UPDATE_FAILED');
    }
  }

  // Delete category
  static async deleteCategory(id: string): Promise<void> {
    try {
      // Check if category exists
      const existingCategory = await prisma.category.findUnique({
        where: { id },
        include: {
          children: true,
          products: true,
        },
      });

      if (!existingCategory) {
        throw new AppError('دسته‌بندی یافت نشد', 404, 'CATEGORY_NOT_FOUND');
      }

      // Check if category has children
      if (existingCategory.children.length > 0) {
        throw new AppError(
          'امکان حذف دسته‌بندی وجود ندارد زیرا دارای زیردسته است',
          400,
          'CATEGORY_HAS_CHILDREN'
        );
      }

      // Check if category has products
      if (existingCategory.products.length > 0) {
        throw new AppError(
          'امکان حذف دسته‌بندی وجود ندارد زیرا دارای محصول است',
          400,
          'CATEGORY_HAS_PRODUCTS'
        );
      }

      // Delete category image if exists
      if (existingCategory.image) {
        const urlParts = existingCategory.image.split('/');
        const filename = urlParts[urlParts.length - 1];
        await UploadService.deleteFile(filename, 'categories');
      }

      // Delete the category
      await prisma.category.delete({
        where: { id },
      });

      logger.info(`Category deleted successfully: ${existingCategory.name} (${existingCategory.slug})`);
    } catch (error) {
      logger.error('Category deletion failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در حذف دسته‌بندی', 500, 'CATEGORY_DELETION_FAILED');
    }
  }

  // Get category tree (hierarchical structure)
  static async getCategoryTree(): Promise<CategoryWithRelations[]> {
    try {
      // Get all root categories (no parent)
      const rootCategories = await prisma.category.findMany({
        where: {
          parentId: null,
          isActive: true,
        },
        include: {
          children: {
            where: { isActive: true },
            include: {
              children: {
                where: { isActive: true },
                orderBy: { sortOrder: 'asc' },
              },
              _count: {
                select: {
                  products: true,
                  children: true,
                },
              },
            },
            orderBy: { sortOrder: 'asc' },
          },
          _count: {
            select: {
              products: true,
              children: true,
            },
          },
        },
        orderBy: { sortOrder: 'asc' },
      });

      return rootCategories as CategoryWithRelations[];
    } catch (error) {
      logger.error('Get category tree failed:', error);
      throw new AppError('خطا در دریافت درخت دسته‌بندی‌ها', 500, 'GET_CATEGORY_TREE_FAILED');
    }
  }

  // Helper method to check if a category is a descendant of another
  private static async isDescendant(ancestorId: string, descendantId: string): Promise<boolean> {
    const descendant = await prisma.category.findUnique({
      where: { id: descendantId },
      include: { parent: true },
    });

    if (!descendant || !descendant.parent) {
      return false;
    }

    if (descendant.parent.id === ancestorId) {
      return true;
    }

    return await this.isDescendant(ancestorId, descendant.parent.id);
  }

  // Get category breadcrumbs
  static async getCategoryBreadcrumbs(categoryId: string): Promise<Category[]> {
    try {
      const breadcrumbs: Category[] = [];
      let currentCategory: any = await prisma.category.findUnique({
        where: { id: categoryId },
        include: { parent: true },
      });

      while (currentCategory) {
        breadcrumbs.unshift(currentCategory);
        currentCategory = currentCategory.parent;
      }

      return breadcrumbs;
    } catch (error) {
      logger.error('Get category breadcrumbs failed:', error);
      throw new AppError('خطا در دریافت مسیر دسته‌بندی', 500, 'GET_BREADCRUMBS_FAILED');
    }
  }
}
