import { LoyaltyAccount, LoyaltyTransaction, LoyaltyTier, LoyaltyTransactionType, User } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

// Types for loyalty management
export interface LoyaltyAccountWithDetails extends LoyaltyAccount {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  transactions: LoyaltyTransaction[];
  _count: {
    transactions: number;
  };
}

export interface CreateLoyaltyAccountData {
  userId: string;
  initialPoints?: number;
  tier?: LoyaltyTier;
}

export interface EarnPointsData {
  userId: string;
  points: number;
  description: string;
  orderId?: string;
  expiresAt?: Date;
}

export interface RedeemPointsData {
  userId: string;
  points: number;
  description: string;
  orderId?: string;
}

export interface LoyaltyFilters {
  tier?: LoyaltyTier;
  minPoints?: number;
  maxPoints?: number;
  hasTransactions?: boolean;
  createdFrom?: Date;
  createdTo?: Date;
  search?: string;
}

export interface LoyaltyListOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  include?: {
    user?: boolean;
    transactions?: boolean;
    _count?: boolean;
  };
}

export interface LoyaltyStatistics {
  totalAccounts: number;
  activeAccounts: number;
  totalPointsIssued: number;
  totalPointsRedeemed: number;
  averagePointsPerAccount: number;
  tierDistribution: Array<{
    tier: LoyaltyTier;
    count: number;
    percentage: number;
  }>;
  topMembers: Array<{
    userId: string;
    firstName: string;
    lastName: string;
    email: string;
    points: number;
    totalEarned: number;
    tier: LoyaltyTier;
  }>;
  pointsActivity: Array<{
    date: string;
    earned: number;
    redeemed: number;
  }>;
}

// Loyalty tier configuration
export const LOYALTY_TIER_CONFIG = {
  BRONZE: { minPoints: 0, maxPoints: 999, multiplier: 1 },
  SILVER: { minPoints: 1000, maxPoints: 4999, multiplier: 1.5 },
  GOLD: { minPoints: 5000, maxPoints: 19999, multiplier: 2 },
  PLATINUM: { minPoints: 20000, maxPoints: null, multiplier: 3 },
};

// Point earning rules
export const POINT_EARNING_RULES = {
  baseRate: 10000, // 1 point per 10,000 IRR
  bonusActivities: {
    firstPurchase: 100,
    review: 20,
    referral: 200,
    birthday: 100,
    socialShare: 10,
  },
  pointExpiry: 365, // days
};

export class LoyaltyService {
  // Get loyalty accounts with filtering and pagination
  static async getLoyaltyAccounts(
    filters: LoyaltyFilters = {},
    options: LoyaltyListOptions = {}
  ): Promise<{
    accounts: LoyaltyAccountWithDetails[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        tier,
        minPoints,
        maxPoints,
        hasTransactions,
        createdFrom,
        createdTo,
        search,
      } = filters;

      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        include = {},
      } = options;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (tier) {
        where.tier = tier;
      }

      if (minPoints !== undefined) {
        where.points = { ...where.points, gte: minPoints };
      }

      if (maxPoints !== undefined) {
        where.points = { ...where.points, lte: maxPoints };
      }

      if (createdFrom || createdTo) {
        where.createdAt = {};
        if (createdFrom) {
          where.createdAt.gte = createdFrom;
        }
        if (createdTo) {
          where.createdAt.lte = createdTo;
        }
      }

      if (hasTransactions !== undefined) {
        if (hasTransactions) {
          where.transactions = { some: {} };
        } else {
          where.transactions = { none: {} };
        }
      }

      if (search) {
        where.user = {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
          ],
        };
      }

      // Build include clause
      const includeClause: any = {};

      if (include.user) {
        includeClause.user = {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        };
      }

      if (include.transactions) {
        includeClause.transactions = {
          orderBy: { createdAt: 'desc' },
          take: 10,
        };
      }

      if (include._count) {
        includeClause._count = {
          select: {
            transactions: true,
          },
        };
      }

      // Execute queries
      const [accounts, total] = await Promise.all([
        prisma.loyaltyAccount.findMany({
          where,
          include: includeClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        prisma.loyaltyAccount.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        accounts: accounts as any as LoyaltyAccountWithDetails[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Error getting loyalty accounts:', error);
      throw new AppError('خطا در دریافت لیست حساب‌های وفاداری', 500, 'GET_LOYALTY_ACCOUNTS_ERROR');
    }
  }

  // Get loyalty account by user ID
  static async getLoyaltyAccountByUserId(userId: string): Promise<LoyaltyAccountWithDetails | null> {
    try {
      const account = await prisma.loyaltyAccount.findUnique({
        where: { userId },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          transactions: {
            orderBy: { createdAt: 'desc' },
            take: 20,
          },
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });

      return account as LoyaltyAccountWithDetails | null;
    } catch (error) {
      logger.error('Error getting loyalty account by user ID:', error);
      throw new AppError('خطا در دریافت حساب وفاداری کاربر', 500, 'GET_LOYALTY_ACCOUNT_ERROR');
    }
  }

  // Create loyalty account
  static async createLoyaltyAccount(data: CreateLoyaltyAccountData): Promise<LoyaltyAccountWithDetails> {
    try {
      const { userId, initialPoints = 0, tier = LoyaltyTier.BRONZE } = data;

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new AppError('کاربر یافت نشد', 404, 'USER_NOT_FOUND');
      }

      // Check if loyalty account already exists
      const existingAccount = await prisma.loyaltyAccount.findUnique({
        where: { userId },
      });

      if (existingAccount) {
        throw new AppError('حساب وفاداری برای این کاربر قبلاً ایجاد شده است', 400, 'LOYALTY_ACCOUNT_EXISTS');
      }

      // Create loyalty account
      const account = await prisma.loyaltyAccount.create({
        data: {
          userId,
          points: initialPoints,
          totalEarned: initialPoints,
          tier,
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          transactions: true,
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });

      // Create initial transaction if points are given
      if (initialPoints > 0) {
        await this.createTransaction({
          accountId: account.id,
          type: LoyaltyTransactionType.BONUS,
          points: initialPoints,
          description: 'امتیاز اولیه عضویت در باشگاه مشتریان',
        });
      }

      logger.info(`Loyalty account created for user: ${userId}`);

      return account as LoyaltyAccountWithDetails;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error creating loyalty account:', error);
      throw new AppError('خطا در ایجاد حساب وفاداری', 500, 'CREATE_LOYALTY_ACCOUNT_ERROR');
    }
  }

  // Earn points
  static async earnPoints(data: EarnPointsData): Promise<LoyaltyTransaction> {
    try {
      const { userId, points, description, orderId, expiresAt } = data;

      // Get or create loyalty account
      let account = await this.getLoyaltyAccountByUserId(userId);
      if (!account) {
        account = await this.createLoyaltyAccount({ userId });
      }

      // Calculate tier multiplier
      const tierConfig = LOYALTY_TIER_CONFIG[account.tier];
      const finalPoints = Math.floor(points * tierConfig.multiplier);

      // Create transaction
      const transaction = await this.createTransaction({
        accountId: account.id,
        type: LoyaltyTransactionType.EARNED,
        points: finalPoints,
        description,
        orderId,
        expiresAt: expiresAt || new Date(Date.now() + POINT_EARNING_RULES.pointExpiry * 24 * 60 * 60 * 1000),
      });

      // Update account points and totals
      const newPoints = account.points + finalPoints;
      const newTotalEarned = account.totalEarned + finalPoints;

      // Check for tier upgrade
      const newTier = this.calculateTier(newPoints);

      await prisma.loyaltyAccount.update({
        where: { id: account.id },
        data: {
          points: newPoints,
          totalEarned: newTotalEarned,
          tier: newTier,
        },
      });

      logger.info(`Points earned: ${finalPoints} for user ${userId}`);

      return transaction;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error earning points:', error);
      throw new AppError('خطا در کسب امتیاز', 500, 'EARN_POINTS_ERROR');
    }
  }

  // Redeem points
  static async redeemPoints(data: RedeemPointsData): Promise<LoyaltyTransaction> {
    try {
      const { userId, points, description, orderId } = data;

      // Get loyalty account
      const account = await this.getLoyaltyAccountByUserId(userId);
      if (!account) {
        throw new AppError('حساب وفاداری یافت نشد', 404, 'LOYALTY_ACCOUNT_NOT_FOUND');
      }

      // Check if user has enough points
      if (account.points < points) {
        throw new AppError('امتیاز کافی برای این عملیات وجود ندارد', 400, 'INSUFFICIENT_POINTS');
      }

      // Create redemption transaction
      const transaction = await this.createTransaction({
        accountId: account.id,
        type: LoyaltyTransactionType.REDEEMED,
        points: -points,
        description,
        orderId,
      });

      // Update account points and totals
      const newPoints = account.points - points;
      const newTotalRedeemed = account.totalRedeemed + points;

      // Check for tier downgrade (if applicable)
      const newTier = this.calculateTier(newPoints);

      await prisma.loyaltyAccount.update({
        where: { id: account.id },
        data: {
          points: newPoints,
          totalRedeemed: newTotalRedeemed,
          tier: newTier,
        },
      });

      logger.info(`Points redeemed: ${points} for user ${userId}`);

      return transaction;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error redeeming points:', error);
      throw new AppError('خطا در استفاده از امتیاز', 500, 'REDEEM_POINTS_ERROR');
    }
  }

  // Create transaction
  static async createTransaction(data: {
    accountId: string;
    type: LoyaltyTransactionType;
    points: number;
    description: string;
    orderId?: string;
    expiresAt?: Date;
  }): Promise<LoyaltyTransaction> {
    try {
      const transaction = await prisma.loyaltyTransaction.create({
        data,
      });

      return transaction;
    } catch (error) {
      logger.error('Error creating loyalty transaction:', error);
      throw new AppError('خطا در ایجاد تراکنش امتیاز', 500, 'CREATE_TRANSACTION_ERROR');
    }
  }

  // Calculate tier based on points
  static calculateTier(points: number): LoyaltyTier {
    if (points >= LOYALTY_TIER_CONFIG.PLATINUM.minPoints) {
      return LoyaltyTier.PLATINUM;
    } else if (points >= LOYALTY_TIER_CONFIG.GOLD.minPoints) {
      return LoyaltyTier.GOLD;
    } else if (points >= LOYALTY_TIER_CONFIG.SILVER.minPoints) {
      return LoyaltyTier.SILVER;
    } else {
      return LoyaltyTier.BRONZE;
    }
  }

  // Calculate points for order amount
  static calculatePointsForOrder(orderAmount: number, tier: LoyaltyTier = LoyaltyTier.BRONZE): number {
    const basePoints = Math.floor(orderAmount / POINT_EARNING_RULES.baseRate);
    const tierConfig = LOYALTY_TIER_CONFIG[tier];
    return Math.floor(basePoints * tierConfig.multiplier);
  }

  // Get loyalty transactions with filtering
  static async getLoyaltyTransactions(
    filters: {
      accountId?: string;
      userId?: string;
      type?: LoyaltyTransactionType;
      orderId?: string;
      dateFrom?: Date;
      dateTo?: Date;
    } = {},
    options: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    transactions: LoyaltyTransaction[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        accountId,
        userId,
        type,
        orderId,
        dateFrom,
        dateTo,
      } = filters;

      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = options;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (accountId) {
        where.accountId = accountId;
      }

      if (userId) {
        where.account = { userId };
      }

      if (type) {
        where.type = type;
      }

      if (orderId) {
        where.orderId = orderId;
      }

      if (dateFrom || dateTo) {
        where.createdAt = {};
        if (dateFrom) {
          where.createdAt.gte = dateFrom;
        }
        if (dateTo) {
          where.createdAt.lte = dateTo;
        }
      }

      // Execute queries
      const [transactions, total] = await Promise.all([
        prisma.loyaltyTransaction.findMany({
          where,
          include: {
            account: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        prisma.loyaltyTransaction.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Error getting loyalty transactions:', error);
      throw new AppError('خطا در دریافت تراکنش‌های امتیاز', 500, 'GET_TRANSACTIONS_ERROR');
    }
  }

  // Get loyalty statistics
  static async getLoyaltyStatistics(): Promise<LoyaltyStatistics> {
    try {
      const [
        totalAccounts,
        activeAccounts,
        pointsStats,
        tierDistribution,
        topMembers,
        recentActivity,
      ] = await Promise.all([
        // Total accounts
        prisma.loyaltyAccount.count(),

        // Active accounts (with transactions in last 30 days)
        prisma.loyaltyAccount.count({
          where: {
            transactions: {
              some: {
                createdAt: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                },
              },
            },
          },
        }),

        // Points statistics
        prisma.loyaltyAccount.aggregate({
          _sum: {
            totalEarned: true,
            totalRedeemed: true,
            points: true,
          },
          _avg: {
            points: true,
          },
        }),

        // Tier distribution
        prisma.loyaltyAccount.groupBy({
          by: ['tier'],
          _count: { tier: true },
        }),

        // Top members
        prisma.loyaltyAccount.findMany({
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: { points: 'desc' },
          take: 10,
        }),

        // Recent activity (last 30 days)
        prisma.$queryRaw`
          SELECT DATE(created_at) as date,
                 SUM(CASE WHEN points > 0 THEN points ELSE 0 END) as earned,
                 SUM(CASE WHEN points < 0 THEN ABS(points) ELSE 0 END) as redeemed
          FROM loyalty_transactions
          WHERE created_at >= ${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `,
      ]);

      // Process tier distribution
      const tierDistributionData = tierDistribution.map((item) => ({
        tier: item.tier,
        count: item._count.tier,
        percentage: totalAccounts > 0 ? (item._count.tier / totalAccounts) * 100 : 0,
      }));

      // Process top members
      const topMembersData = topMembers.map((member) => ({
        userId: member.userId,
        firstName: member.user.firstName,
        lastName: member.user.lastName,
        email: member.user.email,
        points: member.points,
        totalEarned: member.totalEarned,
        tier: member.tier,
      }));

      // Process recent activity
      const pointsActivityData = (recentActivity as any[]).map((item) => ({
        date: item.date.toISOString().split('T')[0],
        earned: Number(item.earned) || 0,
        redeemed: Number(item.redeemed) || 0,
      }));

      return {
        totalAccounts,
        activeAccounts,
        totalPointsIssued: Number(pointsStats._sum.totalEarned) || 0,
        totalPointsRedeemed: Number(pointsStats._sum.totalRedeemed) || 0,
        averagePointsPerAccount: Number(pointsStats._avg.points) || 0,
        tierDistribution: tierDistributionData,
        topMembers: topMembersData,
        pointsActivity: pointsActivityData,
      };
    } catch (error) {
      logger.error('Error getting loyalty statistics:', error);
      throw new AppError('خطا در دریافت آمار وفاداری', 500, 'GET_LOYALTY_STATS_ERROR');
    }
  }

  // Expire points
  static async expirePoints(): Promise<{ expiredTransactions: number; totalPointsExpired: number }> {
    try {
      const now = new Date();

      // Find expired transactions
      const expiredTransactions = await prisma.loyaltyTransaction.findMany({
        where: {
          type: LoyaltyTransactionType.EARNED,
          expiresAt: {
            lte: now,
          },
          // Only transactions that haven't been processed for expiration
          NOT: {
            account: {
              transactions: {
                some: {
                  description: {
                    contains: 'انقضای امتیاز',
                  },
                },
              },
            },
          },
        },
        include: {
          account: true,
        },
      });

      let totalPointsExpired = 0;
      let expiredCount = 0;

      for (const transaction of expiredTransactions) {
        // Create expiration transaction
        await this.createTransaction({
          accountId: transaction.accountId,
          type: LoyaltyTransactionType.EXPIRED,
          points: -transaction.points,
          description: `انقضای امتیاز - ${transaction.description}`,
        });

        // Update account points
        await prisma.loyaltyAccount.update({
          where: { id: transaction.accountId },
          data: {
            points: {
              decrement: transaction.points,
            },
          },
        });

        totalPointsExpired += transaction.points;
        expiredCount++;
      }

      logger.info(`Expired ${expiredCount} transactions, total points: ${totalPointsExpired}`);

      return {
        expiredTransactions: expiredCount,
        totalPointsExpired,
      };
    } catch (error) {
      logger.error('Error expiring points:', error);
      throw new AppError('خطا در انقضای امتیازات', 500, 'EXPIRE_POINTS_ERROR');
    }
  }

  // Adjust points (admin only)
  static async adjustPoints(
    userId: string,
    points: number,
    description: string,
    adminId: string
  ): Promise<LoyaltyTransaction> {
    try {
      // Get or create loyalty account
      let account = await this.getLoyaltyAccountByUserId(userId);
      if (!account) {
        account = await this.createLoyaltyAccount({ userId });
      }

      // Create adjustment transaction
      const transaction = await this.createTransaction({
        accountId: account.id,
        type: LoyaltyTransactionType.ADJUSTMENT,
        points,
        description: `تعدیل امتیاز توسط مدیر - ${description}`,
      });

      // Update account points
      const newPoints = Math.max(0, account.points + points);
      const newTier = this.calculateTier(newPoints);

      await prisma.loyaltyAccount.update({
        where: { id: account.id },
        data: {
          points: newPoints,
          tier: newTier,
          ...(points > 0 && { totalEarned: account.totalEarned + points }),
          ...(points < 0 && { totalRedeemed: account.totalRedeemed + Math.abs(points) }),
        },
      });

      logger.info(`Points adjusted: ${points} for user ${userId} by admin ${adminId}`);

      return transaction;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error adjusting points:', error);
      throw new AppError('خطا در تعدیل امتیاز', 500, 'ADJUST_POINTS_ERROR');
    }
  }

  // Get tier benefits
  static getTierBenefits(tier: LoyaltyTier): {
    multiplier: number;
    minPoints: number;
    maxPoints: number | null;
    benefits: string[];
  } {
    const config = LOYALTY_TIER_CONFIG[tier];
    const benefits = [];

    switch (tier) {
      case LoyaltyTier.BRONZE:
        benefits.push(
          'کسب ۱ امتیاز به ازای هر ۱۰ هزار تومان خرید',
          'اطلاع از تخفیف‌های ویژه',
          'پشتیبانی اولویت‌دار'
        );
        break;
      case LoyaltyTier.SILVER:
        benefits.push(
          'کسب ۱.۵ امتیاز به ازای هر ۱۰ هزار تومان خرید',
          'تخفیف ۵٪ در تمام خریدها',
          'ارسال رایگان برای خریدهای بالای ۳۰۰ هزار تومان',
          'دسترسی زودهنگام به محصولات جدید'
        );
        break;
      case LoyaltyTier.GOLD:
        benefits.push(
          'کسب ۲ امتیاز به ازای هر ۱۰ هزار تومان خرید',
          'تخفیف ۱۰٪ در تمام خریدها',
          'ارسال رایگان برای تمام خریدها',
          'هدیه تولد ویژه',
          'مشاوره رایگان پوست'
        );
        break;
      case LoyaltyTier.PLATINUM:
        benefits.push(
          'کسب ۳ امتیاز به ازای هر ۱۰ هزار تومان خرید',
          'تخفیف ۱۵٪ در تمام خریدها',
          'ارسال اکسپرس رایگان',
          'هدیه تولد ویژه و امتیاز اضافی',
          'مشاوره اختصاصی پوست',
          'دسترسی به محصولات محدود'
        );
        break;
    }

    return {
      multiplier: config.multiplier,
      minPoints: config.minPoints,
      maxPoints: config.maxPoints,
      benefits,
    };
  }
}

export default LoyaltyService;
