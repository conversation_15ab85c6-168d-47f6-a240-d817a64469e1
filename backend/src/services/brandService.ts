import { PrismaClient, Brand } from '@prisma/client';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

const prisma = new PrismaClient();

// Types
export interface CreateBrandData {
  name: string;
  nameEn?: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  country?: string;
  isActive?: boolean;
}

export interface UpdateBrandData {
  name?: string;
  nameEn?: string;
  slug?: string;
  description?: string;
  logo?: string;
  website?: string;
  country?: string;
  isActive?: boolean;
}

export interface BrandWithStats extends Brand {
  productsCount: number;
}

export interface BrandQueryOptions {
  includeInactive?: boolean;
  includeProductCount?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
}

export class BrandService {
  // Get all brands
  static async getBrands(options: BrandQueryOptions = {}): Promise<BrandWithStats[]> {
    try {
      const {
        includeInactive = false,
        includeProductCount = true,
        search,
        limit,
        offset
      } = options;

      const where: any = {};

      // Filter by active status
      if (!includeInactive) {
        where.isActive = true;
      }

      // Search filter
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { nameEn: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      const brands = await prisma.brand.findMany({
        where,
        include: {
          _count: includeProductCount ? {
            select: { products: true }
          } : undefined,
        },
        orderBy: { name: 'asc' },
        take: limit,
        skip: offset,
      });

      return brands.map(brand => ({
        ...brand,
        productsCount: brand._count?.products || 0,
      })) as BrandWithStats[];
    } catch (error) {
      logger.error('Failed to fetch brands:', error);
      throw new AppError('خطا در دریافت برندها', 500, 'BRANDS_FETCH_FAILED');
    }
  }

  // Get brand by ID
  static async getBrandById(id: string): Promise<BrandWithStats> {
    try {
      const brand = await prisma.brand.findUnique({
        where: { id },
        include: {
          _count: {
            select: { products: true }
          },
          products: {
            where: { isActive: true },
            include: {
              images: {
                where: { isPrimary: true },
                take: 1,
              },
            },
            take: 10,
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!brand) {
        throw new AppError('برند یافت نشد', 404, 'BRAND_NOT_FOUND');
      }

      return {
        ...brand,
        productsCount: brand._count.products,
      } as BrandWithStats;
    } catch (error) {
      logger.error('Failed to fetch brand:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت برند', 500, 'BRAND_FETCH_FAILED');
    }
  }

  // Get brand by slug
  static async getBrandBySlug(slug: string): Promise<BrandWithStats> {
    try {
      const brand = await prisma.brand.findUnique({
        where: { slug },
        include: {
          _count: {
            select: { products: true }
          },
          products: {
            where: { isActive: true },
            include: {
              images: {
                where: { isPrimary: true },
                take: 1,
              },
              brand: true,
              categories: {
                include: {
                  category: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!brand) {
        throw new AppError('برند یافت نشد', 404, 'BRAND_NOT_FOUND');
      }

      return {
        ...brand,
        productsCount: brand._count.products,
      } as BrandWithStats;
    } catch (error) {
      logger.error('Failed to fetch brand by slug:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت برند', 500, 'BRAND_FETCH_FAILED');
    }
  }

  // Create brand
  static async createBrand(data: CreateBrandData): Promise<Brand> {
    try {
      // Check if slug already exists
      const existingBrand = await prisma.brand.findUnique({
        where: { slug: data.slug },
      });

      if (existingBrand) {
        throw new AppError('برندی با این نام انگلیسی قبلاً ثبت شده است', 400, 'BRAND_SLUG_EXISTS');
      }

      const brand = await prisma.brand.create({
        data: {
          name: data.name,
          nameEn: data.nameEn,
          slug: data.slug,
          description: data.description,
          logo: data.logo,
          website: data.website,
          country: data.country,
          isActive: data.isActive ?? true,
        },
      });

      logger.info(`Brand created successfully: ${brand.name} (${brand.slug})`);
      return brand;
    } catch (error) {
      logger.error('Brand creation failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در ایجاد برند', 500, 'BRAND_CREATION_FAILED');
    }
  }

  // Update brand
  static async updateBrand(id: string, data: UpdateBrandData): Promise<Brand> {
    try {
      // Check if brand exists
      const existingBrand = await prisma.brand.findUnique({
        where: { id },
      });

      if (!existingBrand) {
        throw new AppError('برند یافت نشد', 404, 'BRAND_NOT_FOUND');
      }

      // Check if slug is being changed and already exists
      if (data.slug && data.slug !== existingBrand.slug) {
        const slugExists = await prisma.brand.findUnique({
          where: { slug: data.slug },
        });

        if (slugExists) {
          throw new AppError('برندی با این نام انگلیسی قبلاً ثبت شده است', 400, 'BRAND_SLUG_EXISTS');
        }
      }

      const brand = await prisma.brand.update({
        where: { id },
        data: {
          name: data.name,
          nameEn: data.nameEn,
          slug: data.slug,
          description: data.description,
          logo: data.logo,
          website: data.website,
          country: data.country,
          isActive: data.isActive,
        },
      });

      logger.info(`Brand updated successfully: ${brand.name} (${brand.slug})`);
      return brand;
    } catch (error) {
      logger.error('Brand update failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در بروزرسانی برند', 500, 'BRAND_UPDATE_FAILED');
    }
  }

  // Delete brand
  static async deleteBrand(id: string): Promise<void> {
    try {
      // Check if brand exists and has products
      const existingBrand = await prisma.brand.findUnique({
        where: { id },
        include: {
          products: true,
        },
      });

      if (!existingBrand) {
        throw new AppError('برند یافت نشد', 404, 'BRAND_NOT_FOUND');
      }

      // Check if brand has products
      if (existingBrand.products.length > 0) {
        throw new AppError('امکان حذف برند وجود ندارد. ابتدا محصولات مرتبط را حذف کنید', 400, 'BRAND_HAS_PRODUCTS');
      }

      await prisma.brand.delete({
        where: { id },
      });

      logger.info(`Brand deleted successfully: ${existingBrand.name} (${existingBrand.slug})`);
    } catch (error) {
      logger.error('Brand deletion failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در حذف برند', 500, 'BRAND_DELETION_FAILED');
    }
  }

  // Get brand statistics
  static async getBrandStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    totalProducts: number;
  }> {
    try {
      const [total, active, totalProducts] = await Promise.all([
        prisma.brand.count(),
        prisma.brand.count({ where: { isActive: true } }),
        prisma.product.count({ where: { brandId: { not: null } } }),
      ]);

      return {
        total,
        active,
        inactive: total - active,
        totalProducts,
      };
    } catch (error) {
      logger.error('Failed to fetch brand statistics:', error);
      throw new AppError('خطا در دریافت آمار برندها', 500, 'BRAND_STATS_FAILED');
    }
  }
}

export default BrandService;
