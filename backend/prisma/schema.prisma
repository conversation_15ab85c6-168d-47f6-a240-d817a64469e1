// Prisma Schema for GlowRoya Persian Skincare E-commerce Platform
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  phone             String?   @unique
  password          String
  firstName         String
  lastName          String
  avatar            String?
  isEmailVerified   Boolean   @default(false)
  isPhoneVerified   Boolean   @default(false)
  role              UserRole  @default(CUSTOMER)
  status            UserStatus @default(ACTIVE)
  lastLoginAt       DateTime?
  emailVerifiedAt   DateTime?
  phoneVerifiedAt   DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  addresses         Address[]
  orders            Order[]
  reviews           Review[]
  reviewVotes       ReviewVote[]
  reviewResponses   ReviewResponse[]
  wishlistItems     WishlistItem[]
  loyaltyAccount    LoyaltyAccount?
  notifications     UserNotification[]
  sessions          UserSession[]
  passwordResets    PasswordReset[]

  // Content Management Relations
  bannersCreated    Banner[] @relation("BannerCreator")
  promotionsCreated Promotion[] @relation("PromotionCreator")
  newslettersCreated NewsletterCampaign[] @relation("NewsletterCreator")
  pagesCreated      PageContent[] @relation("PageCreator")
  mediaUploaded     MediaItem[] @relation("MediaUploader")

  @@map("users")
}

model Address {
  id          String      @id @default(cuid())
  userId      String
  title       String      // e.g., "خانه", "محل کار"
  firstName   String
  lastName    String
  phone       String
  province    String
  city        String
  district    String?
  street      String
  postalCode  String
  isDefault   Boolean     @default(false)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders      Order[]

  @@map("addresses")
}

// Product Management
model Category {
  id          String      @id @default(cuid())
  name        String
  nameEn      String?
  slug        String      @unique
  description String?
  image       String?
  parentId    String?
  isActive    Boolean     @default(true)
  sortOrder   Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  parent      Category?   @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[]  @relation("CategoryHierarchy")
  products    ProductCategory[]

  @@map("categories")
}

model Brand {
  id          String      @id @default(cuid())
  name        String      @unique
  nameEn      String?
  slug        String      @unique
  description String?
  logo        String?
  website     String?
  country     String?
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  products    Product[]

  @@map("brands")
}

model Product {
  id              String      @id @default(cuid())
  name            String
  nameEn          String?
  slug            String      @unique
  description     String?
  shortDescription String?
  sku             String      @unique
  barcode         String?
  brandId         String?
  price           Decimal     @db.Decimal(10, 2)
  comparePrice    Decimal?    @db.Decimal(10, 2)
  costPrice       Decimal?    @db.Decimal(10, 2)
  weight          Decimal?    @db.Decimal(8, 3)
  dimensions      Json?       // {length, width, height}
  isActive        Boolean     @default(true)
  isFeatured      Boolean     @default(false)
  isDigital       Boolean     @default(false)
  requiresShipping Boolean    @default(true)
  trackQuantity   Boolean     @default(true)
  allowBackorder  Boolean     @default(false)
  metaTitle       String?
  metaDescription String?
  tags            String[]
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  brand           Brand?      @relation(fields: [brandId], references: [id])
  categories      ProductCategory[]
  variants        ProductVariant[]
  images          ProductImage[]
  reviews         Review[]
  wishlistItems   WishlistItem[]
  orderItems      OrderItem[]
  inventory       ProductInventory?

  @@map("products")
}

model ProductCategory {
  productId   String
  categoryId  String
  createdAt   DateTime    @default(now())

  // Relations
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  category    Category    @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([productId, categoryId])
  @@map("product_categories")
}

model ProductVariant {
  id          String      @id @default(cuid())
  productId   String
  name        String      // e.g., "سایز کوچک", "رنگ قرمز"
  value       String      // e.g., "50ml", "#FF0000"
  price       Decimal?    @db.Decimal(10, 2)
  sku         String?     @unique
  barcode     String?
  weight      Decimal?    @db.Decimal(8, 3)
  isActive    Boolean     @default(true)
  sortOrder   Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  inventory   ProductInventory[]
  orderItems  OrderItem[]

  @@map("product_variants")
}

model ProductImage {
  id          String      @id @default(cuid())
  productId   String
  url         String
  alt         String?
  isPrimary   Boolean     @default(false)
  sortOrder   Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductInventory {
  id              String      @id @default(cuid())
  productId       String?     @unique
  variantId       String?     @unique
  quantity        Int         @default(0)
  reservedQuantity Int        @default(0)
  lowStockThreshold Int       @default(10)
  location        String?     // Warehouse location
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  product         Product?    @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant         ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("product_inventory")
}

// Order Management
model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  userId          String?
  guestEmail      String?
  status          OrderStatus @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING)
  fulfillmentStatus FulfillmentStatus @default(UNFULFILLED)
  
  // Pricing
  subtotal        Decimal     @db.Decimal(10, 2)
  taxAmount       Decimal     @default(0) @db.Decimal(10, 2)
  shippingAmount  Decimal     @default(0) @db.Decimal(10, 2)
  discountAmount  Decimal     @default(0) @db.Decimal(10, 2)
  totalAmount     Decimal     @db.Decimal(10, 2)
  
  // Shipping
  shippingAddressId String?
  billingAddressId  String?
  shippingMethod    String?
  trackingNumber    String?
  
  // Metadata
  notes           String?
  internalNotes   String?
  tags            String[]
  
  // Timestamps
  placedAt        DateTime?
  shippedAt       DateTime?
  deliveredAt     DateTime?
  cancelledAt     DateTime?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  user            User?       @relation(fields: [userId], references: [id])
  shippingAddress Address?    @relation(fields: [shippingAddressId], references: [id])
  items           OrderItem[]
  payments        Payment[]
  notifications   OrderNotification[]

  @@map("orders")
}

model OrderItem {
  id          String      @id @default(cuid())
  orderId     String
  productId   String
  variantId   String?
  quantity    Int
  unitPrice   Decimal     @db.Decimal(10, 2)
  totalPrice  Decimal     @db.Decimal(10, 2)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  order       Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product     Product     @relation(fields: [productId], references: [id])
  variant     ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

// Payment Management
model Payment {
  id              String      @id @default(cuid())
  orderId         String
  amount          Decimal     @db.Decimal(10, 2)
  currency        String      @default("IRR")
  method          PaymentMethod
  status          PaymentStatus @default(PENDING)
  gateway         String      // e.g., "zarinpal", "mellat"
  gatewayTransactionId String?
  gatewayResponse Json?
  failureReason   String?
  processedAt     DateTime?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  order           Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payments")
}

// Review System
model Review {
  id              String      @id @default(cuid())
  productId       String
  userId          String
  rating          Int         // 1-5
  title           String?
  content         String
  pros            String[]    // Positive aspects
  cons            String[]    // Negative aspects
  isVerified      Boolean     @default(false) // Verified purchase
  isApproved      Boolean     @default(false) // Admin approval
  isRecommended   Boolean     @default(true)  // Would recommend
  helpfulCount    Int         @default(0)
  unhelpfulCount  Int         @default(0)
  skinType        String?     // User's skin type
  ageRange        String?     // User's age range
  usageDuration   String?     // How long they used the product
  moderationStatus ReviewModerationStatus @default(PENDING)
  moderationNotes String?     // Admin moderation notes
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  product         Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  user            User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  images          ReviewImage[]
  votes           ReviewVote[]
  responses       ReviewResponse[]

  @@unique([productId, userId])
  @@map("reviews")
}

model ReviewImage {
  id          String      @id @default(cuid())
  reviewId    String
  url         String
  alt         String?
  sortOrder   Int         @default(0)
  createdAt   DateTime    @default(now())

  // Relations
  review      Review      @relation(fields: [reviewId], references: [id], onDelete: Cascade)

  @@map("review_images")
}

model ReviewVote {
  id          String      @id @default(cuid())
  reviewId    String
  userId      String
  isHelpful   Boolean     // true = helpful, false = unhelpful
  createdAt   DateTime    @default(now())

  // Relations
  review      Review      @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([reviewId, userId])
  @@map("review_votes")
}

model ReviewResponse {
  id          String      @id @default(cuid())
  reviewId    String
  userId      String      // Admin or brand representative
  content     String
  isOfficial  Boolean     @default(false) // Official brand response
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  review      Review      @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("review_responses")
}

// Wishlist System
model WishlistItem {
  id          String      @id @default(cuid())
  userId      String
  productId   String
  createdAt   DateTime    @default(now())

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("wishlist_items")
}

// Loyalty Program
model LoyaltyAccount {
  id              String      @id @default(cuid())
  userId          String      @unique
  points          Int         @default(0)
  totalEarned     Int         @default(0)
  totalRedeemed   Int         @default(0)
  tier            LoyaltyTier @default(BRONZE)
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  user            User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions    LoyaltyTransaction[]

  @@map("loyalty_accounts")
}

model LoyaltyTransaction {
  id              String      @id @default(cuid())
  accountId       String
  type            LoyaltyTransactionType
  points          Int
  description     String
  orderId         String?
  expiresAt       DateTime?
  createdAt       DateTime    @default(now())

  // Relations
  account         LoyaltyAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)

  @@map("loyalty_transactions")
}

// Notification System
model UserNotification {
  id          String      @id @default(cuid())
  userId      String
  title       String
  message     String
  type        NotificationType
  isRead      Boolean     @default(false)
  data        Json?       // Additional notification data
  createdAt   DateTime    @default(now())
  readAt      DateTime?

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_notifications")
}

model OrderNotification {
  id          String      @id @default(cuid())
  orderId     String
  type        OrderNotificationType
  message     String
  sentAt      DateTime?
  createdAt   DateTime    @default(now())

  // Relations
  order       Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_notifications")
}

// Session Management
model UserSession {
  id          String      @id @default(cuid())
  userId      String
  token       String      @unique
  refreshToken String?    @unique
  userAgent   String?
  ipAddress   String?
  isActive    Boolean     @default(true)
  expiresAt   DateTime
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model PasswordReset {
  id          String      @id @default(cuid())
  userId      String
  token       String      @unique
  expiresAt   DateTime
  usedAt      DateTime?
  createdAt   DateTime    @default(now())

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_resets")
}

// Content Management System
model Banner {
  id              String   @id @default(cuid())
  title           String
  subtitle        String?
  description     String?
  type            String   // 'hero', 'promotional', 'announcement', 'category'
  image           String
  mobileImage     String?
  altText         String
  ctaText         String?
  ctaUrl          String?
  ctaType         String?  // 'internal', 'external'
  position        Int      @default(0)
  showOnPages     Json     // Array of page names
  backgroundColor String?
  textColor       String?
  overlayOpacity  Float?
  animationType   String?  // 'fade', 'slide', 'zoom', 'none'
  autoplay        Boolean  @default(false)
  duration        Int?     // seconds
  status          String   @default("draft") // 'draft', 'published', 'scheduled', 'archived'
  isActive        Boolean  @default(true)
  startDate       DateTime?
  endDate         DateTime?
  views           Int      @default(0)
  clicks          Int      @default(0)
  conversions     Int      @default(0)
  seoTitle        String?
  seoDescription  String?
  seoKeywords     Json?    // Array of keywords
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdBy       String
  publishedAt     DateTime?

  // Relations
  creator         User     @relation("BannerCreator", fields: [createdBy], references: [id])

  @@map("banners")
}

model Promotion {
  id                     String   @id @default(cuid())
  title                  String
  description            String?
  type                   String   // 'percentage', 'fixed_amount', 'buy_one_get_one', 'free_shipping'
  discountValue          Float
  minimumOrderAmount     Float?
  maximumDiscountAmount  Float?
  applicableCategories   Json?    // Array of category IDs
  applicableProducts     Json?    // Array of product IDs
  usageLimit             Int?
  usagePerCustomer       Int?
  currentUsage           Int      @default(0)
  code                   String?  @unique
  isCodeRequired         Boolean  @default(false)
  showOnHomepage         Boolean  @default(false)
  showInCart             Boolean  @default(true)
  showOnProductPages     Boolean  @default(false)
  bannerImage            String?
  terms                  String?
  conditions             Json?    // Array of condition strings
  status                 String   @default("draft")
  isActive               Boolean  @default(true)
  startDate              DateTime
  endDate                DateTime
  views                  Int      @default(0)
  clicks                 Int      @default(0)
  conversions            Int      @default(0)
  seoTitle               String?
  seoDescription         String?
  seoKeywords            Json?
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  createdBy              String
  publishedAt            DateTime?

  // Relations
  creator                User     @relation("PromotionCreator", fields: [createdBy], references: [id])

  @@map("promotions")
}

model NewsletterCampaign {
  id                String   @id @default(cuid())
  title             String
  subject           String
  preheader         String?
  content           String
  htmlContent       String?
  campaignType      String   // 'product_update', 'promotion', 'newsletter', 'announcement'
  template          String?
  recipientSegments Json     // Array of segment names
  recipientCount    Int      @default(0)
  sendAt            DateTime?
  timezone          String   @default("Asia/Tehran")
  sentCount         Int      @default(0)
  deliveredCount    Int      @default(0)
  openCount         Int      @default(0)
  clickCount        Int      @default(0)
  unsubscribeCount  Int      @default(0)
  bounceCount       Int      @default(0)
  openRate          Float    @default(0)
  clickRate         Float    @default(0)
  unsubscribeRate   Float    @default(0)
  bounceRate        Float    @default(0)
  status            String   @default("draft")
  isActive          Boolean  @default(true)
  views             Int      @default(0)
  clicks            Int      @default(0)
  conversions       Int      @default(0)
  seoTitle          String?
  seoDescription    String?
  seoKeywords       Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  createdBy         String
  publishedAt       DateTime?

  // Relations
  creator           User     @relation("NewsletterCreator", fields: [createdBy], references: [id])

  @@map("newsletter_campaigns")
}

model PageContent {
  id              String   @id @default(cuid())
  title           String
  content         String
  excerpt         String?
  template        String   @default("default") // 'default', 'landing', 'about', 'contact', 'custom'
  parentPage      String?
  menuOrder       Int      @default(0)
  showInMenu      Boolean  @default(true)
  metaTitle       String?
  metaDescription String?
  canonicalUrl    String?
  noIndex         Boolean  @default(false)
  noFollow        Boolean  @default(false)
  featuredImage   String?
  featuredImageAlt String?
  customFields    Json?    // Additional custom fields
  status          String   @default("draft")
  isActive        Boolean  @default(true)
  slug            String   @unique
  views           Int      @default(0)
  clicks          Int      @default(0)
  conversions     Int      @default(0)
  seoTitle        String?
  seoDescription  String?
  seoKeywords     Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdBy       String
  publishedAt     DateTime?

  // Relations
  creator         User     @relation("PageCreator", fields: [createdBy], references: [id])
  parent          PageContent? @relation("PageHierarchy", fields: [parentPage], references: [id])
  children        PageContent[] @relation("PageHierarchy")

  @@map("page_contents")
}

model MediaItem {
  id          String   @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int      // bytes
  url         String
  altText     String?
  caption     String?
  description String?
  tags        Json?    // Array of tags
  folder      String?  // Organization folder
  isPublic    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  uploadedBy  String

  // Relations
  uploader    User     @relation("MediaUploader", fields: [uploadedBy], references: [id])

  @@map("media_items")
}

// Enums
enum UserRole {
  CUSTOMER
  ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  BANNED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum FulfillmentStatus {
  UNFULFILLED
  PARTIAL
  FULFILLED
  SHIPPED
  DELIVERED
  RETURNED
}

enum PaymentMethod {
  CREDIT_CARD
  BANK_TRANSFER
  ZARINPAL
  MELLAT
  CASH_ON_DELIVERY
  WALLET
}

enum LoyaltyTier {
  BRONZE
  SILVER
  GOLD
  PLATINUM
}

enum LoyaltyTransactionType {
  EARNED
  REDEEMED
  EXPIRED
  BONUS
  ADJUSTMENT
}

enum NotificationType {
  ORDER_UPDATE
  PAYMENT_CONFIRMATION
  SHIPPING_UPDATE
  PROMOTION
  SYSTEM
  REVIEW_REQUEST
}

enum OrderNotificationType {
  ORDER_PLACED
  PAYMENT_RECEIVED
  ORDER_CONFIRMED
  ORDER_SHIPPED
  ORDER_DELIVERED
  ORDER_CANCELLED
}

enum ReviewModerationStatus {
  PENDING
  APPROVED
  REJECTED
  FLAGGED
}
