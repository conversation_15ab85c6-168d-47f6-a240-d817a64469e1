const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testRealData() {
  try {
    console.log('🔍 Testing real database data...');
    
    // Test users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
      }
    });
    console.log('👥 Users found:', users.length);
    users.forEach(user => {
      console.log(`   - ${user.firstName} ${user.lastName} (${user.email}) - ${user.role}`);
    });
    
    // Test categories
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        parentId: true,
      }
    });
    console.log('📂 Categories found:', categories.length);
    categories.forEach(cat => {
      console.log(`   - ${cat.name} (${cat.slug})`);
    });
    
    // Test brands
    const brands = await prisma.brand.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
      }
    });
    console.log('🏷️ Brands found:', brands.length);
    brands.forEach(brand => {
      console.log(`   - ${brand.name} (${brand.slug})`);
    });
    
    // Test products
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        price: true,
        brand: {
          select: {
            name: true
          }
        }
      }
    });
    console.log('🛍️ Products found:', products.length);
    products.forEach(product => {
      console.log(`   - ${product.name} (${product.brand?.name}) - ${product.price} IRR`);
    });
    
    console.log('✅ Real database test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing real data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testRealData();
