# Task 1.5: Customer Management API Documentation

## Overview

This document provides comprehensive documentation for the Customer Management API implemented in Task 1.5. The API includes full CRUD operations for customers, customer analytics, search functionality, bulk operations, and comprehensive customer management features with admin authentication and Persian language support.

## Base URL
```
http://localhost:3001/api/v1/customers
```

## Authentication

All customer management routes require admin authentication with JWT token:

```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "پیام فارسی",
  "data": {
    // Response data
  }
}
```

## Error Response Format

```json
{
  "success": false,
  "message": "پیام خطا",
  "code": "ERROR_CODE",
  "errors": [] // Validation errors if applicable
}
```

---

## API Endpoints

### 1. Get All Customers

**GET** `/api/v1/customers`

Get all customers with advanced filtering, pagination, and search capabilities.

#### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | number | صفحه (پیش‌فرض: 1) | `?page=2` |
| `limit` | number | تعداد نتایج در هر صفحه (پیش‌فرض: 20) | `?limit=50` |
| `search` | string | جستجو در نام، نام خانوادگی، ایمیل | `?search=احمد` |
| `role` | string | فیلتر بر اساس نقش | `?role=CUSTOMER` |
| `status` | string | فیلتر بر اساس وضعیت | `?status=ACTIVE` |
| `isEmailVerified` | boolean | فیلتر ایمیل تأیید شده | `?isEmailVerified=true` |
| `isPhoneVerified` | boolean | فیلتر تلفن تأیید شده | `?isPhoneVerified=false` |
| `hasOrders` | boolean | فیلتر مشتریان با سفارش | `?hasOrders=true` |
| `registrationDateFrom` | date | تاریخ شروع ثبت‌نام | `?registrationDateFrom=2024-01-01` |
| `registrationDateTo` | date | تاریخ پایان ثبت‌نام | `?registrationDateTo=2024-12-31` |
| `lastLoginFrom` | date | تاریخ شروع آخرین ورود | `?lastLoginFrom=2024-06-01` |
| `lastLoginTo` | date | تاریخ پایان آخرین ورود | `?lastLoginTo=2024-06-30` |
| `province` | string | فیلتر بر اساس استان | `?province=تهران` |
| `city` | string | فیلتر بر اساس شهر | `?city=تهران` |
| `sortBy` | string | مرتب‌سازی بر اساس فیلد | `?sortBy=createdAt` |
| `sortOrder` | string | ترتیب مرتب‌سازی (asc/desc) | `?sortOrder=desc` |
| `include` | string | شامل کردن اطلاعات اضافی | `?include=addresses,orderStats,_count` |

#### Response Example

```json
{
  "success": true,
  "message": "لیست مشتریان با موفقیت دریافت شد",
  "data": {
    "customers": [
      {
        "id": "clx1234567890",
        "email": "<EMAIL>",
        "firstName": "احمد",
        "lastName": "محمدی",
        "phone": "09123456789",
        "avatar": null,
        "role": "CUSTOMER",
        "status": "ACTIVE",
        "isEmailVerified": true,
        "isPhoneVerified": false,
        "lastLoginAt": "2024-06-06T10:30:00.000Z",
        "createdAt": "2024-01-15T08:00:00.000Z",
        "updatedAt": "2024-06-06T10:30:00.000Z",
        "addresses": [
          {
            "id": "addr123",
            "title": "منزل",
            "firstName": "احمد",
            "lastName": "محمدی",
            "phone": "09123456789",
            "province": "تهران",
            "city": "تهران",
            "district": "ونک",
            "street": "خیابان ولیعصر، پلاک 123",
            "postalCode": "1234567890",
            "isDefault": true
          }
        ],
        "orderStats": {
          "totalOrders": 5,
          "totalSpent": 2500000
        },
        "_count": {
          "orders": 5,
          "addresses": 2,
          "reviews": 3,
          "wishlistItems": 8
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

### 2. Get Customer by ID

**GET** `/api/v1/customers/:id`

Get detailed information about a specific customer.

#### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `includeOrders` | boolean | شامل کردن سفارش‌ها | `true` |
| `includeAddresses` | boolean | شامل کردن آدرس‌ها | `true` |
| `includeStats` | boolean | شامل کردن آمار | `true` |

#### Response Example

```json
{
  "success": true,
  "message": "اطلاعات مشتری با موفقیت دریافت شد",
  "data": {
    "customer": {
      "id": "clx1234567890",
      "email": "<EMAIL>",
      "firstName": "احمد",
      "lastName": "محمدی",
      "phone": "09123456789",
      "avatar": null,
      "role": "CUSTOMER",
      "status": "ACTIVE",
      "isEmailVerified": true,
      "isPhoneVerified": false,
      "lastLoginAt": "2024-06-06T10:30:00.000Z",
      "emailVerifiedAt": "2024-01-15T08:30:00.000Z",
      "phoneVerifiedAt": null,
      "createdAt": "2024-01-15T08:00:00.000Z",
      "updatedAt": "2024-06-06T10:30:00.000Z",
      "addresses": [
        {
          "id": "addr123",
          "title": "منزل",
          "firstName": "احمد",
          "lastName": "محمدی",
          "phone": "09123456789",
          "province": "تهران",
          "city": "تهران",
          "district": "ونک",
          "street": "خیابان ولیعصر، پلاک 123",
          "postalCode": "1234567890",
          "isDefault": true,
          "createdAt": "2024-01-15T08:15:00.000Z",
          "updatedAt": "2024-01-15T08:15:00.000Z"
        }
      ],
      "orders": [
        {
          "id": "order123",
          "orderNumber": "GR12345678901",
          "status": "DELIVERED",
          "totalAmount": 500000,
          "paymentStatus": "COMPLETED",
          "fulfillmentStatus": "FULFILLED",
          "createdAt": "2024-06-01T10:00:00.000Z",
          "updatedAt": "2024-06-05T14:30:00.000Z",
          "items": [
            {
              "id": "item123",
              "quantity": 2,
              "unitPrice": 250000,
              "product": {
                "id": "prod123",
                "name": "کرم مرطوب‌کننده",
                "slug": "moisturizing-cream",
                "images": [
                  {
                    "url": "/uploads/products/cream.jpg",
                    "isPrimary": true
                  }
                ]
              }
            }
          ]
        }
      ],
      "statistics": {
        "totalOrders": 5,
        "totalSpent": 2500000,
        "averageOrderValue": 500000,
        "recentActivity": [
          {
            "id": "order123",
            "orderNumber": "GR12345678901",
            "status": "DELIVERED",
            "totalAmount": 500000,
            "createdAt": "2024-06-01T10:00:00.000Z"
          }
        ]
      },
      "_count": {
        "orders": 5,
        "addresses": 2,
        "reviews": 3,
        "wishlistItems": 8
      }
    }
  }
}
```

### 3. Create New Customer

**POST** `/api/v1/customers`

Create a new customer (admin only).

#### Request Body

```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "علی",
  "lastName": "احمدی",
  "phone": "09123456789",
  "role": "CUSTOMER",
  "status": "ACTIVE",
  "isEmailVerified": false,
  "isPhoneVerified": false
}
```

#### Response Example

```json
{
  "success": true,
  "message": "مشتری جدید با موفقیت ایجاد شد",
  "data": {
    "customer": {
      "id": "clx1234567891",
      "email": "<EMAIL>",
      "firstName": "علی",
      "lastName": "احمدی",
      "phone": "09123456789",
      "role": "CUSTOMER",
      "status": "ACTIVE",
      "isEmailVerified": false,
      "isPhoneVerified": false,
      "createdAt": "2024-06-06T12:00:00.000Z",
      "updatedAt": "2024-06-06T12:00:00.000Z"
    }
  }
}
```

### 4. Update Customer

**PUT** `/api/v1/customers/:id`

Update customer information (admin only).

#### Request Body

```json
{
  "firstName": "علی رضا",
  "lastName": "احمدی",
  "phone": "09123456788",
  "status": "ACTIVE",
  "isEmailVerified": true,
  "isPhoneVerified": true
}
```

#### Response Example

```json
{
  "success": true,
  "message": "اطلاعات مشتری با موفقیت به‌روزرسانی شد",
  "data": {
    "customer": {
      "id": "clx1234567891",
      "email": "<EMAIL>",
      "firstName": "علی رضا",
      "lastName": "احمدی",
      "phone": "09123456788",
      "avatar": null,
      "role": "CUSTOMER",
      "status": "ACTIVE",
      "isEmailVerified": true,
      "isPhoneVerified": true,
      "lastLoginAt": null,
      "createdAt": "2024-06-06T12:00:00.000Z",
      "updatedAt": "2024-06-06T12:15:00.000Z"
    }
  }
}
```

### 5. Delete Customer

**DELETE** `/api/v1/customers/:id`

Soft delete a customer (admin only). Changes status to BANNED and anonymizes email.

#### Response Example

```json
{
  "success": true,
  "message": "مشتری با موفقیت حذف شد"
}
```

### 6. Get Customer Statistics

**GET** `/api/v1/customers/statistics`

Get comprehensive customer analytics and statistics.

#### Response Example

```json
{
  "success": true,
  "message": "آمار مشتریان با موفقیت دریافت شد",
  "data": {
    "statistics": {
      "totalCustomers": 1250,
      "activeCustomers": 1180,
      "newCustomersThisMonth": 45,
      "customersWithOrders": 890,
      "averageOrderValue": 425000,
      "topCustomersByOrders": [
        {
          "id": "clx1234567890",
          "firstName": "احمد",
          "lastName": "محمدی",
          "email": "<EMAIL>",
          "totalOrders": 25,
          "totalSpent": 12500000
        }
      ],
      "customersByProvince": [
        {
          "province": "تهران",
          "count": 450
        },
        {
          "province": "اصفهان",
          "count": 180
        }
      ],
      "registrationTrends": [
        {
          "date": "2024-06-06",
          "count": 3
        },
        {
          "date": "2024-06-05",
          "count": 5
        }
      ]
    }
  }
}
```

### 7. Search Customers

**GET** `/api/v1/customers/search`

Search customers by name, email, or phone.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `q` | string | Yes | عبارت جستجو |
| `limit` | number | No | حد نتایج (پیش‌فرض: 10) |
| `includeInactive` | boolean | No | شامل کاربران غیرفعال (پیش‌فرض: false) |

#### Response Example

```json
{
  "success": true,
  "message": "نتایج جستجو با موفقیت دریافت شد",
  "data": {
    "customers": [
      {
        "id": "clx1234567890",
        "email": "<EMAIL>",
        "firstName": "احمد",
        "lastName": "محمدی",
        "phone": "09123456789",
        "avatar": null,
        "status": "ACTIVE",
        "role": "CUSTOMER",
        "createdAt": "2024-01-15T08:00:00.000Z",
        "_count": {
          "orders": 5
        }
      }
    ]
  }
}
```

### 8. Bulk Update Customer Status

**PATCH** `/api/v1/customers/bulk-status`

Update status for multiple customers at once.

#### Request Body

```json
{
  "customerIds": ["clx1234567890", "clx1234567891"],
  "status": "SUSPENDED",
  "reason": "نقض قوانین استفاده"
}
```

#### Response Example

```json
{
  "success": true,
  "message": "وضعیت مشتریان با موفقیت به‌روزرسانی شد",
  "data": {
    "updated": 2,
    "errors": []
  }
}
```

### 9. Export Customers

**GET** `/api/v1/customers/export`

Export customer data in CSV or JSON format.

#### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `format` | string | فرمت خروجی (csv/json) | `csv` |
| All filter parameters from "Get All Customers" are supported |

#### Response (CSV)

Returns CSV file with UTF-8 BOM encoding for proper Persian character display.

#### Response (JSON)

```json
{
  "success": true,
  "message": "اطلاعات مشتریان با موفقیت صادر شد",
  "data": {
    "customers": [
      // Array of customer objects
    ]
  }
}
```

### 10. Get Customer Order History

**GET** `/api/v1/customers/:id/orders`

Get detailed order history for a specific customer.

#### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `page` | number | صفحه | `1` |
| `limit` | number | تعداد نتایج | `10` |
| `status` | string | فیلتر وضعیت سفارش | - |

#### Response Example

```json
{
  "success": true,
  "message": "تاریخچه سفارش‌های مشتری با موفقیت دریافت شد",
  "data": {
    "orders": [
      {
        "id": "order123",
        "orderNumber": "GR12345678901",
        "status": "DELIVERED",
        "totalAmount": 500000,
        "paymentStatus": "COMPLETED",
        "fulfillmentStatus": "FULFILLED",
        "createdAt": "2024-06-01T10:00:00.000Z",
        "updatedAt": "2024-06-05T14:30:00.000Z",
        "items": [
          {
            "id": "item123",
            "quantity": 2,
            "unitPrice": 250000,
            "product": {
              "id": "prod123",
              "name": "کرم مرطوب‌کننده",
              "slug": "moisturizing-cream",
              "images": [
                {
                  "url": "/uploads/products/cream.jpg"
                }
              ]
            }
          }
        ],
        "shippingAddress": {
          "id": "addr123",
          "title": "منزل",
          "firstName": "احمد",
          "lastName": "محمدی",
          "phone": "09123456789",
          "province": "تهران",
          "city": "تهران",
          "district": "ونک",
          "street": "خیابان ولیعصر، پلاک 123",
          "postalCode": "1234567890"
        },
        "payments": [
          {
            "id": "payment123",
            "amount": 500000,
            "status": "COMPLETED",
            "method": "ONLINE",
            "createdAt": "2024-06-01T10:05:00.000Z"
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    },
    "summary": {
      "totalOrders": 5,
      "totalSpent": 2500000,
      "averageOrderValue": 500000
    }
  }
}
```

---

## Error Codes

| Code | Description |
|------|-------------|
| `CUSTOMER_NOT_FOUND` | مشتری یافت نشد |
| `EMAIL_ALREADY_EXISTS` | این ایمیل قبلاً ثبت شده است |
| `PHONE_ALREADY_EXISTS` | این شماره تلفن قبلاً ثبت شده است |
| `CUSTOMER_HAS_ACTIVE_ORDERS` | امکان حذف مشتری با سفارش‌های فعال وجود ندارد |
| `SEARCH_QUERY_REQUIRED` | پارامتر جستجو الزامی است |
| `CUSTOMER_IDS_REQUIRED` | لیست شناسه مشتریان الزامی است |
| `INVALID_STATUS` | وضعیت نامعتبر است |
| `GET_CUSTOMERS_ERROR` | خطا در دریافت لیست مشتریان |
| `CREATE_CUSTOMER_ERROR` | خطا در ایجاد مشتری جدید |
| `UPDATE_CUSTOMER_ERROR` | خطا در به‌روزرسانی اطلاعات مشتری |
| `DELETE_CUSTOMER_ERROR` | خطا در حذف مشتری |
| `GET_CUSTOMER_STATS_ERROR` | خطا در دریافت آمار مشتریان |
| `SEARCH_CUSTOMERS_ERROR` | خطا در جستجوی مشتریان |
| `BULK_UPDATE_ERROR` | خطا در به‌روزرسانی گروهی |
| `EXPORT_CUSTOMERS_ERROR` | خطا در صادرات اطلاعات مشتریان |
| `GET_ORDER_HISTORY_ERROR` | خطا در دریافت تاریخچه سفارش‌ها |

---

## Integration Examples

### JavaScript/Fetch

```javascript
// Get customers with filtering
const getCustomers = async (filters = {}) => {
  const queryParams = new URLSearchParams(filters);
  const response = await fetch(`/api/v1/customers?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// Create new customer
const createCustomer = async (customerData) => {
  const response = await fetch('/api/v1/customers', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(customerData)
  });
  return response.json();
};

// Search customers
const searchCustomers = async (query, options = {}) => {
  const params = new URLSearchParams({ q: query, ...options });
  const response = await fetch(`/api/v1/customers/search?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// Export customers
const exportCustomers = async (format = 'csv', filters = {}) => {
  const params = new URLSearchParams({ format, ...filters });
  const response = await fetch(`/api/v1/customers/export?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  if (format === 'csv') {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'customers.csv';
    a.click();
  } else {
    return response.json();
  }
};
```

### cURL Examples

```bash
# Get customers with search and filtering
curl -X GET "http://localhost:3001/api/v1/customers?search=احمد&status=ACTIVE&page=1&limit=20&include=addresses,orderStats" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Create customer
curl -X POST http://localhost:3001/api/v1/customers \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "firstName": "احمد",
    "lastName": "محمدی",
    "phone": "09123456789",
    "role": "CUSTOMER",
    "status": "ACTIVE"
  }'

# Update customer
curl -X PUT http://localhost:3001/api/v1/customers/clx1234567890 \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "احمد رضا",
    "status": "ACTIVE",
    "isEmailVerified": true
  }'

# Search customers
curl -X GET "http://localhost:3001/api/v1/customers/search?q=احمد&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get customer statistics
curl -X GET http://localhost:3001/api/v1/customers/statistics \
  -H "Authorization: Bearer YOUR_TOKEN"

# Bulk update customer status
curl -X PATCH http://localhost:3001/api/v1/customers/bulk-status \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customerIds": ["clx1234567890", "clx1234567891"],
    "status": "SUSPENDED",
    "reason": "نقض قوانین استفاده"
  }'

# Export customers as CSV
curl -X GET "http://localhost:3001/api/v1/customers/export?format=csv&status=ACTIVE" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o customers.csv

# Get customer order history
curl -X GET "http://localhost:3001/api/v1/customers/clx1234567890/orders?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Performance Considerations

### Database Optimization

- **Indexing**: Proper indexes on frequently queried fields (email, phone, status, createdAt)
- **Pagination**: All list endpoints support pagination to handle large datasets
- **Selective Loading**: Use `include` parameter to load only required related data
- **Aggregation**: Customer statistics use efficient database aggregation queries

### Caching Recommendations

- Cache customer statistics for 5-10 minutes
- Cache search results for frequently used queries
- Use Redis for session management and temporary data

### Rate Limiting

- Apply rate limiting on search endpoints to prevent abuse
- Implement stricter limits for export functionality
- Monitor bulk operations for performance impact

---

## Security Features

### Authentication & Authorization

- **JWT Token**: All endpoints require valid admin JWT token
- **Role-based Access**: Only admin users can access customer management endpoints
- **Session Management**: Automatic session invalidation for suspended/banned users

### Data Protection

- **Password Hashing**: Customer passwords are securely hashed using bcrypt
- **Soft Delete**: Customer deletion is soft delete to maintain data integrity
- **Input Validation**: Comprehensive validation on all input fields
- **SQL Injection Prevention**: Prisma ORM provides built-in protection

### Privacy Compliance

- **Data Anonymization**: Deleted customers have emails anonymized
- **Selective Data Export**: Export functionality respects data privacy requirements
- **Audit Trail**: All customer operations are logged for audit purposes

---

## Testing

### Unit Tests

```bash
# Run customer service tests
npm test -- --testPathPattern=customerService

# Run customer controller tests
npm test -- --testPathPattern=customerController

# Run customer routes tests
npm test -- --testPathPattern=customers.routes
```

### Integration Tests

```bash
# Run full customer management API tests
npm test -- --testPathPattern=customer.integration

# Test with real database
npm run test:integration
```

### Load Testing

```bash
# Test customer list endpoint performance
ab -n 1000 -c 10 -H "Authorization: Bearer TOKEN" \
  http://localhost:3001/api/v1/customers

# Test search endpoint performance
ab -n 500 -c 5 -H "Authorization: Bearer TOKEN" \
  "http://localhost:3001/api/v1/customers/search?q=test"
```
```
