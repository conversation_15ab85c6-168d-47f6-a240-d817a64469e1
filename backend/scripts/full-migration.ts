import { exec } from 'child_process';
import { promisify } from 'util';
import StaticFilesMigrator from './migrate-static-files';

const execAsync = promisify(exec);

class FullMigrationRunner {
  private migrator: StaticFilesMigrator;

  constructor() {
    this.migrator = new StaticFilesMigrator();
  }

  async runCommand(command: string, description: string): Promise<void> {
    console.log(`\n🔄 ${description}...`);
    try {
      const { stdout, stderr } = await execAsync(command);
      if (stdout) console.log(stdout);
      if (stderr) console.warn(stderr);
      console.log(`✅ ${description} completed successfully`);
    } catch (error) {
      console.error(`❌ ${description} failed:`, (error as Error).message);
      throw error;
    }
  }

  async checkPrerequisites(): Promise<void> {
    console.log('🔍 Checking prerequisites...');
    
    try {
      // Check if Prisma is available
      await execAsync('npx prisma --version');
      console.log('✅ Prisma CLI available');
      
      // Check if TypeScript is available
      await execAsync('npx tsc --version');
      console.log('✅ TypeScript available');
      
      // Check if database connection works
      await execAsync('npx prisma db pull --preview-feature');
      console.log('✅ Database connection working');
      
    } catch (error) {
      console.error('❌ Prerequisites check failed:', (error as Error).message);
      throw error;
    }
  }

  async runFullMigration(): Promise<void> {
    console.log('🚀 Starting Full Migration Process...');
    console.log('📋 This will migrate all frontend mock data and static files to backend');
    console.log('⏱️  Estimated time: 2-3 minutes\n');

    try {
      // Step 1: Check prerequisites
      await this.checkPrerequisites();

      // Step 2: Generate Prisma client
      await this.runCommand(
        'npx prisma generate',
        'Generating Prisma client'
      );

      // Step 3: Apply database migrations
      await this.runCommand(
        'npx prisma db push',
        'Applying database schema'
      );

      // Step 4: Migrate static files
      console.log('\n📁 Migrating static files...');
      await this.migrator.run();

      // Step 5: Seed database with enhanced data
      await this.runCommand(
        'npx prisma db seed',
        'Seeding database with enhanced mock data'
      );

      // Step 6: Build backend
      await this.runCommand(
        'npm run build',
        'Building backend application'
      );

      console.log('\n🎉 Full Migration Completed Successfully!');
      console.log('\n📊 Migration Results:');
      console.log('   ✅ Static files migrated to backend/uploads/');
      console.log('   ✅ Brand logos available at /uploads/brands/');
      console.log('   ✅ Product images configured with backend URLs');
      console.log('   ✅ Enhanced product data seeded to database');
      console.log('   ✅ Categories, brands, and users created');
      console.log('   ✅ Sample reviews and inventory data added');
      console.log('   ✅ Backend application built and ready');

      console.log('\n🚀 Next Steps:');
      console.log('   1. Start backend server: npm run dev');
      console.log('   2. Backend will be available at: http://localhost:3001');
      console.log('   3. Static files served at: http://localhost:3001/uploads/');
      console.log('   4. API documentation: http://localhost:3001/api/v1/docs');
      console.log('   5. Update frontend to use backend APIs instead of mock data');

      console.log('\n📝 Important Notes:');
      console.log('   - All product images are currently placeholder paths');
      console.log('   - Upload actual product images through admin panel');
      console.log('   - Brand logos are migrated and ready to use');
      console.log('   - Database contains rich Persian product descriptions');
      console.log('   - All data maintains Persian/RTL support');

    } catch (error) {
      console.error('\n💥 Migration failed:', (error as Error).message);
      console.log('\n🔧 Troubleshooting:');
      console.log('   1. Ensure PostgreSQL is running');
      console.log('   2. Check database connection in .env file');
      console.log('   3. Verify all dependencies are installed: npm install');
      console.log('   4. Check if ports 3001 is available');
      console.log('   5. Review error logs above for specific issues');
      process.exit(1);
    }
  }

  async showMigrationStatus(): Promise<void> {
    console.log('📊 Migration Status Check...\n');

    try {
      // Check database connection
      await execAsync('npx prisma db pull --preview-feature');
      console.log('✅ Database: Connected');
    } catch {
      console.log('❌ Database: Connection failed');
    }

    try {
      // Check if uploads directory exists
      const fs = require('fs');
      if (fs.existsSync('./uploads')) {
        console.log('✅ Uploads Directory: Exists');
        
        // Check subdirectories
        const subdirs = ['brands', 'products', 'categories', 'assets', 'icons'];
        for (const subdir of subdirs) {
          if (fs.existsSync(`./uploads/${subdir}`)) {
            console.log(`   ✅ uploads/${subdir}/: Ready`);
          } else {
            console.log(`   ❌ uploads/${subdir}/: Missing`);
          }
        }
      } else {
        console.log('❌ Uploads Directory: Missing');
      }
    } catch {
      console.log('❌ Uploads Directory: Check failed');
    }

    try {
      // Check if build exists
      const fs = require('fs');
      if (fs.existsSync('./dist')) {
        console.log('✅ Build: Ready');
      } else {
        console.log('❌ Build: Missing (run npm run build)');
      }
    } catch {
      console.log('❌ Build: Check failed');
    }

    console.log('\n💡 Run "npm run migrate:all" to perform full migration');
  }
}

// CLI interface
const args = process.argv.slice(2);
const command = args[0];

const runner = new FullMigrationRunner();

switch (command) {
  case 'run':
  case 'full':
    runner.runFullMigration().catch(console.error);
    break;
  case 'status':
  case 'check':
    runner.showMigrationStatus().catch(console.error);
    break;
  default:
    console.log('🔧 GlowRoya Migration Tool');
    console.log('\nUsage:');
    console.log('  npm run migrate:all           - Run full migration');
    console.log('  ts-node scripts/full-migration.ts run   - Run full migration');
    console.log('  ts-node scripts/full-migration.ts status - Check migration status');
    console.log('\nCommands:');
    console.log('  run, full    - Execute complete migration process');
    console.log('  status, check - Check current migration status');
    break;
}

export default FullMigrationRunner;
