#!/bin/bash

# GlowRoya Migration Verification Script
# This script verifies that all migration components are working correctly

echo "🔍 Starting Migration Verification..."
echo "=================================="

BASE_URL="http://localhost:3001"
PASS_COUNT=0
FAIL_COUNT=0

# Function to test endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="$3"
    
    echo -n "Testing $name... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$url" 2>/dev/null)
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo "✅ PASS"
        ((PASS_COUNT++))
        return 0
    else
        echo "❌ FAIL (Status: $status_code)"
        ((FAIL_COUNT++))
        return 1
    fi
}

# Function to test JSON response
test_json_endpoint() {
    local name="$1"
    local url="$2"
    local check_field="$3"
    
    echo -n "Testing $name... "
    
    response=$(curl -s "$url" 2>/dev/null)
    
    if echo "$response" | jq -e ".$check_field" > /dev/null 2>&1; then
        count=$(echo "$response" | jq -r ".data | length" 2>/dev/null || echo "N/A")
        echo "✅ PASS (Items: $count)"
        ((PASS_COUNT++))
        return 0
    else
        echo "❌ FAIL (Invalid JSON or missing field)"
        ((FAIL_COUNT++))
        return 1
    fi
}

echo ""
echo "🏥 Health Check"
echo "---------------"
test_endpoint "Server Health" "$BASE_URL/health" "200"

echo ""
echo "🔌 API Endpoints"
echo "----------------"
test_json_endpoint "Products API" "$BASE_URL/api/v1/products" "success"
test_json_endpoint "Categories API" "$BASE_URL/api/v1/categories" "success"
test_json_endpoint "Brands API" "$BASE_URL/api/v1/brands" "success"

echo ""
echo "🖼️ Static Files"
echo "---------------"
test_endpoint "CeraVe Logo" "$BASE_URL/uploads/brands/cerave-logo.png" "200"
test_endpoint "Garnier Logo" "$BASE_URL/uploads/brands/garnier-logo.png" "200"
test_endpoint "App Logo" "$BASE_URL/uploads/assets/app-logo.png" "200"
test_endpoint "Product Image" "$BASE_URL/uploads/products/serums/hyaluronic-acid-serum-1.jpg" "200"

echo ""
echo "📁 Directory Structure"
echo "----------------------"

# Check uploads directory structure
if [ -d "uploads" ]; then
    echo "✅ Uploads directory exists"
    ((PASS_COUNT++))
    
    for dir in brands products assets icons; do
        if [ -d "uploads/$dir" ]; then
            echo "✅ uploads/$dir/ exists"
            ((PASS_COUNT++))
        else
            echo "❌ uploads/$dir/ missing"
            ((FAIL_COUNT++))
        fi
    done
else
    echo "❌ Uploads directory missing"
    ((FAIL_COUNT++))
fi

echo ""
echo "🗄️ Database Content"
echo "-------------------"

# Test product data quality
echo -n "Testing Product Data Quality... "
products_response=$(curl -s "$BASE_URL/api/v1/products" 2>/dev/null)

if echo "$products_response" | jq -e '.success' > /dev/null 2>&1; then
    product_count=$(echo "$products_response" | jq -r '.data | length')
    persian_count=$(echo "$products_response" | jq -r '[.data[] | select(.name | test("[\u0600-\u06FF]"))] | length')
    
    if [ "$product_count" -gt 0 ] && [ "$persian_count" -gt 0 ]; then
        echo "✅ PASS ($product_count products, $persian_count with Persian names)"
        ((PASS_COUNT++))
    else
        echo "❌ FAIL (No products or no Persian content)"
        ((FAIL_COUNT++))
    fi
else
    echo "❌ FAIL (Could not fetch products)"
    ((FAIL_COUNT++))
fi

# Test brand logos
echo -n "Testing Brand Logos... "
brands_response=$(curl -s "$BASE_URL/api/v1/brands" 2>/dev/null)

if echo "$brands_response" | jq -e '.success' > /dev/null 2>&1; then
    brand_count=$(echo "$brands_response" | jq -r '.data | length')
    logo_count=$(echo "$brands_response" | jq -r '[.data[] | select(.logo != null)] | length')
    
    if [ "$brand_count" -gt 0 ] && [ "$logo_count" -gt 0 ]; then
        echo "✅ PASS ($brand_count brands, $logo_count with logos)"
        ((PASS_COUNT++))
    else
        echo "❌ FAIL (No brands or no logos)"
        ((FAIL_COUNT++))
    fi
else
    echo "❌ FAIL (Could not fetch brands)"
    ((FAIL_COUNT++))
fi

echo ""
echo "📊 Migration Verification Summary"
echo "================================="

TOTAL_TESTS=$((PASS_COUNT + FAIL_COUNT))
SUCCESS_RATE=$((PASS_COUNT * 100 / TOTAL_TESTS))

echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASS_COUNT"
echo "Failed: $FAIL_COUNT"
echo "Success Rate: $SUCCESS_RATE%"

echo ""

if [ $SUCCESS_RATE -ge 90 ]; then
    echo "🎉 Migration verification PASSED! System is ready for development."
    exit 0
elif [ $SUCCESS_RATE -ge 70 ]; then
    echo "⚠️  Migration verification PARTIAL. Some issues need attention."
    exit 1
else
    echo "❌ Migration verification FAILED. Critical issues need to be resolved."
    exit 2
fi
