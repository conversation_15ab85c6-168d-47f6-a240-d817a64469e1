import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const copyFile = promisify(fs.copyFile);
const mkdir = promisify(fs.mkdir);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

interface FileMigration {
  source: string;
  destination: string;
  description: string;
}

class StaticFilesMigrator {
  private frontendPath: string;
  private backendPath: string;
  private uploadsPath: string;

  constructor() {
    this.frontendPath = path.resolve(__dirname, '../..');
    this.backendPath = path.resolve(__dirname, '..');
    this.uploadsPath = path.join(this.backendPath, 'uploads');
  }

  async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await mkdir(dirPath, { recursive: true });
      console.log(`✅ Directory ensured: ${dirPath}`);
    } catch (error) {
      if ((error as any).code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async copyFileWithLogging(source: string, destination: string, description: string): Promise<void> {
    try {
      const sourceStats = await stat(source);
      if (!sourceStats.isFile()) {
        console.log(`⚠️  Skipping ${source} - not a file`);
        return;
      }

      await copyFile(source, destination);
      console.log(`✅ ${description}: ${path.basename(source)} → ${destination}`);
    } catch (error) {
      console.error(`❌ Failed to copy ${description}: ${source} → ${destination}`);
      console.error(`   Error: ${(error as Error).message}`);
    }
  }

  async migrateBrandLogos(): Promise<void> {
    console.log('\n🏷️  Migrating Brand Logos...');
    
    const brandsDir = path.join(this.uploadsPath, 'brands');
    await this.ensureDirectoryExists(brandsDir);

    const brandFiles: FileMigration[] = [
      {
        source: path.join(this.frontendPath, 'public/CeraVe.png'),
        destination: path.join(brandsDir, 'cerave-logo.png'),
        description: 'CeraVe Brand Logo'
      },
      {
        source: path.join(this.frontendPath, 'public/Garnier-Logo.png'),
        destination: path.join(brandsDir, 'garnier-logo.png'),
        description: 'Garnier Brand Logo'
      },
      {
        source: path.join(this.frontendPath, 'public/Nivea-Logo.png'),
        destination: path.join(brandsDir, 'nivea-logo.png'),
        description: 'Nivea Brand Logo'
      },
      {
        source: path.join(this.frontendPath, 'public/loreal.jpg'),
        destination: path.join(brandsDir, 'loreal-logo.jpg'),
        description: 'L\'Oreal Brand Logo'
      },
      {
        source: path.join(this.frontendPath, 'public/loreal-.png'),
        destination: path.join(brandsDir, 'loreal-logo-alt.png'),
        description: 'L\'Oreal Brand Logo (Alt)'
      }
    ];

    for (const file of brandFiles) {
      await this.copyFileWithLogging(file.source, file.destination, file.description);
    }
  }

  async migrateAppAssets(): Promise<void> {
    console.log('\n🎨 Migrating App Assets...');
    
    const assetsDir = path.join(this.uploadsPath, 'assets');
    await this.ensureDirectoryExists(assetsDir);

    const assetFiles: FileMigration[] = [
      {
        source: path.join(this.frontendPath, 'public/logo.png'),
        destination: path.join(assetsDir, 'app-logo.png'),
        description: 'App Logo'
      },
      {
        source: path.join(this.frontendPath, 'public/favicon.ico'),
        destination: path.join(assetsDir, 'favicon.ico'),
        description: 'Favicon'
      }
    ];

    for (const file of assetFiles) {
      await this.copyFileWithLogging(file.source, file.destination, file.description);
    }
  }

  async migrateIcons(): Promise<void> {
    console.log('\n📱 Migrating PWA Icons...');
    
    const iconsDir = path.join(this.uploadsPath, 'icons');
    await this.ensureDirectoryExists(iconsDir);

    const sourceIconsDir = path.join(this.frontendPath, 'public/icons');
    
    try {
      const iconFiles = await readdir(sourceIconsDir);
      
      for (const iconFile of iconFiles) {
        if (iconFile.endsWith('.png') || iconFile.endsWith('.svg')) {
          const source = path.join(sourceIconsDir, iconFile);
          const destination = path.join(iconsDir, iconFile);
          await this.copyFileWithLogging(source, destination, `PWA Icon: ${iconFile}`);
        }
      }
    } catch (error) {
      console.error(`❌ Failed to read icons directory: ${(error as Error).message}`);
    }
  }

  async createPlaceholderProductImages(): Promise<void> {
    console.log('\n🖼️  Creating Product Image Directories...');
    
    const productDirs = [
      'products/serums',
      'products/creams',
      'products/cleansers',
      'products/masks',
      'products/toners',
      'categories',
      'temp'
    ];

    for (const dir of productDirs) {
      const fullPath = path.join(this.uploadsPath, dir);
      await this.ensureDirectoryExists(fullPath);
    }

    // Create a placeholder README file
    const readmePath = path.join(this.uploadsPath, 'README.md');
    const readmeContent = `# GlowRoya Upload Directory

This directory contains all uploaded media files for the GlowRoya e-commerce platform.

## Directory Structure:

- \`brands/\` - Brand logos and assets
- \`products/\` - Product images organized by category
  - \`serums/\` - Serum product images
  - \`creams/\` - Cream product images
  - \`cleansers/\` - Cleanser product images
  - \`masks/\` - Mask product images
  - \`toners/\` - Toner product images
- \`categories/\` - Category images and banners
- \`assets/\` - App logos, favicons, and general assets
- \`icons/\` - PWA icons and app icons
- \`temp/\` - Temporary upload files

## File Naming Convention:

- Brand logos: \`{brand-slug}-logo.{ext}\`
- Product images: \`{product-slug}-{variant}.{ext}\`
- Category images: \`{category-slug}-banner.{ext}\`

## Image Optimization:

All images are automatically optimized using Sharp:
- WebP format for modern browsers
- JPEG fallback for compatibility
- Multiple sizes for responsive design
- Compression for optimal loading

Generated on: ${new Date().toISOString()}
`;

    try {
      await fs.promises.writeFile(readmePath, readmeContent, 'utf8');
      console.log('✅ Created uploads README.md');
    } catch (error) {
      console.error(`❌ Failed to create README: ${(error as Error).message}`);
    }
  }

  async run(): Promise<void> {
    console.log('🚀 Starting Static Files Migration...');
    console.log(`📁 Frontend Path: ${this.frontendPath}`);
    console.log(`📁 Backend Path: ${this.backendPath}`);
    console.log(`📁 Uploads Path: ${this.uploadsPath}`);

    try {
      await this.ensureDirectoryExists(this.uploadsPath);
      await this.migrateBrandLogos();
      await this.migrateAppAssets();
      await this.migrateIcons();
      await this.createPlaceholderProductImages();

      console.log('\n✅ Static Files Migration Completed Successfully!');
      console.log('\n📊 Migration Summary:');
      console.log('   - Brand logos migrated to uploads/brands/');
      console.log('   - App assets migrated to uploads/assets/');
      console.log('   - PWA icons migrated to uploads/icons/');
      console.log('   - Product directories created');
      console.log('   - Documentation created');
      
    } catch (error) {
      console.error('\n❌ Migration failed:', (error as Error).message);
      process.exit(1);
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  const migrator = new StaticFilesMigrator();
  migrator.run().catch(console.error);
}

export default StaticFilesMigrator;
