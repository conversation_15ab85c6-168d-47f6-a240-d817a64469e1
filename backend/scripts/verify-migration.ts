import fs from 'fs';
import path from 'path';

// Use global fetch (Node 18+) or create a simple alternative
const fetch = globalThis.fetch || require('node-fetch');

interface VerificationResult {
  test: string;
  status: 'PASS' | 'FAIL';
  message: string;
  data?: any;
}

class MigrationVerifier {
  private baseUrl: string;
  private results: VerificationResult[] = [];

  constructor(baseUrl: string = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
  }

  private async addResult(test: string, status: 'PASS' | 'FAIL', message: string, data?: any) {
    this.results.push({ test, status, message, data });
    const emoji = status === 'PASS' ? '✅' : '❌';
    console.log(`${emoji} ${test}: ${message}`);
  }

  async verifyHealthCheck(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      const data = await response.json();
      
      if (response.ok && data.status === 'healthy') {
        await this.addResult(
          'Health Check',
          'PASS',
          `Server healthy, uptime: ${Math.round(data.uptime)}s`,
          data
        );
      } else {
        await this.addResult('Health Check', 'FAIL', 'Server not healthy');
      }
    } catch (error) {
      await this.addResult('Health Check', 'FAIL', `Server not responding: ${(error as Error).message}`);
    }
  }

  async verifyProductsAPI(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/products`);
      const data = await response.json();
      
      if (response.ok && data.success && Array.isArray(data.data)) {
        const productCount = data.data.length;
        const persianProducts = data.data.filter((p: any) => 
          p.name && /[\u0600-\u06FF]/.test(p.name)
        ).length;
        
        await this.addResult(
          'Products API',
          'PASS',
          `${productCount} products loaded, ${persianProducts} with Persian names`,
          { total: productCount, persian: persianProducts }
        );
      } else {
        await this.addResult('Products API', 'FAIL', 'Invalid response format');
      }
    } catch (error) {
      await this.addResult('Products API', 'FAIL', `API error: ${(error as Error).message}`);
    }
  }

  async verifyCategoriesAPI(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/categories`);
      const data = await response.json();
      
      if (response.ok && data.success && Array.isArray(data.data)) {
        const categoryCount = data.data.length;
        const persianCategories = data.data.filter((c: any) => 
          c.name && /[\u0600-\u06FF]/.test(c.name)
        ).length;
        
        await this.addResult(
          'Categories API',
          'PASS',
          `${categoryCount} categories loaded, ${persianCategories} with Persian names`,
          { total: categoryCount, persian: persianCategories }
        );
      } else {
        await this.addResult('Categories API', 'FAIL', 'Invalid response format');
      }
    } catch (error) {
      await this.addResult('Categories API', 'FAIL', `API error: ${(error as Error).message}`);
    }
  }

  async verifyBrandsAPI(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/brands`);
      const data = await response.json();
      
      if (response.ok && data.success && Array.isArray(data.data)) {
        const brandCount = data.data.length;
        const brandsWithLogos = data.data.filter((b: any) => b.logo).length;
        
        await this.addResult(
          'Brands API',
          'PASS',
          `${brandCount} brands loaded, ${brandsWithLogos} with logos`,
          { total: brandCount, withLogos: brandsWithLogos }
        );
      } else {
        await this.addResult('Brands API', 'FAIL', 'Invalid response format');
      }
    } catch (error) {
      await this.addResult('Brands API', 'FAIL', `API error: ${(error as Error).message}`);
    }
  }

  async verifyStaticFiles(): Promise<void> {
    const staticFiles = [
      '/uploads/brands/cerave-logo.png',
      '/uploads/brands/garnier-logo.png',
      '/uploads/brands/nivea-logo.png',
      '/uploads/brands/loreal-logo.jpg',
      '/uploads/assets/app-logo.png',
      '/uploads/products/serums/hyaluronic-acid-serum-1.jpg',
      '/uploads/products/creams/bb-cream-spf-30-1.jpg'
    ];

    let successCount = 0;
    
    for (const file of staticFiles) {
      try {
        const response = await fetch(`${this.baseUrl}${file}`);
        if (response.ok) {
          successCount++;
        }
      } catch (error) {
        // File not accessible
      }
    }

    if (successCount === staticFiles.length) {
      await this.addResult(
        'Static Files',
        'PASS',
        `All ${staticFiles.length} test files accessible`,
        { accessible: successCount, total: staticFiles.length }
      );
    } else {
      await this.addResult(
        'Static Files',
        'FAIL',
        `Only ${successCount}/${staticFiles.length} files accessible`
      );
    }
  }

  async verifyUploadsDirectory(): Promise<void> {
    const uploadsPath = path.join(__dirname, '../uploads');
    const requiredDirs = ['brands', 'products', 'assets', 'icons'];
    
    try {
      const stats = await fs.promises.stat(uploadsPath);
      if (!stats.isDirectory()) {
        await this.addResult('Uploads Directory', 'FAIL', 'Uploads path is not a directory');
        return;
      }

      let existingDirs = 0;
      for (const dir of requiredDirs) {
        const dirPath = path.join(uploadsPath, dir);
        try {
          const dirStats = await fs.promises.stat(dirPath);
          if (dirStats.isDirectory()) {
            existingDirs++;
          }
        } catch {
          // Directory doesn't exist
        }
      }

      if (existingDirs === requiredDirs.length) {
        await this.addResult(
          'Uploads Directory',
          'PASS',
          `All ${requiredDirs.length} required directories exist`,
          { existing: existingDirs, required: requiredDirs.length }
        );
      } else {
        await this.addResult(
          'Uploads Directory',
          'FAIL',
          `Only ${existingDirs}/${requiredDirs.length} directories exist`
        );
      }
    } catch (error) {
      await this.addResult('Uploads Directory', 'FAIL', `Directory check failed: ${(error as Error).message}`);
    }
  }

  async verifyProductImages(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/products`);
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        await this.addResult('Product Images', 'FAIL', 'Could not fetch products');
        return;
      }

      const products = data.data;
      let productsWithImages = 0;
      let totalImages = 0;

      for (const product of products) {
        if (product.images && product.images.length > 0) {
          productsWithImages++;
          totalImages += product.images.length;
        }
      }

      if (productsWithImages > 0) {
        await this.addResult(
          'Product Images',
          'PASS',
          `${productsWithImages}/${products.length} products have images (${totalImages} total)`,
          { productsWithImages, totalProducts: products.length, totalImages }
        );
      } else {
        await this.addResult('Product Images', 'FAIL', 'No products have images configured');
      }
    } catch (error) {
      await this.addResult('Product Images', 'FAIL', `Image verification failed: ${(error as Error).message}`);
    }
  }

  async verifyPersianContent(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/products`);
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        await this.addResult('Persian Content', 'FAIL', 'Could not fetch products');
        return;
      }

      const products = data.data;
      const persianRegex = /[\u0600-\u06FF]/;
      
      let persianNames = 0;
      let persianDescriptions = 0;
      let persianTags = 0;

      for (const product of products) {
        if (product.name && persianRegex.test(product.name)) {
          persianNames++;
        }
        if (product.description && persianRegex.test(product.description)) {
          persianDescriptions++;
        }
        if (product.tags && product.tags.some((tag: string) => persianRegex.test(tag))) {
          persianTags++;
        }
      }

      const totalProducts = products.length;
      const persianScore = Math.round(((persianNames + persianDescriptions + persianTags) / (totalProducts * 3)) * 100);

      if (persianScore >= 80) {
        await this.addResult(
          'Persian Content',
          'PASS',
          `${persianScore}% Persian content coverage (${persianNames} names, ${persianDescriptions} descriptions, ${persianTags} tags)`,
          { score: persianScore, names: persianNames, descriptions: persianDescriptions, tags: persianTags }
        );
      } else {
        await this.addResult(
          'Persian Content',
          'FAIL',
          `Only ${persianScore}% Persian content coverage`
        );
      }
    } catch (error) {
      await this.addResult('Persian Content', 'FAIL', `Persian content check failed: ${(error as Error).message}`);
    }
  }

  async runAllVerifications(): Promise<void> {
    console.log('🔍 Starting Migration Verification...\n');

    await this.verifyHealthCheck();
    await this.verifyProductsAPI();
    await this.verifyCategoriesAPI();
    await this.verifyBrandsAPI();
    await this.verifyStaticFiles();
    await this.verifyUploadsDirectory();
    await this.verifyProductImages();
    await this.verifyPersianContent();

    this.generateReport();
  }

  private generateReport(): void {
    const passCount = this.results.filter(r => r.status === 'PASS').length;
    const failCount = this.results.filter(r => r.status === 'FAIL').length;
    const totalTests = this.results.length;
    const successRate = Math.round((passCount / totalTests) * 100);

    console.log('\n📊 Migration Verification Report');
    console.log('================================');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passCount}`);
    console.log(`Failed: ${failCount}`);
    console.log(`Success Rate: ${successRate}%`);

    if (successRate >= 90) {
      console.log('\n🎉 Migration verification PASSED! System is ready for development.');
    } else if (successRate >= 70) {
      console.log('\n⚠️  Migration verification PARTIAL. Some issues need attention.');
    } else {
      console.log('\n❌ Migration verification FAILED. Critical issues need to be resolved.');
    }

    // Save detailed report
    const reportPath = path.join(__dirname, '../verification-report.json');
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: totalTests,
        passed: passCount,
        failed: failCount,
        successRate
      },
      results: this.results
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }
}

// Run verification if called directly
if (require.main === module) {
  const verifier = new MigrationVerifier();
  verifier.runAllVerifications().catch(console.error);
}

export default MigrationVerifier;
